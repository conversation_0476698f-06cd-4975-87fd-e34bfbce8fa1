<template>
  <view class="container">
    <view
      class="container-box"
      v-for="i in list"
      :key="i.Name"
      @click="onClick(i)"
    >
      <image :src="i.Image" class="container-box-image"> </image>
      <text class="container-box-text">{{ i.Name }}</text>
      <view class="container-box-button" :style="{ color: i.TextColor }">
        立即查看
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      list: [
        {
          Name: '肺功能检查报告',
          Image: 'static/bg1.png',
          Type: [2],
          TextColor: '#7687F5',
        },
        {
          Name: '吸气测试报告',
          Image: 'static/bg2.png',
          Type: [1],
          TextColor: '#3FB9E0',
        },
        {
          Name: '呼吸肌力报告',
          Image: 'static/bg3.png',
          Type: [8, 9],
          TextColor: '#39D086',
        },
      ],
    };
  },
  onLoad() {},
  methods: {
    onClick(item) {
      if (item.Name === '呼吸肌力报告') {
        uni.navigateTo({
          url:
            './RDetail?Type=hxjlbg&Types=' +
            encodeURIComponent(JSON.stringify(item.Type)),
        });
      } else {
        uni.navigateTo({
          url:
            './WebView?Type=' + encodeURIComponent(JSON.stringify(item.Type)),
        });
      }
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  &-box {
    position: relative;
    width: 100%;
    border-radius: 16rpx;
    height: 278rpx;
    margin-bottom: 24rpx;

    &-button {
      width: 180rpx;
      height: 60rpx;
      background: #ffffff;
      box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);
      position: absolute;
      right: 32rpx;
      bottom: 32rpx;
      text-align: center;
      line-height: 60rpx;
      border-radius: 40rpx;
      font-size: 32rpx;
    }

    &-image {
      height: 100%;
      width: 100%;
    }

    &-text {
      position: absolute;
      top: 32rpx;
      left: 32rpx;
      z-index: 99;
      font-size: 44rpx;
      font-weight: 600;
      color: #ffffff;
    }
  }
}
</style>
