<template>
  <view class="container">
    <view class="container-data" v-for="(item, index) in list" :key="index">
      <view class="container-data-top">
        <text class="container-data-title"
          >治疗模式：{{ item.Report.ModeStr }}</text
        >
        <text class="container-data-time">{{ item.CreatedTime }}</text>
      </view>
      <u-divider />
      <view class="container-data-wapper">
        <view class="container-data-wapper-box">
          <text class="container-data-wapper-box-title">治疗时间</text>
          <view
            style="
              display: flex;
              align-items: baseline;
              justify-content: center;
            "
          >
            <text class="container-data-wapper-box-number">{{
              (item.Report.WorkingTime / 60).toFixed(1)
            }}</text>
            <text class="container-data-wapper-box-count">分钟</text>
          </view>
        </view>
        <view class="container-data-wapper-box">
          <text class="container-data-wapper-box-title">磁场强度</text>
          <text class="container-data-wapper-box-count">{{
            item.Report.MagneticIntensity
          }}</text>
        </view>
        <view class="container-data-wapper-box">
          <text class="container-data-wapper-box-title">磁场频率</text>
          <text class="container-data-wapper-box-count">{{
            item.Report.MagneticFrequency
          }}</text>
        </view>
        <view class="container-data-wapper-box">
          <text class="container-data-wapper-box-title">设定温度</text>
          <text class="container-data-wapper-box-count"
            >{{ item.Report.Temperature.toFixed(1) }}°</text
          >
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { statisticsReport } from '@/api/training.js';
export default {
  data() {
    return {
      list: [],
    };
  },
  onLoad(option) {
    // 获取用户的治疗数据
    this.handleGetUserTreatmentData(option.visitActionId);
  },
  methods: {
    async handleGetUserTreatmentData(visitId) {
      uni.showLoading({
        title: '加载中',
        mask: true,
      });
      const res = await statisticsReport({
        visitId,
        loadDetail: true,
      });
      if (res.Type === 200) {
        res.Data.Report.Data.Detail.forEach((s) => {
          s.CreatedTime = this.$dateFormat(
            s.CreatedTime,
            'YYYY-MM-DD HH:mm:ss'
          );
        });
        this.list = res.Data.Report.Data.Detail;
      }
      uni.hideLoading();
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  padding: 24rpx;
  background: #f7f6f6;

  &-data {
    background: #ffffff;
    box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);
    border-radius: 16rpx;
    padding: 34rpx;
    margin-bottom: 30rpx;

    &-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 28rpx;
    }

    &-title {
      font-weight: 400;
      font-size: 30rpx;
      color: #333333;
      line-height: 34rpx;
    }

    &-time {
      font-weight: 400;
      font-size: 22rpx;
      color: #999999;
      line-height: 34rpx;
    }

    &-wapper {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 70rpx;

      &-box {
        display: flex;
        flex-direction: column;
        align-items: center;

        &-title {
          font-weight: 400;
          font-size: 22rpx;
          color: #666666;
          line-height: 34rpx;
          margin-bottom: 16rpx;
        }

        &-count {
          font-weight: 400;
          font-size: 22rpx;
          color: #29b7a3;
          line-height: 34rpx;
          display: block;
        }

        &-number {
          font-weight: 500;
          font-size: 38rpx;
          line-height: 34rpx;
          color: #29b7a3;
        }
      }
    }
  }
}
</style>
