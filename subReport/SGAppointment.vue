<template>
  <view class="container">
    <view class="container-top">
      <view class="container-top-info">
        <view class="container-top-info-top">
          <image
            src="./static/yiyuan.png"
            class="container-top-info-top-img"
          ></image>
          <text class="container-top-info-top-text">
            成都汉和中医治未病医院
          </text>
        </view>
        <view class="container-top-info-text">
          作为新都区视力筛查定点机构，免费为学生进行基础复查服务。
        </view>
      </view>
      <view class="container-top-img"></view>
      <view class="container-top-bottom">
        <view class="container-top-bottom-text">
          <u-icon name="map" size="13"></u-icon>
          <span style="margin-left: 8rpx"
            >医院地址：新都区桂湖东路114号（原老中医院）</span
          >
        </view>
        <view class="container-top-bottom-text">
          <u-icon name="phone" size="13"></u-icon>
          <span style="margin-left: 8rpx">咨询电话：</span>
          <u-text mode="phone" :call="true" text="19938200734"></u-text>
        </view>
      </view>
    </view>
    <view class="container-box">
      <u-form labelPosition="left" :model="userInfo" :rules="rules" ref="uForm">
        <u-form-item
          labelWidth="80"
          required
          label="学校名称"
          prop="params.SchoolName"
          borderBottom
          ref="item1"
        >
          <!-- <u-input inputAlign="right" v-model="userInfo.params.SchoolName" border="none"></u-input> -->
          <view
            slot="right"
            style="display: flex; align-items: center"
            @click="onToSelectSchool"
          >
            <text>{{ userInfo.params.SchoolName || '请选择' }}</text>
            <u-icon name="arrow-right" size="18"></u-icon>
          </view>
        </u-form-item>
        <u-form-item
          label="姓名"
          required
          prop="params.Name"
          borderBottom
          ref="item1"
        >
          <u-input
            inputAlign="right"
            v-model="userInfo.params.Name"
            border="none"
          ></u-input>
        </u-form-item>
        <u-form-item
          labelWidth="80"
          required
          label="身份证号"
          prop="params.IdCard"
          borderBottom
          ref="item1"
        >
          <u-input
            @blur="onIdCardChange"
            inputAlign="right"
            v-model="userInfo.params.IdCard"
            border="none"
          ></u-input>
        </u-form-item>
        <!-- <u-form-item label="性别" required prop="params.Sex" borderBottom ref="item1">
					<u-input inputAlign="right" v-model="userInfo.params.Sex" border="none"></u-input>
				</u-form-item> -->
        <u-form-item
          labelWidth="80"
          required
          label="手机号"
          prop="params.Phone"
          borderBottom
          ref="item1"
        >
          <u-input
            inputAlign="right"
            v-model="userInfo.params.Phone"
            border="none"
          ></u-input>
        </u-form-item>
        <!-- <u-form-item labelWidth="80" required label="出生日期" prop="params.BrithDay" borderBottom ref="item1">
					<u-input placeholder="格式:2000-01-01" inputAlign="right" v-model="userInfo.params.BrithDay" border="none"></u-input>
				</u-form-item> -->
        <u-form-item
          labelWidth="80"
          required
          label="预约时间"
          prop="params.ScheId"
          borderBottom
          ref="item1"
        >
          <!-- <u-input inputAlign="right" v-model="userInfo.params.AppointmentTime" border="none"></u-input> -->
          <!-- <text slot="right" @click="onToSelectTime">{{ userInfo.params.ScheName || '请选择' }}</text> -->
          <view
            slot="right"
            style="display: flex; align-items: center"
            @click="onToSelectTime"
          >
            <text>{{ userInfo.params.ScheName || '请选择' }}</text>
            <u-icon name="arrow-right" size="18"></u-icon>
          </view>
        </u-form-item>
      </u-form>
    </view>
    <view class="btnButtomStyle" @click="onSureAppointment">确定</view>
  </view>
</template>

<script>
import {
  idCardTrue,
  validatePhoneNumber,
  idCardGetBri,
  idCardGetSex,
  popUpMsgTempList,
} from '@/utils/utils.js';
const app = getApp();
import { QuerySchoolPage, SaveRecord } from '@/api/appointment.js';
export default {
  data() {
    return {
      userInfo: {
        params: {},
      },
      rules: {
        'params.Name': {
          type: 'string',
          required: true,
          message: '请填写姓名',
          trigger: ['blur', 'change'],
        },
        'params.Sex': {
          type: 'string',
          required: true,
          message: '请填写男或女',
          trigger: ['blur', 'change'],
        },
        'params.Phone': {
          type: 'string',
          required: true,
          message: '请填写手机号',
          trigger: ['blur', 'change'],
        },
        'params.IdCard': {
          type: 'string',
          required: true,
          message: '请填写身份证号',
          trigger: ['blur', 'change'],
        },
        'params.BrithDay': {
          type: 'string',
          required: true,
          message: '请填写出生日期（例如：2000-01-01)',
          trigger: ['blur', 'change'],
        },
        'params.SchoolName': {
          type: 'string',
          required: true,
          message: '请选择学校名称',
          trigger: ['blur', 'change'],
        },
        'params.ScheId': {
          type: 'string',
          required: true,
          message: '请选择预约时间',
          trigger: ['blur', 'change'],
        },
      },
    };
  },
  onLoad() {
    const params = uni.getStorageSync('SGYY');
    if (params) {
      const userInfo = JSON.parse(decodeURIComponent(params));
      this.userInfo.params = userInfo;
      this.onGetList();
    } else {
      const userInfo = app.globalData.userInfo;
      this.userInfo.params.Name = userInfo.Name;
      this.userInfo.params.Phone = userInfo.PhoneNumber;
      if (userInfo.UserCertificates && userInfo.UserCertificates.length > 0) {
        const userIdList = userInfo.UserCertificates.filter(
          (s) => s.CertificateType === 'idCard'
        );
        if (userIdList && userIdList.length > 0) {
          this.$set(
            this.userInfo.params,
            'IdCard',
            userIdList[0].CertificateValue
          );
          this.onIdCardChange(userIdList[0].CertificateValue);
        }
      }
    }
    this.onSetSharePath();
  },
  methods: {
    onSetSharePath() {
      this.share.path =
        '/subGauge/transfer?handler=share&path=/subReport/SGAppointment';
    },
    onIdCardChange(e) {
      if (!e) return;
      // this.userInfo.params.BrithDay = idCardGetBri(e)
      this.$set(this.userInfo.params, 'BrithDay', idCardGetBri(e));
      this.$set(this.userInfo.params, 'Sex', idCardGetSex(e));
    },
    onToSelectSchool() {
      uni.navigateTo({
        url: './SGSchoolList',
      });
    },
    async onGetList() {
      let copyData = {
        IsEnable: true,
        PageIndex: 1,
        PageSize: 1,
        Keyword: this.userInfo.params.SchoolName,
      };
      const res = await QuerySchoolPage(copyData);
      if (res.Type === 200) {
        if (res.Data.Data.length === 1) {
          this.$set(this.userInfo.params, 'SchoolId', res.Data.Data[0].Id);
        } else {
          this.userInfo.params.SchoolName = '';
        }
      } else {
        this.userInfo.params.SchoolName = '';
      }
    },
    onSaveAppointmentUserData() {
      uni.setStorageSync(
        'SGYY',
        encodeURIComponent(JSON.stringify(this.userInfo.params))
      );
    },
    onSubmitAppointment() {
      const copyData = JSON.parse(JSON.stringify(this.userInfo.params));
      delete copyData.ScheName;
      delete copyData.SchoolName;
      copyData.CreatorId = app.globalData.userInfo.Id;
      copyData.Creator = app.globalData.userInfo.Name;
      copyData.UserId = app.globalData.userInfo.Id;
      uni.showLoading({
        title: this.$loadingMsg,
      });
      SaveRecord(copyData).then((res) => {
        if (res.Type !== 200) {
          uni.showToast({
            icon: 'error',
            title: res.Content,
          });
          return;
        }
        this.onSaveAppointmentUserData();
        uni.showToast({
          icon: 'success',
          title: res.Content,
        });
        uni.hideLoading();
        let pages = getCurrentPages();
        const prePage = pages[pages.length - 2]; //上一个页面
        popUpMsgTempList('Appointment', 'ShortTerm', () => {
          uni.showLoading({
            title: this.$loadingMsg,
          });
          if (
            prePage &&
            prePage.$page.fullPath === '/subReport/ReservationRecord'
          ) {
            prePage.$vm.initData();
            setTimeout(() => {
              uni.hideLoading();
              uni.navigateBack();
            }, 1000);
          } else {
            setTimeout(() => {
              uni.hideLoading();
              uni.redirectTo({
                url: './ReservationRecord',
              });
            }, 1000);
          }
        });
      });
    },
    onSureAppointment() {
      this.$refs.uForm.validate().then((res) => {
        if (!idCardTrue(this.userInfo.params.IdCard)) {
          uni.showToast({
            icon: 'none',
            title: '身份证格式不正确',
          });
          return;
        }
        if (!validatePhoneNumber(this.userInfo.params.Phone)) {
          uni.showToast({
            icon: 'none',
            title: '手机号格式不正确',
          });
          return;
        }
        this.onSubmitAppointment();
      });
    },
    onToSelectTime() {
      uni.navigateTo({
        url: './SGAppointmentTime',
      });
    },
    onSelectTime(obj) {
      console.log('obj', obj);
      if (!obj.ScheId) return;
      this.$set(this.userInfo.params, 'ScheId', obj.ScheId);
      this.$set(this.userInfo.params, 'ScheName', obj.ScheName);
    },
    onSelectSchool(obj) {
      console.log('obj', obj);
      if (!obj.SchoolId) return;
      this.$set(this.userInfo.params, 'SchoolId', obj.SchoolId);
      this.$set(this.userInfo.params, 'SchoolName', obj.SchoolName);
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  background: #eef0f3;
  padding: 32rpx;

  &-box {
    background: #ffffff;
    border-radius: 16rpx;
    padding: 0 32rpx;
  }

  &-top {
    position: relative;
    height: 324rpx;
    background: linear-gradient(301deg, #53dcd0 0%, #29b7a3 100%);
    border-radius: 16rpx;
    margin-bottom: 32rpx;

    &-bottom {
      position: absolute;
      background-image: url('./static/yyb.png');
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center center;
      width: 100%;
      height: 128rpx;
      bottom: 0;
      padding: 24rpx 30rpx;

      &-text {
        font-weight: 400;
        font-size: 24rpx;
        color: #656565;
        margin-bottom: 12rpx;
        display: flex;
        justify-content: flex-start;
        align-items: center;
      }
    }

    &-info {
      width: calc(100% - 252rpx);
      padding: 44rpx 0 24rpx 32rpx;

      &-text {
        font-weight: 500;
        font-size: 24rpx;
        color: #ffffff;
      }

      &-top {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-bottom: 16rpx;

        &-img {
          width: 40rpx;
          height: 40rpx;
        }

        &-text {
          font-weight: 600;
          font-size: 32rpx;
          color: #ffffff;
          margin-left: 10rpx;
        }
      }
    }

    &-img {
      background-image: url('./static/yy.png');
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center center;
      width: 252rpx;
      height: 168rpx;
      position: absolute;
      right: 0;
      top: 24rpx;
    }
  }
}
</style>
