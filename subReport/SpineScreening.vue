<template>
  <view class="container-wrapper">
    <scroll-view
      :style="{ height: `${contentHeight + headerHeight + 45}px` }"
      class="container"
      :show-scrollbar="false"
      enable-flex
      scroll-y
    >
      <!-- 工程入口 -->
      <view
        :style="{ height: `${headerHeight}px`, minHeight: `${headerHeight}px` }"
        class="header-container"
        hover-class="hover-class"
        hover-stay-time="70"
        @click="onSpineScreeningProject"
      >
      </view>
      <!-- 标签页 -->
      <u-sticky bgColor="#fff">
        <u-tabs
          :list="[
            { name: '筛查结果' },
            { name: '科普知识' },
            { name: '个人健康档案' },
          ]"
          lineColor="#111111"
          lineHeight="4rpx"
          :activeStyle="{
            color: '#111111',
            fontWeight: '500',
            fontSize: '28rpx',
          }"
          :inactiveStyle="{ color: '#666666', fontSize: '28rpx' }"
          :itemStyle="{ height: '45px', padding: '0 17px !important' }"
          :current="currentTab"
          @change="onChangeTab"
        ></u-tabs>
      </u-sticky>
      <swiper
        :style="{ height: `${contentHeight}px` }"
        :current="currentTab"
        @change="onChangeTab"
      >
        <swiper-item>
          <!-- 筛查结果 -->
          <view v-show="currentTab === 0">
            <SpineScreeningHome
              v-if="spineScreeningLoaded"
              :height="contentHeight"
              :userInfo="userInfo"
              @query="onReQuery"
            />
          </view>
        </swiper-item>
        <swiper-item>
          <!-- 科普知识 -->
          <view v-show="currentTab === 1">
            <SpineMissionList
              v-if="missionListLoaded"
              :height="contentHeight"
              :levelId="reportInfo.levelId"
            />
          </view>
        </swiper-item>
        <swiper-item>
          <!-- 个人健康档案 -->
          <view v-show="currentTab === 2">
            <HealthRecord
              v-if="healthRecordLoaded"
              :height="contentHeight"
              :reportInfo="reportInfo"
              :show="currentTab === 2"
            />
          </view>
        </swiper-item>
      </swiper>

      <!-- 首页内容，必须相对页面固定 -->
      <block v-if="currentTab === 0">
        <!-- 底部按钮 -->
        <view class="button-wrapper">
          <view class="button-item button-outline" @click="onToOtherWeChatMP"
            >预约就诊</view
          >
          <view class="button-item button-solid" @click="onSeeDocs"
            >线上咨询</view
          >
        </view>

        <!-- 联系客服 -->
        <view class="customer-service-image" @click="onCustomerService">
          <image src="/subReport/static/contact-customer-service.png" />
        </view>

        <!-- 医助企业微信弹窗 -->
        <u-popup
          :show="showWeChatQR"
          :zoom="false"
          mode="center"
          bgColor="transparent"
          @close="onCloseWeChatQR"
        >
          <view class="weChat-qr-code-wrapper">
            <view class="weChat-qr-code-image">
              <u--image
                :showLoading="true"
                src="https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/static/frontend/mp-patient/assistant-wechat-for-enterprises.jpg"
                :width="weChatQRImageWidth + 'px'"
                :height="weChatQRImageWidth + 'px'"
              />
            </view>
            <view>
              <u-icon
                name="close-circle"
                color="#fff"
                size="30"
                @click="onCloseWeChatQR"
              />
            </view>
          </view>
        </u-popup>
      </block>
    </scroll-view>

    <!-- 填写查询人信息弹框 -->
    <u-modal
      :show="showUserInfoModel"
      width="600rpx"
      title="学生信息"
      confirmColor="#29B7A3"
      negativeTop="600rpx"
      @confirm="onConfirmUserInfo"
    >
      <view class="user-info-wrapper">
        <text class="user-info-text" style="margin-bottom: 20rpx"
          >学生姓名</text
        >
        <view class="user-info-input-wrapper">
          <input
            class="user-info-input"
            placeholder-class="user-info-placeholder"
            placeholder="请输入学生姓名"
            v-model="userName"
            :focus="nameFocus"
            @input="userName = $event.detail.value"
            @focus="nameFocus = true"
            @blur="nameFocus = false"
          />
          <view
            class="user-info-clear-icon"
            v-if="userName.length > 0"
            @click.stop="handleNameClear"
          >
            <u-icon name="close-circle-fill" color="#999999" />
          </view>
        </view>
        <text
          class="user-info-text"
          style="margin-top: 34rpx; margin-bottom: 20rpx"
          >手机号（见纸质筛查卡右上角二维码旁）</text
        >
        <view class="user-info-input-wrapper">
          <input
            class="user-info-input"
            placeholder-class="user-info-placeholder"
            placeholder="请输入手机号"
            v-model="userPhone"
            :focus="phoneFocus"
            @input="userPhone = $event.detail.value"
            @focus="phoneFocus = true"
            @blur="phoneFocus = false"
          />
          <view
            class="user-info-clear-icon"
            v-if="userPhone.length > 0"
            @click.stop="handlePhoneClear"
          >
            <u-icon name="close-circle-fill" color="#999999" />
          </view>
        </view>
      </view>
    </u-modal>
  </view>
</template>

<script>
import SpineScreeningHome from './components/SpineScreeningHome.vue';
import SpineMissionList from './components/SpineMissionList.vue';
import HealthRecord from './components/HealthRecord.vue';
import { SpineClientEvent } from '../utils/eventKeys';
import { dataIsValid, isValidPhone, objNotEmpty } from '../utils/utils';
import StoreKeys from '../utils/storeKeys';

const kDebugEnable = true;
const app = getApp();
export default {
  components: {
    SpineScreeningHome,
    SpineMissionList,
    HealthRecord,
  },

  data() {
    return {
      // 当前选中的tab
      currentTab: 0,
      contentHeight: 0,

      // 查询人信息
      userInfo: {},
      // 是否显示查询人信息弹框
      showUserInfoModel: false,
      userName: '',
      nameFocus: false,
      userPhone: '',
      phoneFocus: false,

      // 成功匹配脊椎筛查+足底压力测试的相关数据
      // 脊椎报告内容+查询人信息
      reportInfo: {
        levelId: '',
        artValue: 0,
        leftFootArchValue: 0.22,
        rightFootArchValue: 0.22,
      },

      // 头部高度
      headerHeight: 100,

      // 脊柱筛查是否已加载
      spineScreeningLoaded: true,
      // 科普知识是否已加载
      missionListLoaded: false,
      // 个人健康档案是否已加载
      healthRecordLoaded: false,

      // 是否显示医助企业微信
      showWeChatQR: false,
      // 医助企业微信图片宽
      weChatQRImageWidth: 400,
    };
  },

  onLoad() {
    this.getSpineScreeningUserInfo();
    if (!this.userInfo.Name || !this.userInfo.Phone) {
      // 获取到的查询人信息不完整，则弹框让用户填写
      this.userName = this.userInfo.Name;
      this.userPhone = this.userInfo.Phone;
      this.showUserInfoModel = true;
    }

    // 监听点击查看脊柱筛查科普知识
    this.lookSpineMissionsHandler = () => {
      kDebugEnable && console.debug('点击查看脊柱筛查科普知识事件');
      this.currentTab = 1;
    };
    uni.$on(SpineClientEvent.lookSpineMissions, this.lookSpineMissionsHandler);

    // 监听成功匹配脊椎筛查+足底压力测试
    this.spineAndFootTestMatchedHandler = (data) => {
      kDebugEnable && console.debug('成功匹配脊椎筛查+足底压力测试事件', data);
      this.reportInfo = {
        ...data,
        ...this.userInfo,
      };
    };
    uni.$on(
      SpineClientEvent.spineAndFootTestMatched,
      this.spineAndFootTestMatchedHandler
    );
  },

  onReady() {
    const window = uni.getWindowInfo();
    this.contentHeight = window.screenHeight - window.statusBarHeight - 44 - 45;
    this.headerHeight = (window.screenWidth / 375) * 100;
    this.weChatQRImageWidth = window.screenWidth * 0.6;
  },

  onUnload() {
    uni.$off(SpineClientEvent.lookSpineMissions, this.lookSpineMissionsHandler);
    uni.$off(
      SpineClientEvent.spineAndFootTestMatched,
      this.spineAndFootTestMatchedHandler
    );
  },

  methods: {
    // 获取查询人信息
    getSpineScreeningUserInfo() {
      const userInfoStr = uni.getStorageSync(StoreKeys.spineScreeningUserInfo);
      let userInfo = null;
      if (userInfoStr) {
        try {
          userInfo = JSON.parse(userInfoStr);
        } catch (error) {
          kDebugEnable &&
            console.warn(
              `获取查询人信息失败，JSON.parse() 失败，userInfoStr: ${userInfoStr}`,
              error
            );
        }
      }

      if (objNotEmpty(userInfo)) {
        this.userInfo = userInfo;
      } else {
        this.userInfo = {
          Name: app.globalData.userInfo.Name,
          Phone: app.globalData.userInfo.PhoneNumber,
        };
      }

      // 获取到的查询人信息完整，则本地存储，下次直接使用
      if (this.userInfo.Name && this.userInfo.Phone) {
        const userInfoStr = JSON.stringify(this.userInfo);
        uni.setStorageSync(StoreKeys.spineScreeningUserInfo, userInfoStr);
      }

      kDebugEnable &&
        console.debug('查询人信息', JSON.stringify(this.userInfo));
    },

    // 点击重新查询
    onReQuery() {
      kDebugEnable && console.debug('重新查询');
      this.userName = this.userInfo.Name;
      this.userPhone = this.userInfo.Phone;
      this.showUserInfoModel = true;
    },

    // 查询人姓名输入框内容被清空
    handleNameClear() {
      kDebugEnable && console.debug('姓名被清空');
      this.userName = '';
      setTimeout(() => {
        this.nameFocus = true;
      }, 300);
    },

    // 查询人手机号输入框内容被清空
    handlePhoneClear() {
      kDebugEnable && console.debug('查询人手机号输入框内容被清空');
      this.userPhone = '';
      setTimeout(() => {
        this.phoneFocus = true;
      }, 300);
    },

    // 点击确认修改查询人信息
    onConfirmUserInfo() {
      kDebugEnable &&
        console.debug('点击确认修改查询人信息', this.userName, this.userPhone);

      if (!this.userName || !this.userPhone) {
        uni.showToast({
          title: '请填写完整学生信息',
          icon: 'none',
        });
        return;
      } else if (!isValidPhone(this.userPhone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none',
        });
        return;
      }

      const userInfo = {
        Name: this.userName,
        Phone: this.userPhone,
      };
      const userInfoStr = JSON.stringify(userInfo);
      kDebugEnable && console.debug('点击确认修改查询人信息', userInfoStr);
      uni.setStorageSync(StoreKeys.spineScreeningUserInfo, userInfoStr);
      this.showUserInfoModel = false;
      this.userInfo = userInfo;
    },

    // 点击查看工程项目
    onSpineScreeningProject() {
      kDebugEnable && console.debug('点击查看工程项目');
      const imageUrl =
        'https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/static/frontend/mp-patient/%E8%84%8A%E6%9F%B1%E9%99%AA%E4%BC%B4%E8%AE%A1%E5%88%92%E9%A1%B9%E7%9B%AE%E4%BB%8B%E7%BB%8D.jpg';
      uni.navigateTo({
        url:
          '/subReport/ImagePreview?' +
          `url=${imageUrl}` +
          '&title=脊柱健康陪伴计划',
      });
    },

    // 切换标签页
    onChangeTab(e) {
      kDebugEnable && console.debug('onChangeTab', e);

      if (dataIsValid(e.index)) {
        this.currentTab = e.index;
      } else if (dataIsValid(e.detail) && dataIsValid(e.detail.current)) {
        this.currentTab = e.detail.current;
      }

      switch (this.currentTab) {
        case 0:
          this.spineScreeningLoaded = true;
          break;
        case 1:
          this.missionListLoaded = true;
          break;
        case 2:
          this.healthRecordLoaded = true;
          break;
      }
    },

    // 线上咨询 - 查看推荐医生
    onSeeDocs() {
      this.$log.info(`${this.$envVersion}:视光筛查用户点击了"线上咨询"`);
      uni.navigateTo({
        url: '/subReport/SeeSpineDocs',
      });
    },

    // 预约就诊 - 跳转至第三方微信小程序
    onToOtherWeChatMP() {
      this.$log.info(`${this.$envVersion}:视光筛查用户点击了"预约就诊"`);
      const atrValue = this.reportInfo.artValue || 0;
      if (atrValue >= 4) {
        // 脊柱atr绝对值大于等于4，跳转雅安职业技术学院附属医院小程序
        uni.navigateToMiniProgram({
          appId: 'wx05b2bf56f92af3be',
          path: 'pages/Home2023/Home2023',
        });
      } else {
        // 其他情况，跳转易品康小程序
        uni.navigateToMiniProgram({
          appId: 'wx97a53f7810692bff',
          path: 'pages/home/<USER>',
        });
      }
    },

    // 联系客服
    async onCustomerService() {
      this.showWeChatQR = true;
    },

    // 关闭医助企业微信
    onCloseWeChatQR() {
      this.showWeChatQR = false;
    },
  },
};
</script>

<style scoped lang="scss">
.container-wrapper {
  width: 100%;
  height: 100%;
}

.container {
  display: flex;
  justify-content: start;
  align-items: stretch;
  flex-direction: column;

  .header-container {
    position: relative;
    width: 100%;
    height: 100px;
    min-height: 100px;
    background-color: #f8f7f7;
    background: url('https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/static/frontend/mp-patient/spine-banner.png')
      no-repeat center center;
    background-size: 100% 100%;
  }

  /deep/ .u-tabs__wrapper__nav__item {
    height: 100%;
    padding: 0 !important;
  }

  .customer-service-image {
    position: fixed;
    left: 40rpx;
    bottom: 12%;
    z-index: 10000;

    image {
      width: 146rpx;
      height: 168rpx;
    }
  }

  .weChat-qr-code-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    .weChat-qr-code-image {
      padding: 100rpx 50rpx;
      background-color: #ffffff;
      border-radius: 24rpx;
      margin-bottom: 36rpx;
    }
  }

  .button-wrapper {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #ffffff;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;

    .button-item {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      font-weight: 600;
      font-size: 28rpx;
      border-radius: 40px;
      height: 40px;
      border: 1px solid #29b7a3;
    }

    .button-outline {
      background-color: #ffffff;
      color: #29b7a3;
      margin-right: 32rpx;
    }

    .button-solid {
      background-color: #29b7a3;
      color: #ffffff;
    }
  }
}

.user-info-wrapper {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: start;

  .user-info-text {
    line-height: 1;
    font-size: 30rpx;
    align-self: flex-start;
    color: #333333;
  }

  .user-info-input-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #f7fafa;
    height: 75rpx;
    padding-left: 27rpx;
    border-radius: 6rpx;

    .user-info-input {
      flex: 1;
      text-align: left;
      font-size: 28rpx;
      color: #333333;
    }

    /deep/ .user-info-placeholder {
      font-size: 28rpx;
      color: #999999;
    }

    .user-info-clear-icon {
      height: 75rpx;
      width: 75rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.hover-class {
  opacity: 0.5;
}

/deep/ .u-modal__content {
  padding: 45rpx 30rpx 37rpx !important;
  flex-direction: column !important;
  align-items: stretch !important;
}

/deep/ .u-modal__title {
  font-weight: 600 !important;
  font-size: 32rpx !important;
  color: #333333 !important;
}
</style>
