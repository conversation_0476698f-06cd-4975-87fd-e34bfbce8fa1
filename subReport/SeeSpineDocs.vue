<template>
  <view class="container">
    <!-- 轮播图 -->
    <swiper
      v-if="baseOrgInfoList.length"
      :indicator-dots="true"
      :autoplay="true"
    >
      <swiper-item v-for="item in baseOrgInfoList" :key="item.Id">
        <image
          :src="item.CoverImageUrl"
          style="margin: 20rpx 0; width: 100%; height: 278rpx"
          @click="onSeeOrgDetail(item)"
        ></image>
      </swiper-item>
    </swiper>
    <u-gap height="12" bgColor="#F6F6F6"></u-gap>
    <!-- 用户角色筛选 -->
    <u-subsection
      :list="list"
      :current="currentIndex"
      @change="sectionChange"
      activeColor="#29B7A3"
      fontSize="18"
      keyName="name"
      mode="subsection"
    >
    </u-subsection>
    <!-- 搜索 -->
    <u-search
      placeholder="医生、治疗师、护士名字"
      :showAction="true"
      v-model="docSheare.Keyword"
      actionText="搜索"
      :animation="false"
      @search="rest"
      @custom="rest"
    ></u-search>
    <!-- 筛选 -->
    <view class="container-screen display-style2" style="color: #999999">
      <view class="display-style1" style="width: 35%" @click="chooseOrg">
        <p class="p-sytle11" style="color: #29b7a3">
          {{ handleGetSelectOrgName() }}
        </p>
        <u-icon
          name="arrow-down-fill"
          color="#29B7A3"
          size="12"
          customStyle="marginLeft:4px;marginTop:2px"
        >
        </u-icon>
      </view>
      <view class="display-style1" style="width: 22%" @click="chooseDept">
        <p class="p-sytle11" style="color: #29b7a3">
          {{ handleGetSelectDepName() }}
        </p>
        <u-icon
          name="arrow-down-fill"
          color="#29B7A3"
          size="12"
          customStyle="marginLeft:4px;marginTop:2px"
        >
        </u-icon>
      </view>
    </view>
    <!-- 列表 -->
    <u-list
      @scrolltolower="scrolltolower"
      v-if="docList.length > 0"
      customStyle="padding-bottom:40rpx;height: inherit !important"
    >
      <u-list-item v-for="(item, index) in docList" :key="item.id">
        <view class="box" @tap="toSeeDoc(index)" style="position: relative">
          <view class="box-top">
            <u-avatar
              :src="item.Doctor.HeadImg"
              size="60"
              class="avatar-syyle"
            ></u-avatar>
            <view class="box-top-right">
              <view class="box-top-right-top">
                <view class="display-style" style="width: 100%">
                  <p
                    style="font-size: 18px; font-weight: 600; flex: 1"
                    class="p-style11"
                  >
                    {{ item.Doctor.Name }}
                    <span
                      style="
                        font-size: 14px;
                        color: #666666;
                        margin-left: 20upx;
                        margin-top: 4px;
                      "
                      >{{ item.Doctor.WorkerTitle }}</span
                    >
                  </p>

                  <p
                    style="color: #29b7a3; font-size: 18px; font-weight: 600"
                    v-if="item.IsEnable"
                  >
                    <span v-if="item.ShowCost" style="color: #c4c4c4">
                      <span style="text-decoration: line-through"
                        >￥{{ item.ShowCost }}</span
                      >
                      <span>/</span>
                    </span>
                    <span style="color: #29b7a3">{{
                      item.RichTextCost === 0
                        ? '免费'
                        : '￥' + item.RichTextCost
                    }}</span>
                  </p>
                </view>
              </view>
              <view class="box-top-right-top">
                <p style="font-size: 14px; color: #666666; width: 65%">
                  {{ item.Doctor.OrganizationName }}
                  <span style="margin-left: 20upx">{{
                    item.Doctor.DepartmentName
                  }}</span>
                </p>
                <view>
                  <p
                    style="color: #666666; font-size: 18px; font-weight: 600"
                    v-if="!item.IsEnable"
                  >
                    休息中
                  </p>
                </view>
              </view>
            </view>
            <view class="box-top-img1" v-if="item.ShowCost && item.IsEnable">
              限时
            </view>
          </view>
          <image
            src="/static/images/follow.png"
            mode=""
            class="box-top-img"
            style="width: 64rpx; height: 64rpx"
            v-if="item.Doctor.Followed"
          ></image>
          <view class="box-top-right-top1">
            <p
              style="
                font-size: 14px;
                color: #666666;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              "
            >
              擅长：{{ item.Doctor.Skilled }}
            </p>
            <u-line :dashed="true" margin="10px 0 10px 0"></u-line>
            <view class="botton-style">
              <p>
                服务患者：<span style="color: #29b7a3"
                  >{{ item.ConsultCount }}人</span
                >
              </p>
              <p style="color: #29b7a3; font-size: 15px" v-if="item.IsEnable">
                立即咨询>
              </p>
            </view>
          </view>
        </view>
      </u-list-item>

      <u-loadmore status="nomore" v-if="status === 'nomore'" />
    </u-list>
    <u-empty
      mode="data"
      icon="	https://cdn.uviewui.com/uview/empty/data.png"
      v-if="docList.length == 0"
    >
    </u-empty>
    <u-action-sheet
      :actions="depList"
      title="选择科室"
      :show="showDep"
      :safeAreaInsetBottom="true"
      closeOnClickOverlay="close"
      @close="close"
      @select="handleSelectDep"
    ></u-action-sheet>
    <u-action-sheet
      :actions="orgInfoList"
      title="选择医院"
      :show="showHos"
      :safeAreaInsetBottom="true"
      closeOnClickOverlay="close"
      @close="close"
      @select="handleSelectOrg"
    ></u-action-sheet>
    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script>
const app = getApp();
import { getConsortiumUserList } from '@/api/consult.js';
import { OrganizationConsortiumGet } from '@/api/passport.js';
import { queryDepartments } from '@/api/passport.department.js';
export default {
  data() {
    return {
      docList: [],
      docSheare: {
        OrganizationIds: null,
        PageIndex: 1,
        PageSize: 10,
        ConsortiumId: '',
        RoleTypes: ['doctor'],
        Keyword: '',
        DepartmentId: null,
      },
      status: 'nomore',
      orgId: '',
      ConsortiumId: '',
      orgInfoList: [],
      baseOrgInfoList: [],
      list: [
        {
          name: '医生',
          type: 'doctor',
        },
        {
          name: '治疗师',
          type: 'therapist',
        },
        {
          name: '护士',
          type: 'nurse',
        },
      ],
      currentIndex: 0,
      showDep: false,
      showHos: false,
      depList: [],
      depPrams: {
        OrgId: null,
        IsEnabled: true,
        IsLocked: false,
        Pageable: false,
        SingleOne: false,
      },
    };
  },
  onLoad() {
    this.ConsortiumId = '2dad543b-6ab4-42b8-a1a5-b1c834050d44';
    // 获取脊柱的所有机构数据
    this.handleGetAllOrgList();
  },
  methods: {
    handleGetSelectOrgName() {
      if (!this.orgInfoList.length) return '医院';
      const selectOrg = this.docSheare.OrganizationIds;
      if (!selectOrg) return '医院';
      const selectOrgItem = this.orgInfoList.find(
        (s) => s.OrganizationId === selectOrg[0]
      );
      if (selectOrgItem) return selectOrgItem.name;
    },
    handleGetSelectDepName() {
      if (!this.depList.length) return '科室';
      const selectDep = this.docSheare.DepartmentId;
      if (!selectDep) return '科室';
      const selectDepItem = this.depList.find((s) => s.Id === selectDep);
      if (selectDepItem) return selectDepItem.name;
    },
    handleSelectOrg(item) {
      this.docSheare.OrganizationIds = item.OrganizationId
        ? [item.OrganizationId]
        : null;
      this.rest();
      // 获取科室数据
      if (item.OrganizationId) {
        this.depPrams.OrgId = item.OrganizationId;
        this.getDepDataList();
      } else {
        this.depPrams.OrgId = null;
        this.depList = [];
        this.docSheare.DepartmentId = null;
      }
    },
    handleSelectDep(item) {
      this.docSheare.DepartmentId = item.Id;
      this.rest();
    },
    async getDepDataList() {
      let res = await queryDepartments(this.depPrams);
      if (res.Type === 200) {
        res.Data.unshift({
          Name: '全部',
          Id: null,
        });
        res.Data.forEach((e) => {
          e.name = e.Name;
        });
        this.depList = res.Data;
      }
    },
    // 选择机构
    chooseOrg() {
      this.showHos = true;
    },
    chooseDept() {
      this.showDep = true;
    },
    // 关闭列表
    close() {
      this.showDep = false;
      this.showHos = false;
    },
    // 切换搜索类型
    sectionChange(index) {
      if (index == 0) {
        this.docSheare.RoleTypes = ['doctor'];
      } else if (index == 1) {
        this.docSheare.RoleTypes = ['therapist'];
      } else if (index == 2) {
        this.docSheare.RoleTypes = ['nurse'];
      }
      this.currentIndex = index;
      this.rest();
    },
    async handleGetAllOrgList() {
      const data = {
        ConsortiumId: '2dad543b-6ab4-42b8-a1a5-b1c834050d44',
        PageIndex: 1,
        PageSize: 999,
      };
      const res = await OrganizationConsortiumGet(data);
      if (res.Type === 200) {
        this.baseOrgInfoList = JSON.parse(JSON.stringify(res.Data.Data));
        this.orgInfoList = res.Data.Data;
        if (res.Data.Data.length) {
          this.docSheare.ConsortiumId = '2dad543b-6ab4-42b8-a1a5-b1c834050d44';
          this.docSheare.OrganizationIds = null;
          // 设置医院选择数据
          this.handleSetSelectOrgList();
          this.getFinDocList();
        }
      }
    },
    handleSetSelectOrgList() {
      this.orgInfoList.unshift({
        OrganizationId: null,
        OrganizationName: '全部',
      });
      this.orgInfoList.map((s) => {
        s.name = s.OrganizationName;
      });
    },
    rest() {
      this.docSheare.PageIndex = 1;
      this.docList = [];
      this.getFinDocList();
    },
    onSeeOrgDetail(item) {
      uni.navigateTo({
        url:
          './SeeOrgDetail?orgInfo=' +
          encodeURIComponent(JSON.stringify(item)) +
          '&ConsortiumId=' +
          item.ConsortiumId,
      });
    },
    //点击某个医生
    toSeeDoc(index) {
      const item = this.docList[index];
      const data = JSON.stringify(item);
      uni.navigateTo({
        url: `/subPackIndex/docDetail?docId=${item.Doctor.UserId}&needFllow=1`,
      });
    },
    async getFinDocList() {
      let res = await getConsortiumUserList(this.docSheare);
      if (res.Type == 200 && res.Data.Data.length > 0) {
        res.Data.Data.forEach((e) => {
          this.docList.push(e);
        });
      } else {
        this.status = 'nomore';
      }
      if (res.Data.Data.length < this.docSheare.pagesize) {
        this.status = 'nomore';
      } else {
        this.status = 'loadmore';
      }
    },
    scrolltolower() {
      this.docSheare.PageIndex++;
      this.getFinDocList();
    },
    rest() {
      this.docSheare.PageIndex = 1;
      this.docList = [];
      this.getFinDocList();
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  padding: 10px;

  .container-screen {
    width: 100%;
    height: 60px;
    background-color: white;
    margin: 10px 0;
    border-radius: 4px;
  }

  /deep/ .u-list {
    height: auto;
  }

  .container-search {
    background-color: white;
    width: 100%;
    margin-bottom: 10px;
    padding: 10px;

    /deep/ .u-input {
      padding: 0 0 0 6px !important;
    }
  }

  .search-tool {
    margin-top: 20upx;
    width: 100%;
    padding: 10upx 20upx;
  }

  .container-box {
    padding: 0 10upx;
    margin-top: 20px;

    .container-box-each {
      width: 100%;
      height: 100px;
      background-color: red;
    }
  }

  .box {
    background-color: white;
    margin-bottom: 32rpx;
    border-radius: 12upx;
    padding: 40rpx 20rpx 20rpx 20rpx;
    box-shadow: -1px -1px 4px -2px rgba(0, 0, 0, 0.3) inset;

    .box-top-img {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 99;
      transform: rotate(-90deg);
    }

    .box-top-right-top1 {
      margin-top: 20upx;

      .botton-style {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;
      }
    }

    .box-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;

      .box-top-img1 {
        position: absolute;
        top: -20rpx;
        right: 0;
        z-index: 99;
        padding: 4rpx;
        background: #29b7a3;
        color: white;
        font-size: 12px;
        width: 40px;
        text-align: center;
        border-radius: 8px 8px 8px 0px;
      }

      .box-top-right {
        flex: 1;
        margin-left: 20upx;

        .box-top-right-top {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .botton-style {
            width: 138upx;
            height: 60upx;
            background-color: #29b7a3;
            font-size: 15px;
            text-align: center;
            line-height: 60upx;
            border-radius: 30px;
          }
        }
      }
    }
  }
}
</style>
