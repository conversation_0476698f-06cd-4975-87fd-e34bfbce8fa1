<template>
  <view class="container">
    <u-subsection
      :list="list"
      :current="curNow"
      @change="sectionChange"
      custom-style="margin-bottom: 32rpx;"
    ></u-subsection>
    <view class="container-chartsBox" v-if="!showEmpty">
      <qiun-data-charts type="radar" :opts="LDopts" :chartData="LDchartData" />
    </view>
    <view class="container-chartsBox" v-if="!showEmpty">
      <qiun-data-charts type="column" :opts="QYopts" :chartData="QYchartData" />
    </view>
    <view class="container-chartsBox1" v-if="!showEmpty">
      <view
        class="container-chartsBox1-each"
        v-for="(item, idnex) in fourList"
        :key="idnex"
      >
        <qiun-data-charts
          type="area"
          :opts="item.opt"
          :chartData="item.QYchartData"
        />
      </view>
    </view>
    <u-empty
      mode="data"
      icon="http://cdn.uviewui.com/uview/empty/data.png"
      v-if="showEmpty"
    ></u-empty>
  </view>
</template>
<script>
const app = getApp();
import dayjs from 'dayjs';
import { GetReportByUserId } from '@/api/supplier.js';

export default {
  data() {
    return {
      list: ['上肢', '下肢'],
      curNow: 0,
      LDchartData: {},
      LDopts: {
        color: ['#29B7A3'],
        dataLabel: true,
        enableScroll: false,
        legend: {
          show: false,
          position: 'right',
        },
        extra: {
          radar: {
            gridType: 'radar',
            gridColor: '#CCCCCC',
            opacity: 0.2,
            gridCount: 5,
            max: 10,
            labelShow: true,
            border: true,
          },
        },
      },
      QYopts: {
        legend: {
          position: 'top',
        },
        color: [
          '#FAC858',
          '#EE6666',
          '#FAC858',
          '#EE6666',
          '#73C0DE',
          '#3CA272',
          '#FC8452',
          '#9A60B4',
          '#ea7ccc',
        ],
        padding: [15, 15, 0, 5],
        enableScroll: false,
        xAxis: {
          disableGrid: true,
        },
        extra: {
          column: {
            type: 'group',
            width: 30,
            activeBgColor: '#000000',
            activeBgOpacity: 0.08,
            linearType: 'custom',
            seriesGap: 3,
            linearOpacity: 0.5,
            barBorderCircle: true,
            customColor: ['#6EE972', '#FFFFFF'],
          },
        },
      },
      QYchartData: {},
      Fouropts: {
        color: [],
        padding: [15, 15, 0, 15],
        enableScroll: false,
        legend: {
          position: 'top',
        },
        xAxis: {
          disableGrid: true,
          fontSize: 8,
        },
        yAxis: {
          gridType: 'dash',
          dashLength: 2,
        },
        extra: {
          area: {
            type: 'curve',
            opacity: 0.2,
            addLine: true,
            width: 2,
            gradient: true,
            activeType: 'hollow',
          },
        },
      },
      QYchartData0: {},
      fourList: [],
      showEmpty: false,
    };
  },
  async onLoad() {
    if (!app.globalData.token) {
      await getApp().isLaunchFinish();
    }
    this.getServerData();
  },
  methods: {
    sectionChange(index) {
      this.curNow = index;
      this.getServerData();
    },
    async getServerData() {
      const res = await GetReportByUserId({
        userId: app.globalData.userInfo.Id, // '3a09fc31-39cf-b9a5-94d9-9bdfb69aff94' ||
        limb: this.curNow === 0 ? 4 : 5,
      });
      if (res.Type === 200 && res.Data) {
        this.showEmpty = false;
        // 获取雷达图数据组装
        const Data = res.Data.Data;
        let radar = {
          categories: ['brunnstrom', '爆发', '平移', '抬举', '稳定'],
          series: [
            {
              name: '',
              data: [
                Data.workoutPrescriptionEval.radar.brunnstromScore,
                Data.workoutPrescriptionEval.radar.power,
                Data.workoutPrescriptionEval.radar.horizontal,
                Data.workoutPrescriptionEval.radar.vertical,
                Data.workoutPrescriptionEval.radar.stability,
              ],
            },
          ],
        };
        this.LDchartData = JSON.parse(JSON.stringify(radar));
        // 组装brunnstrom数据柱状图
        this.assembleBrunnstrom(Data);
        // 下方四个折线图
        this.assembleLineChart(
          Data.workoutPrescriptionEval.workoutDataMap.detail
        );
      } else if (res.Type === 200 && !res.Data) {
        this.showEmpty = true;
      }
    },
    assembleLineChart(data) {
      const workoutDataMap = data;
      // 获取最新的三条数据
      const newData = workoutDataMap.slice(-3);
      newData.map((s) => {
        const newHistoryData = s.historyData.slice(0, 1);
        s.historyData = newHistoryData[0];
      });
      console.log('newData', newData);
      const fourCharts = [];
      const categories = []; // x轴
      const categoriesDataBF = []; // 数据爆发
      const categoriesDataPY = []; // 数据平移
      const categoriesDataTJ = []; // 数据抬举
      const categoriesDataWD = []; // 数据稳定
      const opt = this.Fouropts;
      newData.forEach((v) => {
        categories.push(dayjs(v.createDate).format('MM-DD'));
        categoriesDataBF.push(v.historyData.power);
        categoriesDataPY.push(v.historyData.horizontal);
        categoriesDataTJ.push(v.historyData.vertical);
        categoriesDataWD.push(v.historyData.stability);
      });
      fourCharts.push(
        JSON.parse(
          JSON.stringify({
            QYchartData: {
              categories,
              series: [
                {
                  name: '爆发',
                  data: categoriesDataBF,
                },
              ],
            },
            opt: {
              ...opt,
              color: ['#347DE4', '#ffffff'],
            },
          })
        )
      );
      fourCharts.push(
        JSON.parse(
          JSON.stringify({
            QYchartData: {
              categories,
              series: [
                {
                  name: '平移',
                  data: categoriesDataPY,
                },
              ],
            },
            opt: {
              ...opt,
              color: ['#5ED8E8', '#ffffff'],
            },
          })
        )
      );
      fourCharts.push(
        JSON.parse(
          JSON.stringify({
            QYchartData: {
              categories,
              series: [
                {
                  name: '抬举',
                  data: categoriesDataTJ,
                },
              ],
            },
            opt: {
              ...opt,
              color: ['#7BE85E', '#ffffff'],
            },
          })
        )
      );
      fourCharts.push(
        JSON.parse(
          JSON.stringify({
            QYchartData: {
              categories,
              series: [
                {
                  name: '稳定',
                  data: categoriesDataWD,
                },
              ],
            },
            opt: {
              ...opt,
              color: ['#E85EB9', '#ffffff'],
            },
          })
        )
      );
      this.fourList = fourCharts;
    },
    assembleBrunnstrom(Data) {
      const categories = [];
      const categoriesData = [];
      Data.workoutPrescriptionEval.brunnstromDataList.forEach((v) => {
        categories.push(dayjs(v.createDate).format('MM-DD'));
        categoriesData.push(v.score);
      });
      let brunnstrom = {
        categories: categories.length === 0 ? [''] : categories,
        series: [
          {
            name: 'brunnstrom',
            data: categoriesData.length === 0 ? [''] : categoriesData,
          },
        ],
      };
      this.QYchartData = JSON.parse(JSON.stringify(brunnstrom));
    },
  },
};
</script>
<style scoped lang="scss">
.container {
  padding: 34rpx 32rpx;

  /* 请根据实际需求修改父元素尺寸，组件自动识别宽高 */
  &-chartsBox {
    width: 100%;
    background: #ffffff;
    box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);
    border-radius: 16rpx;
    margin-bottom: 32rpx;
  }

  &-chartsBox1 {
    width: 100%;
    background: #ffffff;
    box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);
    border-radius: 16rpx;
    margin-bottom: 32rpx;
    display: flex;
    justify-content: space-between;
    align-content: center;
    flex-wrap: wrap;

    &-each {
      width: 50%;
    }
  }
}
</style>
