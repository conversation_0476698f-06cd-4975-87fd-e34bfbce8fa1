<template>
  <view class="container">
    <u-search
      @search="onSearch"
      @custom="onSearch"
      v-model="query.Keyword"
      :showAction="true"
      actionText="搜索"
      :animation="true"
    ></u-search>
    <view class="u-page">
      <u-list @scrolltolower="scrolltolower">
        <u-list-item v-for="(item, index) in dataList" :key="index">
          <u-cell :title="item.Name" @click="onChooseSchool(item)"></u-cell>
        </u-list-item>
      </u-list>
    </view>
  </view>
</template>

<script>
import { QuerySchoolPage } from '@/api/appointment.js';
export default {
  data() {
    return {
      query: {
        PageIndex: 1,
        PageSize: 20,
        IsEnable: true,
        Keyword: '',
      },
      dataList: [],
    };
  },
  onLoad() {
    this.onGetList();
  },
  methods: {
    async onGetList() {
      const res = await QuerySchoolPage(this.query);
      if (res.Type === 200) {
        this.dataList = [...this.dataList, ...res.Data.Data];
      }
    },
    onSearch() {
      this.dataList = [];
      this.onGetList();
    },
    scrolltolower() {
      this.query.PageIndex++;
      this.onGetList();
    },
    onChooseSchool(item) {
      console.log('item', item);
      let pages = getCurrentPages();
      const prePage = pages[pages.length - 2]; //上一个页面
      if (prePage.$vm.onSelectSchool) {
        prePage.$vm.onSelectSchool({
          SchoolId: item.Id,
          SchoolName: item.Name,
        });
      }
      uni.navigateBack();
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  background: #ffffff;
}
</style>
