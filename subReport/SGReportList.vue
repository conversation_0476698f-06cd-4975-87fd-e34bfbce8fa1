<template>
  <view class="container">
    <view v-if="reportList.length > 0">
      <view
        class="container-box"
        v-for="i in reportList"
        :key="i.Id"
        @click="toSeeDetail(i)"
      >
        <view class="container-box-left">
          <text class="container-box-left-name"
            >{{ i.Name }} {{ i.Grade }}{{ i.Clazz }}</text
          >
          <br />
          <text class="container-box-left-time"
            >筛查时间：{{ formatTime(i.CheckDate) }}</text
          >
        </view>
        <u-icon name="arrow-right"></u-icon>
      </view>
      <u-gap height="20" bgColor="#F7F7F7"></u-gap>
    </view>
    <u-empty
      mode="data"
      icon="	https://cdn.uviewui.com/uview/empty/data.png"
      v-else
    >
    </u-empty>
  </view>
</template>

<script>
import { QPGetReportPage } from '@/api/supplier.js';
export default {
  data() {
    return {
      query: {
        pageSize: 10,
        pageIndex: 1,
      },
      reportList: [],
      noData: false,
    };
  },
  onLoad() {
    this.getReportList();
  },
  methods: {
    onReachBottom(e) {
      console.log('触底');
      if (this.noData) return;
      this.query.pageIndex++;
      this.getReportList();
    },
    toSeeDetail(i) {
      if (!i.Id) {
        uni.showModal({
          content: '未找到对应的数据',
          showCancel: false,
        });
        return;
      }
      uni.navigateTo({
        url: './SGReportDetail?Id=' + i.Id,
      });
    },
    formatTime(date) {
      if (!date) return '';
      return this.$dateFormat(date, 'YYYY-MM-DD');
    },
    async getReportList() {
      const userInfo = uni.getStorageSync('SGUser');
      const req = {
        ...userInfo,
        // idCard: '510114201903050020',
        // name: '王译婵',
        ...this.query,
      };
      const res = await QPGetReportPage(req);
      if (res.Type === 200) {
        if (res.Data.Data.length < this.query.pageSize) {
          this.noData = true;
        } else {
          this.noData = false;
        }
        this.reportList = [...this.reportList, ...res.Data.Data];
      }
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  padding: 32rpx;

  &-box {
    background-color: white;
    box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.1);
    border-radius: 24rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32rpx;
    margin-bottom: 20rpx;

    &-left {
      &-name {
        font-size: 28rpx;
        font-weight: 600;
        color: #333333;
      }

      &-time {
        margin-top: 16rpx;
        font-size: 24rpx;
        font-weight: 500;
        color: #29b7a3;
      }
    }
  }
}
</style>
