<template>
  <view class="container">
    <u-subsection
      :list="list"
      :current="current"
      mode="subsection"
      activeColor="#29B7A3"
      @change="onSubsectionChange"
    ></u-subsection>
    <block v-if="current === 0">
      <SeeReportList />
    </block>
    <block v-else>
      <Rehabilitation ref="childRef" />
    </block>
  </view>
</template>

<script>
import Rehabilitation from './Rehabilitation.vue';
import SeeReportList from './SeeReportList.vue';
export default {
  components: {
    Rehabilitation,
    SeeReportList,
  },
  data() {
    return {
      list: ['检测报告', '训练报告'],
      current: 1,
    };
  },
  onLoad() {},
  methods: {
    onSubsectionChange(e) {
      this.current = e;
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  background: #f8f7f7;
  padding: 32rpx;

  /deep/ .u-subsection__bar {
    height: 100%;
  }

  /deep/ .u-subsection {
    height: 80rpx;
    margin-bottom: 32rpx;
  }

  /deep/ .u-subsection__item__text {
    font-size: 36rpx;
    color: #ffffff;
  }
}
</style>
