<template>
  <view>
    <u--image
      :showLoading="true"
      :showError="true"
      :src="imageSrc"
      width="100%"
      :height="imageHeight"
      @error="onImageError"
    >
      <u-empty slot="error" />
    </u--image>
  </view>
</template>

<script>
const kDebugEnable = true;
export default {
  data() {
    return {
      imageSrc: '',
      imageHeight: 753,
    };
  },

  onLoad(options) {
    const { url, title } = options;
    this.imageSrc = url;
    uni.setNavigationBarTitle({
      title,
    });
  },

  onReady() {
    uni.showLoading({
      title: '',
      mask: true,
    });
    const that = this;
    uni.getImageInfo({
      src: this.imageSrc,
      success: function (image) {
        const width = image.width || 1000;
        const height = image.height || 8000;
        kDebugEnable &&
          console.debug(`image.width: ${width}, image.height: ${height}`);

        const res = uni.getWindowInfo();
        that.imageHeight = (res.screenWidth / width) * height;
      },
      complete: () => {
        uni.hideLoading();
      },
    });
  },

  methods: {
    onImageError() {
      this.imageHeight = 500;
    },
  },
};
</script>

<style lang="scss" scoped></style>
