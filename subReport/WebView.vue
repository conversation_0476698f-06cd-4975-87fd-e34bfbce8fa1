<template>
  <block>
    <web-view
      v-if="urls"
      :src="urls"
      style="position: relative; z-index: 100 !important"
    >
      <cover-view
        class="container-bomBtn"
        style="z-index: 99999 !important"
        v-if="IsShowBack"
        @click="handleBackClick"
        >脊柱侧弯科普知识</cover-view
      >
      <!-- <button class="container-bomBtn" style="z-index: 99999 !important;">脊柱侧弯科普知识</button> -->
    </web-view>
    <u-empty v-else />
  </block>
</template>

<script>
const app = getApp();
import { GetPulmonaryReportByUserId } from '@/api/supplier.js';
import { SpineClientEvent } from '../utils/eventKeys';
export default {
  data() {
    return {
      urls: '',
      IsShowBack: false,
    };
  },
  onLoad(option) {
    if (option.Type) {
      const sendTypes = JSON.parse(decodeURIComponent(option.Type));
      console.log('sendTypes', sendTypes);
      this.getReportData(sendTypes);
    } else if (option.Url) {
      this.urls = decodeURIComponent(option.Url);
      if (typeof option.IsShowBack === 'string')
        option.IsShowBack = JSON.parse(option.IsShowBack);
      this.IsShowBack = option.IsShowBack;
    }
  },
  methods: {
    handleBackClick() {
      uni.navigateBack();
      uni.$emit(SpineClientEvent.lookSpineMissions);
    },
    async getReportData(sendTypes) {
      const obj = {
        Phone: app.globalData.userInfo.PhoneNumber, // app.globalData.userInfo.PhoneNumber 12369874123
        Types: sendTypes,
        PageIndex: 1,
        PageSize: 10,
      };
      if (app.globalData.userInfo.UserCertificates) {
        const IdCardNo = app.globalData.userInfo.UserCertificates.filter(
          (v) => v.CertificateType === 'idCard'
        );
        if (IdCardNo.length > 0) {
          obj.IdCardNo = IdCardNo[0].CertificateValue;
        }
      }
      const res = await GetPulmonaryReportByUserId(obj);
      if (res.Type === 200) {
        console.log(Array.isArray(res.Data.Data), res.Data.Data);
        if (res.Data.Data.length > 0) {
          this.urls = res.Data.Data[0].PDFUrl;
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .u-empty {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 0 auto;
}
</style>
