<template>
  <scroll-view
    class="foot-report-list"
    :style="{ height: height + 'px' }"
    scroll-y
    refresher-enabled
    :refresher-triggered="refreshing"
    @refresherrefresh="onRefresh"
    @scrolltolower="onLoadMore"
  >
    <template v-if="footReportList.length > 0">
      <view
        class="report-item"
        v-for="item in footReportList"
        :key="item.Id"
        @click="onFootReportClick(item)"
      >
        <view class="line" />
        <view class="item-header">
          <view class="item-dot" />
          <text class="item-time">{{ item.CreatedTime }}</text>
        </view>
        <image
          class="foot-image"
          src="../static/foot-report.png"
          mode="scaleToFill"
        />
      </view>
      <u-loadmore :status="loadStatus" />
    </template>
    <u-empty v-else />
  </scroll-view>
</template>

<script>
import { dateFormat } from '../../utils/validate.js';
import { getFootReport } from '../../api/supplier.js';

const kDebugEnable = true;
const kPageSize = 10;
export default {
  name: 'FootReportList',
  props: {
    height: {
      type: Number,
      default: 0,
      required: true,
    },
    userInfo: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      footReportList: [],
      // 是否正在刷新
      refreshing: false,
      /**
       * 底部加载状态
       * loadmore: 加载更多
       * loading: 加载中
       * nomore: 没有更多
       */
      loadStatus: 'loadmore',
    };
  },

  created() {
    // 页码
    this.pageIndex = 1;
  },

  watch: {
    userInfo: {
      async handler(newVal) {
        kDebugEnable && console.debug('查询人信息', newVal);
        if (!newVal || !newVal.Phone || !newVal.Name) {
          return;
        }

        uni.showLoading({
          title: '加载中...',
          mask: true,
        });
        await this.onRefresh();
        uni.hideLoading();
      },
      immediate: true,
    },
  },

  methods: {
    // 点击足底压力测试报告
    onFootReportClick(item) {
      kDebugEnable && console.debug('点击足底压力测试报告', item);
      const url = item.PDFUrl;
      const deviceInfo = uni.getDeviceInfo();

      if (
        deviceInfo.platform === 'android' &&
        url.toLowerCase().endsWith('.pdf')
      ) {
        uni.showLoading({
          title: '',
          mask: true,
        });
        uni.downloadFile({
          url,
          success: (res) => {
            if (res.statusCode != 200) {
              uni.showToast({
                icon: 'none',
                title: res.errMsg ?? '获取报告失败',
              });
              return;
            }
            uni.openDocument({
              filePath: res.tempFilePath,
              success: () => {
                console.log('打开文档成功');
              },
            });
          },
          complete: () => {
            uni.hideLoading();
          },
        });
      } else {
        uni.navigateTo({
          url:
            './WebView?Url=' + encodeURIComponent(url) + '&IsShowBack=' + false,
        });
      }
    },

    // 刷新
    async onRefresh() {
      if (this.refreshing || this.loadStatus === 'loading') {
        return;
      }
      kDebugEnable && console.debug('刷新足底压力测试报告列表');

      this.refreshing = true;
      this.pageIndex = 1;
      const r = await this.loadFootReportList();
      this.refreshing = false;
      if (r.Type !== 200) {
        uni.showToast({
          title: r.Content,
          icon: 'none',
        });
        return;
      }
    },

    // 加载更多
    async onLoadMore() {
      if (
        this.loadStatus === 'nomore' ||
        this.loadStatus === 'loading' ||
        this.refreshing
      ) {
        return;
      }
      kDebugEnable && console.debug('加载更多足底压力测试报告列表');

      this.pageIndex++;
      const r = await this.loadFootReportList();
      if (r.Type !== 200) {
        uni.showToast({
          title: r.Content,
          icon: 'none',
        });
      }
    },

    // 请求足底压力测试报告
    async loadFootReportList() {
      const r = await getFootReport({
        Phone: this.userInfo.Phone,
        Name: this.userInfo.Name,
        PageIndex: this.pageIndex,
        PageSize: kPageSize,
      });
      if (r.Type === 200) {
        const footReportList =
          r.Data.Data.map((item) => {
            return {
              ...item,
              CreatedTime: dateFormat(item.CreatedTime, 'YYYY-MM-DD HH:mm:ss'),
            };
          }) || [];

        if (this.pageIndex === 1) {
          this.footReportList = footReportList;
        } else {
          this.footReportList = [...this.footReportList, ...footReportList];
        }
        this.loadStatus =
          this.footReportList.length < r.Data.TotalCount
            ? 'loadmore'
            : 'nomore';
      }

      return r;
    },
  },
};
</script>

<style lang="scss" scoped>
.foot-report-list {
  display: flex;
  flex-direction: column;
  align-items: start;
  justify-content: start;
  padding: 32rpx;
  box-sizing: border-box;

  .foot-image {
    margin: 15rpx 0 43rpx 36rpx;
    width: 172rpx;
    height: 260rpx;
  }
}

.report-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: start;
  overflow: hidden;

  .line {
    position: absolute;
    top: 14rpx;
    left: 10rpx;
    width: 2rpx;
    height: 100%;
    background-color: #eeeeee;
  }

  .item-header {
    display: flex;
    align-items: center;
    justify-content: start;

    .item-dot {
      width: 20rpx;
      height: 20rpx;
      background-color: #29b7a3;
      border-radius: 50%;
      margin-right: 16rpx;
      z-index: 10;
    }

    .item-time {
      font-weight: 500;
      font-size: 28rpx;
      line-height: 1;
      color: #333333;
    }
  }
}
</style>
