<template>
  <scroll-view
    :style="{ height: height + 'px' }"
    class="container"
    :show-scrollbar="false"
    enable-flex
    scroll-y
  >
    <!-- 患者信息卡片 -->
    <view class="report-card">
      <view class="patient-info-wrapper">
        <image
          class="avatar"
          src="https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/static/frontend/mp-patient/default-child.png"
        ></image>
        <text class="patient-name" :decode="true">
          {{ reportInfo.Name || userInfo.Name }}&nbsp;&nbsp;{{
            reportInfo.Sex || ''
          }}&nbsp;&nbsp;{{ reportInfo.Age || '' }}
        </text>
        <view class="re-query-button" @click="onReQuery">[重新查询]</view>
      </view>
      <!-- 报告数据 -->
      <template
        v-if="
          (reportInfo && Object.keys(reportInfo).length) ||
          (footReportInfo && Object.keys(footReportInfo).length)
        "
      >
        <!-- ATR角 -->
        <view class="report-info-wrapper" v-if="showInfo.AtrConclusion">
          <text class="report-title">脊椎筛查ATR角：</text>
          <text class="report-value">{{ showInfo.AtrConclusion || '' }}</text>
        </view>
        <!-- 足弓指数 -->
        <view
          class="report-info-wrapper"
          v-if="showInfo.LeftFootConclusion || showInfo.RightFootConclusion"
        >
          <text class="report-title">足弓指数：</text>
          <text v-if="showInfo.LeftFootConclusion" class="report-value">{{
            showInfo.LeftFootConclusion
          }}</text>
          <text
            v-if="showInfo.LeftFootConclusion && showInfo.RightFootConclusion"
            >，</text
          >
          <text v-if="showInfo.RightFootConclusion" class="report-value">{{
            showInfo.RightFootConclusion
          }}</text>
        </view>
        <!-- 报告按钮 -->
        <view class="report-button-wrapper">
          <view
            v-if="reportInfo && Object.keys(reportInfo).length"
            class="report-button-item"
            @click="onToSeeSpineReport"
          >
            <image
              class="report-button-item-icon"
              src="../static/spine-icon.png"
            />
            <text>脊柱筛查报告</text>
          </view>
          <view
            v-if="
              reportInfo &&
              Object.keys(reportInfo).length &&
              footReportInfo &&
              Object.keys(footReportInfo).length &&
              footReportInfo.PDFUrl
            "
            style="width: 47rpx"
          ></view>
          <view
            v-if="
              footReportInfo &&
              Object.keys(footReportInfo).length &&
              footReportInfo.PDFUrl
            "
            class="report-button-item"
            @click="onToSeeFootReport"
          >
            <image
              class="report-button-item-icon"
              src="../static/foot-icon.png"
            />
            <text>足底压力测试报告</text>
          </view>
        </view>
      </template>
      <view v-else class="report-info-wrapper">
        <p class="report-title">暂无检测报告</p>
      </view>
    </view>
    <!-- 富文本 - 建议 -->
    <view v-if="showInfo.SuggestContent" class="suggest-content">
      <u-parse :content="showInfo.SuggestContent"></u-parse>
    </view>
    <!-- 青少年足部发育健康自测方案 -->
    <view
      v-if="footArchAbnormal"
      class="self-test-container"
      :style="{
        height: `${selfTestMethodImageHeight}px`,
        minHeight: `${selfTestMethodImageHeight}px`,
        width: `${selfTestMethodImageWidth}px`,
      }"
      @click="onSelfTestMethod"
    >
      <view class="button-wrapper">点击查看</view>
    </view>
    <!-- 推荐医院 -->
    <view
      v-if="orgInfoList && Object.keys(orgInfoList).length"
      class="recommend-hospital"
      v-for="item in orgInfoList"
      :key="item.OrganizationId"
    >
      <u--image
        :src="item.OrganizationHeadImg"
        width="80rpx"
        height="80rpx"
        shape="circle"
      ></u--image>
      <view class="hospital-info">
        <text class="hospital-name">{{ item.OrganizationName || '--' }}</text>
        <text v-if="item.Address" class="hospital-address">{{
          item.Address
        }}</text>
      </view>
      <view
        class="hospital-button-icon"
        hover-class="op-50"
        hover-stay-time="70"
        @click="onOpenMap(item)"
      >
        <image class="button-icon" src="/subReport/static/location.png" />
        <text>导航</text>
      </view>
      <view
        class="hospital-button-icon"
        hover-class="op-50"
        hover-stay-time="70"
        @click="onCallPhone(item)"
      >
        <image class="button-icon" src="/subReport/static/phone.png" />
        <text>电话</text>
      </view>
    </view>
    <!-- 底部留白 -->
    <view style="min-height: 174px"></view>
  </scroll-view>
</template>

<script>
import {
  OrganizationConsortiumGet,
  ConsortiumGetList,
} from '@/api/passport.js';
import { querySuggestLevel } from '@/api/content.js';
import { getReportPage, getFootReport } from '@/api/supplier.js';
import { arrayNotEmpty, dataIsValid, objNotEmpty } from '@/utils/utils';
import { SpineClientEvent } from '../../utils/eventKeys';
import { pxToRpx } from '../../utils/utils';

const kDebugEnable = true;
export default {
  props: {
    height: {
      type: Number,
      default: 0,
      required: true,
    },

    /**
     * 查询人信息
     *
     * @type {Object}
     * @property {string} Name 姓名
     * @property {string} Phone 手机号
     */
    userInfo: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      // 脊椎筛查报告详情
      reportInfo: {},
      // 足底压力测试报告详情
      footReportInfo: {},
      // 足底是否有异常
      footArchAbnormal: false,
      // 自测方法图片
      selfTestMethodImageWidth: 343,
      selfTestMethodImageHeight: 85,
      // 用于显示的信息
      showInfo: {},

      // 建议等级列表
      suggestLevelList: [],

      // 脊椎筛查医联体ID
      consortiumId: '',
      // 推荐机构列表
      orgInfoList: [],
    };
  },

  watch: {
    userInfo: {
      async handler(newVal) {
        kDebugEnable && console.debug('查询人信息', newVal);
        if (!newVal || !newVal.Phone || !newVal.Name) {
          return;
        }

        uni.showLoading({
          title: '加载中...',
          mask: true,
        });
        const rs = await Promise.all([
          this.getSuggestLevelList(),
          this.getSpineReport(),
          this.getFootReport(),
        ]);
        uni.hideLoading();
        const fail = rs.find((v) => v.Type !== 200);
        if (fail) {
          uni.showToast({
            title: fail.Content,
            icon: 'none',
          });
          return;
        }

        this.getInfoByReportValue();
      },
      immediate: true,
    },
  },

  onReady() {
    const window = uni.getWindowInfo();
    this.selfTestMethodImageWidth = window.screenWidth - pxToRpx(64, true);
    this.selfTestMethodImageHeight =
      (this.selfTestMethodImageWidth / 686) * 171;
  },

  methods: {
    // 点击重新查询
    onReQuery() {
      kDebugEnable && console.debug('重新查询');
      this.$emit('query');
    },

    // 点击自测方法
    onSelfTestMethod() {
      kDebugEnable && console.debug('点击自测方法');
      const imageUrl =
        'https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/static/frontend/mp-patient/%E8%B6%B3%E9%83%A8%E5%81%A5%E5%BA%B7%E5%8F%91%E8%82%B2%E8%87%AA%E6%B5%8B%E6%96%B9%E6%B3%95.jpg';
      uni.navigateTo({
        url: '/subReport/ImagePreview?' + `url=${imageUrl}` + '&title=自检方法',
      });
    },

    // 查看脊柱筛查报告
    onToSeeSpineReport() {
      kDebugEnable && console.debug('查看脊柱筛查报告');
      if (!this.reportInfo || !this.reportInfo.Url) {
        uni.showModal({
          title: '温馨提示',
          content: '暂无报告数据',
          showCancel: false,
        });
        return;
      }

      uni.navigateTo({
        url:
          '/subReport/WebView?Url=' +
          encodeURIComponent(this.reportInfo.Url) +
          '&IsShowBack=' +
          true,
      });
    },

    // 查看足底压力测试报告
    async onToSeeFootReport() {
      kDebugEnable && console.debug('查看足底压力测试报告');

      if (!this.footReportInfo.PDFUrl) {
        uni.showModal({
          title: '温馨提示',
          content: '暂无报告数据',
          showCancel: false,
        });
        return;
      }

      const deviceInfo = uni.getDeviceInfo();
      if (
        deviceInfo.platform === 'android' &&
        this.footReportInfo.PDFUrl.toLowerCase().endsWith('.pdf')
      ) {
        uni.showLoading({
          title: '',
          mask: true,
        });
        uni.downloadFile({
          url: this.footReportInfo.PDFUrl,
          success: (res) => {
            if (res.statusCode != 200) {
              uni.showToast({
                icon: 'none',
                title: res.errMsg ?? '获取报告失败',
              });
              return;
            }

            wx.openDocument({
              filePath: res.tempFilePath,
              success: () => {
                console.log('打开文档成功');
              },
            });
          },
          complete: () => {
            uni.hideLoading();
          },
        });
      } else {
        uni.navigateTo({
          url:
            '/subReport/WebView?Url=' +
            encodeURIComponent(this.footReportInfo.PDFUrl) +
            '&IsShowBack=' +
            false,
        });
      }
    },

    // 拨打电话
    onCallPhone(item) {
      kDebugEnable && console.debug('拨打机构电话', item);

      if (!item.Phone) {
        uni.showModal({
          title: '温馨提示',
          content: '未查询到机构的联系电话',
          showCancel: false,
        });
        this.$log.warn(
          `${this.$envVersion}:未查询到${item.OrganizationName}的联系电话`,
          item
        );
        return;
      }

      uni.makePhoneCall({
        phoneNumber: item.Phone,
      });
    },

    // 点击导航
    onOpenMap(item) {
      kDebugEnable && console.debug('点击导航', item);

      if (!item.LatLon || !item.Address) {
        uni.showModal({
          title: '温馨提示',
          content: '未查询到机构的地址信息',
          showCancel: false,
        });
        this.$log.warn(
          `${this.$envVersion}:未查询到${item.OrganizationName}的地址信息`,
          item
        );
        return;
      }

      const LatLon = item.LatLon.split(',');
      uni.openLocation({
        latitude: LatLon[1] * 1,
        longitude: LatLon[0] * 1,
        scale: 18,
        address: item.Address,
        fail: (e) => {
          this.$log.info(
            `${this.$envVersion}:打开${item.OrganizationName}地图失败`,
            e
          );
        },
        success: (success) => {
          this.$log.info(
            `${this.$envVersion}:打开${item.OrganizationName}地图成功`
          );
        },
      });
    },

    // 根据ATR值、足弓指数，获取对应的建议、推荐宣教、推荐机构
    async getInfoByReportValue() {
      const atrValue = dataIsValid(this.reportInfo?.ArtMax)
        ? this.reportInfo.ArtMax
        : 0;
      const leftFootArch = dataIsValid(this.footReportInfo.LeftFootArch)
        ? this.footReportInfo.LeftFootArch
        : 0.22;
      const rightFootArch = dataIsValid(this.footReportInfo.RightFootArch)
        ? this.footReportInfo.RightFootArch
        : 0.22;

      // 匹配满足脊椎ATR值+左足弓指数的建议等级
      const leftLevelList =
        this.suggestLevelList.filter((v) => {
          const atrResult =
            atrValue >= v.SuggestRule.StartPoint &&
            atrValue <= v.SuggestRule.EndPoint;
          const leftFootArchResult =
            (v.FootArchSuggestRule &&
              v.FootArchSuggestRule.some(
                (r) =>
                  leftFootArch >= r.StartPoint && leftFootArch <= r.EndPoint
              )) ||
            false;
          return atrResult && leftFootArchResult;
        }) || [];

      // 匹配满足脊椎ATR值+右足弓指数的建议等级
      const rightLevelList =
        this.suggestLevelList.filter((v) => {
          const atrResult =
            atrValue >= v.SuggestRule.StartPoint &&
            atrValue <= v.SuggestRule.EndPoint;
          const rightFootArchResult =
            (v.FootArchSuggestRule &&
              v.FootArchSuggestRule.some(
                (r) =>
                  rightFootArch >= r.StartPoint && rightFootArch <= r.EndPoint
              )) ||
            false;
          return atrResult && rightFootArchResult;
        }) || [];
      if (leftLevelList.length === 0 && rightLevelList.length === 0) return;

      var showInfo = {};

      // 设置左脚结论
      showInfo.LeftFootConclusion =
        dataIsValid(this.footReportInfo.LeftFootArch) &&
        arrayNotEmpty(leftLevelList)
          ? `左（${leftFootArch}，${leftLevelList[0].FootArchName}）`
          : '';

      // 设置右脚结论
      showInfo.RightFootConclusion =
        dataIsValid(this.footReportInfo.RightFootArch) &&
        arrayNotEmpty(rightLevelList)
          ? `右（${rightFootArch}， ${rightLevelList[0].FootArchName}）`
          : '';

      // 获取有异常的结论
      const abnormalLevelList = [
        ...leftLevelList.filter((v) => v.FootArchNormal === false),
        ...rightLevelList.filter((v) => v.FootArchNormal === false),
      ];

      // 足底是否有异常
      this.footArchAbnormal = abnormalLevelList.length > 0;

      kDebugEnable && console.debug('左脚匹配值', leftLevelList);
      kDebugEnable && console.debug('右脚匹配值', rightLevelList);
      kDebugEnable && console.debug('异常值', abnormalLevelList);

      // 左右脚匹配的结论不同时，优先取有异常的建议
      const levelInfo = (() => {
        if (abnormalLevelList.length > 0) {
          return abnormalLevelList[0];
        } else if (leftLevelList.length > 0) {
          return leftLevelList[0];
        } else {
          return rightLevelList[0];
        }
      })();
      if (!objNotEmpty(levelInfo)) {
        this.showInfo = showInfo;
        return;
      }
      kDebugEnable && console.debug('匹配的建议', levelInfo);

      // 设置建议
      showInfo.SuggestContent = levelInfo.SuggestContent;
      // 设置脊椎ATR角结论
      showInfo.AtrConclusion =
        dataIsValid(this.reportInfo?.ArtMax) && levelInfo.Name
          ? `${atrValue}°，${levelInfo.Name}`
          : '';
      this.showInfo = showInfo;

      // 触发成功匹配脊椎筛查+足底压力测试事件
      const data = {
        levelId: levelInfo.Id,
        artValue: atrValue,
        leftFootArchValue: leftFootArch,
        rightFootArchValue: rightFootArch,
        ...this.reportInfo,
      };
      uni.$emit(SpineClientEvent.spineAndFootTestMatched, data);

      // 推荐机构
      const orgIds = levelInfo.SuggestOrgIds;
      if (arrayNotEmpty(orgIds)) {
        this.getOrganizationConsortiumList(orgIds);
      }
    },

    // 获取各个建议等级
    async getSuggestLevelList() {
      const res = await querySuggestLevel({
        pageIndex: 1,
        pageSize: 999,
        type: 1,
      });
      if (res.Type === 200) {
        this.suggestLevelList = res.Data.Data;
      }

      return res;
    },

    // 获取脊椎筛查报告
    async getSpineReport() {
      const res = await getReportPage({
        Phone: this.userInfo.Phone,
        Name: this.userInfo.Name,
      });
      if (res.Type === 200) {
        this.reportInfo = res.Data[0] || {};
        if (objNotEmpty(this.reportInfo)) {
          kDebugEnable && console.debug('获取到脊椎筛查报告', this.reportInfo);
          this.reportInfo.ArtMax = Math.abs(this.reportInfo.ArtMax);
        }
      }

      return res;
    },

    // 获取足底压力测试报告
    async getFootReport() {
      const res = await getFootReport({
        Phone: this.userInfo.Phone,
        Name: this.userInfo.Name,
        IsLatest: true,
      });
      if (res.Type === 200 && res.Data.Data) {
        this.footReportInfo = res.Data.Data[0] || {};
      }
      return res;
    },

    // 获取医联体机构列表
    async getOrganizationConsortiumList(orgIds) {
      // Type: 1 表示请求脊椎筛查
      // 默认取第一个 作所以这里传的PageSize为1
      const resData = await ConsortiumGetList({
        Type: 1,
      });
      if (resData.Type !== 200 || !resData.Data.length) {
        return;
      }

      // 获取脊椎筛查医联体id
      this.consortiumId = resData.Data[0].Id;

      // 获取推荐机构列表
      const data = {
        ConsortiumId: this.consortiumId,
        OrgIds: orgIds,
        PageIndex: 1,
        PageSize: 999,
      };
      const res = await OrganizationConsortiumGet(data);
      if (res.Type !== 200) {
        this.$log.warn(`${this.$envVersion}:获取推荐机构列表失败`, res);
        uni.showToast({
          title: res.Content,
          icon: 'none',
        });
        return;
      }

      this.orgInfoList = res.Data.Data;
      if (arrayNotEmpty(res.Data.Data)) {
        getApp().changeOrgAndMark({
          orgId: res.Data.Data[0].OrganizationId,
          orgName: res.Data.Data[0].OrganizationName,
        });
      }
    },
  },
};
</script>

<style scoped lang="scss">
/deep/ .u-image__error {
  height: 278rpx !important;
  width: 100% !important;
}

.container {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: start;
  padding-top: 23rpx;

  .report-card {
    background-color: #ffffff;
    border-radius: 24rpx;
    padding: 24rpx;
    display: flex;
    flex-direction: column;
    justify-content: start;
    align-items: stretch;
    margin: 0 32rpx 24rpx;

    .patient-info-wrapper {
      display: flex;
      justify-content: start;
      align-items: center;
      margin-bottom: 9rpx;

      .avatar {
        width: 75rpx;
        height: 75rpx;
        border-radius: 16rpx;
      }

      .patient-name {
        margin-left: 24rpx;
        font-weight: 600;
        font-size: 28rpx;
        color: #333333;
      }

      .re-query-button {
        margin-left: 30rpx;
        font-size: 28rpx;
        font-weight: 600;
        color: #29b7a3;
      }
    }

    .report-info-wrapper {
      display: flex;
      justify-content: start;
      align-items: center;
      margin-top: 15rpx;

      .report-title {
        font-size: 26rpx;
        color: #333333;
      }

      .report-value {
        font-size: 24rpx;
        color: #d52b2b;
      }
    }

    .report-button-wrapper {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 24rpx;

      .report-button-item {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: 400;
        font-size: 26rpx;
        color: #29b7a3;
        background-color: #e9f8f6;
        border-radius: 16rpx;
        padding: 18rpx 0;

        .report-button-item-icon {
          width: 26rpx;
          height: 26rpx;
          margin-right: 8rpx;
        }
      }
    }
  }

  .suggest-content {
    margin: 0 32rpx 24rpx;
  }

  .self-test-container {
    margin: 0 32rpx 24rpx;
    height: 171rpx;
    min-height: 171rpx;
    background-color: #f8f7f7;
    background: url('https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/static/frontend/mp-patient/self-test-method.png')
      no-repeat center center;
    background-size: 100% 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;

    .button-wrapper {
      margin-right: 28rpx;
      font-weight: 500;
      font-size: 24rpx;
      color: #15b1a4;
      padding: 12rpx 18rpx;
      background-color: #ffffff;
      border-radius: 46rpx;
    }
  }

  .recommend-hospital {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #ffffff;
    border-radius: 24rpx;
    margin: 0 32rpx 24rpx;
    padding: 24rpx 0 24rpx 24rpx;

    .hospital-info {
      margin-left: 20rpx;
      display: flex;
      justify-content: start;
      align-items: start;
      flex-direction: column;
      flex: 1;

      .hospital-name {
        font-weight: 600;
        font-size: 28rpx;
        color: #333333;
        line-height: 1;
      }

      .hospital-address {
        font-size: 24rpx;
        color: #989898;
        line-height: 1;
        margin-top: 18rpx;
      }
    }

    .hospital-button-icon {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;

      padding: 10rpx 30rpx;
      font-size: 24rpx;
      color: #989898;

      .button-icon {
        width: 35rpx;
        height: 35rpx;
        margin-bottom: 6rpx;
      }
    }
  }
}

.op-50 {
  opacity: 0.5;
}
</style>
