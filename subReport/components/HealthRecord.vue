<template>
  <view :style="{ height: height + 'px' }" class="container">
    <!-- 患者信息 -->
    <view class="patient-wrapper">
      <u--image
        :showLoading="true"
        :src="
          reportInfo.HeadImg ||
          'https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/static/frontend/mp-patient/default-child.png'
        "
        width="76rpx"
        height="76rpx"
        mode="aspectFill"
      ></u--image>
      <view class="patient-info-wrapper">
        <view>
          <text v-if="reportInfo.Name" class="info-text">{{
            reportInfo.Name
          }}</text>
          <text v-if="reportInfo.Sex" class="info-text">{{
            reportInfo.Sex
          }}</text>
          <text v-if="reportInfo.Age" class="info-text">{{
            reportInfo.Age
          }}</text>
        </view>
        <text v-if="reportInfo.Birthday" class="birthday-text"
          >出生日期：{{ reportInfo.Birthday }}</text
        >
      </view>
    </view>
    <!-- 分页控制器 -->
    <view class="tabs-wrapper" v-if="mountTabs">
      <u-tabs
        :list="[
          { name: '脊柱筛查' },
          { name: '足底压力测试' },
          { name: '病历报告' },
          { name: '治疗方案' },
        ]"
        lineHeight="0"
        :activeStyle="{
          color: '#29b7a3',
          fontSize: '26rpx',
          backgroundColor: '#DFF3F1',
          borderRadius: '30px',
          border: '1px solid #29B7A3',
          padding: '0 32rpx',
          lineHeight: '30px',
          height: '30px',
        }"
        :inactiveStyle="{
          color: '#333333',
          fontSize: '26rpx',
          backgroundColor: '#ffffff',
          borderRadius: '30px',
          border: '1px solid #ffffff',
          padding: '0 32rpx',
          lineHeight: '30px',
          height: '30px',
        }"
        :itemStyle="{
          margin: '10px 5px',
        }"
        :current="currentTab"
        @change="onChangeTab"
      ></u-tabs>
    </view>
    <block>
      <!-- 脊椎筛查报告 -->
      <view v-show="currentTab === 0">
        <CobbAndATRChart
          v-if="cobbAndATRChartLoaded"
          :height="contentHeight"
          :userInfo="userInfo"
          :show="show && currentTab === 0"
        />
      </view>
      <!-- 足底压力测试 -->
      <view v-show="currentTab === 1">
        <FootReportList
          v-if="footReportListLoaded"
          :height="contentHeight"
          :userInfo="userInfo"
        />
      </view>
      <!-- 病历报告 -->
      <view v-show="currentTab === 2">
        <MedicalReportList
          v-if="medicalReportsLoaded"
          :height="contentHeight"
        />
      </view>
      <!-- 治疗方案 -->
      <view v-show="currentTab === 3">
        <TreatmentPlanList
          v-if="treatmentPlansLoaded"
          :height="contentHeight"
        />
      </view>
    </block>
  </view>
</template>

<script>
import CobbAndATRChart from './CobbAndATRChart.vue';
import FootReportList from './FootReportList.vue';
import MedicalReportList from './MedicalReportList.vue';
import TreatmentPlanList from './TreatmentPlanList.vue';
import { pxToRpx } from '../../utils/utils';

const kDebugEnable = true;
export default {
  components: {
    CobbAndATRChart,
    FootReportList,
    MedicalReportList,
    TreatmentPlanList,
  },

  props: {
    height: {
      type: Number,
      default: 0,
      required: true,
    },
    /**
     * 是否正在显示
     */
    show: {
      type: Boolean,
      default: false,
    },
    /**
     * 脊椎报告相关数据+查询人信息
     */
    reportInfo: {
      type: Object,
      required: true,
    },
  },

  computed: {
    userInfo() {
      const userInfo = {
        Name: this.reportInfo.Name,
        Phone: this.reportInfo.Phone,
      };
      kDebugEnable && console.debug('获取到查询人信息', userInfo);
      return userInfo;
    },
  },

  data() {
    return {
      // 当前选中的tab
      currentTab: 0,
      contentHeight: 0,

      // 挂载tabs
      mountTabs: false,

      cobbAndATRChartLoaded: true, // Cobb和ATR图表是否已加载
      footReportListLoaded: false, // 足底压力测试报告是否已加载
      medicalReportsLoaded: false, // 病历报告是否已加载
      treatmentPlansLoaded: false, // 治疗方案是否已加载
    };
  },

  async mounted() {
    this.contentHeight = this.height - pxToRpx(148, true) - 50;

    // 延迟加载Tabs，避免偏移
    setTimeout(() => {
      this.mountTabs = true;
    }, 300);
  },

  methods: {
    onChangeTab(e) {
      kDebugEnable && console.debug('onChangeTab', e);

      this.currentTab = e.index;
      switch (this.currentTab) {
        case 0:
          this.cobbAndATRChartLoaded = true;
          break;
        case 1:
          this.footReportListLoaded = true;
          break;
        case 2:
          this.medicalReportsLoaded = true;
          break;
        case 3:
          this.treatmentPlansLoaded = true;
          break;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  height: 100%;
  width: 100%;

  .patient-wrapper {
    display: flex;
    align-items: center;
    justify-content: start;
    background-color: #ffffff;
    border-radius: 22rpx;
    padding: 24rpx 26rpx;
    margin: 24rpx 32rpx 0;

    .patient-info-wrapper {
      flex: 1;
      padding-left: 18rpx;
      display: flex;
      flex-direction: column;
      align-items: start;
      justify-content: center;

      .info-text {
        font-weight: 600;
        font-size: 28rpx;
        color: #333333;
        margin-right: 10rpx;
      }

      .birthday-text {
        margin-top: 14rpx;
        font-size: 22rpx;
        color: #666666;
      }
    }
  }

  .tabs-wrapper {
    margin: 0 32rpx;
  }
}
</style>
