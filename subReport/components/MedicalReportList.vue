<template>
  <view :style="{ height: height + 'px' }" class="container">
    <scroll-view
      class="medical-report-list"
      :style="{ height: height + 'px' }"
      scroll-y
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
      @scrolltolower="onLoadMore"
    >
      <template v-if="medicalReportList.length > 0">
        <view
          class="medical-report-item"
          v-for="item in medicalReportList"
          :key="item.Id"
          hover-class="op-50"
          hover-stay-time="70"
          @click="onCheckMedicalReport(item)"
        >
          <text class="item-time">{{ item.InDate }}</text>
          <view class="item-middle-info">
            <text class="info-text" :decode="true"
              >{{ item.OrganizationName || '--' }}&nbsp;&nbsp;{{
                item.DepartmentName || ''
              }}</text
            >
            <view class="edit-wrapper" @click.stop="onEditMedicalReport(item)"
              >编辑</view
            >
            <view
              class="delete-wrapper"
              @click.stop="onDeleteMedicalReport(item)"
              >删除</view
            >
          </view>
          <text v-if="item.Diagnose" class="item-diagnose"
            >诊断：{{ item.Diagnose }}</text
          >
        </view>
        <u-loadmore :status="loadStatus" />
        <view style="height: 82px"></view>
      </template>
      <u-empty v-else />
    </scroll-view>
    <u-button
      shape="circle"
      text="上传病历"
      customStyle="width:90%;bottom: 30px;position: fixed; left: 50%;transform: translateX(-50%);backgroundColor:#29B7A3;color:#fff"
      @click="onUploadMedicalReport"
    ></u-button>
  </view>
</template>

<script>
import { dateFormat } from '../../utils/validate.js';
import { MedicalClientEvent } from '../../utils/eventKeys.js';
import { getUserCaseInfo, delMedical } from '../../api/record.js';

const kDebugEnable = true;
const kPageSize = 10;
const app = getApp();
export default {
  name: 'MedicalReportList',
  props: {
    /** 组件高度 */
    height: {
      type: Number,
      default: 0,
      required: true,
    },
  },
  data() {
    return {
      medicalReportList: [],
      // 是否正在刷新
      refreshing: false,
      /**
       * 底部加载状态
       * loadmore: 加载更多
       * loading: 加载中
       * nomore: 没有更多
       */
      loadStatus: 'loadmore',
    };
  },

  created() {
    this.pageIndex = 1;
  },

  async mounted() {
    uni.showLoading({
      title: '加载中...',
      mask: true,
    });
    await this.onRefresh();
    uni.hideLoading();
    this.listenEvents();
  },

  destroyed() {
    uni.$off(MedicalClientEvent.updateMedical, this.handleMedicalUpdate);
  },

  methods: {
    // 监听事件
    listenEvents() {
      const that = this;
      this.handleMedicalUpdate = (id) => {
        kDebugEnable && console.debug('监听病历添加更新事件', id);
        that.onRefresh();
      };
      uni.$on(MedicalClientEvent.updateMedical, that.handleMedicalUpdate);
    },

    // 点击查看病历报告
    onCheckMedicalReport(item) {
      kDebugEnable && console.debug('点击查看病历报告', item);
      uni.navigateTo({
        url:
          '/subPackIndex/user/addMedical' +
          `?age=${app.globalData.userInfo.Age}` +
          `&orderId=${item.Id}` +
          `&type=disable`,
      });
    },

    // 点击编辑病历报告
    onEditMedicalReport(item) {
      kDebugEnable && console.debug('点击编辑病历报告', item);
      uni.navigateTo({
        url:
          '/subPackIndex/user/addMedical' +
          `?age=${app.globalData.userInfo.Age}` +
          `&orderId=${item.Id}`,
      });
    },

    // 点击删除病历报告
    onDeleteMedicalReport(item) {
      kDebugEnable && console.debug('点击删除病历报告', item);

      const that = this;
      uni.showModal({
        title: '温馨提示',
        content: '确定要删除吗?',
        success: function (res) {
          if (res.confirm) {
            that.handleConfirmDeleteMedicalReport(item.Id);
          }
        },
      });
    },

    // 点击上传病历
    onUploadMedicalReport() {
      uni.navigateTo({
        url: '/subPackIndex/user/addMedical?age=' + app.globalData.userInfo.Age,
      });
    },

    // 确认删除病历报告
    async handleConfirmDeleteMedicalReport(reportId) {
      uni.showLoading({
        title: '删除中...',
        mask: true,
      });
      let res = await delMedical(reportId);
      uni.hideLoading();
      if (res.Type !== 200) {
        uni.showToast({
          title: res.Content,
          icon: 'none',
        });
        return;
      }

      uni.showToast({
        title: '删除成功',
        icon: 'success',
      });
      uni.startPullDownRefresh();
    },

    // 刷新
    async onRefresh() {
      if (this.refreshing || this.loadStatus === 'loading') {
        return;
      }
      kDebugEnable && console.debug('刷新病历报告列表');

      this.refreshing = true;
      this.pageIndex = 1;
      const r = await this.loadMedicalReportList();
      this.refreshing = false;
      if (r.Type !== 200) {
        uni.showToast({
          title: r.Content,
          icon: 'none',
        });
        return;
      }
    },

    // 加载更多
    async onLoadMore() {
      if (
        this.loadStatus === 'nomore' ||
        this.loadStatus === 'loading' ||
        this.refreshing
      ) {
        return;
      }
      kDebugEnable && console.debug('加载更多病历报告列表');

      this.pageIndex++;
      const r = await this.loadMedicalReportList();
      if (r.Type !== 200) {
        uni.showToast({
          title: r.Content,
          icon: 'none',
        });
      }
    },

    // 请求病历报告列表
    async loadMedicalReportList() {
      const r = await getUserCaseInfo({
        userId: app.globalData.userInfo.Id,
        dateSerachType: 0,
        type: 0,
        pageindex: this.pageIndex,
        pagesize: kPageSize,
        IsSelfBuild: true,
      });
      if (r.Type === 200) {
        const medicalReportList = r.Data.map((item) => {
          return {
            ...item,
            Diagnose: item.VisitDiagnoses.filter(
              (v) => v.DiagnoseTypeName === '诊断'
            )
              .map((v) => v.DiagnoseName)
              .join(','),
            InDate: item.InDate ? dateFormat(item.InDate, 'YYYY-MM-DD') : '',
          };
        });

        if (this.pageIndex === 1) {
          this.medicalReportList = medicalReportList;
        } else {
          this.medicalReportList = [
            ...this.medicalReportList,
            ...medicalReportList,
          ];
        }
        this.loadStatus =
          medicalReportList.length < kPageSize ? 'nomore' : 'loadmore';
      }

      return r;
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: start;
}

.medical-report-list {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: start;

  .medical-report-item {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    justify-content: start;
    margin: 0 32rpx 30rpx;
    padding: 32rpx;
    background-color: #ffffff;
    border-radius: 16rpx;

    .item-time {
      font-weight: 600;
      font-size: 28rpx;
      color: #333333;
    }

    .item-middle-info {
      margin: 14rpx 0;
      display: flex;
      align-items: center;
      justify-content: start;

      .info-text {
        font-weight: 400;
        font-size: 28rpx;
        color: #333333;
        flex: 1;
      }

      .edit-wrapper {
        background-color: #ffffff;
        border: 1rpx solid #cccccc;
        border-radius: 6rpx;
        padding: 10rpx 26rpx;
        font-weight: 400;
        font-size: 26rpx;
        color: #333333;
        line-height: 1;
        margin-left: 16rpx;
      }

      .delete-wrapper {
        background-color: #ffedf1;
        border: 1rpx solid #ffedf1;
        border-radius: 6rpx;
        padding: 10rpx 26rpx;
        font-weight: 400;
        font-size: 26rpx;
        color: #ff4273;
        line-height: 1;
        margin-left: 16rpx;
      }
    }

    .item-diagnose {
      font-weight: 400;
      font-size: 28rpx;
      color: #666666;
    }
  }
}
</style>
