<template>
  <scroll-view
    class="treatment-plan-list"
    :style="{ height: height + 'px' }"
    scroll-y
    refresher-enabled
    :refresher-triggered="refreshing"
    @refresherrefresh="onRefresh"
    @scrolltolower="onLoadMore"
  >
    <template v-if="treatmentPlanList.length > 0">
      <view
        class="treatment-plan-item"
        v-for="item in treatmentPlanList"
        :key="item.Id"
        @click="onTreatmentPlanClick(item)"
      >
        <u--image
          :src="item.ActionUnitImgURL"
          width="129rpx"
          height="129rpx"
          radius="16rpx"
          mode="aspectFill"
        ></u--image>
        <view class="treatment-plan-content">
          <view class="content-top">
            <text class="content-time"
              >{{ item.CreatedTime }}-{{ item.ActualFinishedTime }}</text
            >
            <view
              class="content-type"
              :style="{ backgroundColor: item.TypeColor }"
              >{{ item.TypeText }}</view
            >
          </view>
          <text class="content-title">{{ item.Name }}</text>
        </view>
      </view>
      <u-loadmore :status="loadStatus" />
    </template>
    <u-empty v-else />
  </scroll-view>
</template>

<script>
import { dateFormat } from '../../utils/validate.js';
import { getPatPrescription } from '../../api/consult.js';

const kDebugEnable = true;
const kPageSize = 10;
const app = getApp();
export default {
  name: 'TreatmentPlanList',
  props: {
    /** 组件高度 */
    height: {
      type: Number,
      default: 0,
      required: true,
    },
  },
  data() {
    return {
      // 展示数据
      treatmentPlanList: [],
      // 是否正在刷新
      refreshing: false,
      /**
       * 底部加载状态
       * loadmore: 加载更多
       * loading: 加载中
       * nomore: 没有更多
       */
      loadStatus: 'loadmore',
    };
  },

  created() {
    // 页码
    this.pageIndex = 1;
    // 全量数据
    this.allTreatmentPlanList = [];
  },

  async mounted() {
    uni.showLoading({
      title: '加载中...',
      mask: true,
    });
    await this.onRefresh();
    uni.hideLoading();
  },

  methods: {
    // 刷新
    async onRefresh() {
      if (this.refreshing || this.loadStatus === 'loading') {
        return;
      }

      this.refreshing = true;
      this.pageIndex = 1;
      const r = await this.loadTreatmentPlanList();
      if (r.Type !== 200) {
        uni.showToast({
          title: r.Content,
          icon: 'none',
        });
        return;
      }

      this.treatmentPlanList = this.allTreatmentPlanList.slice(
        0,
        Math.min(kPageSize, this.allTreatmentPlanList.length)
      );
      kDebugEnable &&
        console.debug('治疗方案列表长度', this.treatmentPlanList.length);
      this.refreshing = false;
    },

    // 加载更多
    onLoadMore() {
      if (
        this.loadStatus === 'nomore' ||
        this.loadStatus === 'loading' ||
        this.refreshing
      ) {
        return;
      }

      this.loadStatus = 'loading';
      this.pageIndex++;
      this.treatmentPlanList = this.allTreatmentPlanList.slice(
        0,
        Math.min(
          this.pageIndex * kPageSize + kPageSize,
          this.allTreatmentPlanList.length
        )
      );
      this.loadStatus =
        this.treatmentPlanList.length < this.allTreatmentPlanList.length
          ? 'loadmore'
          : 'nomore';
    },

    // 点击治疗方案
    onTreatmentPlanClick(item) {
      kDebugEnable && console.debug('点击治疗方案', item);

      if (item.TreatType === 1 || item.TreatType === 3) {
        // 居家 + 院内，跳转康复计划详情
        uni.navigateTo({
          url:
            '/subPackTraining/trainingPlanDetailPage?trainingPlanId=' +
            item.ProgramId,
        });
      } else {
        // 线下，跳转执行记录
        uni.navigateTo({
          url:
            '/subPackIndex/community/treatmentDetail?prescriptionId=' +
            item.StrId,
        });
      }
    },

    // 请求治疗方案列表
    async loadTreatmentPlanList() {
      const r = await getPatPrescription({
        patId: app.globalData.userInfo.Id,
        state: 2,
      });
      if (r.Type === 200) {
        const treatmentPlanList = r.Data.map((item) => {
          // 格式化开始时间
          const formattedCreatedTime = dateFormat(
            item.CreatedTime,
            'YYYY.MM.DD'
          );

          // 计算结束时间
          let endTime;
          if (item.ActualFinishedTime) {
            // 如果有实际结束时间，使用实际结束时间
            endTime = dateFormat(item.ActualFinishedTime, 'YYYY.MM.DD');
          } else {
            // 如果没有实际结束时间，但有剩余天数，则用创建时间 + 剩余天数
            endTime = dateFormat(item.CreatedTime, 'YYYY.MM.DD', true, {
              Count: item.RemainingLife || 0,
              Type: 'day',
            });
          }

          return {
            ...item,
            CreatedTime: formattedCreatedTime,
            ActualFinishedTime: endTime,
            TypeColor:
              item.TreatType === 1
                ? '#29B7A3'
                : item.TreatType === 2
                  ? '#4DBDEA'
                  : '#E7812F',
            TypeText:
              item.TreatType === 1
                ? '居家'
                : item.TreatType === 2
                  ? '线下'
                  : '院内',
          };
        });

        this.allTreatmentPlanList = treatmentPlanList;
        this.loadStatus =
          this.allTreatmentPlanList.length > kPageSize ? 'loadmore' : 'nomore';
      }

      return r;
    },
  },
};
</script>

<style lang="scss" scoped>
.treatment-plan-list {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: start;

  .treatment-plan-item {
    display: flex;
    align-items: center;
    justify-content: start;
    margin: 0 32rpx 30rpx;
    padding: 32rpx 0 32rpx 32rpx;
    background-color: #ffffff;
    border-radius: 16rpx;
    overflow: hidden;

    .treatment-plan-content {
      flex: 1;
      margin-left: 26rpx;
      display: flex;
      flex-direction: column;
      align-items: stretch;
      justify-content: start;

      .content-top {
        display: flex;
        align-items: center;
        justify-content: start;

        .content-time {
          font-weight: 400;
          font-size: 26rpx;
          color: #333333;
          flex: 1;
        }

        .content-type {
          font-weight: 500;
          font-size: 24rpx;
          color: #ffffff;
          line-height: 1;
          padding: 10rpx 24rpx;
          border-radius: 50rpx 0 0 50rpx;
        }
      }

      .content-title {
        font-weight: 600;
        font-size: 28rpx;
        color: #333333;
        margin: 15rpx 32rpx 0 0;
      }
    }
  }
}
</style>
