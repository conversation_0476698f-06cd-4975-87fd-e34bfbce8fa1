<template>
  <scroll-view
    :style="{ height: height + 'px' }"
    class="spine-report"
    scroll-y
    refresher-enabled
    :refresher-triggered="refreshing"
    @refresherrefresh="onRefresh"
  >
    <template v-if="cobbList.length > 0 || atrList.length > 0">
      <!-- Cobb角度变化趋势 -->
      <view class="line-chart" v-if="cobbList.length > 0">
        <view class="chart-title">Cobb角度变化趋势</view>
        <view class="chart-container">
          <qiun-data-charts
            type="line"
            canvas2d
            :chartData="cobbChartData"
            :opts="cobbOpts"
            tooltipFormat="tlineTooltip1"
            :reshow="show"
            :optsWatch="false"
            :animation="false"
          />
        </view>
        <view class="chart-info">
          <text class="chart-info-title">0度＜cobb角≤10度：</text>
          <text class="chart-info-text">姿势不良</text>
        </view>
        <view class="chart-info">
          <text class="chart-info-title">10度＜Cobb角≤20度：</text>
          <text class="chart-info-text">轻度脊柱侧弯</text>
        </view>
        <view class="chart-info">
          <text class="chart-info-title">20度＜cobb角≤30度：</text>
          <text class="chart-info-text">中度脊柱侧弯</text>
        </view>
        <view class="chart-info">
          <text class="chart-info-title">Cobb角≥40度：</text>
          <text class="chart-info-text">重度脊柱侧弯</text>
        </view>
      </view>
      <!-- ATR角度变化趋势 -->
      <view class="line-chart" v-if="atrList.length > 0">
        <view class="chart-title">ATR角度变化趋势</view>
        <view class="chart-container">
          <qiun-data-charts
            type="line"
            :height="180"
            canvas2d
            :chartData="atrChartData"
            :opts="atrOpts"
            :optsWatch="false"
            :reshow="show"
            tooltipFormat="tlineTooltip1"
            :animation="false"
          />
        </view>
        <view class="chart-info">
          <text class="chart-info-title">0度≤ATR角＜4度：</text>
          <text class="chart-info-text">正常</text>
        </view>
        <view class="chart-info">
          <text class="chart-info-title">4度≤ATR角＜7度：</text>
          <text class="chart-info-text">脊柱侧弯轻度风险</text>
        </view>
        <view class="chart-info">
          <text class="chart-info-title">ATR角≥7度：</text>
          <text class="chart-info-text">脊柱侧弯高风险</text>
        </view>
      </view>
      <!-- ATR角度记录列表 -->
      <view class="atr-record-list" v-if="atrList.length > 0">
        <view
          class="report-item"
          v-for="item in atrList"
          :key="item.ReportTime"
          @click="onAtrRecordClick(item)"
        >
          <view class="line" />
          <view class="item-header">
            <view class="item-dot" />
            <text class="item-time">{{ item.ReportTime }}</text>
          </view>
          <view class="atr-info">
            <image
              class="atr-image"
              src="../static/logo999.png"
              mode="scaleToFill"
            />
            <text class="atr-text">
              ATR角：{{ item.ArtMax }}°{{ item.Level ? `，${item.Level}` : '' }}
            </text>
            <u-icon name="arrow-right" size="15" color="#F8F7F7" />
          </view>
        </view>
      </view>
    </template>
    <u-empty v-else />
  </scroll-view>
</template>

<script>
import { arrayNotEmpty } from '../../utils/utils.js';
import { dateFormat } from '../../utils/validate.js';
import { getCobbList, getReportPage } from '../../api/supplier.js';
import { querySuggestLevel } from '../../api/content.js';

const kDebugEnable = true;
const app = getApp();
export default {
  name: 'CobbAndATRChart',
  props: {
    height: {
      type: Number,
      default: 0,
      required: true,
    },
    /**
     * 是否正在显示
     */
    show: {
      type: Boolean,
      default: false,
    },
    userInfo: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      // 是否正在刷新
      refreshing: false,

      // Cobb角列表
      cobbList: [],
      cobbOpts: {
        color: ['#2ED1BB'],
        enableScroll: false,
        fontSize: 9,
        fontColor: '#999999',
        xAxis: {
          fontSize: 9,
          fontColor: '#999999',
        },
        legend: {
          show: false,
        },
      },
      cobbChartData: {
        categories: [],
        series: [
          {
            name: 'Cobb角',
            data: [],
          },
        ],
      },

      // ATR角列表
      atrList: [],
      atrOpts: {
        color: ['#EBB72C'],
        enableScroll: false,
        fontSize: 9,
        fontColor: '#999999',
        xAxis: {
          fontSize: 9,
          fontColor: '#999999',
        },
        legend: {
          show: false,
        },
      },
      atrChartData: {
        categories: [],
        series: [
          {
            name: 'ATR角',
            data: [],
          },
        ],
      },
    };
  },

  watch: {
    userInfo: {
      async handler(newVal) {
        kDebugEnable && console.debug('查询人信息', newVal);
        if (!newVal || !newVal.Phone || !newVal.Name) {
          return;
        }

        uni.showLoading({
          title: '加载中...',
          mask: true,
        });
        await this.onRefresh();
        uni.hideLoading();
      },
      immediate: true,
    },
  },

  methods: {
    // 点击ATR角记录
    onAtrRecordClick(item) {
      kDebugEnable && console.debug('点击ATR角记录', item);
      uni.navigateTo({
        url:
          './WebView?Url=' +
          encodeURIComponent(item.Url) +
          '&IsShowBack=' +
          false,
      });
    },

    // 刷新
    async onRefresh() {
      if (this.refreshing) {
        return;
      }
      kDebugEnable && console.debug('刷新Cobb和ATR图表');

      this.refreshing = true;
      const rs = await Promise.all([this.loadCobbList(), this.loadATRList()]);
      this.refreshing = false;
      const failed = rs.find((r) => r.Type !== 200);
      if (failed) {
        uni.showToast({
          title: failed.Content,
          icon: 'none',
        });
      }
    },

    // 请求Cobb角列表
    async loadCobbList() {
      const r = await getCobbList({
        UserId: app.globalData.userInfo.Id,
        PageIndex: 1,
        PageSize: 999,
      });
      if (r.Type === 200) {
        this.cobbList = r.Data.Data || [];
        // 处理Cobb角折线图数据
        this.handleCobbChartData();
      }

      return r;
    },

    // 处理Cobb角折线图数据
    handleCobbChartData() {
      if (!arrayNotEmpty(this.cobbList)) {
        return;
      }

      // 按照日期正排序，取最后5条数据
      const sortedData = [...this.cobbList]
        .sort((a, b) => new Date(a.CreatedTime) - new Date(b.CreatedTime))
        .slice(-5);
      kDebugEnable && console.debug('cobb排序后列表', sortedData);

      // 提取日期和Score值
      const categories = sortedData.map((item) => {
        return dateFormat(item.CreatedTime, 'YYYY.MM.DD');
      });

      const seriesData = sortedData.map((item) => item.Score);

      // 更新图表数据
      this.cobbChartData = {
        categories: categories,
        series: [
          {
            name: 'Cobb角',
            data: seriesData,
          },
        ],
      };
    },

    // 获取ATR等级
    async getAtrLevelList(atrList) {
      const res = await querySuggestLevel({
        pageIndex: 1,
        pageSize: 999,
        type: 1,
      });
      if (res.Type !== 200 || !arrayNotEmpty(res.Data.Data)) return;

      const levelList = res.Data.Data;
      this.atrList = atrList.map((item) => {
        const atrValue = item.ArtMax || 0;
        const list = levelList.filter(
          (v) =>
            atrValue >= v.SuggestRule.StartPoint &&
            atrValue <= v.SuggestRule.EndPoint
        );
        if (list.length > 0) {
          item.Level = list[0].Name;
        }
        return item;
      });
    },

    // 请求ATR角列表
    async loadATRList() {
      const r = await getReportPage({
        Phone: this.userInfo.Phone,
        Name: this.userInfo.Name,
        PageNo: 1,
        PageSize: 999,
      });
      if (r.Type === 200) {
        const atrList = r.Data || [];

        // 处理ATR角折线图数据
        this.handleATRChartData(atrList);
        // 获取ATR等级
        this.getAtrLevelList(atrList);
      }

      return r;
    },

    // 处理ATR角折线图数据
    handleATRChartData(atrList) {
      if (!arrayNotEmpty(atrList)) {
        return;
      }

      // 按照日期排序
      const sortedData = [...atrList]
        .sort((a, b) => new Date(a.ReportTime) - new Date(b.ReportTime))
        .slice(-5);
      kDebugEnable && console.debug('ATR角排序后列表', sortedData);

      // 提取日期和Score值
      const categories = sortedData.map((item) => {
        return dateFormat(item.ReportTime, 'YYYY.MM.DD');
      });
      const seriesData = sortedData.map((item) => item.ArtMax);

      // 更新图表数据
      this.atrChartData = {
        categories: categories,
        series: [
          {
            name: 'ATR角',
            data: seriesData,
          },
        ],
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.spine-report {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: start;

  .line-chart {
    padding-bottom: 24rpx;
    margin: 0 32rpx 30rpx;
    background-color: #ffffff;
    border-radius: 16rpx;

    .chart-title {
      padding: 32rpx;
      font-weight: 500;
      font-size: 28rpx;
      color: #333333;
    }

    .chart-container {
      height: 350rpx;
      width: 100%;
    }

    .chart-info {
      display: flex;
      align-items: center;
      justify-content: start;
      margin-top: 15rpx;
      margin-left: 32rpx;

      .chart-info-title {
        font-weight: 500;
        font-size: 26rpx;
        color: #777777;
      }

      .chart-info-text {
        font-weight: 500;
        font-size: 26rpx;
        color: #333333;
      }
    }
  }

  .atr-record-list {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    justify-content: start;
    margin: 14rpx 44rpx 44rpx;

    .atr-info {
      padding: 24rpx 32rpx;
      margin: 24rpx 0 39rpx 36rpx;
      display: flex;
      align-items: center;
      justify-content: start;
      background-color: #ffffff;
      box-shadow: 0rpx 3rpx 8rpx 0rpx #29b7a314;
      border-radius: 16rpx;

      .atr-image {
        width: 96rpx;
        height: 96rpx;
        margin-right: 16rpx;
      }

      .atr-text {
        font-weight: 400;
        font-size: 28rpx;
        color: #333333;
      }
    }
  }
}

.report-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: start;
  overflow: hidden;

  .line {
    position: absolute;
    top: 14rpx;
    left: 10rpx;
    width: 2rpx;
    height: 100%;
    background-color: #eeeeee;
  }

  .item-header {
    display: flex;
    align-items: center;
    justify-content: start;

    .item-dot {
      width: 20rpx;
      height: 20rpx;
      background-color: #29b7a3;
      border-radius: 50%;
      margin-right: 16rpx;
      z-index: 10;
    }

    .item-time {
      font-weight: 500;
      font-size: 28rpx;
      line-height: 1;
      color: #333333;
    }
  }
}
</style>
