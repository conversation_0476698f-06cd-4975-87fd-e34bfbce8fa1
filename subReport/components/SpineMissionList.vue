<template>
  <view class="container" :style="{ height: height + 'px' }">
    <template v-if="typeList.length > 0">
      <!-- 标签栏 -->
      <u-sticky>
        <view class="tabs-wrapper" v-if="mountTabs">
          <u-tabs
            :activeStyle="{
              color: '#ffffff',
              fontWeight: 'bold',
              fontSize: '24rpx',
              padding: '16rpx 36rpx',
              backgroundColor: '#29B7A3',
              borderRadius: '16rpx',
            }"
            :inactiveStyle="{
              color: '#333333',
              fontWeight: 'bold',
              fontSize: '24rpx',
              padding: '16rpx 36rpx',
              backgroundColor: '#ffffff',
              borderRadius: '16rpx',
            }"
            :itemStyle="{ margin: '32rpx 16rpx !important' }"
            keyName="Name"
            lineHeight="0"
            :list="typeList"
            @click="onChangeList"
          ></u-tabs>
        </view>
      </u-sticky>
      <!-- 文章列表 -->
      <template v-if="articleList.length > 0">
        <view
          class="mission-item"
          hover-class="op-50"
          hover-stay-time="70"
          v-for="i in articleList"
          :key="i.Id"
          @click="onLookArticle(i)"
        >
          <u--image
            :src="i.ShowImg"
            width="112rpx"
            height="112rpx"
            radius="16rpx"
          ></u--image>
          <text class="mission-text">{{ i.Title || '--' }}</text>
        </view>
      </template>
      <u-empty
        v-else
        mode="data"
        icon="http://cdn.uviewui.com/uview/empty/data.png"
        text="没有推荐的内容"
      ></u-empty>
    </template>
    <u-empty v-else />
  </view>
</template>

<script>
import { nextTick } from 'vue';
import { querySuggestType, querySuggestInfo } from '@/api/content.js';
import { getMissionImageUrl } from '@/utils/mission';
import { arrayNotEmpty } from '@/utils/utils';

const kDebugEnable = true;
const app = getApp();
export default {
  props: {
    /** 脊柱筛查等级id */
    levelId: {
      type: String,
      value: undefined,
      required: true,
    },

    height: {
      type: Number,
      value: 0,
      required: true,
    },
  },

  data() {
    return {
      // 推荐文章分类列表
      typeList: [],
      // 文章列表
      articleList: [],

      // 加载tabs
      mountTabs: false,
    };
  },

  watch: {
    levelId(newVal, oldVal) {
      kDebugEnable && console.debug('脊柱筛查等级id', newVal, '旧值:', oldVal);
      if (newVal !== oldVal || !this.initialized) {
        // 第一次，或者levelId发生变化
        this.initialized = false;
        this.initData();
      }
    },
  },

  mounted() {
    this.initData();
  },

  methods: {
    // 切换文章列表
    async onChangeList(e) {
      uni.showLoading({
        title: '加载中...',
        mask: true,
      });
      const r = await this.getArticleList(e.Id);
      uni.hideLoading();
      if (r.Type !== 200) {
        uni.showToast({
          title: r.Content,
          icon: 'none',
        });
      }
    },

    // 查看推荐文章
    onLookArticle(i) {
      uni.navigateTo({
        url: '/subPropaganda/detail?id=' + i.RecoveryMissionId,
      });
    },

    // 初始化数据
    async initData() {
      if (this.initialized) return;

      this.initialized = true;
      uni.showLoading({
        title: '加载中...',
        mask: true,
      });
      const r0 = await this.getArticleTypeList();
      if (r0.Type !== 200) {
        uni.hideLoading();
        uni.showToast({
          title: r0.Content,
          icon: 'none',
        });
        return;
      }
      if (!arrayNotEmpty(this.typeList)) {
        uni.hideLoading();
        return;
      }

      const r1 = await this.getArticleList(this.typeList[0].Id);
      uni.hideLoading();
      if (r1.Type !== 200) {
        uni.showToast({
          title: r1.Content,
          icon: 'none',
        });
      }
    },

    // 获取推荐文章分类列表
    async getArticleTypeList() {
      const res = await querySuggestType({
        pageIndex: 1,
        pageSize: 999,
        LevelId: this.levelId,
      });
      if (res.Type === 200) {
        this.typeList = res.Data.Data;

        // 延迟加载tabs，避免偏移
        setTimeout(() => {
          this.mountTabs = true;
        }, 200);
      }

      return res;
    },

    // 获取文章列表
    async getArticleList(typeId) {
      const res = await querySuggestInfo({
        levelId: this.levelId,
        pageIndex: 1,
        pageSize: 99,
        typeId,
      });
      if (res.Type === 200) {
        res.Data.Data.forEach((e, index) => {
          if (!e.ShowImg) {
            e.ShowImg = getMissionImageUrl(index);
          }
        });
        this.articleList = res.Data.Data;
      }

      return res;
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  overflow-y: scroll;

  .tabs-wrapper {
    padding: 0 32rpx;
    background-color: #f7f7f7;
  }

  .mission-item {
    min-height: 172rpx;
    background: #ffffff;
    border-radius: 16rpx;
    margin: 0 32rpx 24rpx;
    padding: 24rpx 22rpx;
    display: flex;
    justify-content: start;
    align-items: center;

    .mission-text {
      flex: 1;
      margin-left: 28rpx;
      font-weight: 500;
      font-size: 28rpx;
      color: #323233;
    }
  }
}

.op-50 {
  opacity: 0.5;
}
</style>
