<template>
  <view class="container">
    <view class="container-top">
      <view
        class="container-top-item"
        v-for="(item, index) in dayList"
        :key="item.Date"
      >
        <view class="container-top-item-week">
          {{ item.Week }}
        </view>
        <view
          :class="
            chooseDayIndex === index ? 'container-top-item-dayChoose' : ''
          "
          class="container-top-item-day"
          @click="onChooseDay(index)"
        >
          {{ index === 0 ? '今天' : item.DateLast }}
        </view>
        <view
          :class="
            onGetIsFull(item.Rows) ? '' : 'container-top-item-pointNoFullChoose'
          "
          class="container-top-item-pointFullChoose"
        ></view>
      </view>
    </view>
    <view class="container-time">
      <view
        :class="[
          chooseTimeIndex === index ? 'container-time-itemChoose' : '',
          onIsFull(index) ? 'container-time-itemFull' : '',
        ]"
        @click="onChooseTime(index)"
        class="container-time-item"
        v-for="(item, index) in showChooseTime"
        :key="item.Id"
      >
        {{ item.Time + `(余${item.Num - item.UsedNum})` }}
      </view>
    </view>
    <view class="btnButtomStyle" @click="onSureTime">确定</view>
  </view>
</template>

<script>
import { GetSchedulingPage, QueryTimeRangePage } from '@/api/appointment.js';
const dayjs = require('dayjs');
const localizedFormat = require('dayjs/plugin/localizedFormat');
const localeData = require('dayjs/plugin/localeData');
dayjs.extend(localizedFormat);
dayjs.extend(localeData);
dayjs.locale('zh-cn');
export default {
  data() {
    return {
      chooseTimeIndex: null,
      chooseDayIndex: 0,
      dayList: [],
      timeList: [],
      showChooseTime: [],
    };
  },
  async onLoad() {
    await this.onGetTimeList();
    this.onGetDayList();
  },
  methods: {
    onIsFull(index) {
      const item = this.showChooseTime[index];
      return item.Num <= item.UsedNum;
    },
    async onGetTimeList() {
      const res = await QueryTimeRangePage({
        PageIndex: 1,
        PageSize: 999,
        // OrgId: "bbdcbc14-290f-43f6-91d9-fd31529dbec3",
      });
      if (res.Type === 200) {
        res.Data.Data.forEach((s) => {
          s.StartTime = s.StartTime.split(':').slice(0, 2).join(':');
          s.EndTime = s.EndTime.split(':').slice(0, 2).join(':');
        });
        this.timeList = res.Data.Data;
      }
    },
    onGetIsFull(row) {
      let Num = 0;
      let UsedNum = 0;

      for (let obj of row) {
        Num += obj.Num;
        UsedNum += obj.UsedNum;
      }
      return Num === UsedNum;
    },
    async onGetDayList() {
      const data = {
        StartDate: this.$dateFormat(new Date(), 'YYYY-MM-DD 00:00:00'),
        EndDate: this.$dateFormat(new Date(), 'YYYY-MM-DD 00:00:00', false, {
          Count: 6,
          Type: 'days',
        }),
        States: [1, 2],
      };
      const chineseWeekdayMap = ['日', '一', '二', '三', '四', '五', '六'];
      const res = await GetSchedulingPage(data);
      if (res.Type === 200) {
        res.Data.forEach((element) => {
          element.Date = this.$dateFormat(element.Date, 'YYYY-MM-DD');
          element.DateLast = this.$dateFormat(element.Date, 'MM-DD');
          element.Week = chineseWeekdayMap[dayjs(element.Date).format('d')];
        });
        this.dayList = res.Data;
        this.changeDay(res.Data[0].Rows);
      }
    },
    changeDay(row) {
      row.forEach((s) => {
        this.timeList.forEach((v) => {
          if (v.Id === s.TimeRangeId) {
            s.Time = v.StartTime + '-' + v.EndTime;
          }
        });
      });
      console.log('row', row);
      this.showChooseTime = row;
    },
    onSureTime() {
      console.log('选中的时间段');
      let pages = getCurrentPages();
      const prePage = pages[pages.length - 2]; //上一个页面
      if (prePage.$vm.onSelectTime) {
        const item = this.showChooseTime[this.chooseTimeIndex];
        prePage.$vm.onSelectTime({
          ScheId: item.Id,
          ScheName:
            this.$dateFormat(item.Date, 'YYYY-MM-DD') + `(${item.Time})`,
        });
      }
      uni.navigateBack();
    },
    onChooseTime(index) {
      console.log('index', index);
      const item = this.showChooseTime[index];
      if (item.Num === item.UsedNum) {
        return;
      }
      this.chooseTimeIndex = index;
    },
    onChooseDay(index) {
      this.chooseDayIndex = index;
      const item = this.dayList[index].Rows;
      this.changeDay(item);
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  background: #eef0f3;
  &-time {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: wrap;
    &-item {
      width: 330rpx;
      height: 132rpx;
      background: #ffffff;
      border-radius: 16rpx;
      font-weight: 600;
      font-size: 28rpx;
      color: #29b7a3;
      line-height: 132rpx;
      text-align: center;
      margin-left: 26rpx;
      margin-top: 32rpx;
    }
    &-itemChoose {
      border: 2px solid #29b7a3;
    }
    &-itemFull {
      color: #cccccc;
    }
  }
  &-top {
    width: 100%;
    height: 172rpx;
    background: #ffffff;
    border-radius: 0rpx 0rpx 16rpx 16rpx;
    display: flex;
    align-items: center;
    justify-content: space-around;
    &-item {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      &-week {
        font-weight: 600;
        font-size: 24rpx;
        color: #b5b5b5;
      }
      &-day {
        font-weight: 600;
        font-size: 28rpx;
        color: #29b7a3;
        border-radius: 24rpx;
        width: 92rpx;
        height: 48rpx;
        line-height: 48rpx;
        text-align: center;
        margin: 16rpx 0;
      }
      &-dayChoose {
        border: 2rpx solid #29b7a3;
      }
      &-pointFullChoose {
        width: 12rpx;
        height: 12rpx;
        background: #d8d8d8;
        border-radius: 50%;
      }
      &-pointNoFullChoose {
        background: #29b7a3;
      }
      &-pointHaveCount {
        background: #29b7a3;
      }
    }
  }
}
</style>
