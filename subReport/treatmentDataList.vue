<template>
  <view class="container-box">
    <view
      class="container-box-data"
      v-if="option.flowType === 'Oa'"
      @click="handleDetailClick"
    >
      <text class="container-box-data-title">多功能骨关节理疗仪治疗数据</text>
      <u-divider />
      <view class="container-box-data-wapper">
        <view class="container-box-data-wapper-box">
          <text class="container-box-data-wapper-box-title">治疗次数</text>
          <view
            style="
              display: flex;
              align-items: baseline;
              justify-content: center;
            "
          >
            <text class="container-box-data-wapper-box-number">{{
              treatmentData.Count
            }}</text>
            <text class="container-box-data-wapper-box-count">次</text>
          </view>
        </view>
        <view class="container-box-data-wapper-box">
          <text class="container-box-data-wapper-box-title">总时长</text>
          <view
            style="
              display: flex;
              align-items: baseline;
              justify-content: center;
            "
          >
            <text class="container-box-data-wapper-box-number">{{
              (treatmentData.Total / 60).toFixed(1)
            }}</text>
            <text class="container-box-data-wapper-box-count">分钟</text>
          </view>
        </view>
        <view class="container-box-data-wapper-box">
          <text class="container-box-data-wapper-box-title">平均时长</text>
          <view
            style="
              display: flex;
              align-items: baseline;
              justify-content: center;
            "
          >
            <text class="container-box-data-wapper-box-number">{{
              (treatmentData.Avg / 60).toFixed(1)
            }}</text>
            <text class="container-box-data-wapper-box-count">分钟/次</text>
          </view>
        </view>
      </view>
    </view>

    <view
      class="container-box-charts"
      v-for="(item, index) in newCharts"
      :key="index"
    >
      <text class="container-box-charts-text">
        {{ item.type }}
      </text>
      <qiun-data-charts
        :onzoom="true"
        :tapLegend="false"
        type="area"
        :ontouch="true"
        :canvas2d="true"
        :opts="opts"
        :chartData="item.data"
      />
    </view>
    <u-empty
      mode="data"
      icon="http://cdn.uviewui.com/uview/empty/data.png"
      text="暂无数据"
      v-if="option.flowType !== 'Oa' && !newCharts.length"
    ></u-empty>
  </view>
</template>

<script>
const app = getApp();
import { statisticsReport } from '@/api/training.js';
import { getSigns } from '@/api/record.js';
import { ItemGroupBy } from '@/utils/utils.js';
import dayjs from 'dayjs';
export default {
  data() {
    return {
      newCharts: [],
      treatmentData: {
        Avg: 0,
        Count: 0,
        Total: 0,
      },
      option: {},
      chartData: {
        categories: ['2018', '2019', '2020', '2021', '2022', '2023'],
        series: [
          {
            name: '成交量A',
            data: [35, 8, 25, 37, 4, 20],
          },
        ],
      },
      opts: {
        color: ['#29B7A3'],
        padding: [15, 0, 0, 0],
        enableScroll: false,
        legend: {},
        xAxis: {
          disableGrid: true,
        },
        yAxis: {
          gridType: 'dash',
          dashLength: 2,
        },
        extra: {
          area: {
            type: 'straight',
            opacity: 0.2,
            addLine: true,
            width: 2,
            gradient: true,
            activeType: 'hollow',
          },
        },
      },
    };
  },
  async onLoad(option) {
    await app.isLaunchFinish();
    this.option = option;
    // 获取用户的治疗数据
    await this.handleGetUserTreatmentData(option.visitActionId);
  },
  methods: {
    handleDetailClick() {
      uni.navigateTo({
        url: './TreatmentDataDetail?visitActionId=' + this.option.visitActionId,
      });
    },
    async handleGetUserTreatmentData(visitId) {
      const res = await statisticsReport({
        visitId,
        loadDetail: false,
      });
      if (res.Type === 200 && res.Data) {
        this.treatmentData.Avg = res.Data.Report.Data.Avg;
        this.treatmentData.Count = res.Data.Report.Data.Count;
        this.treatmentData.Total = res.Data.Report.Data.Total;
        const startTime = this.$dateFormat(
          res.Data.StartTime,
          'YYYY-MM-DD HH:mm:ss'
        );
        const endTime = this.$dateFormat(
          res.Data.EndTime,
          'YYYY-MM-DD HH:mm:ss'
        );
        this.handleGetChartsListData(startTime, endTime);
      }
    },
    async handleGetChartsListData(startTime, endTime) {
      uni.showLoading({
        title: '加载中',
      });
      try {
        const res = await getSigns({
          UserId: app.globalData.userInfo.Id,
          Types: [4, 5, 6], //对应 疼痛/生活质量/焦虑抑郁  三种类型
          BeginTime: startTime || '2023-01-01',
          EndTime:
            endTime || this.$dateFormat(new Date(), 'YYYY-MM-DD 23:59:59'),
          PageIndex: 1,
          PageSize: 9999,
        });
        if (res.Type === 200) {
          res.Data.Data.forEach((s) => {
            if (s.Data) {
              s.Data = JSON.parse(s.Data).Value;
            }
          });
          this.handleProcssChartsData(res.Data.Data);
        }
      } catch (e) {
        uni.showModal({
          title: '提示',
          content: '初始化图表失败',
          showCancel: false,
        });
        this.$log.error(this.$envVersion, '初始化图表失败', JSON.stringify(e));
      } finally {
        uni.hideLoading();
      }
    },
    handleProcssChartsData(list) {
      const filterList = this.handleFilterLastTime(list);
      filterList.forEach((item) => {
        item.SignTime = dayjs(item.SignTime).format('YYYY-MM-DD');
      });
      const result = ItemGroupBy(filterList, 'Type', ['SignTime', 'Data']);
      this.handleSetParamsToCharts(result);
    },
    // 只保留一天中 最后一次的填写
    handleFilterLastTime(list) {
      const map = new Map();
      list.forEach((item) => {
        const date = dayjs(item.SignTime).format('YYYY-MM-DD'); // 取日期部分
        const type = item.Type; // 取Type
        const key = `${date}_${type}`; // 日期+类型作为唯一key
        if (!map.has(key)) {
          map.set(key, item);
        } else {
          const existing = map.get(key);
          if (dayjs(item.SignTime).isAfter(dayjs(existing.SignTime))) {
            map.set(key, item);
          }
        }
      });
      return Array.from(map.values());
    },
    handleSetParamsToCharts(list) {
      const newCharts = list.map((s) => {
        let name =
          s.type === 4 ? '疼痛' : s.type === 5 ? '生活质量' : '焦虑抑郁';
        const chartData = {
          categories: s.data.map((v) => v.SignTime),
          series: [
            {
              name,
              data: s.data.map((v) => v.Data),
            },
          ],
        };
        return {
          type: name,
          data: chartData,
        };
      });
      console.log('newCharts', newCharts);
      this.newCharts = newCharts;
    },
  },
};
</script>

<style scoped lang="scss">
.container-box {
  padding: 32rpx;

  &-charts {
    margin-top: 32rpx;
    background: #ffffff;
    box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);
    border-radius: 16rpx;
    padding: 34rpx;

    &-text {
      font-weight: 400;
      font-size: 30rpx;
      color: #333333;
      margin-bottom: 28rpx;
    }
  }

  &-data {
    background: #ffffff;
    box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);
    border-radius: 16rpx;
    padding: 34rpx;

    &-title {
      font-weight: 400;
      font-size: 30rpx;
      color: #333333;
      line-height: 34rpx;
      margin-bottom: 28rpx;
    }

    &-wapper {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 70rpx;

      &-box {
        display: flex;
        flex-direction: column;
        align-items: center;

        &-title {
          font-weight: 400;
          font-size: 22rpx;
          color: #666666;
          line-height: 34rpx;
          margin-bottom: 16rpx;
        }

        &-number {
          font-weight: 500;
          font-size: 38rpx;
          line-height: 34rpx;
          color: #29b7a3;
        }

        &-count {
          font-weight: 400;
          font-size: 22rpx;
          color: #29b7a3;
          line-height: 34rpx;
          display: block;
        }
      }
    }
  }
}
</style>
