<template>
  <view class="container">
    <view class="container-data" @click="handleDetailClick">
      <text class="container-data-title">多功能骨关节理疗仪治疗数据</text>
      <u-divider />
      <view class="container-data-wapper">
        <view class="container-data-wapper-box">
          <text class="container-data-wapper-box-title">治疗次数</text>
          <text class="container-data-wapper-box-count">3次</text>
        </view>
        <view class="container-data-wapper-box">
          <text class="container-data-wapper-box-title">总时长</text>
          <text class="container-data-wapper-box-count">52分钟</text>
        </view>
        <view class="container-data-wapper-box">
          <text class="container-data-wapper-box-title">平均时长</text>
          <text class="container-data-wapper-box-count">18分钟/次</text>
        </view>
      </view>
    </view>
    <view class="container-chart">
      <text class="container-chart-title">疼痛等级</text>
      <qiun-data-charts type="area" :opts="opts" :chartData="chartData" />
    </view>
    <view class="container-chart">
      <text class="container-chart-title">生活质量</text>
      <qiun-data-charts type="area" :opts="opts" :chartData="chartData" />
    </view>
    <view class="container-chart">
      <text class="container-chart-title">焦虑或抑郁程度</text>
      <qiun-data-charts type="area" :opts="opts" :chartData="chartData" />
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      opts: {
        color: [
          '#1890FF',
          '#91CB74',
          '#FAC858',
          '#EE6666',
          '#73C0DE',
          '#3CA272',
          '#FC8452',
          '#9A60B4',
          '#ea7ccc',
        ],
        padding: [15, 0, 0, 0],
        enableScroll: false,
        legend: {},
        xAxis: {
          disableGrid: true,
        },
        yAxis: {
          gridType: 'dash',
          dashLength: 2,
        },
        extra: {
          area: {
            type: 'curve',
            opacity: 0.2,
            addLine: true,
            width: 2,
            gradient: true,
            activeType: 'hollow',
          },
        },
      },
      chartData: {
        categories: ['2018', '2019', '2020', '2021', '2022', '2023'],
        series: [
          {
            name: '成交量A',
            data: [35, 8, 25, 37, 4, 20],
          },
        ],
      },
    };
  },
  onLoad() {},
  methods: {
    handleDetailClick() {
      uni.navigateTo({
        url: './TreatmentDataDetail',
      });
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  padding: 24rpx;
  background: #f7f6f6;

  &-data {
    background: #ffffff;
    box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);
    border-radius: 16rpx;
    padding: 34rpx;

    &-title {
      font-weight: 400;
      font-size: 30rpx;
      color: #333333;
      line-height: 34rpx;
      margin-bottom: 28rpx;
    }

    &-wapper {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 70rpx;

      &-box {
        display: flex;
        flex-direction: column;
        align-items: center;

        &-title {
          font-weight: 400;
          font-size: 22rpx;
          color: #666666;
          line-height: 34rpx;
          margin-bottom: 16rpx;
        }

        &-count {
          font-weight: 400;
          font-size: 22rpx;
          color: #29b7a3;
          line-height: 34rpx;
          display: block;
        }
      }
    }
  }

  &-chart {
    background: #ffffff;
    box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);
    border-radius: 16rpx;
    padding: 24rpx;
    margin-top: 24rpx;

    &-title {
      margin-bottom: 24rpx;
    }
  }
}
</style>
