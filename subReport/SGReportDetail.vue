<template>
  <view class="container">
    <view class="container-basicInformation">
      <view class="container-basicInformation-top display-style">
        <text class="fontBoldSize">基本信息</text>
        <text style="color: #999999">{{
          formatTime(reportData.CheckDate)
        }}</text>
      </view>
      <view class="container-basicInformation-user">
        <div>
          <text style="font-size: 28rpx; font-weight: 400; color: #666666"
            ><text style="font-weight: 600; color: #333333">姓名：</text
            >{{ reportData.Name }}</text
          >
          <br />
          <text
            style="
              font-size: 28rpx;
              font-weight: 400;
              color: #666666;
              margin-top: 20rpx;
            "
            ><text style="font-weight: 600; color: #333333">学校：</text
            >{{ reportData.SchoolName }}</text
          >
        </div>
        <span
          class="container-basicInformation-user-result"
          @click="onToSeeReportList"
          >历次结果</span
        >
      </view>
    </view>
    <view class="container-basicInformation container-fullWidth">
      <view class="container-fullWidth-top">
        <text class="fontBoldSize">视力屈光度检查结果</text>
      </view>
      <view class="container-basicInformation-table">
        <view class="container-basicInformation-table-top display-style">
          <view class="fontBoldSize" style="text-align: center; width: 33%">
            项目名称
          </view>
          <view class="fontBoldSize" style="text-align: center; width: 33%">
            右眼
          </view>
          <view class="fontBoldSize" style="text-align: center; width: 33%">
            左眼
          </view>
        </view>
        <view
          class="container-basicInformation-table-top container-basicInformation-table-mid display-style"
        >
          <view style="text-align: center; width: 33%"> 裸眼视力 </view>
          <view style="text-align: center; width: 33%">
            {{ reportData.NakedDegreeOd }}
          </view>
          <view style="text-align: center; width: 33%">
            {{ reportData.NakedDegreeOs }}
          </view>
        </view>
        <view
          class="container-basicInformation-table-top container-basicInformation-table-mid display-style"
        >
          <view style="text-align: center; width: 33%"> 球镜（S） </view>
          <view style="text-align: center; width: 33%">
            {{ reportData.SphOd }}
            <view v-if="reportData.SphOd > 0" style="color: #29b7a3"
              >(远视{{ reportData.SphOd * 100 }}度)</view
            >
            <view v-if="reportData.SphOd < 0" style="color: #29b7a3"
              >(近视{{ Math.abs(reportData.SphOd) * 100 }}度)</view
            >
          </view>
          <view style="text-align: center; width: 33%">
            {{ reportData.SphOs }}
            <view v-if="reportData.SphOs > 0" style="color: #29b7a3"
              >(远视{{ reportData.SphOs * 100 }}度)</view
            >
            <view v-if="reportData.SphOs < 0" style="color: #29b7a3"
              >(近视{{ Math.abs(reportData.SphOs) * 100 }}度)</view
            >
          </view>
        </view>
        <view
          class="container-basicInformation-table-top container-basicInformation-table-mid display-style"
        >
          <view style="text-align: center; width: 33%"> 柱镜（C） </view>
          <view style="text-align: center; width: 33%">
            {{ reportData.CylOd }}
            <view style="color: #29b7a3" v-if="reportData.CylOd !== 0"
              >(散光{{ Math.abs(reportData.CylOd) * 100 }}度)</view
            >
          </view>
          <view style="text-align: center; width: 33%">
            {{ reportData.CylOs }}
            <view style="color: #29b7a3" v-if="reportData.CylOs !== 0"
              >(散光{{ Math.abs(reportData.CylOs) * 100 }}度)</view
            >
          </view>
        </view>
        <view
          class="container-basicInformation-table-top container-basicInformation-table-mid display-style"
        >
          <view style="text-align: center; width: 33%"> 轴位（A） </view>
          <view style="text-align: center; width: 33%">
            {{ reportData.AxleOd }}
          </view>
          <view style="text-align: center; width: 33%">
            {{ reportData.AxleOs }}
          </view>
        </view>
        <view
          class="container-basicInformation-table-top container-basicInformation-table-mid display-style"
        >
          <view style="text-align: center; width: 33%"> 等效球镜度（SE） </view>
          <view style="text-align: center; width: 33%">
            {{ reportData.DXQJDY }}
          </view>
          <view style="text-align: center; width: 33%">
            {{ reportData.DXQJDZ }}
          </view>
        </view>
      </view>
      <view class="container-fullWidth-top">
        <text class="remarkStyle"
          >注：以上数据仅为筛查数据，不作为诊断依据</text
        >
      </view>
    </view>
    <view class="container-basicInformation container-fullWidth">
      <view class="container-fullWidth-top">
        <text class="fontBoldSize">综合评估（视力+屈光）</text>
      </view>
      <view class="container-basicInformation-box">
        <view
          >右眼：{{ reportInfo.rightLyslRes }}。{{
            reportInfo.rightLyslResSu
          }}</view
        >
        <view style="margin-top: 20rpx"
          >左眼：{{ reportInfo.leftLyslRes }}。{{
            reportInfo.leftLyslResSu
          }}</view
        >
      </view>
    </view>
    <view class="container-basicInformation container-fullWidth">
      <view class="container-fullWidth-top" style="margin-bottom: 32rpx">
        <text class="fontBoldSize">视力屈光变化趋势图</text>
      </view>
      <view
        class="container-basicInformation-box container-basicInformation-tabs"
      >
        <u-tabs
          :activeStyle="{ fontWeight: 'bold', transform: 'scale(1.05)' }"
          :list="list"
          @click="tabClick"
          lineColor="#1BBC9C"
        ></u-tabs>
        <view class="charts-box">
          <qiun-data-charts
            :canvas2d="true"
            canvasId="uahsduaduhaufhabhfuaghsfu31231"
            type="column"
            :opts="opts"
            :chartData="chartData"
          />
        </view>
      </view>
    </view>
    <view class="container-basicInformation container-fullWidth">
      <view class="container-fullWidth-top">
        <text class="fontBoldSize">参考标准</text>
      </view>
      <view class="container-basicInformation-box">
        <view class="container-basicInformation-box-title"
          >视力不良判断标准</view
        >
        <view class="container-basicInformation-box-text"
          >1.轻度视力不良：单眼裸眼视力 = 4.9</view
        >
        <view class="container-basicInformation-box-text"
          >2.中度视力不良：4.6 ≤ 单眼裸眼视力 ≤ 4.8</view
        >
        <view class="container-basicInformation-box-text"
          >3.重度视力不良：单眼裸眼视力 ≤ 4.5</view
        >
        <view class="container-basicInformation-box-title">近视判断标准</view>
        <view class="container-basicInformation-box-text"
          >1.裸眼视力小于5.0, 等效球镜度 ≤ -0.50D 为筛查性近视</view
        >
        <view class="container-basicInformation-box-text"
          >2.夜戴角膜塑形镜(OK镜)记为近视</view
        >
        <view class="container-basicInformation-box-text"
          >3.《近视防控适宜技术指南》</view
        >
        <view class="container-basicInformation-box-title"
          >远视储备判断标准</view
        >
        <view class="container-basicInformation-box-text"
          >10岁及以下儿童生理情况下眼屈光为远视状态，并随年龄增长向正视化转变。6岁及以下儿童，眼屈光在100-300度之间
          (等效球镜为1.00D-3.00D) ; 7-10岁儿童，眼屈光在25-300度之间 (等效球镜为
          0.25D3.00D)</view
        >
      </view>
      <view class="container-fullWidth-top">
        <text class="remarkStyle"
          >注：视力范围为3.3～5.3，数值越大视力越好，5.0以上属于好视力。</text
        >
      </view>
    </view>
    <u-button
      v-if="!reportData.CheckUserId"
      @click="onSureReslut"
      text="我已阅读并知晓结果"
      shape="circle"
      customStyle="color: #ffffff;width:90%;bottom: 30px;position: fixed; left: 50%;transform: translateX(-50%);z-index:999;background: #14b593"
    ></u-button>
    <u-button
      v-else
      @click="onToAppointment"
      text="复查预约"
      shape="circle"
      customStyle="color: #ffffff;width:90%;bottom: 30px;position: fixed; left: 50%;transform: translateX(-50%);z-index:999;background: #14b593"
    ></u-button>
    <u-gap height="80" bgColor="#F7F7F7"></u-gap>
    <u-overlay :show="show" @click="show = false">
      <view class="overlaybox">
        <view class="overlaybox-mid">
          <text class="overlaybox-mid-text"
            >温馨提示：在专业眼科医疗机构定期复查并建立屈光发育档案是预防近视发生、发展的最有效手段。</text
          >
          <view class="overlaybox-mid-box">
            <view class="overlaybox-mid-box-button"> 取消 </view>
            <view
              class="overlaybox-mid-box-button overlaybox-mid-box-button1"
              @click="onToAppointmentDetail"
            >
              前往预约
            </view>
          </view>
        </view>
      </view>
    </u-overlay>
  </view>
</template>

<script>
const app = getApp();
import {
  QPGetReportById,
  QPGetReportPage,
  SetCheckReportUser,
} from '@/api/supplier.js';

import { querySuggestLevel } from '@/api/content.js';

export default {
  data() {
    return {
      show: false,
      reportInfo: {
        leftLyslRes: '',
        rightLyslRes: '',
      },
      reportList: [],
      reportData: {},
      list: [
        {
          name: '裸眼视力',
        },
        {
          name: '远/近视(球镜)',
        },
        {
          name: '散光(柱镜)',
        },
      ],
      chartData: {},
      opts: {
        color: ['#6EE972', '#6EE1E9'],
        padding: [15, 15, 0, 5],
        enableScroll: false,
        legend: {},
        xAxis: {
          // disableGrid: true,
        },
        yAxis: {
          data: [
            {
              min: 0,
              max: 6.0,
            },
          ],
          splitNumber: 6,
        },
        extra: {
          column: {
            type: 'group',
            width: 30,
            activeBgColor: '#000000',
            activeBgOpacity: 0.08,
            linearType: 'custom',
            seriesGap: 5,
            linearOpacity: 0.5,
            barBorderCircle: true,
            customColor: ['#6EE972', '#6EE1E9'],
          },
        },
      },
      series: [],
    };
  },
  onLoad({ Id }) {
    this.getReportDetail(Id);
    this.getReportList();
  },
  methods: {
    onToAppointmentDetail() {
      const params = {
        SchoolName: this.reportData.SchoolName,
        Name: this.reportData.Name,
        Phone: app.globalData.userInfo.PhoneNumber,
        IdCard: this.reportData.IdCard,
        BrithDay: this.$dateFormat(this.reportData.Birthday, 'YYYY-MM-DD'),
        Sex: this.reportData.Sex,
      };
      uni.navigateTo({
        url:
          './SGAppointment?params=' +
          encodeURIComponent(JSON.stringify(params)),
      });
    },
    onToAppointment() {
      // this.show = true
      this.onToAppointmentDetail();
    },
    onSureReslut() {
      SetCheckReportUser({
        UserId: getApp().globalData.userInfo.Id,
        DataId: this.reportData.Id,
      })
        .then((res) => {
          if (res.Type !== 200) {
            uni.showToast({
              title: res.Content,
              icon: 'none',
            });
            return;
          }
          this.reportData.CheckUserId = getApp().globalData.userInfo.Id;
          this.show = true;
        })
        .catch((err) => {
          uni.showToast({
            title: err.Content,
            icon: 'error',
          });
        });
    },
    onToSeeReportList() {
      const pages = this.$u.pages();
      console.log('pages', pages);
      if (pages[pages.length - 2].route === 'subReport/SGReportList') {
        uni.navigateBack();
        return;
      }
      uni.navigateTo({
        url: './SGReportList',
      });
    },
    async getTageList() {
      const res = await querySuggestLevel({
        pageIndex: 1,
        pageSize: 999,
        type: 2,
      });
      if (res.Type === 200) {
        this.judgementStandard(res.Data.Data);
      }
    },
    judgementStandard(List) {
      // 获取左右眼结论、建议
      const leftLyslData = this.getStandard(
        this.reportData?.NakedDegreeOs,
        this.reportData?.DXQJDZ,
        List
      );
      this.reportInfo.leftLyslRes = leftLyslData.standard;
      this.reportInfo.leftLyslResSu = leftLyslData.suggestion;
      const rightLyslData = this.getStandard(
        this.reportData?.NakedDegreeOd,
        this.reportData?.DXQJDY,
        List
      );
      this.reportInfo.rightLyslRes = rightLyslData.standard;
      this.reportInfo.rightLyslResSu = rightLyslData.suggestion;
    },
    getStandard(lysl, qjd, List) {
      if (typeof lysl !== 'number')
        return {
          standard: '暂无结论',
          suggestion: '暂无建议',
        };
      if (typeof qjd !== 'number')
        return {
          standard: '暂无结论',
          suggestion: '暂无建议',
        };
      const isBetweenLysl = List.filter(
        (v) =>
          lysl >= v.SuggestRule.LYSLZXZ &&
          lysl <= v.SuggestRule.LYSLZDZ &&
          qjd >= v.SuggestRule.DXQJDZXZ &&
          qjd <= v.SuggestRule.DXQJDZDZ
      );
      // const isBetweenQjd = List.filter(v => (qjd >= v.SuggestRule.DXQJDZXZ && qjd <= v.SuggestRule
      // 	.DXQJDZDZ))
      if (isBetweenLysl.length > 0) {
        return {
          standard: isBetweenLysl[0].Name,
          suggestion: isBetweenLysl[0].SuggestContent,
        };
      } else {
        return {
          standard: '暂无结论',
          suggestion: '暂无建议',
        };
      }
    },
    async getReportList() {
      // 获取最近三条报告信息
      const userInfo = uni.getStorageSync('SGUser');
      const req = {
        ...userInfo,
        pageIndex: 1,
        pageSize: 3,
      };
      const res = await QPGetReportPage(req);
      if (res.Type === 200) {
        this.reportList = res.Data.Data;
        this.getChartData('NakedDegreeOd', 'NakedDegreeOs', '裸眼视力');
      }
    },
    tabClick(item) {
      console.log('item', item);
      if (item.index === 0) {
        this.getChartData('NakedDegreeOd', 'NakedDegreeOs', '裸眼视力');
        this.opts.yAxis.data = [
          {
            min: 0,
            max: 6.0,
          },
        ];
        this.opts.yAxis.splitNumber = 6;
      } else if (item.index === 1) {
        const [min, max] = this.getChartData('SphOd', 'SphOs', '球镜');
        console.log(min, max);
        this.opts.yAxis.data = [
          {
            min: -10,
            max: 10,
          },
        ];
        this.opts.yAxis.splitNumber = 4;
      } else if (item.index === 2) {
        const [min, max] = this.getChartData('CylOd', 'CylOs', '柱镜');
        this.opts.yAxis.data = [
          {
            // min: -16,
            // max: 0
            min: 0,
            max: -10,
          },
        ];
        this.opts.yAxis.splitNumber = 5;
      }
    },
    getChartData(r, l, name) {
      const categories = this.reportList.map((v) =>
        this.$dateFormat(v.CheckDate, 'YYYY-MM-DD')
      );
      const rData = this.reportList.map((v) => v[r]);
      const lData = this.reportList.map((v) => v[l]);
      this.series = [
        {
          name: name + '右',
          data: rData,
        },
        {
          name: name + '左',
          data: lData,
        },
      ];
      let res = {
        categories,
        series: this.series,
      };
      this.chartData = JSON.parse(JSON.stringify(res));
      return [
        Math.round(Math.min(...[...rData, ...lData])),
        Math.round(Math.max(...[...rData, ...lData])),
      ];
    },
    formatTime(date) {
      if (!date) return '';
      return this.$dateFormat(date, 'YYYY-MM-DD');
    },
    async getReportDetail(id) {
      const res = await QPGetReportById({
        id,
      });
      if (res.Type === 200) {
        this.reportData = res.Data;
        this.getTageList();
      }
    },
  },
};
</script>

<style scoped lang="scss">
.overlaybox {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;

  &-mid {
    position: relative;
    width: 622rpx;
    height: 738rpx;
    background-image: url('/subReport/static/bgSG.png');
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center center;

    &-text {
      font-weight: 600;
      font-size: 28rpx;
      color: #29b7a3;
      position: absolute;
      top: 44rpx;
      left: 32rpx;
      right: 32rpx;
    }

    &-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: absolute;
      bottom: 32rpx;
      left: 32rpx;
      right: 32rpx;

      &-button {
        width: 268rpx;
        height: 80rpx;
        background: #ffffff;
        box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);
        border-radius: 40rpx;
        border: 2rpx solid #cccccc;
        text-align: center;
        line-height: 80rpx;
        font-weight: 600;
        font-size: 28rpx;
        color: #676767;
      }

      &-button1 {
        background: #29b7a3;
        border: none !important;
        color: #ffffff;
      }
    }
  }
}

.container {
  position: relative;

  .charts-box {
    width: 100%;
    height: 400rpx;
  }

  .fontBoldSize {
    font-size: 34rpx;
    font-weight: 600;
    color: #333333;
  }

  .remarkStyle {
    font-size: 24rpx;
    font-weight: 400;
    color: #979797;
  }

  &-sure {
  }

  &-basicInformation {
    padding: 32rpx 32rpx 0 32rpx;

    &-user {
      padding: 32rpx;
      background: #ffffff;
      box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.1);
      border-radius: 16rpx;
      margin-top: 24rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;

      &-result {
        color: #29b7a3;
      }
    }

    &-table {
      &-top {
        margin-top: 32rpx;
        background: linear-gradient(180deg, #e5fff7 0%, #ffffff 100%);
        height: 106rpx;
      }

      &-mid {
        margin-top: 0;
        background: white;
      }
    }

    &-box {
      margin-top: 24rpx;
      background: linear-gradient(180deg, #e5fff7 0%, #ffffff 100%);
      box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.1);
      border-radius: 16rpx;
      padding: 32rpx;

      &-title {
        font-size: 28rpx;
        font-weight: 600;
        color: #333333;
        margin-bottom: 8rpx;
      }

      &-text {
        font-size: 24rpx;
        font-weight: 600;
        color: #9a9a9a;
        margin-bottom: 8rpx;
      }
    }

    &-tabs {
      margin-top: 0;
      padding-top: 0;
    }
  }

  &-fullWidth {
    padding: 0 !important;

    &-top {
      padding: 32rpx 32rpx 0 32rpx;
    }
  }
}
</style>
