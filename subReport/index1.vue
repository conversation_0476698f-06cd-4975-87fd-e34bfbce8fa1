<template>
  <view class="container">
    <image src="static/bg4.png" class="container-img"> </image>
    <text class="container-label"> 关注青少年身体健康，呵护孩子茁壮成长 </text>
    <view class="container-box">
      <view
        v-for="(i, index) in list"
        :key="index"
        class="container-box-each"
        :style="{ background: i.bgColor }"
        @click="onClick(i)"
      >
        <image
          :src="`static/logo${index + 3}.png`"
          style="width: 96rpx; height: 96rpx; margin-right: 32rpx"
        ></image>
        <text class="container-box-each-label">{{ i.lable }}</text>
        <image
          :src="`static/jt${index + 3}.png`"
          style="width: 40rpx; height: 40rpx; margin-left: auto"
        ></image>
      </view>
    </view>
    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script>
export default {
  data() {
    return {
      list: [
        // {
        // 	lable: '呼吸康复报告',
        // 	bgColor: 'linear-gradient(316deg, #E8FFFA 0%, #FFFFFF 100%)',
        // 	url: '/subReport/SeeReport'
        // },
        // {
        // 	lable: '易脑复苏VR康复',
        // 	bgColor: 'linear-gradient(316deg, #FEF8E8 0%, #FFFFFF 100%)',
        // 	url: '/subReport/VR'
        // },
        {
          lable: '脊柱筛查',
          bgColor: 'linear-gradient(316deg, #FFE9F3 0%, #FFFFFF 100%)',
          url: '/subReport/SpineScreening',
        },
        {
          lable: '视光筛查',
          bgColor: 'linear-gradient(316deg, #E8FFFA 0%, #FFFFFF 100%)',
          url: '/subReport/UserLogin',
        },
        // {
        // 	lable: '其他',
        // 	bgColor: 'linear-gradient(316deg, #E4F5FF 0%, #FFFFFF 100%)'
        // }
      ],
    };
  },
  onLoad() {},
  methods: {
    onClick(e) {
      if (e.url) {
        uni.navigateTo({
          url: e.url,
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  position: relative;

  &-img {
    width: 100%;
    height: 100vh;
    z-index: -1;
  }

  &-label {
    position: absolute;
    top: 152rpx;
    left: 32rpx;
    font-size: 22rpx;
    font-weight: 600;
    color: #ffffff;
  }

  &-box {
    position: absolute;
    top: 248rpx;
    left: 50%;
    transform: translateX(-50%);
    width: calc(100% - 64rpx);

    &-each {
      width: 100%;
      height: 172rpx;
      box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.1);
      border-radius: 24rpx;
      padding: 38rpx 48rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      z-index: 9999;
      margin-bottom: 24rpx;
    }
  }
}
</style>
