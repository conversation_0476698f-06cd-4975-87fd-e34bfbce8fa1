<template>
  <view class="container">
    <view class="container-user">
      <view class="display-style1">
        <u-icon name="account-fill" size="22"></u-icon>
        <span style="margin: 0 4rpx; font-size: 32rpx">{{
          userInfo.name
        }}</span>
        <span>({{ onGetIdCard() }})</span>
      </view>
      <span style="color: #29b7a3" @click="changeUser">重新查询</span>
    </view>
    <!-- 	<view class="container-top">
			<view class="container-top-title" @click="onToSeeReport">
				<view class="container-top-title-left">
					<image src="static/logo4.png" style="width: 96rpx;height: 96rpx;"></image>
					<view class="container-top-title-left-top">
						<p style="font-size: 32rpx;color: #333333;font-weight: 600;margin-bottom: 16rpx;">视光筛查报告</p>
						<p style="font-size: 24rpx;color: #FF5656;font-weight: 600;">
							左眼：{{reportInfo.leftLyslRes || '暂无数据'}}, 右眼：{{reportInfo.rightLyslRes || '暂无数据'}}
						</p>
					</view>
				</view>
				<image src="static/jt999.png" style="width: 40rpx;height: 40rpx;"></image>
			</view>
			<u-divider :text="false"></u-divider>
			<text class="container-top-dis">建议：{{showInfo.SuggestContent || '暂无建议'}}</text>
		</view> -->
    <view class="container-result" @click="onToSeeReport">
      <view class="container-result-top">
        <span class="container-result-top-left">视光筛查</span>
        <view class="container-result-top-right"> 电子报告 </view>
        <image src="./static/eye.png" class="container-result-top-img"></image>
      </view>
      <view class="container-result-mid">
        <view class="container-result-mid-top">
          <image
            src="./static/sgfile.png"
            class="container-result-mid-top-img"
          ></image>
          <span class="container-result-mid-top-end">检查结果</span>
        </view>
        <view class="container-result-mid-eye">
          <view class="container-result-mid-eye-box">
            <image
              src="./static/leftEye.png"
              class="container-result-mid-eye-box-eye"
            ></image>
            <span class="container-result-mid-eye-box-text"
              >左眼：{{ reportInfo.leftLyslRes || '暂无数据' }}</span
            >
          </view>
          <view class="container-result-mid-eye-box">
            <image
              src="./static/rightEye.png"
              class="container-result-mid-eye-box-eye"
            ></image>
            <span class="container-result-mid-eye-box-text"
              >右眼：{{ reportInfo.rightLyslRes || '暂无数据' }}</span
            >
          </view>
        </view>
      </view>
      <view class="container-result-bom">
        <view class="container-result-bom-text">
          1、左眼裸眼视力（{{
            reportInfo.NakedDegreeOs || '暂无数据'
          }}），右眼裸眼视力（{{ reportInfo.NakedDegreeOd || '暂无数据' }}）
        </view>
        <view
          class="container-result-bom-text"
          style="margin-bottom: 24rpx !important"
        >
          2、左眼等效球镜（{{
            reportInfo.DXQJDZ || '暂无数据'
          }}），右眼等效球镜（{{ reportInfo.DXQJDY || '暂无数据' }}）
        </view>
        <u-divider></u-divider>
        <text class="container-result-bom-dis"
          >建议：{{ showInfo.SuggestContent || '暂无建议' }}</text
        >
      </view>
    </view>
    <image
      v-if="orgInfo && orgInfo.CoverImageUrl"
      :src="orgInfo.CoverImageUrl"
      style="margin: 20rpx 0; width: 100%; height: 278rpx; border-radius: 24rpx"
      @click="onSeeOrgDetail"
    ></image>
    <u-image
      :customStyle="{
        'margin': '20rpx 0',
        'width': '100%',
        'height': '278rpx',
        'border-radius': '24rpx',
      }"
      v-else
    >
      <view slot="error" style="font-size: 24rpx">暂无推荐</view>
    </u-image>
    <view class="container-article">
      <view class="container-article-title">
        <text style="font-size: 32rpx; font-weight: 600; color: #333333"
          >相关文章</text
        >
        <!-- <text style="font-size: 28rpx;font-weight: 600;color: #999999;" @click="onLookMoreXJ">更多</text> -->
      </view>
      <u-tabs
        :activeStyle="{
          color: '#333333',
          fontWeight: 'bold',
          transform: 'scale(1.05)',
          fontSize: '28rpx',
        }"
        keyName="Name"
        lineColor="#1BBC9C"
        :list="list"
        @click="onChangeList"
        v-if="list.length > 0"
      ></u-tabs>
      <view class="container-article-list" v-if="articleList.length > 0">
        <view
          class="container-article-list-each"
          v-for="i in articleList"
          :key="i.Id"
          @click="onLookArticle(i)"
        >
          <image :src="i.ShowImg" style="width: 124rpx; height: 124rpx"></image>
          <view class="container-article-list-each-right">
            <span class="container-article-list-each-right-test">{{
              i.Type
            }}</span>
            <span>{{ i.Title }}</span>
          </view>
        </view>
      </view>
      <u-empty
        v-if="articleList.length === 0"
        mode="data"
        icon="http://cdn.uviewui.com/uview/empty/data.png"
        text="没有推荐的内容"
      ></u-empty>
    </view>
    <u-gap height="84" bgColor="#F8F7F7"></u-gap>
    <view class="assistanceImage" @click="onCustomerService">
      <image
        src="/subReport/static/zxkf.png"
        style="width: 146rpx; height: 168rpx"
      ></image>
      <!-- <view class="assistanceImage-text">
				咨询客服
			</view> -->
    </view>
    <view class="container-bottom">
      <!-- <view class="container-bottom-each" @click="onCustomerService">
				咨询客服
			</view> -->
      <view class="container-bottom-each" @click="onToAppointment">
        复查预约
      </view>
      <view
        class="container-bottom-each container-bottom-other"
        @click="onSeeDocs"
      >
        问医生
      </view>
    </view>
  </view>
</template>

<script>
import {
  OrganizationConsortiumGet,
  ConsortiumGetList,
} from '@/api/passport.js';
import {
  querySuggestLevel,
  querySuggestType,
  querySuggestInfo,
} from '@/api/content.js';
import { getOrganizationById } from '@/api/passport.js';
const app = getApp();
import { QPGetReportPage } from '@/api/supplier.js';
import { GetUserNotSignCount } from '@/api/appointment.js';
import { getMissionImageUrl } from '../utils/mission';
export default {
  data() {
    return {
      showOverlay: false,
      articleList: [],
      orgInfo: {},
      list: [],
      reportInfo: {},
      showInfo: {},
      levelId: '',
      ConsortiumId: '',
      userInfo: {},
    };
  },
  onLoad() {
    const userInfo = uni.getStorageSync('SGUser');
    if (userInfo) {
      this.userInfo = userInfo;
    }
    this.getReport();
    this.getOrganizationConsortiumList();
  },
  methods: {
    async onToAppointment() {
      if (this.reportInfo && this.reportInfo.Name && this.reportInfo.IdCard) {
        const params = {
          SchoolName: this.reportInfo.SchoolName,
          Name: this.reportInfo.Name,
          Phone: app.globalData.userInfo.PhoneNumber,
          IdCard: this.reportInfo.IdCard,
          BrithDay: this.$dateFormat(this.reportInfo.Birthday, 'YYYY-MM-DD'),
          Sex: this.reportInfo.Sex,
        };
        uni.setStorageSync('SGYY', encodeURIComponent(JSON.stringify(params)));
      }
      const res = await GetUserNotSignCount({
        userId: app.globalData.userInfo.Id,
      });
      if (res.Type === 200) {
        if (res.Data > 0) {
          uni.navigateTo({
            url: './ReservationRecord',
          });
        } else {
          uni.navigateTo({
            url: './SGAppointment',
          });
        }
      } else {
        uni.showToast({
          icon: 'none',
          title: res.Content,
        });
      }
    },
    onGetIdCard() {
      if (!this.userInfo || !this.userInfo.idCard) {
        return '';
      }
      const idCard = this.userInfo.idCard;
      if (idCard.length < 8) {
        return idCard; // 如果身份证号不足8位，则不做脱敏处理
      }

      // 计算身份证号脱敏后的长度
      var length = idCard.length - 8;

      // 生成星号字符串
      var stars = '*'.repeat(length);

      // 将身份证号的前四位、星号字符串、后四位拼接起来
      return idCard.substr(0, 4) + stars + idCard.substr(-4);
    },
    changeUser() {
      uni.navigateTo({
        url: './UserLogin?refLogin=true',
      });
    },
    onSeeOrgDetail() {
      const orgInfo = encodeURIComponent(JSON.stringify(this.orgInfo));
      uni.navigateTo({
        url:
          './SeeOrgDetail?orgInfo=' +
          orgInfo +
          '&ConsortiumId=' +
          this.ConsortiumId,
      });
    },
    onSeeDocs() {
      if (!this.orgInfo.OrganizationId) {
        uni.showModal({
          title: '温馨提示',
          content: '未查询到推荐医院, 无法获取到推荐的医生',
          showCancel: false,
        });
        return;
      }
      uni.navigateTo({
        url:
          './SeeSpineDocs?orgId=' +
          this.orgInfo.OrganizationId +
          '&ConsortiumId=' +
          this.ConsortiumId,
      });
    },
    onLookArticle(i) {
      uni.navigateTo({
        url: '/subPropaganda/detail?id=' + i.RecoveryMissionId,
      });
    },
    async onCustomerService() {
      const res = await getOrganizationById(this.orgInfo.OrganizationId);
      if (res.Type === 200 && res.Data.AssistantQrCode) {
        const AssistantQrCode = res.Data.AssistantQrCode;
        uni.navigateTo({
          url: './Consultation?AssistantQrCode=' + AssistantQrCode,
        });
      } else {
        uni.showModal({
          title: '温馨提示',
          content: '未查询到客服信息',
          showCancel: false,
        });
      }
    },
    onLookMoreXJ() {
      if (this.list.length === 0) {
        uni.showModal({
          content: '无推荐的内容',
          title: '温馨提示',
          showCancel: false,
        });
        return;
      }
    },
    async getOrganizationConsortiumList() {
      // 视光筛查2 脊柱筛查1
      const resData = await ConsortiumGetList({
        Type: 2,
      });
      if (resData.Type === 200) {
        this.ConsortiumId = resData.Data[0].Id;
      } else {
        return;
      }
      // 默认取第一个 作所以这里传的PageSize为1
      const data = {
        ConsortiumId: this.ConsortiumId,
        PageIndex: 1,
        PageSize: 1,
      };
      const res = await OrganizationConsortiumGet(data);
      if (res.Type === 200) {
        this.orgInfo = res.Data.Data[0];
        getApp().changeOrgAndMark({
          orgId: res.Data.Data[0].OrganizationId,
          orgName: res.Data.Data[0].OrganizationName,
        });
      }
    },
    async getTageList() {
      const res = await querySuggestLevel({
        pageIndex: 1,
        pageSize: 999,
        type: 2,
      });
      if (res.Type === 200) {
        this.getXJTageByAtrValue(res.Data.Data);
      }
    },
    getStandard(lysl, qjd, List) {
      if (typeof lysl !== 'number') {
        return {
          standard: '暂无结论',
          suggestion: '暂无建议',
        };
      }
      if (typeof qjd !== 'number') {
        return {
          standard: '暂无结论',
          suggestion: '暂无建议',
        };
      }
      const isBetweenLysl = List.filter(
        (v) =>
          lysl >= v.SuggestRule.LYSLZXZ &&
          lysl <= v.SuggestRule.LYSLZDZ &&
          qjd >= v.SuggestRule.DXQJDZXZ &&
          qjd <= v.SuggestRule.DXQJDZDZ
      );
      if (isBetweenLysl.length > 0) {
        return {
          findData: isBetweenLysl[0],
          standard: isBetweenLysl[0].Name,
          suggestion: isBetweenLysl[0].SuggestContent,
        };
      } else {
        return {
          standard: '暂无结论',
          suggestion: '暂无建议',
        };
      }
    },
    judgementStandard(List) {
      if (!this.reportInfo || !this.reportInfo.Id) {
        this.getListRightTages(List[0].Id);
        return;
      }
      // 获取左眼结论
      const leftLyslData = this.getStandard(
        this.reportInfo?.NakedDegreeOs,
        this.reportInfo?.DXQJDZ,
        List
      );
      this.reportInfo.leftLyslRes = leftLyslData.standard;

      const rightLyslData = this.getStandard(
        this.reportInfo?.NakedDegreeOd,
        this.reportInfo?.DXQJDY,
        List
      );
      this.reportInfo.rightLyslRes = rightLyslData.standard;

      // 获取建议
      const suData =
        this.reportInfo?.DXQJDZ < this.reportInfo?.DXQJDY
          ? leftLyslData.findData
          : rightLyslData.findData;
      this.showInfo = suData;
      const levelId = suData.Id;
      this.levelId = levelId;
      this.getListRightTages(levelId);
    },
    async getXJTageByAtrValue(List) {
      // 裸眼视力的评判标准
      this.judgementStandard(List);
    },
    async getListRightTages(levelId) {
      const res = await querySuggestType({
        pageIndex: 1,
        pageSize: 999,
        levelId,
      });
      if (res.Type === 200) {
        this.list = res.Data.Data;
        this.getArtList(res.Data.Data[0].Id);
      }
    },
    async getArtList(typeId) {
      const typeName = this.list.filter((v) => v.Id === typeId)[0].Name;
      const res = await querySuggestInfo({
        levelId: this.levelId,
        pageIndex: 1,
        pageSize: 99,
        typeId,
      });
      if (res.Type === 200) {
        res.Data.Data.forEach((e, index) => {
          if (!e.ShowImg) {
            e.ShowImg = getMissionImageUrl(index);
          }
          e.Tage = typeName;
        });
        this.articleList = res.Data.Data;
      }
    },
    async getReport(Phone) {
      const res = await QPGetReportPage({
        ...this.userInfo,
        // idCard: '510114201711280219',
        // name: '刘瑞泽',
        pageIndex: 1,
        pageSize: 1,
      });
      if (res.Type === 200) {
        this.reportInfo = res.Data.Data[0];
        this.getTageList();
      }
    },
    onChangeList(e) {
      this.getArtList(e.Id);
    },
    onToSeeReport() {
      if (!this.reportInfo || !this.reportInfo.Id) {
        uni.showToast({
          title: '暂无报告数据',
          icon: 'none',
        });
        return;
      }
      if (this.reportInfo.Name && this.reportInfo.IdCard) {
        const params = {
          SchoolName: this.reportInfo.SchoolName,
          Name: this.reportInfo.Name,
          Phone: app.globalData.userInfo.PhoneNumber,
          IdCard: this.reportInfo.IdCard,
          BrithDay: this.$dateFormat(this.reportInfo.Birthday, 'YYYY-MM-DD'),
          Sex: this.reportInfo.Sex,
        };
        uni.setStorageSync('SGYY', encodeURIComponent(JSON.stringify(params)));
      }
      uni.navigateTo({
        url: './SGReportDetail?Id=' + this.reportInfo.Id,
      });
    },
  },
};
</script>

<style scoped lang="scss">
/deep/ .u-image__error {
  height: 278rpx !important;
  width: 100% !important;
}

.container {
  padding: 36rpx;
  background: #f8f7f7;
  position: relative;

  .assistanceImage {
    position: fixed;
    right: 40rpx;
    bottom: 12%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 10000;

    &-text {
      width: 100%;
      height: 42rpx;
      text-align: center;
      line-height: 42rpx;
      color: white;
      background: #29b7a3;
      border-radius: 22rpx;
      font-weight: 600;
      font-size: 24rpx;
    }
  }

  &-user {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24rpx;
  }

  &-bottom {
    height: 168rpx;
    background: #ffffff;
    box-shadow: 0rpx -4rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);
    border-radius: 16rpx 16rpx 0rpx 0rpx;
    position: fixed;
    left: 0;
    bottom: 0;
    z-index: 99;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-around;

    &-each {
      width: 45%;
      height: 70rpx;
      background: white;
      box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);
      border-radius: 40rpx;
      border: 2rpx solid #29b7a3;
      text-align: center;
      line-height: 70rpx;
      color: #29b7a3;
      font-weight: 600;
      font-size: 28rpx;
    }

    &-other {
      background: #29b7a3;
      box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);
      border-radius: 40rpx;
      color: #ffffff;
    }
  }

  &-article {
    margin: 0 0 32rpx 0;

    &-list {
      &-each {
        min-height: 172rpx;
        background: #ffffff;
        box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.1);
        border-radius: 24rpx;
        margin: 24rpx 4rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24rpx 32rpx;

        &-right {
          flex: 1;
          margin-left: 32rpx;

          &-test {
            text-align: center;
            padding: 4rpx 16rpx;
            background: rgba(27, 188, 156, 0.2);
            border-radius: 8rpx;
            font-weight: 600;
            font-size: 24rpx;
            color: #29b7a3;
            margin-right: 20rpx;
          }

          &-title {
            color: #29b7a3;
            height: 20px;
            background: rgba(27, 188, 156, 0.2);
            border-radius: 8rpx;
            padding: 0 20rpx;
            margin-right: 10px;
          }
        }
      }
    }

    &-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  &-top {
    width: 100%;
    background: #ffffff;
    border-radius: 24rpx;
    box-shadow: 2.5px 3px 9px 0px rgba(0, 0, 0, 0.08);
    padding: 40rpx;

    &-dis {
      margin: 22rpx;
      font-size: 24rpx;
      font-weight: 400;
      color: #989898;
    }

    &-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 26rpx;

      &-left {
        display: flex;
        justify-content: space-between;
        align-items: center;

        &-top {
          margin-left: 32rpx;
        }
      }
    }
  }

  &-result {
    position: relative;
    background: white;
    border-radius: 0 0 24rpx 24rpx;

    &-bom {
      width: 100%;
      padding: 0 40rpx;
      margin-top: 24rpx;
      transform: translateY(-50rpx);

      &-text {
        font-weight: 400;
        font-size: 24rpx;
        color: #333333;
        margin-bottom: 8rpx;
      }

      &-dis {
        font-weight: 400;
        font-size: 24rpx;
        color: #29b7a3;
        margin-top: 24rpx;
        margin-bottom: 40rpx;
      }
    }

    &-mid {
      z-index: 999;
      transform: translateY(-50rpx);
      padding: 36rpx 32rpx 20rpx 32rpx;
      background-image: url('./static/sgresult.png');
      width: 100%;
      border-radius: 24rpx;
      background-color: #e7fdf9;

      &-eye {
        margin-top: 20rpx;
        display: flex;
        justify-content: flex-start;
        align-items: center;

        &-box {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          margin-right: 24rpx;

          &-eye {
            width: 32rpx;
            height: 32rpx;
            margin-right: 8rpx;
          }

          &-text {
            font-weight: 600;
            font-size: 28rpx;
            color: #ff5656;
          }
        }
      }

      &-top {
        display: flex;
        align-items: center;
        justify-content: flex-start;

        &-img {
          width: 44rpx;
          height: 44rpx;
        }

        &-end {
          font-weight: 600;
          font-size: 32rpx;
          color: #333333;
          margin-left: 16rpx;
        }
      }
    }

    &-top {
      background: linear-gradient(to right, #29b7a3ff, #08b9d9ff);
      width: 100%;
      padding: 24rpx 40rpx 60rpx 40rpx;
      display: flex;
      border-radius: 24rpx;
      justify-content: space-between;
      align-items: center;
      position: relative;

      &-img {
        position: absolute;
        top: 0;
        right: -20rpx;
        width: 110rpx;
        height: 68rpx;
      }

      &-left {
        font-weight: 600;
        font-size: 32rpx;
        color: #ffffff;
        line-height: 44rpx;
      }

      &-right {
        border-radius: 26rpx;
        border: 2rpx solid #ffffff;
        color: white;
        padding: 8rpx 24rpx;
        font-size: 24rpx;
      }
    }
  }
}
</style>
