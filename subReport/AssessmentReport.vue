<template>
  <view class="container">
    <u-sticky offset-top="0">
      <view class="container-top flex-between-center">
        <view class="flex-center-center" @click="() => (showCalendar = true)">
          <text class="container-top-time">{{
            query.BeginDate + '~' + query.EndDate
          }}</text>
          <u-icon
            name="arrow-down-fill"
            size="12"
            color="rgba(41, 183, 163, 1)"
          ></u-icon>
        </view>
        <view
          class="flex-end-center"
          style="flex: 1; margin-left: 24rpx"
          @click="showActionSheet = true"
        >
          <text class="container-top-time text-max1">{{
            onGetReportName()
          }}</text>
          <u-icon
            name="arrow-down-fill"
            size="12"
            color="rgba(41, 183, 163, 1)"
          ></u-icon>
        </view>
      </view>
    </u-sticky>
    <view class="container-list" v-if="list.length">
      <block v-for="(item, index) in list" :key="index">
        <view
          class="container-list-item flex-between-center"
          @click="handleDetailClick(item)"
        >
          <view class="container-list-item-left">
            <text class="container-list-item-left-name">{{
              item.ActionName
            }}</text>
            <text class="container-list-item-left-time">{{
              item.CompletedTime
            }}</text>
          </view>
          <u-icon name="arrow-right" size="24" color="#8c8c8c"></u-icon>
        </view>
      </block>
    </view>
    <u-empty
      v-else
      mode="data"
      icon="http://cdn.uviewui.com/uview/empty/data.png"
      text="当前没有训练报告"
    >
    </u-empty>
    <u-calendar
      :defaultDate="defaultDate"
      mode="range"
      color="#25ae99"
      :allowSameDay="true"
      :max-date="calendarMaxDay"
      :min-date="calendarMinDate"
      :monthNum="13"
      :show="showCalendar"
      @confirm="confirmCalendar"
      @close="showCalendar = false"
    ></u-calendar>
    <u-action-sheet
      cancelText="取消"
      @close="showActionSheet = false"
      @select="selectClick"
      :closeOnClickOverlay="true"
      :closeOnClickAction="true"
      :actions="actionsList"
      :show="showActionSheet"
    ></u-action-sheet>
  </view>
</template>

<script>
const dayjs = require('dayjs');
import { queryJCaiTechReport } from '@/api/supplier.js';
import { JC } from '@/constants.js';
const app = getApp();
export default {
  data() {
    return {
      showActionSheet: false,
      actionsList: [],
      defaultDate: [],
      calendarMaxDay: dayjs().format('YYYY-MM-DD'),
      calendarMinDate: dayjs().subtract(1, 'year').format('YYYY-MM-DD'),
      showCalendar: false,
      list: [],
      query: {
        UserId: app.globalData.userInfo.Id,
        ProgramId: null, //方案ID
        ActionExecuteId: null, //打卡记录ID
        ActionId: null, //方案明细ID
        ContentId: null, //动作基础数据ID
        BeginDate: dayjs().subtract(1, 'month').format('YYYY-MM-DD'),
        EndDate: dayjs().format('YYYY-MM-DD'),
        PageIndex: 1,
        PageSize: 10,
        ActionType: null,
      },
    };
  },
  methods: {
    onGetReportName() {
      const filterList = this.actionsList.filter(
        (s) => s.Type === this.query.ActionType
      );
      console.log('filterList', filterList);
      return filterList.length > 0 ? filterList[0].name : '全部';
    },
    selectClick(e) {
      console.log('e', e);
      this.query.ActionType = e.Type;
      this.onGetListData(false);
    },
    handleDetailClick(item) {
      const actionType = item.ActionType;
      const endDate = item.CompletedTime;
      const actionExecuteId = item.ActionExecuteId;
      uni.navigateTo({
        url: `./AssessmentReportDetail?actionType=${actionType}&endDate=${endDate}&actionExecuteId=${actionExecuteId}`,
      });
    },
    confirmCalendar(e) {
      this.query.BeginDate = e[0];
      this.query.EndDate = e[e.length - 1];
      this.defaultDate = e;
      this.onGetListData(false);
      this.showCalendar = false;
    },
    async onGetListData(flag) {
      const copyData = JSON.parse(JSON.stringify(this.query));
      copyData.BeginDate = dayjs(copyData.BeginDate).format(
        'YYYY-MM-DD 00:00:00'
      );
      copyData.EndDate = dayjs(copyData.EndDate).format('YYYY-MM-DD 23:59:59');
      const res = await queryJCaiTechReport(copyData);
      if (res.Type === 200) {
        res.Data.Data.forEach((item) => {
          item.CreatedTime = this.$dateFormat(
            item.CreatedTime,
            'YYYY-MM-DD HH:mm:ss'
          );
          item.CompletedTime = this.$dateFormat(
            item.CompletedTime,
            'YYYY-MM-DD HH:mm:ss'
          );
        });
        if (flag) {
          this.list = this.list.concat(res.Data.Data);
        } else {
          this.list = res.Data.Data;
        }
      } else {
        uni.showModal({
          title: '温馨提示',
          content: res.Content,
          showCancel: false,
        });
      }
    },
  },
  onLoad() {
    const dates = [];
    const today = dayjs();
    const oneMonthAgo = today.subtract(1, 'month');
    for (
      let date = today;
      date.isAfter(oneMonthAgo, 'day');
      date = date.subtract(1, 'day')
    ) {
      dates.push(date.format('YYYY-MM-DD'));
    }
    this.defaultDate = dates.reverse();
    this.onGetListData();
    JC.forEach((s) => (s.name = s.Name));
    JC.unshift({ name: '全部', Type: null });
    this.actionsList = JC;
  },
  onReachBottom() {
    console.log('触底了');
    this.query.PageIndex++;
    this.onGetListData(true);
  },
  async onPullDownRefresh() {
    // uni.startPullDownRefresh()
    // console.log('下拉刷新')
    // await this.onGetListData(false)
    // uni.stopPullDownRefresh()
  },
};
</script>

<style lang="scss" scoped>
/deep/ .u-action-sheet {
  max-height: 800rpx !important;
}
.container {
  &-top {
    background: #ffffff;
    position: relative;
    padding: 20rpx 32rpx;
    &-time {
      font-weight: 600;
      font-size: 28rpx;
      color: #29b7a3;
      line-height: 42rpx;
      margin-right: 14rpx;
    }
    &-img {
      width: 32rpx;
      height: 32rpx;
    }
  }
  &-list {
    padding: 32rpx;
    &-item {
      background: #ffffff;
      border-radius: 24rpx;
      padding: 32rpx;
      margin-bottom: 24rpx;
      &-left {
        flex: 1;
        &-name {
          font-weight: 600;
          font-size: 28rpx;
          color: #333333;
          line-height: 40rpx;
        }
        &-time {
          font-weight: 400;
          font-size: 28rpx;
          color: #999999;
          line-height: 40rpx;
          display: block;
          margin-top: 16rpx;
        }
      }
    }
  }
}
</style>
