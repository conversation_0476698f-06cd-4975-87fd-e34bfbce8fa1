<template>
  <view class="container">
    <view class="container-time flex-center-center" @click="handleTimeClick">
      <text style="margin-right: 14rpx">{{
        query.StartTime + '	~	' + query.EndTime
      }}</text>
      <u-icon name="arrow-down-fill" color="#25ad99" size="14"></u-icon>
    </view>
    <u-subsection
      :list="columns"
      keyName="label"
      inactiveColor="#29B7A3"
      :current="currentSubsection"
      mode="subsection"
      activeColor="#29B7A3"
      @change="onSubsectionChange"
    ></u-subsection>
    <block v-if="showType === 'GJZB'">
      <view class="container-top">
        <view class="container-top-left">关键指标</view>
        <view class="container-top-right" @click="showType = 'XXBG'">
          <image
            src="./static/xxbg.png"
            style="width: 36rpx; height: 36rpx; margin-right: 12rpx"
            mode=""
          ></image>
          <text>详细报告</text>
        </view>
      </view>
      <block v-if="currentSubsection === 3">
        <view
          class="container-charts"
          v-for="(item, index) in TestExhale"
          :key="index"
        >
          <view style="margin-bottom: 16rpx" class="flex-start-center">
            <u-badge :isDot="true" :bgColor="item.chartColor[0]"></u-badge>
            <text style="margin-left: 8rpx">{{ item.chartName }}</text>
          </view>
          <view style="height: 200px">
            <qiun-data-charts
              :onzoom="true"
              :tapLegend="false"
              type="area"
              :ontouch="true"
              :canvas2d="true"
              :opts="{ ...opts, color: item.chartColor }"
              :chartData="item"
            />
          </view>
        </view>
      </block>
      <block v-if="currentSubsection === 1">
        <view
          class="container-charts"
          v-for="(item, index) in TrainInhale"
          :key="index"
        >
          <view style="margin-bottom: 16rpx" class="flex-start-center">
            <u-badge :isDot="true" :bgColor="item.chartColor[0]"></u-badge>
            <text style="margin-left: 8rpx">{{ item.chartName }}</text>
          </view>
          <view style="height: 200px">
            <qiun-data-charts
              :onzoom="true"
              :tapLegend="false"
              type="area"
              :ontouch="true"
              :canvas2d="true"
              :opts="{ ...opts, color: item.chartColor }"
              :chartData="item"
            />
          </view>
        </view>
      </block>
      <block v-if="currentSubsection === 0">
        <view
          class="container-charts"
          v-for="(item, index) in TrainExhale"
          :key="index"
        >
          <view style="margin-bottom: 16rpx" class="flex-start-center">
            <u-badge :isDot="true" :bgColor="item.chartColor[0]"></u-badge>
            <text style="margin-left: 8rpx">{{ item.chartName }}</text>
          </view>
          <view style="height: 200px">
            <qiun-data-charts
              :onzoom="true"
              :tapLegend="false"
              type="area"
              :ontouch="true"
              :canvas2d="true"
              :opts="{ ...opts, color: item.chartColor }"
              :chartData="item"
            />
          </view>
        </view>
      </block>
    </block>
    <view v-if="showType === 'GJZB'" style="height: 40rpx"></view>
    <block v-if="showType === 'XXBG'">
      <view class="container-top">
        <view class="container-top-left">详细报告</view>
        <view
          class="container-top-right"
          @click="showType = 'GJZB'"
          v-if="currentSubsection !== 2"
        >
          <image
            src="./static/gjzb.png"
            style="width: 36rpx; height: 36rpx; margin-right: 12rpx"
            mode=""
          ></image>
          <text>关键指标</text>
        </view>
      </view>
      <u-steps
        dot
        direction="column"
        :current="99999"
        activeColor="#29B7A3"
        v-if="listData.length > 0"
      >
        <u-steps-item
          :title="i.OutCreatedTime"
          v-for="(i, index) in listData"
          :key="i.Time"
        >
          <view slot="desc">
            <view
              class="container-desc"
              :style="{ background: o.BGColor.bgColor }"
              v-for="o in i.Data"
              :key="o.Id"
              @click="onStepsItem(o)"
            >
              <view class="container-desc-left">
                {{ o.InCreatedTime }}
              </view>
              <view class="container-desc-right">
                <span>{{ o.BGColor.text }}</span>
                <span :style="{ color: o.BGColor.iconColor }">></span>
              </view>
            </view>
          </view>
        </u-steps-item>
      </u-steps>
    </block>
    <u-empty
      mode="data"
      icon="http://cdn.uviewui.com/uview/empty/data.png"
      v-if="!listData.length && showType === 'XXBG'"
    ></u-empty>
    <!-- <u-picker :show="show" :columns="columns" @confirm="confirm" @close="show = false" @cancel="show = false"
			keyName="label"></u-picker> -->
    <u-calendar
      :defaultDate="defaultDate"
      mode="range"
      color="#25ae99"
      :allowSameDay="true"
      :max-date="calendarMaxDay"
      :min-date="calendarMinDate"
      :monthNum="13"
      :show="showCalendar"
      @confirm="confirmCalendar"
      @close="showCalendar = false"
    ></u-calendar>
  </view>
</template>

<script>
const dayjs = require('dayjs');
const app = getApp();
import {
  GetXeekReportByUserId,
  getReportLineChartByType,
} from '@/api/supplier.js';
import { getPrescriptionDefaultReportDate } from '@/api/consult.js';

export default {
  data() {
    return {
      defaultDate: [],
      calendarMaxDay: dayjs().format('YYYY-MM-DD'),
      calendarMinDate: dayjs().subtract(1, 'year').format('YYYY-MM-DD'),
      TrainInhale: [],
      TrainExhale: [],
      TestExhale: [],
      chartData: {
        categories: ['2018', '2019', '2020', '2021', '2022', '2023'],
        series: [
          {
            name: '成交量A',
            data: [35, 8, 25, 37, 4, 20],
          },
          {
            name: '成交量B',
            data: [70, 40, 65, 100, 44, 68],
          },
          {
            name: '成交量C',
            data: [100, 80, 95, 150, 112, 132],
          },
        ],
      },
      opts: {
        color: ['#1890FF'],
        padding: [20, 0, 0, 0],
        enableScroll: true,
        legend: {},
        xAxis: {
          disableGrid: true,
          scrollShow: false,
          itemCount: 8,
          rotateLabel: true,
          rotateAngle: 45,
        },
        yAxis: {
          gridType: 'dash',
          dashLength: 2,
        },
        extra: {
          area: {
            type: 'straight',
            width: 2,
            activeType: 'hollow',
            gradient: true,
          },
        },
        background: 'rgb(29, 157, 219)',
        legend: {
          show: false,
        },
      },
      showType: 'GJZB',
      currentSubsection: 0,
      showCalendar: false,
      listData: [],
      countList: [],
      chooseType: '全部',
      show: false,
      columns: [
        {
          label: '呼气训练',
          id: ['train_exhale'],
        },
        {
          label: '吸气训练',
          id: ['train_inhale'],
        },
        {
          label: '气道廓清',
          id: ['airway_clearance'],
        },
        {
          label: '呼吸测试',
          id: ['test_exhale', 'test_inhale'],
        },
      ],
      bgColor: {
        test_inhale: {
          bgColor: 'linear-gradient(316deg, #FFE7F4 0%, #FFFFFF 100%)',
          iconColor: '#D34492',
          text: '呼吸测试报告',
        },
        test_exhale: {
          bgColor: 'linear-gradient(316deg, #FFE7F4 0%, #FFFFFF 100%)',
          iconColor: '#D34492',
          text: '呼吸测试报告',
        },
        train_inhale: {
          bgColor: 'linear-gradient(316deg, #EAF3FF 0%, #FFFFFF 100%)',
          iconColor: '#347DE4',
          text: '吸气训练报告',
        },
        train_exhale: {
          bgColor: 'linear-gradient(316deg, #FFF1E6 0%, #FFFFFF 100%)',
          iconColor: '#D38144',
          text: '呼气训练报告',
        },
        airway_clearance: {
          bgColor: 'linear-gradient(316deg, #E8FFFA 0%, #FFFFFF 100%)',
          iconColor: '#29B7A3',
          text: '气道廓清报告',
        },
      },
      query: {
        UserId: app.globalData.userInfo.Id,
        Types: ['train_exhale'],
        PageIndex: 1,
        PageSize: 10,
        EndTime: dayjs().format('YYYY-MM-DD'),
        StartTime: dayjs().subtract(1, 'month').format('YYYY-MM-DD'),
      },
    };
  },
  created() {
    // 获取当前用户最近一次报告时间
    this.onGetUserLastReportDate();
    const dates = [];
    const today = dayjs();
    const oneMonthAgo = today.subtract(1, 'month');
    for (
      let date = today;
      date.isAfter(oneMonthAgo, 'day');
      date = date.subtract(1, 'day')
    ) {
      dates.push(date.format('YYYY-MM-DD'));
    }
    this.defaultDate = dates.reverse();
  },
  methods: {
    async onGetUserLastReportDate() {
      const res = await getPrescriptionDefaultReportDate({
        Manufacturer: 2,
        UserId: app.globalData.userInfo.Id,
      });
      if (res.Type !== 200) {
        // 获取详细报告接口
        this.getReportList();
        // 获取关键指标接口
        this.onGetKeyReport();
        return;
      }
      if (!res.Data.EndDate || !res.Data.StartDate) {
        // 获取详细报告接口
        this.getReportList();
        // 获取关键指标接口
        this.onGetKeyReport();
        return;
      }
      this.query.EndTime = dayjs(res.Data.EndDate).format('YYYY-MM-DD');
      this.query.StartTime = dayjs(res.Data.StartDate).format('YYYY-MM-DD');
      this.getReportList();
      // 获取关键指标接口
      this.onGetKeyReport();
    },
    async onGetKeyReport() {
      const copyData = JSON.parse(JSON.stringify(this.query));
      delete copyData.PageIndex;
      delete copyData.PageSize;
      copyData.Types =
        copyData.Types.length > 1 ? ['test_exhale'] : copyData.Types;
      const res = await getReportLineChartByType(copyData);
      if (res.Type === 200) {
        this.onGetChartsList(res.Data);
      }
    },
    confirmCalendar(e) {
      console.log('e', e);
      this.query.StartTime = e[0];
      this.query.EndTime = e[e.length - 1];
      this.defaultDate = e;
      this.getReportList();
      this.onGetKeyReport();
      this.showCalendar = false;
    },
    handleTimeClick() {
      this.showCalendar = true;
    },
    onSubsectionChange(e) {
      this.currentSubsection = e;
      this.query.Types = this.columns[e].id;
      if (e === 2) {
        this.showType = 'XXBG';
      } else {
        this.onGetKeyReport();
      }
      this.getReportList();
    },
    onStepsItem(item) {
      console.log('item', item);
      if (item.Id) {
        uni.navigateTo({
          url: './RDetail?Id=' + item.Id + '&Type=' + item.Type,
        });
      }
    },
    async getReportList() {
      // const data = {
      // 	UserId: app.globalData.userInfo.Id // '3a09fc31-39cf-b9a5-94d9-9bdfb69aff94' ||
      // }
      const res = await GetXeekReportByUserId(this.query);
      if (res.Type === 200) {
        uni.showLoading({
          title: '正在加载数据中',
        });
        res.Data.forEach((v, index) => {
          v.OutCreatedTime = this.$dateFormat(v.Time, 'YYYY-MM-DD');
          v.Index = v.Time + index;
          v.Data.forEach((o) => {
            o.BGColor = this.bgColor[o.Type];
            o.InCreatedTime = this.$dateFormat(o.CreatedTime, 'HH:mm:ss');
            o.CreatedTimeDay = this.$dateFormat(o.CreatedTime, 'MM.DD');
          });
        });
        // 组装按照天分组展示
        this.listData = res.Data;
        this.$nextTick(() => {
          uni.hideLoading();
        });
      }
    },
    onGetChartsList(listData) {
      if (!listData) return;
      const list = listData;
      const type = this.query.Types[0];
      let x_line;
      let metrics = [];
      switch (type) {
        case 'test_exhale':
          x_line = list.TestExhale.map((s) =>
            this.$dateFormat(s.Date, 'MM.DD')
          );
          metrics = [
            {
              key: 'FVC',
              name: '用力肺活量（呼气）',
              unit: '(ml)',
              color: ['#4683e2'],
            },
            {
              key: 'MEP',
              name: '最大呼气压力',
              unit: '(cmH2O)',
              color: ['#53d3e5'],
            },
            {
              key: '',
              name: '一秒率',
              unit: '',
              color: ['#70e553'],
            },
            {
              key: 'PEF',
              name: '呼气峰值流量',
              unit: '(L/min)',
              color: ['#e85eb9'],
            },
          ];
          let TestExhale = [];
          metrics.forEach((s) => {
            const value = list.TestExhale.map((v) => v.Exhale[s.key]);
            const chartData = {
              categories: x_line,
              series: [
                {
                  name: s.key,
                  data: value,
                },
              ],
              chartName: s.name + s.key + s.unit,
              chartColor: s.color,
            };
            TestExhale.push(chartData);
          });
          this.TestExhale = TestExhale;
          break;
        case 'train_inhale':
          x_line = list.TrainInhale.map((s) =>
            this.$dateFormat(s.Date, 'MM.DD')
          );
          metrics = [
            {
              key: 'MIP_Max',
              name: '最大吸气压力',
              unit: '(cmH2O)',
              color: ['#4683e2'],
            },
            {
              key: 'FIVC_Avg',
              name: '平均吸气量',
              unit: '(ml)',
              color: ['#53d3e5'],
            },
            {
              key: 'FIVC_All',
              name: '总吸气量',
              unit: '(ml)',
              color: ['#70e553'],
            },
            {
              key: 'PIF_Avg',
              name: '平均吸气流量',
              unit: '(L/min)',
              color: ['#e85eb9'],
            },
          ];
          let TrainInhale = [];
          metrics.forEach((s) => {
            const value = list.TrainInhale.map((v) => v.Inhale[s.key]);
            const chartData = {
              categories: x_line,
              series: [
                {
                  name: s.key,
                  data: value,
                },
              ],
              chartName: s.name + s.key + s.unit,
              chartColor: s.color,
            };
            TrainInhale.push(chartData);
          });
          this.TrainInhale = TrainInhale;
          break;
        case 'train_exhale':
          x_line = list.TrainExhale.map((s) =>
            this.$dateFormat(s.Date, 'MM.DD')
          );
          metrics = [
            {
              key: 'MEP_Max',
              name: '最大呼气压力',
              unit: '(cmH2O)',
              color: ['#4683e2'],
            },
            {
              key: 'FVC_Avg',
              name: '平均用力肺活量',
              unit: '(ml)',
              color: ['#53d3e5'],
            },
            {
              key: 'FVC_All',
              name: '总呼气量',
              unit: '(ml)',
              color: ['#70e553'],
            },
            {
              key: 'PEF_Avg',
              name: '平均呼气流量',
              unit: '(L/min)',
              color: ['#e85eb9'],
            },
          ];
          let TrainExhale = [];
          metrics.forEach((s) => {
            const value = list.TrainExhale.map((v) => v.Exhale[s.key]);
            const chartData = {
              categories: x_line,
              series: [
                {
                  name: s.key,
                  data: value,
                },
              ],
              chartName: s.name + s.key + s.unit,
              chartColor: s.color,
            };
            TrainExhale.push(chartData);
          });
          this.TrainExhale = TrainExhale;
          break;
        case 3:
          break;
        default:
          break;
      }
    },
    confirm(e) {
      const choose = e.value[0];
      if (choose.label === this.chooseType) {
        this.show = false;
        return;
      }
      this.chooseType = choose.label;
      this.show = false;
      this.query.Types = choose.id;
      this.getReportList();
    },
  },
};
</script>

<style scoped lang="scss">
/deep/ .u-subsection__item__text {
  font-size: 28rpx;
  color: #ffffff;
}

.container {
  height: 100vh;

  &-charts {
    background: #ffffff;
    box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);
    border-radius: 16rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
  }

  &-time {
    background: #ffffff;
    padding: 18rpx 0;
    font-size: 28rpx;
    margin-bottom: 32rpx;
    font-weight: 600;
    font-size: 28rpx;
    color: #29b7a3;
    line-height: 42rpx;
  }

  &-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;

    &-left {
      font-size: 32rpx;
      font-family:
        PingFangSC-Semibold,
        PingFang SC;
      font-weight: 600;
      color: #333333;
    }

    &-right {
      padding: 6rpx 32rpx;
      background: #ffffff;
      box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);
      border-radius: 24rpx;
      font-size: 24rpx;
      font-weight: 600;
      color: #29b7a3;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  &-title {
    font-size: 28rpx;
    font-family:
      PingFangSC-Semibold,
      PingFang SC;
    font-weight: 600;
    color: #29b7a3;
  }

  &-desc {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;

    &-left {
      font-size: 24rpx;
      font-family:
        PingFangSC-Regular,
        PingFang SC;
      font-weight: 400;
      color: #333333;
    }

    &-right {
      flex: 1;
      height: 100rpx;
      line-height: 100rpx;
      font-size: 32rpx;
      font-family:
        PingFangSC-Semibold,
        PingFang SC;
      font-weight: 600;
      color: #333333;
      margin-left: 16rpx;
      padding: 0 20rpx;
      border-radius: 16rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}
</style>
