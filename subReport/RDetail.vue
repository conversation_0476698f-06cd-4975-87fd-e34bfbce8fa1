<template>
  <view class="container">
    <view class="container-top">
      <span class="container-top-left">基本信息</span>
      <span class="container-top-right">{{ info.CreatedTime }}</span>
    </view>
    <view class="container-user">
      <view class="container-user-info">
        <span class="container-user-info-left">姓名：</span>
        <span class="container-user-info-right">{{ info.Name }}</span>
      </view>
      <view class="container-user-info">
        <span class="container-user-info-left">性别：</span>
        <span class="container-user-info-right">{{ info.Gender }}</span>
      </view>
      <view class="container-user-info" v-if="info.Age">
        <span class="container-user-info-left">年龄：</span>
        <span class="container-user-info-right">{{ info.Age }}岁</span>
      </view>
      <view class="container-user-info">
        <span class="container-user-info-left">身高：</span>
        <span class="container-user-info-right">{{ info.Height || '' }}cm</span>
      </view>
      <view class="container-user-info">
        <span class="container-user-info-left">体重：</span>
        <span class="container-user-info-right">{{ info.Weight || '' }}kg</span>
      </view>
      <view class="container-user-info" v-if="Type === 'hxjlbg'">
        <span class="container-user-info-left">民族：</span>
        <span class="container-user-info-right">{{ info.Nation || '' }}</span>
      </view>
      <view class="container-user-info" v-if="Type === 'hxjlbg'">
        <span class="container-user-info-left">门诊/住院号：</span>
        <span class="container-user-info-right">{{
          info.ResidenceNo || ''
        }}</span>
      </view>
      <view class="container-user-info" v-if="Type === 'hxjlbg'">
        <span class="container-user-info-left">选用预计值：</span>
        <span class="container-user-info-right">{{
          info.RefEquations >= 0
            ? ['默认', '中国人4-80岁', 'Standard'][info.RefEquations]
            : ''
        }}</span>
      </view>
      <view class="container-user-info" v-if="Type === 'hxjlbg'">
        <span class="container-user-info-left">设备型号：</span>
        <span class="container-user-info-right">{{
          info.DeviceType || ''
        }}</span>
      </view>
      <view class="container-user-info" v-if="Type === 'hxjlbg'">
        <span class="container-user-info-left">检查医生：</span>
        <span class="container-user-info-right">{{
          info.DoctorName || ''
        }}</span>
      </view>
    </view>
    <view
      class="container-user"
      v-if="info.Type === 'train_exhale' || info.Type === 'train_inhale'"
    >
      <view class="container-user-info">
        <span class="container-user-info-left">训练次数：</span>
        <span class="container-user-info-right">{{ data.Result.count }}次</span>
      </view>
      <view class="container-user-info">
        <span class="container-user-info-left">训练难度：</span>
        <span class="container-user-info-right"
          >{{ data.Result.difficulty_level }}星</span
        >
      </view>
      <view class="container-user-info">
        <span class="container-user-info-left">训练模式：</span>
        <span class="container-user-info-right">{{
          data.Result.train_type === 1 ? '自动' : '手动'
        }}</span>
      </view>
    </view>
    <!-- 起到廓清 -->
    <view class="container-user" v-if="info.Type === 'airway_clearance'">
      <view class="container-user-info">
        <span class="container-user-info-left">阻力级别：</span>
        <span class="container-user-info-right">{{
          data.Result.difficulty_level
        }}</span>
      </view>
      <view class="container-user-info">
        <span class="container-user-info-left">呼吸次数：</span>
        <span class="container-user-info-right">{{ data.Result.count }}次</span>
      </view>
      <view class="container-user-info">
        <span class="container-user-info-left">训练模式：</span>
        <span class="container-user-info-right">{{
          data.Result.train_type === 1 ? '自动' : '手动'
        }}</span>
      </view>
      <view class="container-user-info">
        <span class="container-user-info-left">震动频率：</span>
        <span class="container-user-info-right"
          >{{ data.Result.train_frequence }}Hz</span
        >
      </view>
      <view class="container-user-info">
        <span class="container-user-info-left">震动总时间：</span>
        <span class="container-user-info-right"
          >{{ data.Result.duration }}s</span
        >
      </view>
    </view>

    <!-- 图表 -->
    <view
      v-if="Type === 'train_inhale'"
      class="flex-start-center-wrap"
      style="margin: 32rpx 0; background: white"
    >
      <view
        style="width: 50%; height: 150px"
        v-for="(item, index) in TrainInhale"
        :key="index"
      >
        <qiun-data-charts
          type="line"
          :canvas2d="true"
          :opts="opts"
          :chartData="item"
        />
      </view>
    </view>
    <view
      v-if="Type === 'train_exhale'"
      class="flex-start-center-wrap"
      style="margin: 32rpx 0; background: white"
    >
      <view
        style="width: 50%; height: 150px"
        v-for="(item, index) in TrainExhale"
        :key="index"
      >
        <qiun-data-charts
          type="line"
          :canvas2d="true"
          :opts="opts"
          :chartData="item"
        />
      </view>
    </view>
    <view
      v-if="Type === 'test_exhale'"
      class="flex-start-center-wrap"
      style="margin: 32rpx 0; background: white"
    >
      <view
        style="width: 50%; height: 150px"
        v-for="(item, index) in TestExhale"
        :key="index"
      >
        <qiun-data-charts
          type="line"
          :canvas2d="true"
          :opts="opts"
          :chartData="item"
        />
      </view>
    </view>

    <view class="container-indicators">
      <span class="container-top-left">本次训练指标</span>
    </view>
    <!-- 呼气、吸气训练报告 -->
    <template
      v-if="info.Type === 'train_exhale' || info.Type === 'train_inhale'"
    >
      <view class="container-topList">
        <view class="container-topList-each2"> 参考 </view>
        <view class="container-topList-each2"> 实测值 </view>
      </view>
      <template v-if="info.Type === 'train_exhale'">
        <view class="container-topList container-eachList">
          <view class="container-topList-each2">
            最大呼气压MEP_max
            <br />
            (cmH2O)
          </view>
          <view class="container-topList-each2">
            {{ data.Result ? data.Result.MEP_max : '' }}
          </view>
        </view>
        <view class="container-topList container-eachList">
          <view class="container-topList-each2">
            平均呼气量FVC_avg
            <br />
            (ml)
          </view>
          <view class="container-topList-each2">
            {{ data.Result ? data.Result.FVC_avg : '' }}
          </view>
        </view>
        <view class="container-topList container-eachList">
          <view class="container-topList-each2">
            总呼气量FVC_all
            <br />
            (ml)
          </view>
          <view class="container-topList-each2">
            {{ data.Result ? data.Result.FVC_all : '' }}
          </view>
        </view>
        <view class="container-topList container-eachList">
          <view class="container-topList-each2">
            平均呼气流量PEF_avg
            <br />
            (L/min)
          </view>
          <view class="container-topList-each2">
            {{ data.Result ? data.Result.PEF_avg : '' }}
          </view>
        </view>
      </template>
      <template v-else>
        <view class="container-topList container-eachList">
          <view class="container-topList-each2">
            最大吸气压MIP_max
            <br />
            (cmH2O)
          </view>
          <view class="container-topList-each2">
            {{ data.Result ? data.Result.MIP_max : '' }}
          </view>
        </view>
        <view class="container-topList container-eachList">
          <view class="container-topList-each2">
            平均吸气量FIVC_avg
            <br />
            (ml)
          </view>
          <view class="container-topList-each2">
            {{ data.Result ? data.Result.FIVC_avg : '' }}
          </view>
        </view>
        <view class="container-topList container-eachList">
          <view class="container-topList-each2">
            总吸气量FIVC_all
            <br />
            (ml)
          </view>
          <view class="container-topList-each2">
            {{ data.Result ? data.Result.FIVC_all : '' }}
          </view>
        </view>
        <view class="container-topList container-eachList">
          <view class="container-topList-each2">
            平均吸气流量PIF_avg
            <br />
            (L/min)
          </view>
          <view class="container-topList-each2">
            {{ data.Result ? data.Result.PIF_avg : '' }}
          </view>
        </view>
      </template>
    </template>
    <!-- 起到廓清 -->
    <template v-if="info.Type === 'airway_clearance'">
      <view class="container-topList">
        <view class="container-topList-each"> 参考 </view>
        <view class="container-topList-each"> 最小值 </view>
        <view class="container-topList-each"> 平均值 </view>
        <view class="container-topList-each"> 最大值 </view>
      </view>
      <view class="container-topList container-eachList">
        <view class="container-topList-each1">
          振动频率
          <br />
          (Hz)
        </view>
        <view class="container-topList-each1">
          {{ data.Result ? data.Result.frequence_min : '' }}
        </view>
        <view class="container-topList-each1">
          {{ data.Result ? data.Result.frequence_avg : '' }}
        </view>
        <view class="container-topList-each1">
          {{ data.Result ? data.Result.frequence_max : '' }}
        </view>
      </view>
      <view class="container-topList container-eachList">
        <view class="container-topList-each1">
          正向压力
          <br />
          (cmH2O)
        </view>
        <view class="container-topList-each1">
          {{ data.Result ? data.Result.pressure_min : '' }}
        </view>
        <view class="container-topList-each1">
          {{ data.Result ? data.Result.pressure_avg : '' }}
        </view>
        <view class="container-topList-each1">
          {{ data.Result ? data.Result.pressure_max : '' }}
        </view>
      </view>
      <view class="container-topList container-eachList">
        <view class="container-topList-each1">
          振动幅度
          <br />
          (cmH2O)
        </view>
        <view class="container-topList-each1">
          {{ data.Result ? data.Result.amplitude_min : '' }}
        </view>
        <view class="container-topList-each1">
          {{ data.Result ? data.Result.amplitude_avg : '' }}
        </view>
        <view class="container-topList-each1">
          {{ data.Result ? data.Result.amplitude_max : '' }}
        </view>
      </view>
    </template>
    <template v-if="info.Type === 'test_inhale' || info.Type === 'test_exhale'">
      <view class="container-topList">
        <view class="container-topList-each"> 参考 </view>
        <view class="container-topList-each"> 实测值 </view>
        <view class="container-topList-each"> 预计值 </view>
      </view>
      <template v-if="info.Type === 'test_inhale'">
        <view class="container-topList container-eachList">
          <view class="container-topList-each1">
            用力肺活量(吸气)FIVC
            <br />
            (ml)
          </view>
          <view class="container-topList-each1">
            <span
              :style="data.Inhale.FIVC < data.Ref.FIVC ? 'color: red' : ''"
              >{{ data.Inhale ? data.Inhale.FIVC : '' }}</span
            >
            <span
              v-if="data.Inhale && data.Inhale.FIVC < data.Ref.FIVC"
              style="color: red"
              >↓</span
            >
          </view>
          <view class="container-topList-each1">
            {{ data.Ref ? data.Ref.FIVC : '' }}
          </view>
        </view>
        <view class="container-topList container-eachList">
          <view class="container-topList-each1">
            最大吸气压力MIP
            <br />
            (cmH2O)
          </view>
          <view class="container-topList-each1">
            <span :style="data.Inhale.MIP < data.Ref.MIP ? 'color: red' : ''">{{
              data.Inhale ? data.Inhale.MIP : ''
            }}</span>
            <span
              v-if="data.Inhale && data.Inhale.MIP < data.Ref.MIP"
              style="color: red"
              >↓</span
            >
          </view>
          <view class="container-topList-each1">
            {{ data.Ref ? data.Ref.MIP : '' }}
          </view>
        </view>
        <view class="container-topList container-eachList">
          <view class="container-topList-each1">
            吸气峰值流量PIE
            <br />
            (L/min)
          </view>
          <view class="container-topList-each1">
            <span :style="data.Inhale.PIF < data.Ref.PIF ? 'color: red' : ''">{{
              data.Inhale ? data.Inhale.PIF : ''
            }}</span>
            <span
              v-if="data.Inhale && data.Inhale.PIF < data.Ref.PIF"
              style="color: red"
              >↓</span
            >
          </view>
          <view class="container-topList-each1">
            {{ data.Ref ? data.Ref.PIF : '' }}
          </view>
        </view>
        <view class="container-topList container-eachList">
          <view class="container-topList-each1"> 身体质量指数BMI </view>
          <view class="container-topList-each1">
            <span
              :style="
                getBMI(info.Weight, info.Height) < 18.5 ? 'color: red' : ''
              "
              >{{ getBMI(info.Weight, info.Height) }}</span
            >
            <span
              v-if="getBMI(info.Weight, info.Height) < 18.5"
              style="color: red"
              >↓</span
            >
          </view>
          <view class="container-topList-each1"> 18.5-24 </view>
        </view>
      </template>
      <template v-else>
        <view class="container-topList container-eachList">
          <view class="container-topList-each1">
            用力肺活量(呼气)FVC
            <br />
            (ml)
          </view>
          <view class="container-topList-each1">
            <span :style="data.Exhale.FVC < data.Ref.FVC ? 'color: red' : ''">{{
              data.Exhale ? data.Exhale.FVC : ''
            }}</span>
            <span
              v-if="data.Exhale && data.Exhale.FVC < data.Ref.FVC"
              style="color: red"
              >↓</span
            >
          </view>
          <view class="container-topList-each1">
            {{ data.Ref ? data.Ref.FVC : '' }}
          </view>
        </view>
        <view class="container-topList container-eachList">
          <view class="container-topList-each1">
            最大呼气压力MEP
            <br />
            (cmH2O)
          </view>
          <view class="container-topList-each1">
            <span :style="data.Exhale.MEP < data.Ref.MEP ? 'color: red' : ''">{{
              data.Exhale ? data.Exhale.MEP : ''
            }}</span>
            <span
              v-if="data.Exhale && data.Exhale.MEP < data.Ref.MEP"
              style="color: red"
              >↓</span
            >
          </view>
          <view class="container-topList-each1">
            {{ data.Ref ? data.Ref.MEP : '' }}
          </view>
        </view>
        <view class="container-topList container-eachList">
          <view class="container-topList-each1">
            呼气峰值流量PEF
            <br />
            (L/min)
          </view>
          <view class="container-topList-each1">
            <span :style="data.Exhale.PEF < data.Ref.PEF ? 'color: red' : ''">{{
              data.Exhale ? data.Exhale.PEF : ''
            }}</span>
            <span
              v-if="data.Exhale && data.Exhale.PEF < data.Ref.PEF"
              style="color: red"
              >↓</span
            >
          </view>
          <view class="container-topList-each1">
            {{ data.Ref ? data.Ref.PEF : '' }}
          </view>
        </view>
        <view class="container-topList container-eachList">
          <view class="container-topList-each1">
            一秒用力呼气容积FEV1
            <br />
            (ml)
          </view>
          <view class="container-topList-each1">
            <span
              :style="data.Exhale.FEV1 < data.Ref.FEV1 ? 'color: red' : ''"
              >{{ data.Exhale ? data.Exhale.FEV1 : '' }}</span
            >
            <span
              v-if="data.Exhale && data.Exhale.FEV1 < data.Ref.FEV1"
              style="color: red"
              >↓</span
            >
          </view>
          <view class="container-topList-each1">
            {{ data.Ref ? data.Ref.FEV1 : '' }}
          </view>
        </view>
        <view class="container-topList container-eachList">
          <view class="container-topList-each1"> 一秒率 FEV1_FVC </view>
          <view class="container-topList-each1">
            <span
              :style="
                data.Exhale.FEV1_FVC < data.Ref.FEV1_FVC ? 'color: red' : ''
              "
              >{{ data.Exhale ? data.Exhale.FEV1_FVC : '' }}</span
            >
            <span
              v-if="data.Exhale && data.Exhale.FEV1_FVC < data.Ref.FEV1_FVC"
              style="color: red"
              >↓</span
            >
          </view>
          <view class="container-topList-each1">
            {{ data.Ref ? data.Ref.FEV1_FVC : '' }}
          </view>
        </view>
        <view class="container-topList container-eachList">
          <view class="container-topList-each1"> 身体质量指数BMI </view>
          <view class="container-topList-each1">
            <span
              :style="
                getBMI(info.Weight, info.Height) < 18.5 ? 'color: red' : ''
              "
              >{{ getBMI(info.Weight, info.Height) }}</span
            >
            <span
              v-if="getBMI(info.Weight, info.Height) < 18.5"
              style="color: red"
              >↓</span
            >
          </view>
          <view class="container-topList-each1"> 18.5-24 </view>
        </view>
        <view class="container-topList container-eachList">
          <view class="container-topList-each1"> 肺活量体重指数 </view>
          <view class="container-topList-each1">
            <span
              :style="
                getFVC(data.Exhale.FVC, info.Weight) <
                (info.Gender === '男' ? 64 : 57)
                  ? 'color: red'
                  : ''
              "
              >{{ getFVC(data.Exhale.FVC, info.Weight) }}</span
            >
            <span
              v-if="
                getFVC(data.Exhale.FVC, info.Weight) <
                (info.Gender === '男' ? 64 : 57)
              "
              style="color: red"
              >↓</span
            >
          </view>
          <view class="container-topList-each1">
            <span v-if="info.Gender === '男'">>64</span>
            <span v-else>>57</span>
          </view>
        </view>
      </template>
    </template>
    <!-- 呼吸肌力报告 -->
    <template v-if="Type === 'hxjlbg'">
      <view class="container-topList">
        <view class="container-topList-each"> 指标 </view>
        <view class="container-topList-each"> BEST </view>
        <view class="container-topList-each"> %Pred </view>
        <view class="container-topList-each"> Reference </view>
      </view>
      <view
        class="container-topList container-eachList"
        v-for="i in data"
        :key="i.Id"
      >
        <view class="container-topList-each1">
          {{ i.Result[0].Parameter }}
          <br />
          ({{ i.Result[0].Unit }})
        </view>
        <view class="container-topList-each1">
          {{ i.Result ? i.Result[0].BestValue : '' }}
        </view>
        <view class="container-topList-each1">
          {{ i.Result ? i.Result[0].BestPre : '' }}
        </view>
        <view class="container-topList-each1">
          {{ i.Result ? i.Result[0].PreValue : '' }}
        </view>
      </view>
    </template>
  </view>
</template>

<script>
const app = getApp();
import {
  GetReportDetailById,
  GetPulmonaryReportByUserId,
  getReportLineChartByReportId,
} from '@/api/supplier.js';

export default {
  data() {
    return {
      info: {},
      TestExhale: [],
      TrainExhale: [],
      TrainInhale: [],
      barTitle: {
        test_inhale: '肺功能检测报告',
        test_exhale: '肺功能检测报告',
        train_inhale: '吸气训练报告',
        train_exhale: '呼气训练报告',
        airway_clearance: '气道廓清报告',
      },
      data: {},
      dataMap: {
        test_inhale: 'inhale',
        test_exhale: 'exhale',
      },
      Type: '',
      opts: {
        color: [
          '#1890FF',
          '#91CB74',
          '#FAC858',
          '#EE6666',
          '#73C0DE',
          '#3CA272',
          '#FC8452',
          '#9A60B4',
          '#ea7ccc',
        ],
        padding: [15, 10, 0, 15],
        dataLabel: false,
        dataPointShape: false,
        enableScroll: false,
        dataPointShape: true,
        legend: {},
        xAxis: {
          disableGrid: true,
          fontSize: 8,
        },
        yAxis: {
          gridType: 'dash',
          dashLength: 2,
          data: [
            {
              fontSize: 10,
            },
          ],
        },
        extra: {
          line: {
            type: 'curve',
            width: 2,
            activeType: 'solid',
            linearType: 'custom',
            onShadow: true,
            animation: 'horizontal',
          },
        },
      },
      defseries: [
        {
          name: '',
          linearColor: [
            [0, '#1890FF'],
            [0.25, '#00B5FF'],
            [0.5, '#00D1ED'],
            [0.75, '#00E6BB'],
            [1, '#90F489'],
          ],
          setShadow: [3, 8, 10, '#1890FF'],
          data: [],
        },
      ],
    };
  },
  async onLoad({ Id, Type, Types }) {
    if (!app.globalData.token) {
      await getApp().isLaunchFinish();
    }
    this.Type = Type;
    if (Id) {
      this.getDetail(Id);
    } else if (Type === 'hxjlbg') {
      const sendTypes = JSON.parse(decodeURIComponent(Types));
      this.getReportData(sendTypes);
    }
    if (
      Type === 'train_exhale' ||
      Type === 'train_inhale' ||
      Type === 'test_exhale' ||
      Type === 'test_inhale'
    ) {
      this.onGetEchartsDataList(Id);
    }
  },
  methods: {
    async onGetEchartsDataList(reportId) {
      const res = await getReportLineChartByReportId({
        reportId,
      });
      if (res.Type === 200) {
        res.Data.TrainInhale.forEach((s) => {
          s.Date = this.$dateFormat(s.Date, 'MM.DD');
        });
        res.Data.TrainExhale.forEach((s) => {
          s.Date = this.$dateFormat(s.Date, 'MM.DD');
        });
        res.Data.TestExhale.forEach((s) => {
          s.Date = this.$dateFormat(s.Date, 'MM.DD');
        });
        if (this.Type === 'train_inhale') {
          // 获取train_inhale的图表数据
          this.onGetTrainInhaleChartData(res.Data.TrainInhale.reverse());
        } else if (this.Type === 'train_exhale') {
          // 获取train_exhale的图标数据
          this.onGetTrainExhaleChartData(res.Data.TrainExhale.reverse());
        } else if (this.Type === 'test_exhale' || this.Type === 'test_inhale') {
          // 获取train_exhale的图标数据
          this.onGetTestExhaleChartData(res.Data.TestExhale.reverse());
        }
      }
    },
    onGetTestExhaleChartData(list) {
      // 获取横坐标数据
      const x_line = list.map((s) => s.Date);
      const metrics = [
        {
          key: 'FVC',
          name: '用力肺活量',
          unit: '(mL)',
        },
        {
          key: 'MEP',
          name: '最大呼（吸）气压力',
          unit: '(cmH2O)',
        },
        {
          key: 'FEV1_FVC',
          name: '一秒率',
          unit: '',
        },
        {
          key: 'PEF',
          name: '呼（吸）气峰值流量',
          unit: '(L/min)',
        },
      ];
      const chartData = metrics.map((metric) => {
        const seriesData = list.map((s) => s.Exhale[metric.key]);
        const series = JSON.parse(JSON.stringify(this.defseries));
        series[0].name = metric.key;
        series[0].data = seriesData;
        return {
          categories: x_line,
          series,
        };
      });
      this.TestExhale = chartData;
    },
    onGetTrainExhaleChartData(list) {
      // 获取横坐标数据
      const x_line = list.map((s) => s.Date);
      const metrics = [
        {
          key: 'MEP_Max',
          name: '最大呼气压',
          unit: '(cmH2O)',
        },
        {
          key: 'FVC_Avg',
          name: '平均呼气量',
          unit: '(ml)',
        },
        {
          key: 'FVC_All',
          name: '总呼气量',
          unit: '(ml)',
        },
        {
          key: 'PEF_Avg',
          name: '平均呼气流量',
          unit: '(L/min)',
        },
      ];

      const chartData = metrics.map((metric) => {
        const seriesData = list.map((s) => s.Exhale[metric.key]);
        const series = JSON.parse(JSON.stringify(this.defseries));
        series[0].name = metric.key;
        series[0].data = seriesData;
        return {
          categories: x_line,
          series,
        };
      });
      this.TrainExhale = chartData;
    },
    onGetTrainInhaleChartData(list) {
      // 获取横坐标数据
      const x_line = list.map((s) => s.Date);
      const metrics = [
        {
          key: 'MIP_Max',
          name: '最大吸气压',
          unit: '(cmH2O)',
        },
        {
          key: 'FIVC_Avg',
          name: '平均吸气量',
          unit: '(ml)',
        },
        {
          key: 'FIVC_All',
          name: '总吸气量',
          unit: '(ml)',
        },
        {
          key: 'PIF_Avg',
          name: '平均吸气流量',
          unit: '(L/min)',
        },
      ];

      const chartData = metrics.map((metric) => {
        const seriesData = list.map((s) => s.Inhale[metric.key]);
        const series = JSON.parse(JSON.stringify(this.defseries));
        series[0].name = metric.key;
        series[0].data = seriesData;
        return {
          categories: x_line,
          series,
        };
      });
      this.TrainInhale = chartData;
    },
    getBMI(Weight, Height) {
      const data = Weight / ((Height / 100) * (Height / 100));
      return data.toFixed(1);
    },
    getFVC(FVC, Weight) {
      const data = FVC / Weight;
      return data.toFixed(1);
    },
    async getDetail(id) {
      const res = await GetReportDetailById({
        id,
      });
      if (res.Type === 200) {
        uni.setNavigationBarTitle({
          title: this.barTitle[res.Data.Type],
        });
        res.Data.CreatedTime = this.$dateFormat(res.Data.CreatedTime);
        this.info = res.Data;
        this.data = res.Data.Result[0];
      }
    },
    async getReportData(sendTypes) {
      const obj = {
        Phone: app.globalData.userInfo.PhoneNumber, // app.globalData.userInfo.PhoneNumber 12369874123
        Types: sendTypes,
        PageIndex: 1,
        PageSize: 10,
      };
      if (app.globalData.userInfo.UserCertificates) {
        const IdCardNo = app.globalData.userInfo.UserCertificates.filter(
          (v) => v.CertificateType === 'idCard'
        );
        if (IdCardNo.length > 0) {
          obj.IdCardNo = IdCardNo[0].CertificateValue;
        }
      }
      const res = await GetPulmonaryReportByUserId(obj);
      if (res.Type === 200) {
        if (res.Data.Data.length > 0) {
          res.Data.Data[0].CreatedTime = this.$dateFormat(
            res.Data.Data[0].CreatedTime
          );
          this.info = res.Data.Data[0];
          this.data = res.Data.Data;
        } else {
          uni.showModal({
            title: '温馨提示',
            content: '暂无数据',
            showCancel: false,
          });
        }
      }
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  background-color: #f8f7f7;
  height: 100vh;

  &-topList {
    background: linear-gradient(180deg, #e5fff7 0%, #ffffff 100%);
    box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.1);
    border-radius: 16rpx;
    border-bottom: 2rpx solid #f4f4f4;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &-each {
      font-size: 32rpx;
      font-family:
        PingFangSC-Semibold,
        PingFang SC;
      font-weight: 600;
      color: #333333;
      text-align: center;
      width: 33.3%;
      padding: 32rpx;
    }

    &-each1 {
      font-size: 28rpx;
      font-family:
        PingFangSC-Semibold,
        PingFang SC;
      font-weight: 600;
      color: #323232;
      text-align: center;
      width: 33.3%;
      padding: 32rpx;
    }

    &-each2 {
      font-size: 28rpx;
      font-family:
        PingFangSC-Semibold,
        PingFang SC;
      font-weight: 600;
      color: #323232;
      text-align: center;
      width: 50%;
      padding: 32rpx;
    }
  }

  &-eachList {
    background: #ffffff;
    border-radius: 0;
    box-shadow: none;
  }

  &-top {
    width: calc(100% - 64rpx);
    margin: 32rpx 32rpx 24rpx 32rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;

    &-left {
      font-size: 34rpx;
      font-family:
        PingFangSC-Semibold,
        PingFang SC;
      font-weight: 600;
      color: #333333;
    }

    &-right {
      font-size: 28rpx;
      font-family:
        PingFangSC-Semibold,
        PingFang SC;
      font-weight: 600;
      color: #999999;
    }
  }

  &-indicators {
    width: calc(100% - 64rpx);
    margin: 32rpx 32rpx 24rpx 32rpx;
  }

  &-user {
    margin: 0 auto;
    width: calc(100% - 64rpx);
    padding: 32rpx;
    background: #ffffff;
    box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.1);
    border-radius: 16rpx;
    flex-wrap: wrap;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 20rpx;

    &-info {
      width: 45%;
      margin-bottom: 8rpx;

      &-left {
        font-size: 28rpx;
        font-family:
          PingFangSC-Semibold,
          PingFang SC;
        font-weight: 600;
        color: #333333;
      }

      &-right {
        font-size: 28rpx;
        font-family:
          PingFangSC-Regular,
          PingFang SC;
        font-weight: 400;
        color: #666666;
      }
    }

    &-info:last-child {
      flex-grow: 1;
    }
  }
}
</style>
