<template>
  <view class="container">
    <view class="container-box">
      <text class="container-box-title">请录入信息</text>
      <view class="display-style1" style="margin-top: 20rpx">
        <text style="margin-right: 20rpx">身份证号：</text>
        <u-input
          placeholder="请输入"
          border="surround"
          v-model="userInfo.idCard"
        ></u-input>
      </view>
      <view class="display-style1" style="margin-top: 20rpx">
        <text style="margin-right: 20rpx">学生姓名：</text>
        <u-input
          placeholder="请输入"
          border="surround"
          v-model="userInfo.name"
        ></u-input>
      </view>
    </view>
    <u-button
      type="success"
      @click="onSave"
      shape="circle"
      text="确定"
      :custom-style="btnSaveStyle"
    >
    </u-button>
  </view>
</template>

<script>
const app = getApp();
import {
  idCardGetSex,
  idCardGetBri,
  getAgeFromIDCard,
  idCardTrue,
} from '@/utils/utils.js';
import { saveUserAuthen } from '@/services/userAuthen/index.js';
export default {
  data() {
    return {
      userInfo: {
        idCard: '',
        name: '',
      },
      btnSaveStyle: {
        width: '90%',
        bottom: '30px',
        position: 'fixed',
        left: '50%',
        transform: 'translateX(-50%)',
        fontSize: '16px',
      },
    };
  },
  onLoad({ refLogin }) {
    uni.onKeyboardHeightChange(this.listener);
    const localUser = uni.getStorageSync('SGUser');
    if (localUser && !refLogin) {
      uni.redirectTo({
        url: './OptometryScreening',
      });
      return;
    }
    if (
      !localUser &&
      app.globalData.userInfo.UserCertificates &&
      app.globalData.userInfo.UserCertificates.length > 0
    ) {
      this.userInfo.idCard = app.globalData.userInfo.UserCertificates.filter(
        (v) => v.CertificateType === 'idCard'
      )[0].CertificateValue;
      this.userInfo.name = app.globalData.userInfo.Name;
      this.onSave();
    }
  },
  onUnload() {
    uni.offKeyboardHeightChange(this.listener);
  },
  methods: {
    listener(res) {
      console.log('res', res);
      if (res.height > 0) {
        this.btnSaveStyle.bottom = res.height + 'px';
      } else {
        this.btnSaveStyle.bottom = '30px';
      }
    },
    onSave() {
      if (!this.userInfo.name || !this.userInfo.idCard) {
        uni.showModal({
          content: '请输入身份证和学生姓名',
          showCancel: false,
        });
        return;
      }
      if (!idCardTrue(this.userInfo.idCard)) {
        uni.showModal({
          content: '请输入正确的身份证',
          showCancel: false,
        });
        return;
      }
      if (app.globalData.userInfo.WorkflowStatus !== 2) {
        const smQuery = {
          UserId: app.globalData.userInfo.Id,
          Name: this.userInfo.name,
          Sex: idCardGetSex(this.userInfo.idCard),
          Birthday: idCardGetBri(this.userInfo.idCard),
          Age: getAgeFromIDCard(this.userInfo.idCard),
          UserCertificates: [
            {
              CertificateType: 'idCard',
              CertificateValue: this.userInfo.idCard,
            },
          ],
        };
        saveUserAuthen(smQuery, false);
      }
      uni.setStorageSync('SGUser', this.userInfo);
      uni.redirectTo({
        url: './OptometryScreening',
      });
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  position: relative;
  height: 100vh;
  background-color: white;

  &-box {
    text-align: center;
    width: 90%;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: 200rpx auto;

    &-title {
      font-size: 40rpx;
      font-weight: 600;
      color: #323232;
    }
  }
}
</style>
