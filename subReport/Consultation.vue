<template>
  <view class="container">
    <view class="container-box">
      <image
        class="container-box-image"
        mode="aspectFit"
        show-menu-by-longpress
        :src="AssistantQrCode"
      ></image>
      <p style="font-weight: 500; color: #333333; margin-top: 56rpx">
        长按添加客服 获取详细资料
      </p>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      AssistantQrCode: '',
    };
  },
  onLoad({ AssistantQrCode }) {
    this.AssistantQrCode = AssistantQrCode;
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
.container {
  position: relative !important;

  &-box {
    width: 90%;
    height: 732rpx;
    background: #ffffff;
    border-radius: 32rpx;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    text-align: center;
    padding-top: 20rpx;

    &-image {
      margin-top: 96rpx;
      width: 440rpx;
      height: 440rpx;
    }
  }
}
</style>
