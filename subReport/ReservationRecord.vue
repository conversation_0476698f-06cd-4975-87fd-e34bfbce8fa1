<template>
  <view class="container">
    <u-list @scrolltolower="scrolltolower">
      <u-list-item v-for="item in tableData" :key="item.Id">
        <view
          class="container-box"
          :class="item.State === 1 ? '' : 'container-disable'"
        >
          <view class="container-box-top">
            <view class="container-box-top-item">
              <text class="container-box-top-item-left">日期：</text>
              <text class="container-box-top-item-text">{{
                item.ScheDate
              }}</text>
            </view>
            <span
              :style="item.State === 1 ? 'color: #29B7A3' : 'color: #CDCDCD'"
              >{{ stateList[item.State] }}</span
            >
          </view>
          <view class="container-box-mid">
            <text class="container-box-top-item-left">时段：</text>
            <text class="container-box-top-item-text"
              >{{ item.StartTime }}-{{ item.EndTime }}</text
            >
          </view>
          <view class="container-box-top">
            <view class="container-box-top-item">
              <text class="container-box-top-item-left">姓名：</text>
              <text class="container-box-top-item-text">{{ item.Name }}</text>
            </view>
            <view
              class="container-box-top-btn"
              v-if="item.State === 1"
              @click="onSubmitCancelReason(item)"
            >
              取消预约
            </view>
          </view>
        </view>
      </u-list-item>
    </u-list>
    <u-gap height="100" bgColor="#f6f6f6"></u-gap>
    <view class="btnButtomStyle" @click="onSureAppointment">预约登记</view>
  </view>
</template>

<script>
const dayjs = require('dayjs');
const app = getApp();
import { GetRecords, CancelRecord } from '@/api/appointment.js';
export default {
  data() {
    return {
      query: {
        EndDate: null,
        StartDate: null,
        PageIndex: 1,
        PageSize: 10,
        States: null,
        TimeRangeId: null,
      },
      tableData: [],
      stateList: ['有误的状态', '未签到', '已签到', '已取消'],
    };
  },
  onLoad() {
    this.onGetList();
  },
  methods: {
    onSubmitCancelReason(row) {
      uni.showModal({
        content: '是否确认取消？',
        title: '温馨提示',
        success: async (res) => {
          if (res.confirm) {
            const req = {
              Id: row.Id,
              CancelReason: '',
              Action: 1,
              UpdaterId: app.globalData.userInfo.Id,
            };
            const data = await CancelRecord(req);
            uni.showToast({
              icon: 'none',
              title: data.Content,
            });
            if (data.Type === 200) {
              this.tableData = [];
              this.onGetList();
            }
          }
        },
      });
    },
    onSureAppointment() {
      uni.navigateTo({
        url: './SGAppointment',
      });
    },
    async onGetList() {
      if (this.tableData.length && this.tableData.length % 10) return;
      this.query.UserId = app.globalData.userInfo.Id;
      uni.showLoading({
        title: this.$loadingMsg,
      });
      const res = await GetRecords(this.query);
      if (res.Type === 200) {
        res.Data.Data.forEach((s) => {
          s.ScheDate = dayjs(s.ScheDate).format('YYYY-MM-DD');
          s.StartTime = s.StartTime.split(':').slice(0, 2).join(':');
          s.EndTime = s.EndTime.split(':').slice(0, 2).join(':');
        });
        this.tableData = [...this.tableData, ...res.Data.Data];
        uni.hideLoading();
      }
    },
    scrolltolower() {
      this.query.PageIndex++;
      this.onGetList();
    },
    initData() {
      this.tableData = [];
      this.onGetList();
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding: 32rpx;

  &-box {
    height: 212rpx;
    background: #ffffff;
    box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);
    border-radius: 16rpx;
    padding: 24rpx 32rpx;
    margin-bottom: 32rpx;

    &-mid {
      margin: 16rpx 0;
    }

    &-top {
      display: flex;
      justify-content: space-between;
      align-items: center;

      &-item {
        &-left {
          font-weight: 600;
          font-size: 32rpx;
          color: #333333;
        }

        &-text {
          font-weight: 400;
          font-size: 32rpx;
          color: #333333;
        }
      }

      &-btn {
        width: 144rpx;
        height: 50rpx;
        box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);
        border-radius: 40rpx;
        border: 2rpx solid #cdcdcd;
        font-weight: 400;
        font-size: 24rpx;
        color: #676767;
        text-align: center;
        line-height: 50rpx;
      }
    }
  }

  &-disable {
    color: #cdcdcd !important;
    background: #ffffff !important;
  }
}
</style>
