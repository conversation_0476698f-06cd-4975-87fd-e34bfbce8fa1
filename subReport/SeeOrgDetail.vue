<template>
  <view class="container">
    <!-- <view class="container-top">
      <view class="container-top-title">
        <view class="container-top-title-left">
          <image
            :src="orgData.OrganizationHeadImg"
            style="width: 96rpx; height: 96rpx"
          ></image>
          <view class="container-top-title-left-top" style="flex: 1">
            <p
              style="
                font-size: 32rpx;
                color: #333333;
                font-weight: 600;
                margin-bottom: 16rpx;
              "
            >
              {{ orgData.OrganizationName }}
            </p>
            <view>
              <span style="font-size: 24rpx; font-weight: 600; color: #333333"
                >医院地址：</span
              >
              <span style="font-size: 24rpx; font-weight: 400">{{
                orgData.Address || '暂无'
              }}</span>
            </view>
            <p
              style="font-size: 24rpx; font-weight: 600; color: #333333"
              @click="callPhone"
            >
              咨询电话：<span
                style="text-decoration: underline; color: #29b7a3"
                v-if="orgData.Phone"
                >{{ orgData.Phone }}</span
              >
            </p>
          </view>
        </view>
      </view>
      <u-divider></u-divider>
      <text class="container-top-dis">{{ orgData.Introduction }}</text>
    </view>

    <view class="container-docList" v-if="docList.length > 0">
      <view class="container-docList-title">
        <text style="font-size: 32rpx; font-weight: 600; color: #333333"
          >推荐医生</text
        >
        <text
          style="font-size: 28rpx; font-weight: 600; color: #999999"
          @click="onLookMoreDocs"
          >更多</text
        >
      </view>
      <u-list customStyle="padding-bottom:0;height: inherit !important">
        <u-list-item v-for="(item, index) in docList" :key="item.id">
          <view class="box" @tap="toSeeDoc(index)" style="position: relative">
            <view class="box-top">
              <u-avatar
                :src="item.Doctor.HeadImg"
                size="60"
                class="avatar-syyle"
              ></u-avatar>
              <view class="box-top-right">
                <view class="box-top-right-top">
                  <view class="display-style" style="width: 100%">
                    <p
                      style="font-size: 18px; font-weight: 600; flex: 1"
                      class="p-style11"
                    >
                      {{ item.Doctor.Name }}
                      <span
                        style="
                          font-size: 14px;
                          color: #666666;
                          margin-left: 20upx;
                          margin-top: 4px;
                        "
                        >{{ item.Doctor.WorkerTitle }}</span
                      >
                    </p>

                    <p
                      style="color: #29b7a3; font-size: 18px; font-weight: 600"
                      v-if="item.IsEnable"
                    >
                      <span v-if="item.ShowCost" style="color: #c4c4c4">
                        <span style="text-decoration: line-through"
                          >￥{{ item.ShowCost }}</span
                        >
                        <span>/</span>
                      </span>
                      <span style="color: #29b7a3">{{
                        item.RichTextCost === 0
                          ? '免费'
                          : '￥' + item.RichTextCost
                      }}</span>
                    </p>
                  </view>
                </view>
                <view class="box-top-right-top">
                  <p style="font-size: 14px; color: #666666; width: 65%">
                    {{ item.Doctor.OrganizationName }}
                    <span style="margin-left: 20upx">{{
                      item.Doctor.DepartmentName
                    }}</span>
                  </p>
                  <view>
                    <p
                      style="color: #666666; font-size: 18px; font-weight: 600"
                      v-if="!item.IsEnable"
                    >
                      休息中
                    </p>
                  </view>
                </view>
              </view>
              <view class="box-top-img1" v-if="item.ShowCost && item.IsEnable">
                限时
              </view>
            </view>
            <image
              src="/static/images/follow.png"
              mode=""
              class="box-top-img"
              style="width: 64rpx; height: 64rpx"
              v-if="item.Doctor.Followed"
            ></image>
            <view class="box-top-right-top1">
              <p
                style="
                  font-size: 14px;
                  color: #666666;
                  overflow: hidden;
                  white-space: nowrap;
                  text-overflow: ellipsis;
                "
              >
                擅长：{{ item.Doctor.Skilled }}
              </p>
              <u-line :dashed="true" margin="10px 0 10px 0"></u-line>
              <view class="botton-style">
                <p>
                  服务患者：<span style="color: #29b7a3"
                    >{{ item.ConsultCount }}人</span
                  >
                </p>
                <p style="color: #29b7a3; font-size: 15px" v-if="item.IsEnable">
                  立即咨询>
                </p>
              </view>
            </view>
          </view>
        </u-list-item>
      </u-list>
    </view> -->
    <view class="container-introduce" v-if="orgData.Introduce">
      <u-parse :content="orgData.Introduce"></u-parse>
    </view>
  </view>
</template>

<script>
const app = getApp();
import { getOrganizationById } from '@/api/passport.js';
import { getConsortiumUserList } from '@/api/consult.js';
export default {
  data() {
    return {
      orgData: {},
      docList: [],
      depSheare: {
        OrganizationId: '',
        PageIndex: 1,
        PageSize: 2,
        ConsortiumId: '',
      },
    };
  },
  onLoad({ orgInfo, ConsortiumId }) {
    const orgData = JSON.parse(decodeURIComponent(orgInfo));
    this.orgData = orgData;
    this.depSheare.ConsortiumId = ConsortiumId;
    this.getOrgOtherDetail(orgData.OrganizationId);
    this.getFinDocList();
  },
  methods: {
    onLookMoreDocs() {
      uni.navigateTo({
        url:
          './SeeSpineDocs?orgId=' +
          this.orgData.OrganizationId +
          '&ConsortiumId=' +
          this.depSheare.ConsortiumId,
      });
    },
    callPhone() {
      uni.makePhoneCall({
        phoneNumber: this.orgData.Phone,
      });
    },
    async getOrgOtherDetail(orgId) {
      const res = await getOrganizationById(orgId);
      if (res.Type === 200) {
        this.orgData = {
          ...this.orgData,
          Address: res.Data.Address,
          Phone: res.Data.Phone,
        };
      }
    },
    //点击某个医生
    toSeeDoc(index) {
      const item = this.docList[index];
      const data = JSON.stringify(item);
      uni.navigateTo({
        url: `/subPackIndex/docDetail?docId=${item.Doctor.UserId}&needFllow=1`,
      });
    },
    async getFinDocList() {
      this.depSheare.OrganizationId = this.orgData.OrganizationId;
      let res = await getConsortiumUserList(this.depSheare);
      if (res.Type == 200 && res.Data.Data.length > 0) {
        this.docList = res.Data.Data;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  padding: 28rpx;
  background: #f8f7f7;
  position: relative;

  &-introduce {
  }

  &-docList {
    margin: 32rpx 0 32rpx 0;

    &-list {
      &-each {
        min-height: 172rpx;
        background: #ffffff;
        box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.1);
        border-radius: 24rpx;
        margin: 24rpx 4rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24rpx 32rpx;

        &-right {
          flex: 1;
          margin-left: 32rpx;

          &-title {
            color: #29b7a3;
            height: 20px;
            background: rgba(27, 188, 156, 0.2);
            border-radius: 8rpx;
            padding: 0 20rpx;
            margin-right: 10px;
          }
        }
      }
    }

    &-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;
    }
  }

  &-top {
    width: 100%;
    background: #ffffff;
    border-radius: 24rpx;
    box-shadow: 2.5px 3px 9px 0px rgba(0, 0, 0, 0.08);
    padding: 40rpx;

    &-dis {
      margin: 22rpx;
      font-size: 24rpx;
      font-weight: 400;
      color: #333333;
    }

    &-title {
      margin-bottom: 26rpx;

      &-left {
        display: flex;
        justify-content: flex-start;
        align-items: center;

        &-top {
          margin-left: 32rpx;
        }
      }
    }
  }
}

.box {
  background-color: white;
  margin-bottom: 32rpx;
  border-radius: 12upx;
  padding: 40rpx 20rpx 20rpx 20rpx;
  box-shadow: -1px -1px 4px -2px rgba(0, 0, 0, 0.3) inset;

  .box-top-img {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 99;
    transform: rotate(-90deg);
  }

  .box-top-right-top1 {
    margin-top: 20upx;

    .botton-style {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
    }
  }

  .box-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;

    .box-top-img1 {
      position: absolute;
      top: -20rpx;
      right: 0;
      z-index: 99;
      padding: 4rpx;
      background: #29b7a3;
      color: white;
      font-size: 12px;
      width: 40px;
      text-align: center;
      border-radius: 8px 8px 8px 0px;
    }

    .box-top-right {
      flex: 1;
      margin-left: 20upx;

      .box-top-right-top {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .botton-style {
          width: 138upx;
          height: 60upx;
          background-color: #29b7a3;
          font-size: 15px;
          text-align: center;
          line-height: 60upx;
          border-radius: 30px;
        }
      }
    }
  }
}
</style>
