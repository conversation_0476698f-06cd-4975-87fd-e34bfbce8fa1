<template>
  <view class="container">
    <view class="container-search">
      <!-- <u-input placeholder="请输入医院的名称" customStyle="{backgroundColor:#FAFAFA}" v-model="query.keyword">
				<u-button slot="suffix" @tap="searchHospital" type="success" icon="search"></u-button>
			</u-input> -->
      <u-search
        placeholder="请输入医院的名称"
        :showAction="true"
        v-model="query.keyword"
        actionText="搜索"
        :animation="false"
        @search="searchHospital"
        @custom="searchHospital"
      ></u-search>
    </view>
    <u-grid col="2" :border="true">
      <u-grid-item
        v-for="org in orgList"
        :key="org.Id"
        bgColor="#ffffff"
        @click="chooseOrg(org)"
        customStyle="overflow: hidden;"
      >
        <u--image
          shape="circle"
          :showLoading="true"
          :src="org.HeadImg"
          width="100px"
          height="100px"
          :customStyle="{ marginTop: '20px' }"
        ></u--image>
        <p
          style="
            height: 62px;
            margin-top: 10px;
            font-size: 16px;
            font-weight: 600;
            text-overflow: ellipsis;
            padding: 10px;
            width: 100%;
            text-align: center;
          "
        >
          {{ org.Name }}
        </p>
      </u-grid-item>
    </u-grid>
    <u-loadmore
      status="loadmore"
      loadmoreText="点我加载其他医院"
      color="#29B7A3"
      lineColor="#29B7A3"
      dashed
      line
      v-if="showMore"
      @loadmore="onLoocMoreOrg"
    />
    <u-gap height="20" bgColor="none"></u-gap>
    <u-popup
      :show="showPrivacyPopup"
      :closeOnClickOverlay="false"
      :round="10"
      mode="bottom"
      @close="closePrivacyPopup()"
    >
      <view style="padding: 40rpx">
        <p
          style="
            text-align: center;
            margin-bottom: 40rpx;
            font-size: 20px;
            font-weight: 700;
          "
        >
          提示
        </p>
        <text>
          在你使用服务之前，请仔细阅读<text
            @click="onClickPrivacyPopupTitle()"
            style="color: #29b7a3"
            >{{ PrivacyPopupTitle }}</text
          >。如果你同意{{ PrivacyPopupTitle }}，请点击“同意”开始使用。
        </text>
        <view class="display-style" style="margin-top: 40rpx">
          <!-- <u-button
            text="拒绝"
            @click="handleRefusePrivacyAuthorization()"
          ></u-button>
          <button
            id="agree-btn"
            class="btn-agree"
            open-type="agreePrivacyAuthorization"
            @agreeprivacyauthorization="handleAgreePrivacyAuthorization()"
          >
            同意
          </button> -->
          <button
            class="refuse-agree"
            @click="handleRefusePrivacyAuthorization()"
          >
            拒绝
          </button>
          <button
            id="agree-btn"
            class="btn-agree"
            open-type="agreePrivacyAuthorization"
            @agreeprivacyauthorization="handleAgreePrivacyAuthorization()"
          >
            同意
          </button>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
const app = getApp();
import { OrganizationConsortiumGet } from '@/api/passport.js';
import { getOrgListInfo1 } from '@/api/passport.js';
import { getLocaDetail } from '@/api/other.js';
export default {
  data() {
    return {
      orgList: [],
      query: {
        keyword: '',
        userId: app.globalData.userInfo.Id || '',
        region: '',
        full: false,
        consortiumOrganId: null,
      },
      consortiumQuery: {
        ConsortiumId: '',
        Keyword: '',
        PageIndex: 1,
        PageSize: 9999,
      },
      showMore: false,
      option: {},
    };
  },
  async onLoad(option) {
    this.checkUserLogin();
    this.saveWXCallBack();
    this.option = option;
    if (option.type) {
      await this.getLocalTions();
    } else if (option.consortiumId) {
      this.consortiumQuery.ConsortiumId = option.consortiumId;
      this.onGetOrgListByConsortiumId();
    } else {
      const orgId = uni.getStorageSync('chooseOrgID');
      if (!orgId) {
        setTimeout(() => {
          this.getOrgList();
        }, 500);
      } else {
        app.globalData.orgId = orgId;
        getApp().showMainPage();
      }
    }
  },
  methods: {
    async onGetOrgListByConsortiumId() {
      const res = await OrganizationConsortiumGet(this.consortiumQuery);
      if (res.Type !== 200) {
        uni.showToast({
          title: res.Content,
          icon: 'none',
        });
        return;
      }
      res.Data.Data.forEach((v) => {
        v.Name = v.OrganizationName;
        v.Id = v.OrganizationId;
        v.HeadImg = v.OrganizationHeadImg;
      });
      this.orgList = res.Data.Data;
      this.showMore = false;
      if (res.Data.Data && res.Data.Data.length > 0) {
        uni.setNavigationBarTitle({
          title: res.Data.Data[0].ConsortiumName,
        });
      }
    },
    checkUserLogin() {
      if (!app.globalData.userInfo.Id) {
        if (uni.getStorageSync('chooseOrgID')) {
          this.query.consortiumOrganId = uni.getStorageSync('chooseOrgID');
        } else {
          delete this.query.consortiumOrganId;
        }
      } else {
        delete this.query.consortiumOrganId;
      }
    },
    getLocalTions() {
      uni.getLocation({
        type: 'wgs84',
        success: (res) => {
          console.log('res', res);
          if (res.errMsg == 'getLocation:ok') {
            let prams = res.longitude + ',' + res.latitude;
            this.getLocaInfoDetail(prams);
          }
        },
        fail: (err) => {
          this.getOrgList();
        },
      });
    },
    async getLocaInfoDetail(prams) {
      let res = await getLocaDetail(prams);
      this.query.region = res.adcode.substring(0, 2);
      this.getOrgList();
    },
    async getOrgList() {
      uni.showLoading({
        title: this.$loadingMsg,
      });
      let res = await getOrgListInfo1(this.query);
      if (res.Type == 200) {
        this.orgList = res.Data.Data;
      }
      this.showMore = !this.query.full;
      this.$nextTick(() => {
        uni.hideLoading();
      });
    },
    onLoocMoreOrg() {
      this.query.full = true;
      this.getOrgList();
    },
    chooseOrg(item) {
      console.log('选择医院', item);

      const orgId = item.Id,
        orgName = item.Name;
      getApp().changeOrgAndMark({
        orgId,
        orgName,
      });

      uni.redirectTo({
        url: '/subPackIndex/seeDoc?Refresh=1',
      });
    },
    // 搜索医院
    searchHospital() {
      const isFlag = uni.$inputValueRegExp.test(this.query.keyword);
      if (!isFlag) {
        uni.showToast({
          title: this.$errorInputValue,
          icon: 'none',
        });
        return;
      }
      if (this.option.consortiumId) {
        this.consortiumQuery.Keyword = this.query.keyword;
        this.onGetOrgListByConsortiumId();
        return;
      }
      this.query.pageindex = 1;
      this.orgList = [];
      this.getOrgList();
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  .container-search {
    background-color: white;
    width: 100%;
    margin-bottom: 10px;
    padding: 10px;

    /deep/ .u-input {
      padding: 0 0 0 6px !important;
    }
  }

  /deep/ .u-grid-item {
    border-radius: 10px !important;
    margin-bottom: 8rpx;
    width: calc((100% - 16rpx) / 2) !important;
    margin-right: 8rpx;
  }
}
</style>
