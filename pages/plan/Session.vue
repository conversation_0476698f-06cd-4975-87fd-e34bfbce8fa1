<template>
  <u-loading-page :loading="loading" loading-mode="spinner"></u-loading-page>
</template>

<script>
const app = getApp();
import { getTodayTrainingProgram } from '../../api/training';
import SessionState from '../../libs/util/session_state';
import { LOGIN_FINISH } from '../../services/LoginService';
import { objNotEmpty } from '../../utils/utils';

export default {
  data() {
    return {
      loading: true,
    };
  },
  onLoad() {
    if (!getApp().isLoggedIn()) {
      console.warn('用户未登录');
      getApp().openLoginPage({
        reLaunch: true,
        redirect: `/pages/plan/Session`,
      });
      return;
    }
    console.log('当前登录用户', getApp().globalData.userInfo);
    // 获取方案，进入诊后会话页面
    this.redirectToSessionPage();
  },
  methods: {
    async redirectToSessionPage() {
      // 获取居家训练会话
      let r = await getTodayTrainingProgram(app.globalData.userInfo.Id);
      if (r.Type !== 200) {
        this.loading = false;
        // uni.showToast({
        //   title: `获取方案失败，${r.Content}`,
        //   icon: 'none'
        // });
        uni.showModal({
          title: '提示',
          content: '您现在没有康复方案,请先发起咨询',
          cancelText: '不用',
          confirmText: '好的',
          success: (res) => {
            if (res.confirm) {
              uni.navigateTo({
                url: `/subPackIndex/seeDoc?index=${0}`,
              });
            } else {
              uni.reLaunch({
                url: '/pages/index/index',
              });
            }
          },
        });
        return;
      }

      let roomId = (r.Data || {}).RoomId;
      if (!roomId) {
        this.loading = false;
        return;
      }

      var session;
      try {
        session = await SessionState.instance().findSession(roomId);
      } catch (e) {
        this.loading = false;
        uni.showToast({
          title: e,
          icon: 'none',
        });
        return;
      }

      this.loading = false;

      if (objNotEmpty(session)) {
        uni.reLaunch({
          url: '/subPackChat/sessionChatPage?roomId=' + roomId,
        });
      } else {
        uni.showToast({
          title: '未获取到会话',
          icon: 'none',
        });
      }
    },
  },
};
</script>

<style></style>
