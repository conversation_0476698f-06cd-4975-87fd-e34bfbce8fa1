<template>
  <view style="background-color: #f7f7f7">
    <!-- 温馨提示 -->
    <u-modal
      :show="alertIndex == 1"
      :closeOnClickOverlay="true"
      @close="alertIndex = 0"
      title="温馨提示"
      content="您还没有登录,请登录之后再查看"
    >
      <button type="primary" slot="confirmButton" @click="onUserLogin">
        点击登录
      </button>
    </u-modal>
    <u-modal
      :show="alertIndex == 2"
      title="温馨提示"
      content="你收到了新的康复计划，赶快执行吧！"
      showConfirmButton="true"
      showCancelButton="true"
      confirmText="立即执行"
      cancelText="暂缓"
      @confirm="onSure"
      @cancel="onCancel"
    >
    </u-modal>
    <u-modal
      :show="alertIndex == 3"
      title="方案说明"
      :content="trainingPlan.Remark"
      showConfirmButton="true"
      @confirm="alertIndex = 0"
    >
    </u-modal>

    <!-- 侧边固定Tag -->
    <view
      class="navigator-item-tag"
      hover-class="hover-class"
      style="top: 250rpx"
      @click="onOpenAllPlan"
    >
      <text>切换计划</text>
    </view>

    <!-- 联系医助 -->
    <view
      class="assistanceImage"
      v-if="
        Object.keys(trainingPlan).length > 0 &&
        Object.keys(palnAssisInfo).length > 0
      "
      @click="onOpenAssistanceCard"
    >
      <image
        src="/static/training/assistance.png"
        style="width: 144rpx; height: 168rpx"
      ></image>
      <view class="assistanceImage-text">联系医助</view>
    </view>
    <!-- 添加医助弹框 -->
    <u-overlay :mask-click-able="false" :show="showAssistantOverlay">
      <view class="overLayBox">
        <view
          style="
            margin: auto;
            display: flex;
            flex-direction: column;
            align-items: center;
          "
        >
          <view class="overLayBox-overLay">
            <text class="overLayBox-overLay-text"
              >您的居家康复方案已生成，请添加医生助手，以便居家治疗期间获取更好的服务。</text
            >
            <image
              v-if="
                palnAssisInfo.UserExternalIdentify &&
                palnAssisInfo.UserExternalIdentify.WeChatQrCode
              "
              :show-menu-by-longpress="true"
              :src="palnAssisInfo.UserExternalIdentify.WeChatQrCode"
              class="overLayBox-overLay-image"
            ></image>
            <text class="overLayBox-overLay-name">{{
              palnAssisInfo.Name
            }}</text>
            <text class="overLayBox-overLay-phone">{{
              palnAssisInfo.PhoneNumber
            }}</text>
          </view>
          <u-icon
            customStyle="margin-top: 36rpx;"
            name="close-circle"
            color="#ffffff"
            size="30"
            @click="onCloseAssistantOverlay"
          ></u-icon>
        </view>
      </view>
    </u-overlay>

    <!-- 主滚动界面 -->
    <scroll-view class="scroll-content" scroll-y="true" enable-flex="true">
      <!-- 今日训练计划 -->
      <view v-if="Object.keys(trainingPlan).length > 0">
        <!-- 卡片 -->
        <view class="card-section">
          <!-- 渐变头 -->
          <view class="card-header">
            <image
              class="card-header-icon"
              src="/static/training/training-card.png"
            />
            <text class="card-header-text">{{ trainingPlan.Name }}</text>
            <view
              class="card-header-button"
              hover-class="hover-class"
              @click="onContactDoctor"
            >
              <text>咨询指导</text>
              <u-badge
                numberType="overflow"
                :absolute="true"
                :offset="[-5, -10]"
                max="99"
                :value="unReadCount"
              >
              </u-badge>
            </view>
          </view>
          <!-- 圆环进度相关 -->
          <view class="card-bottom-view">
            <!-- 剩余天数 -->
            <view class="card-data-view">
              <text class="card-data-title">剩余天数</text>
              <text class="card-data-content"
                >{{ trainingPlan.RemainingLife }}天</text
              >
            </view>
            <!-- 圆圈 -->
            <view class="card-chart-view" @click="onOneClickClockIn">
              <view
                class="card-chart-inner-view"
                :style="{
                  'background-color': canClockIn ? '#31DBC3' : '#FFFFFF',
                }"
              >
                <text
                  :style="{
                    'color': canClockIn ? '#FFFFFF' : '#999999',
                    'font-size': '15px',
                  }"
                >
                  一键打卡
                </text>
                <text
                  class="card-chart-inner-view-subtitle"
                  :style="{ color: canClockIn ? '#FFFFFF' : '#999999' }"
                >
                  {{ completeCount }}
                  <text
                    :style="{
                      'color': canClockIn ? '#FFFFFF' : '#999999',
                      'font-size': '15px',
                    }"
                  >
                    /{{ totalCount }}
                  </text>
                </text>
              </view>
            </view>
            <!-- 计划完成度 -->
            <view class="card-data-view">
              <text class="card-data-title">计划完成度</text>
              <text class="card-data-content"
                >{{ Math.ceil(trainingPlan.FinishRate * 100) }}%</text
              >
            </view>
          </view>
        </view>
        <!-- 方案说明 -->
        <view
          class="picker-row-item"
          hover-class="hover-class"
          @click="alertIndex = 3"
        >
          <text class="picker-row-text"
            >方案说明：{{ trainingPlan.Remark }}</text
          >
          <uni-icons type="right" size="15" color="#999999"></uni-icons>
        </view>
        <!-- 辅具 -->
        <view
          style="margin: 0 32rpx 24rpx 32rpx; height: 100px"
          v-if="trainingPlan.AssistInfos && trainingPlan.AssistInfos.length > 0"
        >
          <u-image
            :showLoading="true"
            src="https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/8620426420417842176.png"
            width="100%"
            height="100px"
            @click="onOpenAssistiveDevicePage"
            mode="scaleToFill"
          />
        </view>
        <!-- 标签栏 -->
        <view class="tabs-menu-view">
          <u-subsection
            :list="tabTitles"
            mode="subsection"
            :current="currentIndex"
            activeColor="#29B7A3"
            inactiveColor="#333333"
            bgColor="#ffffff"
            fontSize="16"
            @change="onClickTab"
          />
        </view>
        <!-- 康复治疗 -->
        <view class="list-warpper" v-show="currentIndex == 0">
          <u-list
            v-if="
              communityMoItems.length > 0 ||
              trainingMoItems.length > 0 ||
              treatmentActions.length > 0
            "
          >
            <!-- 康复治疗 - 社区医嘱 -->
            <u-list-item v-for="(item, index) in communityMoItems" :key="index">
              <view
                class="list-medical-advice-item"
                @click="onOpenCommunityDetailPage(item)"
              >
                <view class="flex-between-center">
                  <text class="list-medical-advice-item-title">{{
                    item.BaseMoItemName
                  }}</text>
                  <text class="list-medical-advice-item-count"
                    >{{ item.ExecuteCount || 0
                    }}<text class="list-medical-advice-item-total"
                      >/{{ item.TotalCount || 0 }}</text
                    ></text
                  >
                </view>
                <view
                  class="flex-between-center"
                  style="margin-top: 8rpx; align-items: center"
                >
                  <text
                    class="list-medical-advice-item-content"
                    v-if="item.MoItemMethod !== 5"
                  >
                    {{
                      `${item.FreqDay}天${item.Freq}次 共${item.TotalCount}次`
                    }}
                  </text>
                  <view v-else></view>
                  <view
                    class="treatment-point-button"
                    @click.stop="onFindCommunityTreatmentPoint(item)"
                    >寻找治疗点</view
                  >
                </view>
              </view>
            </u-list-item>
            <!-- 康复治疗 - 穴位、耗材、宣教医嘱 -->
            <u-list-item v-for="(item, index) in trainingMoItems" :key="index">
              <view
                class="list-medical-advice-item"
                @click="onOpenMedicalAdviceDetailPage(item)"
              >
                <!-- 穴位、耗材显示医嘱名 -->
                <view
                  class="flex-between-center"
                  v-if="item.MoItemMethod !== 6"
                >
                  <text class="list-medical-advice-item-title">{{
                    item.MoItemName
                  }}</text>
                </view>
                <!-- 穴位-->
                <view
                  v-if="item.MoItemMethod === 1"
                  class="flex-between-center"
                  style="margin-top: 8rpx; align-items: start"
                >
                  <text class="list-medical-advice-item-content">
                    {{
                      `${item.FreqDay}天${item.Freq}次 共${item.TotalCount}次`
                    }}
                  </text>
                </view>
                <!-- 宣教 -->
                <view
                  v-if="item.MoItemMethod === 6"
                  class="flex-between-center"
                >
                  <image
                    src="https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/img-xj.png"
                    class="mission-image"
                  />
                  <text class="mission-text">{{ item.MoItemName }}</text>
                </view>
              </view>
            </u-list-item>
            <!-- 康复治疗 - 分组动作医嘱 -->
            <u-list-item
              v-for="(actionGroup, index) in treatmentActions"
              :key="index"
            >
              <view class="list-group-action-item">
                <!-- 分组头 -->
                <view
                  class="list-group-action-item-header flex-start-center"
                  @click="onClickGroupActionHeader(actionGroup.type, index)"
                >
                  <image
                    class="list-group-action-item-header-icon"
                    src="https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/static/frontend/mp-patient/action-icon.png"
                  />
                  <text class="list-group-action-item-header-title">
                    {{
                      actionGroup.type +
                      (actionGroup.data.length
                        ? '(' + actionGroup.data.length + ')'
                        : '')
                    }}
                  </text>
                  <u-icon
                    :name="actionGroup.isOpen ? 'arrow-down' : 'arrow-right'"
                    :size="20"
                  ></u-icon>
                </view>
                <!-- 该分组下的动作 -->
                <block
                  v-for="(action, actionIndex) in actionGroup.data"
                  :key="action.Id"
                  v-if="actionGroup.isOpen"
                >
                  <view
                    class="list-punch-item"
                    @click="onOpenActionDetailPage(action)"
                  >
                    <u--image
                      :src="action.ActionUnitImgURL"
                      width="120rpx"
                      height="120rpx"
                      radius="8rpx"
                    >
                    </u--image>
                    <view
                      class="flex-center-stretch-column"
                      style="flex: 1; margin-left: 32rpx"
                    >
                      <text class="list-punch-item-title">{{
                        action.Name
                      }}</text>
                      <view class="list-punch-item-chart">
                        <text class="list-punch-item-count">
                          <text
                            :class="{
                              'list-punch-item-count-finish':
                                action.TodayFinishCount > 0,
                            }"
                            >{{ action.TodayFinishCount }}</text
                          >/{{ action.TodayShouldCount }}
                        </text>
                        <!-- 进度条 -->
                        <view class="list-punch-item-chart-inactive">
                          <view
                            class="list-punch-item-chart-active"
                            :style="{
                              width:
                                (action.TodayFinishCount * 100.0) /
                                  action.TodayShouldCount +
                                '%',
                            }"
                          >
                          </view>
                        </view>
                        <view class="list-punch-item-practice">
                          <view
                            v-if="
                              action.FollowVideo &&
                              action.FollowVideo.length > 0
                            "
                            class="list-punch-item-practice-button"
                            :class="{
                              'list-punch-item-practice-button-disabled':
                                action.TodayFinishCount >=
                                action.TodayShouldCount,
                            }"
                            @click.stop="onStartFollow(index, actionIndex)"
                          >
                            开始跟练
                          </view>
                        </view>
                      </view>
                    </view>
                  </view>
                  <u-divider
                    v-if="actionIndex !== actionGroup.data.length - 1"
                  ></u-divider>
                </block>
              </view>
            </u-list-item>
          </u-list>
          <!-- 康复治疗 - 无数据 -->
          <view style="padding-bottom: 24upx" v-else>
            <u-empty
              mode="data"
              icon="http://cdn.uviewui.com/uview/empty/data.png"
              text="当前方案没有治疗医嘱"
            >
            </u-empty>
          </view>
        </view>
        <!-- 康复评定 -->
        <view class="list-warpper" v-show="currentIndex == 1">
          <!-- 康复评定 - 列表 -->
          <u-list v-if="assessList.length > 0">
            <u-list-item
              v-for="(item, index) in assessList"
              :key="item.BaseEvaluateGaugeId"
            >
              <view class="list-assess-item" @click="onOpenAssessPage(item)">
                <view class="list-assess-item-top">
                  <u--image
                    :showLoading="true"
                    src="/static/images/lb.png"
                    width="34px"
                    height="41.5px"
                  >
                  </u--image>
                  <p class="list-assess-item-top name">{{ item.Name }}</p>
                  <p
                    class="list-assess-item-top evaluated"
                    v-if="item.TotalCount > 1"
                  >
                    已评估{{ item.TotalCount }}次
                  </p>
                  <p
                    class="list-assess-item-top evaluated"
                    v-else-if="item.TotalCount == 1"
                    style="color: #29b7a3"
                  >
                    {{ item.SumPoint }}分
                  </p>
                  <p class="list-assess-item-top unevaluated" v-else>未评估</p>
                </view>
                <view class="list-assess-item-bottom">
                  <p
                    v-if="item.IsEvaluate"
                    class="list-assess-item-bottom time"
                  >
                    上次评估时间：{{ item.EvaluateTime }}
                  </p>
                  <view style="flex: 1"></view>
                  <span class="list-assess-item-bottom button">
                    {{ item.IsEvaluate ? '重新评测>' : '立即测评>' }}
                  </span>
                </view>
              </view>
            </u-list-item>
          </u-list>
          <!-- 康复评定 - 无数据 -->
          <view style="padding-bottom: 24rpx" v-else>
            <u-empty
              mode="data"
              icon="http://cdn.uviewui.com/uview/empty/data.png"
              text="当前方案没有康复评定"
            >
            </u-empty>
          </view>
        </view>
      </view>
      <!-- 无进行中计划 -->
      <view style="margin-bottom: 32rpx" v-else>
        <u-empty
          mode="data"
          icon="http://cdn.uviewui.com/uview/empty/data.png"
          text="当前暂无进行中的康复计划"
        >
        </u-empty>
      </view>

      <!-- 可执行方案列表 -->
      <view class="list-warpper" v-if="availableTrainingPlanList.length > 0">
        <view class="list-plan-title" style="color: #29b7a3">可执行计划</view>
        <u-list>
          <u-list-item
            v-for="(item, index) in availableTrainingPlanList"
            :key="index"
          >
            <view class="list-plan-item" @click="onSwitchPlan(item.Id)">
              <view class="list-plan-item-top">
                <u--image
                  :src="item.ActionUnitImgURL"
                  width="120rpx"
                  height="120rpx"
                  radius="8rpx"
                />
                <view class="list-plan-item-top-middle-view" style="flex: 1">
                  <text class="list-plan-item-top-title">{{ item.Name }}</text>
                  <text class="list-plan-item-top-content">{{
                    item.CreatorName + '  ' + item.StartTime
                  }}</text>
                </view>
                <view class="button-submit">切换计划</view>
              </view>
              <view class="list-plan-chart">
                <view class="list-plan-chart-title">
                  <text>剩余{{ item.RemainingLife }}天</text>
                  <text>{{ (item.FinishRate * 100).toFixed(2) }}%</text>
                </view>
                <view
                  class="list-plan-chart-inactive"
                  style="margin-top: 10upx"
                >
                  <view
                    class="list-plan-chart-active"
                    :style="{ width: item.FinishRate * 100 + '%' }"
                  />
                </view>
              </view>
            </view>
          </u-list-item>
        </u-list>
      </view>

      <!-- 已失效计划列表 -->
      <view class="list-warpper" v-if="unavailableTrainingPlanList.length > 0">
        <view class="list-plan-title" style="color: #ff3b30">已失效计划</view>
        <u-list>
          <u-list-item
            v-for="(item, index) in unavailableTrainingPlanList"
            :key="index"
          >
            <view class="list-plan-item" @click="onCheckPlanDetail(item.Id)">
              <view class="list-plan-item-top">
                <u--image
                  :src="item.ActionUnitImgURL"
                  width="120rpx"
                  height="120rpx"
                  radius="8rpx"
                />
                <view class="list-plan-item-top-middle-view" style="flex: 1">
                  <text
                    class="list-plan-item-top-title"
                    style="color: #cccccc"
                    >{{ item.Name }}</text
                  >
                  <text class="list-plan-item-top-content">{{
                    item.CreatorName + '  ' + item.StartTime
                  }}</text>
                </view>
                <view
                  class="button-outside"
                  @click.stop="onToEvaluation(item.Id)"
                  v-if="!item.IsEva"
                >
                  评价
                </view>
              </view>
              <view class="list-plan-chart">
                <view class="list-plan-chart-title" style="color: #cccccc">
                  <text>剩余{{ item.RemainingLife }}天</text>
                  <text>{{ (item.FinishRate * 100).toFixed(2) }}%</text>
                </view>
                <view
                  class="list-plan-chart-inactive"
                  style="margin-top: 10upx"
                >
                  <view
                    class="list-plan-chart-active"
                    :style="{ width: item.FinishRate * 100 + '%' }"
                  >
                  </view>
                </view>
              </view>
            </view>
          </u-list-item>
        </u-list>
      </view>

      <!-- 上拉提示尾部 -->
      <view class="pull-foot-view" v-if="footerState == 2 || footerState == 3">
        {{ footerState == 2 ? '正在加载中...' : '没有更多数据了' }}
      </view>
    </scroll-view>
  </view>
</template>

<script>
import {
  getTodayTrainingProgram,
  startTrainingProgram,
  homeTrainingActionPunchCard,
  getAllTrainingPlanList,
  actionsOneClickIn,
} from '../../api/training.js';
import StoreKeys from '../../utils/storeKeys.js';
import {
  arrayNotEmpty,
  dataIsValid,
  numberValid,
  objNotEmpty,
  stringNotEmpty,
  ItemGroupBy,
} from '../../utils/utils.js';
import {
  AssessClientEvent,
  PrescriptionClientEvent,
  SessionClientEvent,
  TrainingClientEvent,
} from '../../utils/eventKeys';
import { IMApi, IMManager } from 'kfx-im';
import { getGaugeList } from '../../subGauge/api';
import SessionState from '../../libs/util/session_state';
import { dateFormat } from '../../utils/validate';
import { LOGIN_FINISH } from '../../services/LoginService';

const kDebugEnable = false;
const app = getApp();
export default {
  data() {
    return {
      // 0:关闭弹出 1:未登录 2:新训练方案 3:方案说明
      alertIndex: 0,
      // 尾部显示状态（0：无状态 1：正在加载更多 2：没有更多数据）
      footerState: 0,

      // 医助信息
      palnAssisInfo: {},
      // 是否展示医助信息
      showAssistantOverlay: false,

      // 未读消息
      unReadCount: 0,

      // 当前选中的tab标签下标
      currentIndex: 0,
      tabTitles: ['康复治疗', '康复评定'],

      // 有新的未开始的训练方案
      newTrainingPlan: {},

      // 当前训练方案
      trainingPlan: {},
      // 社区医嘱 - 普通、穴位、耗材
      communityMoItems: [],
      // 康复治疗 - 穴位、耗材、宣教医嘱
      trainingMoItems: [],
      // 康复治疗 - 分组动作
      treatmentActions: [],
      // 康复治疗 - 评估
      assessList: [],

      // 可执行方案(有效的)
      availableTrainingPlanList: [],
      // 已失效方案
      unavailableTrainingPlanList: [],

      // 当前方案是否可以一键打卡
      canClockIn: false,
    };
  },

  computed: {
    // 圆圈数据 - 已打卡次数
    completeCount() {
      return numberValid(this.trainingPlan.TodayFinishCount);
    },

    // 圆圈数据 - 需要打卡总次数
    totalCount() {
      return numberValid(this.trainingPlan.TodayShouldCount);
    },
  },

  created() {
    // 非响应式数据，挂载到 Vue 组件实例上
    // 官网性能优化数据更新👇🏻
    // https://uniapp.dcloud.net.cn/tutorial/performance.html#%E4%BC%98%E5%8C%96%E6%95%B0%E6%8D%AE%E6%9B%B4%E6%96%B0
    this.nonReactiveData = {
      // 康复治疗 - 动作 - 收起分组标题
      collapseGroupActionTypeList: [],

      /**
       * 是否需要在页面显示时候，刷新今日训练数据
       *
       * 1、动作详情 - 特殊打卡成功
       * 2、动作详情 - 普通打卡成功
       */
      needRefreshTodayTraining: false,

      /**
       * 是否需要网络请求会话未读数
       *
       * 初始化时候需要通过网络请求来同步消息未读数，之后医生端发送消息，
       * 未读数变化，IMManager 内部会自动缓存本 session，不需要再网络请求
       */
      needRequestUnreadCount: true,

      // 是否切换Tab，刷新数据
      needRefreshDataWhenSwitchTab: false,

      pageIndex: 1,
      haveMore: true,
    };
  },

  async onLoad(option) {
    // 是否开启自定义分享
    this.isdefalut = true;
    this.share.title = '康复路上，与您同行！';
    this.listenEvents();
    await this.loadNewData();

    // 此处标记，避免初始化切换，重复请求数据
    this.nonReactiveData.needRefreshDataWhenSwitchTab = true;
  },

  async onShow() {
    if (this.nonReactiveData.needRefreshTodayTraining) {
      kDebugEnable && console.debug('今日训练 - onShow - 刷新今日训练数据');
      this.nonReactiveData.needRefreshTodayTraining = false;
      const r = await this.loadTrainingPlan();
      if (r.Type != 200) {
        uni.showToast({
          title: r.Content,
          icon: 'none',
        });
      }
    }
  },

  onHide() {
    this.saveCollapseGroups();
  },

  onUnload() {
    kDebugEnable && console.debug('今日训练 - onUnload');

    uni.$off(LOGIN_FINISH, this.loginFunction);
    uni.$off(TrainingClientEvent.switchTrainingPlan, this.switchPlanFunction);
    uni.$off(
      TrainingClientEvent.trainingPlanChanged,
      this.trainingPlanChangedFunction
    );
    uni.$off(
      PrescriptionClientEvent.payTreatSuccess,
      this.treatmentOrderPaySuccessHandler
    );
    uni.$off(
      TrainingClientEvent.punchSuccess,
      this.trainingPunchSuccessFunction
    );
    uni.$off(
      SessionClientEvent.sessionListChanged,
      this.sessionListChangedHandler
    );
    uni.$off(AssessClientEvent.updateAssess, this.updateAssessSuccessFunction);
  },

  onPullDownRefresh: async function () {
    await this.loadNewData();
    uni.stopPullDownRefresh();
  },

  onReachBottom: async function () {
    if (!this.nonReactiveData.haveMore) return;
    await this.loadMoreData();
  },

  onTabItemTap(item) {
    kDebugEnable && console.debug('切换Tab', item);
    if (!this.nonReactiveData.needRefreshDataWhenSwitchTab) return;

    // 备注：前线反馈，偶现无方案情况
    // 由于执行方案逻辑变化较大，暂不知由于哪里未通知本页面进行刷新，或者获取刷新后，本页未显示，导致刷新无效
    // 排查阻力较大，暂无法解决，故临时处理，每次进入本页就刷新一次
    this.loadNewData();
  },

  methods: {
    // 关闭医助弹框
    onCloseAssistantOverlay() {
      this.showAssistantOverlay = false;

      // 检查是否需要弹出完善用户信息弹窗
      this.checkNeedShowUserUpdate();
    },

    // 点击打开医助信息
    onOpenAssistanceCard() {
      this.showAssistantOverlay = true;
    },

    // 点击去登录
    onUserLogin() {
      getApp().openLoginPage();
    },

    // 打开全部方案
    onOpenAllPlan() {
      uni.navigateTo({
        url: '/subPackTraining/trainingPlanListPage',
      });
    },

    // 打开社区医嘱详情
    onOpenCommunityDetailPage(item) {
      if (!item.Id) {
        uni.showToast({
          title: '医嘱id为空',
          icon: 'none',
        });
        return;
      }

      uni.navigateTo({
        url:
          '/subPackIndex/community/detail?id=' +
          item.Id +
          '&moItmeId=' +
          item.BaseMoItemId,
      });
    },

    // 社区医嘱 - 寻找治疗点
    onFindCommunityTreatmentPoint(item) {
      uni.navigateTo({
        url:
          '/subPackIndex/community/viewCommunity?moItemId=' + item.BaseMoItemId,
      });
    },

    // 打开居家治疗医嘱（穴位、耗材、宣教）详情
    onOpenMedicalAdviceDetailPage(item) {
      let data = JSON.stringify(item);
      uni.navigateTo({
        url: '/subPackTraining/trainingMedicalAdviceDetailPage',
        success: (res) => {
          res.eventChannel.emit(
            'indexSendDataToTrainingMedicalAdviceDetail',
            data
          );
        },
      });
    },

    // 打开动作详情
    onOpenActionDetailPage(item) {
      const actionsList = this.treatmentActions.map((s) => s.data).flat();
      let actions = item.Type == 0 ? actionsList : this.dailyActions;
      let index = actions.findIndex(
        (element) => element.Id == item.Id && stringNotEmpty(element.Id)
      );
      uni.navigateTo({
        url: `/subPackTraining/actionDetailPage?index=${index}&disabled=false&trainingPlanId=${this.trainingPlan.Id}`,
      });
    },

    // 点击开始跟练
    onStartFollow(index, actionIndex) {
      if (!this.trainingPlan.Id) {
        uni.showToast({
          title: '方案id为空',
          icon: 'none',
        });
        return;
      }

      const videoUrls = [];
      let currentVideoIndex = -1; // 当前准备跟练的视频，在轮播列表中的索引

      const action = this.treatmentActions[index].data[actionIndex];
      if (action.TodayFinishCount >= action.TodayShouldCount) {
        // 无打卡次数: 只看该动作视频，无自动打卡
        videoUrls.push(
          ...action.FollowVideo.map((url) => {
            return {
              actionId: action.Id,
              valid: false,
              url: url,
            };
          })
        );
        currentVideoIndex = 0;
      } else {
        // 有打卡次数: 则遍历当前分组动作，获取可打卡的视频，并找到当前视频，在跟练轮播列表中的索引
        // 遍历分组动作组
        this.treatmentActions[index].data.forEach((action, actionDataIndex) => {
          // 检查是否有剩余打卡次数且存在跟练视频
          if (
            (action.TodayFinishCount || 0) < (action.TodayShouldCount || 0) &&
            arrayNotEmpty(action.FollowVideo)
          ) {
            // 将视频添加到数组
            videoUrls.push(
              ...action.FollowVideo.map((url) => {
                return {
                  actionId: action.Id,
                  valid: true,
                  url: url,
                };
              })
            );

            // 如果当前是目标动作，记录其视频在新数组中的起始位置
            if (actionDataIndex === actionIndex) {
              currentVideoIndex = videoUrls.length - action.FollowVideo.length;
            }
          }
        });
      }

      if (videoUrls.length === 0) {
        uni.showToast({
          title: '暂无跟练视频',
          icon: 'none',
        });
        return;
      }

      uni.navigateTo({
        url:
          '/subPackTraining/followVideoPage' +
          `?videoUrls=${encodeURIComponent(JSON.stringify(videoUrls))}` +
          `&index=${currentVideoIndex}` +
          `&trainingPlanId=${this.trainingPlan.Id}`,
      });
    },

    // 点击分组动作头部进行展开/收起
    onClickGroupActionHeader(type, index) {
      let collapseTypeList = this.nonReactiveData.collapseGroupActionTypeList;
      if (collapseTypeList.includes(type)) {
        this.nonReactiveData.collapseGroupActionTypeList =
          collapseTypeList.filter((s) => s !== type);
      } else {
        this.nonReactiveData.collapseGroupActionTypeList.push(type);
      }
      kDebugEnable &&
        console.debug(
          '点击展开/收起动作分组头',
          type,
          this.nonReactiveData.collapseGroupActionTypeList
        );
      this.treatmentActions[index].isOpen =
        !this.treatmentActions[index].isOpen;
    },

    // 打开自主评估页面
    onOpenAssessPage(item) {
      let url = '';
      let roomId = this.trainingPlan.RoomId || '';
      if (item.TotalCount > 0) {
        // url = `/subGauge/result?itemInfo=${encodeURIComponent(
        //   JSON.stringify(item)
        // )}&roomType=1&roomId=${roomId}`;
        url = `/subGauge/gaugeDetailList?disabled=false&patGaugeId=${item.EvaluateGaugeId}&DctSendSign=${item.DctSendSign}&RelatedId=${item.RelatedId}&id=${item.BaseEvaluateGaugeId}&Source=${item.Source}`;
      } else {
        url = `/subGauge/autonomy?id=${item.BaseEvaluateGaugeId}&DctSendSign=${item.DctSendSign}&roomType=1&roomId=${roomId}&RelatedId=${item.RelatedId}`;
      }

      uni.navigateTo({
        url: url,
      });
    },

    // 打开辅具页面
    onOpenAssistiveDevicePage() {
      const info = this.trainingPlan.AssistInfos;
      uni.navigateTo({
        url:
          '/subAssistiveDevice/chooseAssistive?info=' +
          encodeURIComponent(JSON.stringify(info)) +
          '&TrainingActionId=' +
          this.trainingPlan.TrainingActionId,
      });
    },

    // 点击咨询指导
    async onContactDoctor(e) {
      // 获取居家训练会话
      uni.showLoading({
        title: '正在打开...',
        mask: true,
      });
      let roomId = this.trainingPlan.RoomId;
      var session;
      try {
        session = await SessionState.instance().findSession(roomId);
        uni.hideLoading();
      } catch (e) {
        uni.hideLoading();
        uni.showToast({
          title: e,
          icon: 'none',
        });
        return;
      }

      if (objNotEmpty(session)) {
        uni.navigateTo({
          url:
            '/subPackChat/sessionChatPage?roomId=' +
            roomId +
            '&programId=' +
            this.trainingPlan.Id,
        });
      } else {
        uni.showToast({
          title: '未获取到会话',
          icon: 'none',
        });
      }
    },

    // 点击选择标签栏
    onClickTab(index) {
      this.currentIndex = index;
    },

    // 点击一键打卡
    async onOneClickClockIn() {
      if (!this.canClockIn) return;

      uni.showLoading({
        mask: true,
      });
      let r = await this.loadOneClickIn();
      uni.hideLoading();
      if (r) {
        // 打卡成功刷新数据
        this.loadTrainingPlan();
      }
    },

    // 点击切换计划
    async onSwitchPlan(trainingId) {
      uni.showLoading({
        title: '正在切换...',
        mask: true,
      });
      let r = await this.loadStartTraining(trainingId);
      uni.hideLoading();
      if (r.Type == 200) {
        this.loadNewData();
      }
    },

    // 点击查看计划详情
    onCheckPlanDetail(trainingId) {
      uni.navigateTo({
        url: `/subPackTraining/trainingPlanDetailPage?trainingPlanId=${trainingId}`,
      });
    },
    // 已失效的计划可以去评价
    onToEvaluation(Id) {
      console.log('计划的Id', Id);
      uni.navigateTo({
        url: '/subServices/planIndex?Id=' + Id,
      });
    },

    // 温馨提示确认
    async onSure(e) {
      let type = this.alertIndex;
      this.alertIndex = 0;
      switch (type) {
        case 2:
          {
            // 新训练方案
            uni.setStorage({
              key: StoreKeys.ignoreTrainingId,
              data: this.newTrainingPlan.Id,
            });
            this.onSwitchPlan(this.newTrainingPlan.Id);
          }
          break;
        default:
          break;
      }
    },

    // 温馨提示取消
    onCancel(e) {
      let type = this.alertIndex;
      this.alertIndex = 0;

      switch (type) {
        case 2:
          {
            // 暂缓新训练方案
            uni.setStorage({
              key: StoreKeys.ignoreTrainingId,
              data: this.newTrainingPlan.Id,
              success: function () {},
              fail: function () {
                uni.showToast({
                  title: '计划暂缓失败',
                  icon: 'none',
                });
              },
            });
          }
          break;
        default:
          break;
      }
    },

    // 微信一件登录
    async onWeChatLogin(e) {
      const r = await getApp().loginInWithWX(e);
      if (r) {
        this.alertIndex = 0;
        this.loadNewData();
      }
    },

    // ------------------------- 其他操作 -------------------------

    // 监听事件
    listenEvents() {
      let that = this;
      // 登录成功
      this.loginFunction = function (e) {
        that.alertIndex = 0;
        that.loadNewData();
      };
      uni.$on(LOGIN_FINISH, this.loginFunction);

      // 切换方案
      this.switchPlanFunction = function (e) {
        kDebugEnable && console.debug('切换方案事件', e);
        that.loadNewData();
      };
      uni.$on(TrainingClientEvent.switchTrainingPlan, this.switchPlanFunction);

      // 调整了训练方案
      this.trainingPlanChangedFunction = function (e) {
        kDebugEnable && console.debug('调整了训练方案事件', e);
        that.loadNewData();
      };
      uni.$on(
        TrainingClientEvent.trainingPlanChanged,
        this.trainingPlanChangedFunction
      );

      // 治疗订单支付成功
      this.treatmentOrderPaySuccessHandler = function (e) {
        kDebugEnable && console.debug('治疗订单支付成功事件', e);
        that.loadNewData();
      };
      uni.$on(
        PrescriptionClientEvent.payTreatSuccess,
        this.treatmentOrderPaySuccessHandler
      );

      /**
       * 打卡成功
       *
       * 触发场景：
       * 1. 非跟练视频 - 播放自动打卡
       * 2. 上传图片/视频 - 特殊打卡
       * 3. 手动打卡
       * 4. 跟练视频 - 跟练视频完成后批量打发
       */
      this.trainingPunchSuccessFunction = async function (e) {
        kDebugEnable && console.debug('打卡成功事件', e);
        that.nonReactiveData.needRefreshTodayTraining = true;
      };
      uni.$on(
        TrainingClientEvent.punchSuccess,
        this.trainingPunchSuccessFunction
      );

      // 获取指导会话未读消息数量
      this.sessionListChangedHandler = (sessions) => {
        that.refreshUnReadCount();
      };
      uni.$on(
        SessionClientEvent.sessionListChanged,
        this.sessionListChangedHandler
      );

      // 提交量表成功
      this.updateAssessSuccessFunction = async function (e) {
        let r = await that.loadAssessList();
        if (r.Type != 200) {
          uni.showToast({
            title: r.Content,
            icon: 'none',
          });
        }
      };
      uni.$on(AssessClientEvent.updateAssess, this.updateAssessSuccessFunction);
    },

    // 检查是否需要弹出完善用户信息弹窗
    // 没有Name名字，并且当次打开小程序是否已经弹过一次
    checkNeedShowUserUpdate() {
      const isOpenDialog = uni.getStorageSync(StoreKeys.showPerfectUserInfo)
        ? Boolean(uni.getStorageSync(StoreKeys.showPerfectUserInfo))
        : false;
      if (!app.globalData.userInfo.Name && !isOpenDialog) {
        this.handleShowUserUpdate();
      }
    },

    // 弹出完善用户信息的弹窗
    handleShowUserUpdate() {
      uni.setStorageSync(StoreKeys.showPerfectUserInfo, true);
      uni.showModal({
        title: '温馨提示',
        content: '为了更好地为您提供服务，请您先完善个人信息',
        success: (res) => {
          if (res.confirm) {
            uni.navigateTo({
              url: `/subPrescription/userUpdate?OrganizationId=${this.trainingPlan.OrganizationId}&Id=${this.trainingPlan.VisitId}&DepartmentName=${this.trainingPlan.DeptName}&OrganizationName=${this.trainingPlan.OrganizationName}&DoctorName=${this.trainingPlan.CreatorName}&DepartmentId=${this.trainingPlan.DeptId}&Scene=planUpdateUser`,
            });
          }
        },
      });
    },

    // ------------------------- 数据刷新相关 -------------------------

    // 获取userId
    getUserId() {
      let userId = app.globalData.userInfo.Id;
      if (!stringNotEmpty(userId)) {
        this.alertIndex = 1;
        return null;
      }
      return userId;
    },

    // 检查是否有新计划
    async checkNewTrainingPlan() {
      // 筛选未执行的方案
      let unexecutedPlans = this.availableTrainingPlanList.filter(
        (e) => e.State == 0
      );
      if (!arrayNotEmpty(unexecutedPlans)) {
        this.newTrainingPlan = {};
        return;
      }

      // 检查是否有新方案
      var newTrainingPlan = {};

      // 被忽略的计划id
      let ignoreTrainingId = uni.getStorageSync(StoreKeys.ignoreTrainingId);

      // 如果当前没有正在进行中的方案，则直接取第一个方案，如果该方案没有被忽略，则作为最新方案，进行提示
      if (
        !objNotEmpty(this.trainingPlan) &&
        ignoreTrainingId != unexecutedPlans[0].Id
      ) {
        newTrainingPlan = unexecutedPlans[0];
      }

      // 获取比当前训练方案时间还新的未执行的方案，如果该方案没有被忽略，则作为最新方案，进行提示
      if (
        Date.parse(this.trainingPlan.StartTime) <
          Date.parse(unexecutedPlans[0].StartTime) &&
        ignoreTrainingId != unexecutedPlans[0].Id
      ) {
        newTrainingPlan = unexecutedPlans[0];
      }

      this.newTrainingPlan = newTrainingPlan;
      if (objNotEmpty(this.newTrainingPlan)) {
        this.alertIndex = 2;
      }
    },

    // 保存收起的动作分组头
    saveCollapseGroups() {
      kDebugEnable &&
        console.debug(
          '今日训练 - onHide',
          this.nonReactiveData.collapseGroupActionTypeList
        );

      // 本地存储展开的分组头
      uni.setStorageSync(
        StoreKeys.collapseGroupActionTypes,
        this.nonReactiveData.collapseGroupActionTypeList
      );
    },

    // 刷新消息未读数据量
    refreshUnReadCount() {
      if (!dataIsValid(this.trainingPlan)) {
        this.unReadCount = 0;
        return;
      }

      let roomId = this.trainingPlan.RoomId;
      let session = IMManager.instance
        .getSessionList()
        .find((e) => e.sessionId == roomId && roomId);
      let that = this;
      if (session) {
        // 直接从会话列表中获取
        if (
          dataIsValid(session.unreadCount) &&
          this.unReadCount != session.unreadCount
        ) {
          this.unReadCount = session.unreadCount;
        }
      } else {
        // 未找到对应会话，直接请求未读数量
        if (!this.nonReactiveData.needRequestUnreadCount) return;
        this.loadUnreadCount();
      }
    },

    // 刷新圆圈状态
    refreshCircleState() {
      // 是否可以一键打卡
      this.canClockIn = this.treatmentActions.some((group) => {
        const r = group.data.some(
          (action) =>
            dataIsValid(action.TodayFinishCount) &&
            dataIsValid(action.TodayShouldCount) &&
            action.TodayFinishCount < action.TodayShouldCount
        );

        return r;
      });
    },

    // ------------------------- 网络请求相关 -------------------------

    // 重新加载数据
    async loadNewData() {
      kDebugEnable && console.debug('刷新 - 重新加载新数据');
      let userId = this.getUserId();
      if (userId == null) {
        console.warn('今日训练 - 刷新：未获取到患者UserId');
        uni.stopPullDownRefresh();
        return;
      }

      uni.showLoading({
        title: '正在刷新...',
        mask: true,
      });

      this.nonReactiveData.pageIndex = 1;
      this.nonReactiveData.haveMore = true;
      this.trainingPlan = {};
      this.newTrainingPlan = {};
      this.availableTrainingPlanList = [];
      this.unavailableTrainingPlanList = [];
      let rs = await Promise.all([
        this.loadTrainingPlan(),
        this.loadTrainingList(),
      ]);
      const fail = rs.find((r) => r.Type != 200);
      uni.hideLoading();
      if (fail) {
        uni.showToast({
          title: fail.Content,
          icon: 'none',
        });
        console.warn('今日训练 - 刷新：请求失败', fail.Content);
        return;
      }

      if (objNotEmpty(this.trainingPlan)) {
        // const params = uni.getEnterOptionsSync()
        // console.log("params", params)
        this.loadAssessList().then((r) => {
          if (r.Type != 200) {
            uni.showToast({
              title: r.Content,
              icon: 'none',
            });
          }
        });
      }
      this.checkNewTrainingPlan();
    },

    // 加载更多数据
    async loadMoreData() {
      kDebugEnable && console.debug('刷新 - 更多数据');
      let userId = this.getUserId();
      if (userId == null) return;
      if (!this.nonReactiveData.haveMore) return;

      uni.showLoading({
        title: '正在加载...',
        mask: true,
      });
      this.footerState = 2;
      this.nonReactiveData.pageIndex++;
      let r = await this.loadTrainingList();
      uni.hideLoading();
      if (r.Type != 200) {
        uni.showToast({
          title: r.Content,
          icon: 'none',
        });
      }
    },

    // 获取今日训练方案
    async loadTrainingPlan() {
      const userId = this.getUserId();
      let r = await getTodayTrainingProgram(userId);
      if (r.Type != 200) return r;

      // 医嘱类型（0:普通医嘱 1:穴位 2:咨询 3:评定 5:耗材 6:宣教）
      const trainingPlan = r.Data;

      // 社区医嘱，显示普通、穴位、耗材医嘱
      if (arrayNotEmpty(trainingPlan.CommunityMoItems)) {
        this.communityMoItems =
          trainingPlan.CommunityMoItems.filter(
            (e) =>
              e.MoItemMethod == 0 || e.MoItemMethod == 1 || e.MoItemMethod == 5
          ) || [];
      } else {
        this.communityMoItems = [];
      }

      // 居家医嘱，训练动作分组展示
      if (arrayNotEmpty(trainingPlan.TrainingActions)) {
        const actionsList = ItemGroupBy(trainingPlan.TrainingActions, 'Group');
        const collapseGroupActionTypes = uni.getStorageSync(
          StoreKeys.collapseGroupActionTypes
        );
        this.nonReactiveData.collapseGroupActionTypeList =
          collapseGroupActionTypes || [];
        kDebugEnable &&
          console.debug(
            '收起的分组名',
            this.nonReactiveData.collapseGroupActionTypeList
          );

        actionsList.forEach((s, index) => {
          s.type = s.type || '指导资料' + (index + 1);
          s.isOpen = !this.nonReactiveData.collapseGroupActionTypeList.includes(
            s.type
          );
        });
        this.treatmentActions = actionsList;
      } else {
        this.treatmentActions = [];
      }

      // 居家治疗性医嘱，显示穴位、耗材、宣教
      if (arrayNotEmpty(trainingPlan.TrainingMoItems)) {
        this.trainingMoItems =
          trainingPlan.TrainingMoItems.filter(
            (e) =>
              e.MoItemMethod == 1 || e.MoItemMethod == 5 || e.MoItemMethod == 6
          ) || [];
      } else {
        this.trainingMoItems = [];
      }

      this.trainingPlan = trainingPlan;
      this.refreshCircleState();
      this.refreshUnReadCount();

      // 赋值给医助信息
      this.palnAssisInfo = trainingPlan.Assist || {};
      // 检查是否需要弹出弹框
      let showAssistantMap =
        uni.getStorageSync(StoreKeys.showAssistantMap) || {};
      kDebugEnable && console.debug('showAssistantMap', showAssistantMap);

      if (
        !showAssistantMap[trainingPlan.Id] &&
        Object.keys(this.palnAssisInfo).length > 0
      ) {
        // 先弹出医助弹框，再弹出完善用户信息弹框
        showAssistantMap[trainingPlan.Id] = true;
        uni.setStorageSync(StoreKeys.showAssistantMap, showAssistantMap);
        this.showAssistantOverlay = true;
      } else {
        this.showAssistantOverlay = false;

        // 检查是否弹出完善用户信息弹框
        this.checkNeedShowUserUpdate();
      }

      return r;
    },

    // 获取自主评估列表
    async loadAssessList() {
      if (!this.trainingPlan || !this.trainingPlan.Id) {
        return {
          Type: 400,
          Data: null,
          Content: '未获取到方案id',
        };
      }

      this.assessList = [];
      let userId = this.getUserId();
      let params = {
        patId: userId,
        programId: this.trainingPlan.Id,
        isLoadDoctor: false,
      };
      let r = await getGaugeList(params);
      if (r.Type == 200) {
        var assessMap = {};
        // 将同一量表放入一个数组
        for (var element of r.Data) {
          var list = assessMap[element.BaseEvaluateGaugeId] || [];
          list.push(element);
          assessMap[element.BaseEvaluateGaugeId] = list;
        }

        Object.values(assessMap).forEach((list) => {
          // 筛选已评估量表
          var evaluatedList = list.filter((e) => {
            return e.IsEvaluate == true;
          });
          // 量表按提交时间降序排列
          evaluatedList.sort((a, b) => {
            return b.EvaluateTime < a.EvaluateTime ? -1 : 1;
          });

          // 如果有提交记录，则取最新一次提交数据用作展示
          // 如果没有提交过，则取返回的第一条数据
          var assess = evaluatedList[0] || list[0];
          assess.TotalCount = evaluatedList.length;
          assess.EvaluateTime = this.$dateFormat(
            assess.EvaluateTime,
            'YYYY-MM-DD'
          );
          this.assessList.push(assess);
        });
      }

      return r;
    },

    // 获取全部训练方案
    async loadTrainingList() {
      const userId = this.getUserId();
      let r = await getAllTrainingPlanList(
        userId,
        this.nonReactiveData.pageIndex
      );
      console.debug('获取训练计划方案列表', r.Data);
      if (r.Type == 200 && r.Data) {
        const total = r.Data.Total;

        // 过滤当前执行中方案
        let allTrainingPlanList = r.Data.Data.filter((e) => e.State != 1);

        // 数据格式处理
        allTrainingPlanList.forEach((item) => {
          item.StartTime = dateFormat(item.StartTime, 'YYYY-MM-DD');
        });

        // 筛选有效、失效方案
        let availableTrainingPlanList = allTrainingPlanList.filter(
          (e) => e.Group == 0
        );
        let unavailableTrainingPlanList = allTrainingPlanList.filter(
          (e) => e.Group == 1
        );
        if (this.nonReactiveData.pageIndex == 1) {
          this.availableTrainingPlanList = availableTrainingPlanList;
          this.unavailableTrainingPlanList = unavailableTrainingPlanList;
        } else {
          this.availableTrainingPlanList.push(...availableTrainingPlanList);
          this.unavailableTrainingPlanList.push(...unavailableTrainingPlanList);
        }

        // 更新尾部控件状态
        let listLength =
          this.availableTrainingPlanList.length +
          this.unavailableTrainingPlanList.length;
        if (objNotEmpty(this.trainingPlan)) {
          listLength++;
        }
        if (listLength == total) {
          this.nonReactiveData.haveMore = false;
          this.footerState = 3;
        } else {
          this.nonReactiveData.haveMore = true;
          this.footerState = 1;
        }
      }

      return r;
    },

    // 开始训练计划
    async loadStartTraining(trainingId) {
      let userId = this.getUserId();
      if (userId == null || !stringNotEmpty(trainingId))
        return {
          Type: 0,
          Content: '方案id为空',
        };

      let r = await startTrainingProgram(userId, trainingId);
      if (r.Type == 200) {
        // 切换方案前，先保存当前收起动作分组头
        this.saveCollapseGroups();
      }
      if (r.Type != 200) {
        uni.showToast({
          title: r.msg,
          icon: 'none',
        });
      }

      return r;
    },

    // 一键打卡
    async loadOneClickIn() {
      if (!dataIsValid(this.trainingPlan.Id)) {
        uni.showToast({
          title: '方案id为空',
          icon: 'none',
        });
        return false;
      }

      let r = await actionsOneClickIn(this.trainingPlan.Id);
      if (r.Type != 200) {
        uni.showToast({
          title: r.Content,
          icon: 'none',
        });
        return false;
      }

      return true;
    },

    // 请求今日训练会话未读数量
    async loadUnreadCount() {
      if (!dataIsValid(this.trainingPlan)) return;

      let roomId = this.trainingPlan.RoomId;
      let userId = this.getUserId();
      if (!stringNotEmpty(roomId) || !stringNotEmpty(userId)) return;

      this.nonReactiveData.needRequestUnreadCount = false;
      let imApi = new IMApi();
      let r = await imApi.getRoomUnreadCount(userId, [roomId]);
      if (r.isFailure) {
        this.nonReactiveData.needRequestUnreadCount = true;
        uni.showToast({
          title: r.msg,
          icon: 'none',
        });
        return;
      }

      let unreadCount = r.data[roomId];
      if (unreadCount && unreadCount != this.unReadCount) {
        this.unReadCount = unreadCount;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import url('@/common/common.css');

.overLayBox {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;

  &-overLay {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 572rpx;
    background: #ffffff;
    border-radius: 32rpx;
    padding: 48rpx 34rpx;

    &-text {
      font-weight: 500;
      font-size: 28rpx;
      color: #333333;
    }

    &-image {
      width: 444rpx;
      height: 444rpx;
      margin: 28rpx 32rpx 0 30rpx;
    }

    &-name {
      overflow-wrap: break-word;
      color: rgba(51, 51, 51, 1);
      font-size: 32rpx;
      font-family: PingFangSC-Semibold;
      font-weight: 600;
      white-space: nowrap;
      align-self: center;
      margin-top: 16rpx;
    }

    &-phone {
      overflow-wrap: break-word;
      color: rgba(51, 51, 51, 1);
      font-size: 28rpx;
      font-weight: 600;
      text-align: right;
      white-space: nowrap;
      align-self: center;
    }
  }
}

.scroll-content {
  background-color: #f7f7f7;
}

// 今日训练 - 顶部卡片
.card-section {
  height: 450rpx;
  margin: 24rpx 38rpx;
  background-color: white;
  display: flex;
  flex-direction: column;
  justify-content: start;
  border-radius: 8rpx;
  box-shadow: 0px 1px 8px rgba(255, 255, 255, 0.6);
}

.card-header {
  background-color: #29b7a3;
  border-top-left-radius: 8rpx;
  border-top-right-radius: 8rpx;
  padding: 0 24rpx;
  height: 128rpx;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;

  &-icon {
    width: 48rpx;
    height: 46rpx;
  }

  &-text {
    font-size: 32rpx;
    color: white;
    flex: 1;
    margin-left: 20rpx;
  }

  &-button {
    width: 162rpx;
    height: 56rpx;
    border-radius: 28rpx;
    border: 4rpx solid white;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;

    text {
      color: white;
      font-size: 26rpx;
    }
  }
}

.card-icon-view {
  border-radius: 8rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 80rpx;
  height: 80rpx;
}

.card-bottom-view {
  flex: 1;
  padding: 24rpx 0;
  display: flex;
  flex-direction: row;
  align-items: stretch;
  justify-content: center;
}

.card-chart-view {
  width: 126px;
  height: 126px;
  box-shadow: 0 2px 10px #80b2b2b2;
  padding: 8px;
  border-radius: 50%;
  // position: relative;
  background-color: #f6f6f6;

  // 水波球 - 内圈
  &-inner {
    background-color: #abf0e6;
    border-radius: 50%;
  }

  // 水波球 - 文字
  &-text {
    font-size: 15px;
    font-weight: bold;
    color: white;
  }
}

.card-chart-inner-view {
  width: 110px;
  height: 110px;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  &-text {
    font-size: 15px;
    color: white;
  }

  &-subtitle {
    font-size: 25px;
    font-weight: bold;
    color: white;
  }
}

.card-data-view {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
}

.card-data-title {
  color: #999999;
  font-size: 26rpx;
}

.card-data-content {
  font-size: 32rpx;
  color: #29b7a3;
  font-weight: bold;
}

// 今日训练 - 方案说明
.picker-row-item {
  margin: 0 32rpx 24rpx 32rpx;
  padding: 16rpx 24rpx;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  background-color: white;
  border-radius: 8rpx;
  box-shadow: 0 1px 8px rgba(255, 255, 255, 0.6);
}

.picker-row-text {
  flex: 1;
  font-size: 28rpx;
  color: #29b7a3;
  /* 定义为自适应布局   */
  display: -webkit-box;
  /* 超出部分隐藏 */
  overflow: hidden;
  /* 竖直方向的超出隐藏 */
  -webkit-box-orient: vertical;
  /* 限制最多2行 */
  -webkit-line-clamp: 1;
}

// 今日训练 - 标签栏
.tabs-menu-view {
  margin: 0 32rpx;
  background-color: white;
  border-radius: 8rpx;

  /deep/ .u-subsection__bar {
    height: 100%;
  }

  /deep/ .u-subsection {
    height: 90rpx;
  }
}

// 列表外层
.list-warpper {
  margin: 0 32rpx;

  /deep/ .u-list {
    height: auto !important;
  }
}

// 镂空按钮
.button-outside {
  border-radius: 48rpx;
  padding: 8rpx 16rpx;
  min-width: 120rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 500;
  font-size: 26rpx;
  box-sizing: border-box;
  border: 2rpx solid #29b7a3;
  color: #23b4a3;
}

// 实心按钮
.button-submit {
  background-color: #29b7a3;
  color: white;
  border-radius: 48rpx;
  padding: 8rpx 16rpx;
  min-width: 120rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 500;
  font-size: 26rpx;
  box-sizing: border-box;
}

// 康复治疗医嘱
.list-medical-advice-item {
  margin-top: 20rpx;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: stretch;
  border-radius: 8rpx;
  background-color: white;

  // 医嘱名
  &-title {
    flex: 1;
    color: #23b4a3;
    font-size: 28rpx;
    font-weight: 600;
    display: -webkit-box;
    overflow: hidden;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  &-content {
    color: #666666;
    font-size: 24rpx;
  }

  // 社区医嘱 - 已执行次数
  &-count {
    color: #29b7a3;
    font-size: 28rpx;
    font-weight: 600;
  }

  // 社区医嘱 - 总次数
  &-total {
    color: #666666;
    font-size: 24rpx;
    font-weight: 600;
  }

  // 社区医嘱 - 治疗点按钮
  .treatment-point-button {
    color: #29b7a3;
    font-size: 24rpx;
    padding: 10rpx 20rpx;
    background-color: #f5f5f5;
    border-radius: 56rpx;
    margin-top: 18rpx;
  }

  .mission-image {
    width: 124rpx;
    height: 124rpx;
  }

  .mission-text {
    flex: 1;
    margin-left: 32rpx;
    font-size: 28rpx;
    color: #333333;
    font-weight: 600;
  }
}

// 康复治疗 - 分组动作
.list-group-action-item {
  margin-top: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: stretch;
  border-radius: 8rpx;
  background-color: white;

  // 分组头
  &-header {
    padding: 24rpx;

    &-icon {
      width: 40rpx;
      height: 40rpx;
    }

    &-title {
      flex: 1;
      margin-left: 16rpx;
      font-weight: 500;
      font-size: 32rpx;
      color: #333333;
    }
  }
}

// 康复治疗 - 分组下的动作医嘱
.list-punch-item {
  margin: 20rpx 0;
  padding: 0 24rpx;
  display: flex;
  flex-direction: row;
  justify-content: start;
  align-items: center;

  &-title {
    color: #333333;
    font-size: 32rpx;
    display: -webkit-box;
    overflow: hidden;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  &-count {
    color: #666666;
    font-size: 26rpx;
    margin-right: 21rpx;

    &-finish {
      color: #29b7a3;
    }
  }

  &-chart {
    flex: 1;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    &-inactive {
      height: 16rpx;
      width: 100%;
      border-radius: 8rpx;
      background-color: #f6f6f6;
      position: relative;
      flex: 1;
    }

    &-active {
      height: 16rpx;
      width: 20%;
      border-radius: 8rpx;
      background-color: #29b7a3;
      position: absolute;
      top: 0;
      left: 0;
    }
  }

  &-practice {
    margin: 6rpx 0 0 64rpx;
    height: 60rpx;
    width: 155rpx;

    &-button {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      font-weight: 500;
      font-size: 28rpx;
      color: #ffffff;
      background-color: #29b7a3;
      border-radius: 50rpx;
      height: 100%;
      width: 100%;
    }

    &-button-disabled {
      opacity: 0.5;
    }
  }
}

// 康复评定
.list-assess-item {
  margin-top: 20rpx;
  padding: 24rpx;
  background-color: white;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: stretch;

  &-top {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    .name {
      margin: 0 32rpx;
      flex: 1;
      font-size: 30rpx;
      color: #333333;
    }

    .evaluated {
      font-size: 30rpx;
      font-weight: bold;
      color: #23b4a3;
    }

    .unevaluated {
      font-size: 30rpx;
      font-weight: bold;
      color: #ff3b30;
    }
  }

  &-bottom {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    .time {
      margin-top: 24rpx;
      font-size: 30rpx;
      color: #999999;
    }

    .button {
      margin-top: 24rpx;
      font-size: 30rpx;
      color: #23b4a3;
    }
  }
}

// 历史方案列表（有效、无效）
.list-plan-title {
  font-weight: 500;
  font-size: 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-bottom: 32rpx;
  padding-top: 24rpx;
}

.list-plan-item {
  padding: 32rpx 24rpx;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: stretch;
  background-color: white;

  &-top {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    &-middle-view {
      margin: 0 24rpx;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: flex-start;
    }

    &-title {
      color: #29b7a3;
      font-size: 30rpx;
    }

    &-content {
      margin-top: 20rpx;
      color: #999999;
      font-size: 26upx;
    }
  }
}

.list-plan-chart {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: stretch;

  &-title {
    margin-top: 10rpx;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    color: #29b7a3;
    font-size: 26rpx;
  }

  &-inactive {
    height: 16rpx;
    width: 100%;
    border-radius: 8rpx;
    margin: 24rpx 0rpx 0 0;
    background-color: #f6f6f6;
    position: relative;
  }

  &-active {
    height: 16upx;
    width: 20%;
    border-radius: 8upx;
    background-color: #29b7a3;
    position: absolute;
    top: 0;
    left: 0;
  }
}

// 医助
.assistanceImage {
  position: fixed;
  right: 0;
  bottom: 10%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10000;

  &-text {
    width: 100%;
    height: 42rpx;
    text-align: center;
    line-height: 42rpx;
    color: white;
    background: #29b7a3;
    border-radius: 22rpx;
    font-weight: 600;
    font-size: 24rpx;
  }
}

.hover-class {
  opacity: 0.5;
}

/deep/ .u-divider {
  margin-bottom: 0 !important;
  margin-top: 0 !important;
}

/deep/ .u-cell__title-text {
  font-weight: 700;
  font-size: 28rpx;
  // color: #323233;
  color: #23b4a3;
  margin-left: 16rpx;
}

/deep/ .u-cell__body {
  padding-left: 0 !important;
  padding-right: 0 !important;
}

/deep/ .u-collapse-item__content__text {
  padding-left: 0 !important;
  padding-right: 0 !important;
}
</style>
