<template>
  <!-- <page-meta :root-font-size="newFonSize"></page-meta> -->
  <view class="user">
    <!-- 用户头部的信息 -->
    <view class="cl-bg">
      <view
        class="userInfo"
        :style="'top:' + (topHeigth + buttonHeight) + 'px'"
      >
        <!--  userLogin-->
        <view class="u-avatar-item" @click="updateUserInfoOrLogin">
          <u-image :src="userInfo.HeadImg" width="60px" height="60px"></u-image>
        </view>
        <view style="color: white" @click="updateUserInfo" v-if="userInfo.Id">
          <view class="display-style">
            <view style="margin-right: 20upx">{{
              userInfo.Name || userInfo.NickName
            }}</view>
            <u-icon name="edit-pen" color="#ffffff" size="22"></u-icon>
          </view>
          <p style="margin-top: 10upx">{{ userInfo.PhoneNumberStr }}</p>
        </view>
        <view class="icon-all" v-if="userInfo.Id">
          <u-icon
            name="server-fill"
            color="#ffffff"
            size="28"
            @click="toUs"
            customStyle="margin-right: 6px;"
          >
          </u-icon>
          <u-icon
            name="bell"
            color="#ffffff"
            size="28"
            customStyle="margin-right: 6px;"
            @click="lookInfo"
          >
          </u-icon>
          <u-badge :isDot="true" type="success" v-if="showbadge"></u-badge>
          <u-icon
            name="setting"
            color="#ffffff"
            size="28"
            customStyle="margin-right: 6px;"
            @click="toSetting"
          >
          </u-icon>
        </view>
        <u-button
          type="default"
          @click="onTopLogin"
          customStyle="color: white;font-size: 24px;"
          class="userNameAndPhone"
          v-else
          >点击登录</u-button
        >
      </view>
    </view>

    <!-- 三个按钮的信息 -->
    <view class="botton-list">
      <view class="u-avatar-item1" @click="listGoPage('wz')">
        <view
          style="
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: #d8fffa;
            display: flex;
            justify-content: center;
            align-items: center;
          "
        >
          <u-avatar
            src="/static/wenzhendingdan.png"
            customStyle="{margin:0 auto}"
          ></u-avatar>
        </view>
        <p style="font-size: 15px; margin-top: 10upx; color: #333333">
          咨询订单
        </p>
      </view>
      <view class="u-avatar-item1" @click="listGoPage('zl')">
        <view
          style="
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: #d8fffa;
            display: flex;
            justify-content: center;
            align-items: center;
          "
        >
          <u-avatar
            src="/static/zhiliaodingdan.png"
            customStyle="{margin:0 auto}"
          ></u-avatar>
        </view>
        <p style="font-size: 15px; margin-top: 10upx; color: #333333">
          治疗订单
        </p>
      </view>
      <view class="u-avatar-item1" @click="listGoPage('sb')">
        <view
          style="
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: #d8fffa;
            display: flex;
            justify-content: center;
            align-items: center;
          "
        >
          <u-avatar
            src="/static/tuihuanshebei.png"
            customStyle="{margin:0 auto}"
          ></u-avatar>
        </view>
        <p style="font-size: 15px; margin-top: 10upx; color: #333333">
          退还设备
        </p>
      </view>
    </view>

    <!-- 数组列表 -->
    <view style="padding: 0 20px; width: 100%; transform: translateY(-20upx)">
      <view v-for="(item, index) in list" :key="item.title">
        <u-cell
          :border="false"
          :isLink="true"
          iconStyle="fontSize: 28px"
          @click="goToUrl(item, index)"
          v-if="item.isShow"
        >
          <u-avatar :src="item.icon" slot="icon" :size="24"></u-avatar>
          <p slot="title" style="margin-left: 10px">{{ item.title }}</p>
          <p slot="value">{{ item.value || '' }}</p>
        </u-cell>
      </view>
    </view>
    <u-modal
      :show="show"
      :closeOnClickOverlay="true"
      @close="cancel"
      title="温馨提示"
      content="您还没有登录,请登录之后再查看"
    >
      <button
        type="primary"
        slot="confirmButton"
        open-type="getPhoneNumber"
        @getphonenumber="getPhoneNumber"
      >
        微信一键登录
      </button>
    </u-modal>
    <u-modal
      :show="showModal2"
      title="提示"
      content="您还没有实名认证,请先实名认证"
      confirmText="去认证"
      cancelText="取消"
      confirmColor="#29B7A3"
      @confirm="confirm2"
      @cancel="showModal2 = false"
      :showCancelButton="true"
    ></u-modal>
  </view>
</template>

<script>
const app = getApp();
import { createNamespacedHelpers } from 'vuex';
const { mapGetters } = createNamespacedHelpers('message');
import config from '@/config';
import { getSetting, getlist } from '@/api/bff.js';
import { GetUserScanLog } from '@/api/passport.js';
export default {
  data() {
    return {
      showModal3: false,
      newFonSize: uni.getStorageSync('root_font_size') || 14,
      showModal2: false,
      show: false,
      src: '',
      userInfo: {},
      list: [
        {
          icon: '/static/images/cf.png',
          title: '治疗方案',
          url: '/subPrescription/index',
          isShow: true,
          code: 'ZLFA',
        },
        {
          icon: '/static/images/bl.png',
          title: '病历档案',
          url: '/subPackIndex/user/medicalRecords',
          isShow: true,
          code: 'BLDA',
        },
        {
          icon: '/static/images/gaugeArchive.png',
          title: '评估档案',
          url: '/subGauge/gaugeArchives',
          isShow: true,
          code: 'PGDA',
          needCertification: true,
        },
        {
          icon: '/static/images/dz.png',
          title: '地址管理',
          url: '/subPackIndex/user/addressMgr',
          isShow: true,
          code: 'DZGL',
        },
        {
          icon: '/static/images/tzlr.png',
          title: '体征记录',
          url: '/subPhysical/index',
          isShow: true,
          code: 'TZLR',
        },
        {
          icon: '/static/images/sb.png',
          title: '我的设备',
          url: '/subPackIndex/user/myDevice',
          isShow: true,
          code: 'WDSB',
        },
        {
          icon: '/static/images/<EMAIL>',
          title: '用户反馈',
          url: '/subPackIndex/user/userFeedback',
          isShow: true,
          code: 'YHFK',
        },
      ],
      defaultList: [
        {
          icon: '/static/images/cf.png',
          title: '治疗方案',
          url: '/subPrescription/index',
          isShow: true,
          code: 'ZLFA',
        },
        {
          icon: '/static/images/bl.png',
          title: '病历档案',
          url: '/subPackIndex/user/medicalRecords',
          isShow: true,
          code: 'BLDA',
        },
        {
          icon: '/static/images/gaugeArchive.png',
          title: '评估档案',
          url: '/subGauge/gaugeArchives',
          isShow: true,
          code: 'PGDA',
          needCertification: true,
        },
        {
          icon: '/static/images/mine-follow-up.png',
          title: '随访任务',
          url: '/subFollowUp/followUpDetailListPage',
          isShow: true,
          code: 'SFRW',
        },
        {
          icon: '/static/images/baogao.png',
          title: '我的报告',
          url: '/subReport/index',
          isShow: true,
          code: 'WDBG',
        },
        {
          icon: '/static/images/dz.png',
          title: '地址管理',
          url: '/subPackIndex/user/addressMgr',
          isShow: true,
          code: 'DZGL',
        },
        {
          icon: '/static/images/tzlr.png',
          title: '体征记录',
          url: '/subPhysical/index',
          isShow: true,
          code: 'TZLR',
        },
        {
          icon: '/static/images/sb.png',
          title: '我的设备',
          url: '/subPackIndex/user/myDevice',
          isShow: true,
          code: 'WDSB',
        },
        {
          icon: '/static/images/<EMAIL>',
          title: '用户反馈',
          url: '/subPackIndex/user/userFeedback',
          isShow: true,
          code: 'YHFK',
        },
        {
          icon: '/static/images/Ly.png',
          title: '来源',
          url: '/subPackIndex/user/userFrom',
          isShow: true,
          code: 'LY',
          value: '',
        },
        {
          icon: '/static/images/Group4064.png',
          title: '门诊记录',
          url: '/subPackIndex/user/outpatientRecords',
          isShow: false,
          code: 'MZJL',
          needCertification: true,
        },
        {
          icon: '/static/images/myFj.png',
          title: '我的辅具',
          url: '',
          isShow: true,
          code: 'WDFJ',
        },
        {
          icon: '/static/images/pgfa.png',
          title: '评估方案',
          url: '/subPackIndex/user/reportCenter',
          isShow: true,
          code: 'GNZAPG',
        },
      ],
      topHeigth: 0,
      buttonHeight: 0,
    };
  },
  computed: mapGetters(['showbadge']),
  onLoad(option) {
    console.log('option', option);
    let menuButtonInfo = uni.getMenuButtonBoundingClientRect();
    this.topHeigth = menuButtonInfo.top;
    this.buttonHeight = menuButtonInfo.height;
  },
  async onShow() {
    await getApp().isLaunchFinish();
    if (app.globalData.userInfo.Id) {
      this.initData();
      this.show = false;
    }
  },
  methods: {
    openLoginPage() {
      getApp().openLoginPage();
    },
    onTopLogin() {
      this.openLoginPage();
    },
    async getUserFrom() {
      const res = await GetUserScanLog({
        userId: app.globalData.userInfo.Id,
      });
      if (res.Type === 200 && res.Data) {
        this.defaultList.forEach((v) => {
          if (v.code === 'LY' && res.Data && res.Data.Inviter) {
            v.value =
              res.Data.Inviter.length > 6
                ? res.Data.Inviter.slice(0, 5) + '...'
                : res.Data.Inviter;
          }
        });
      }
    },
    async getMinAppMenuByOrgId() {
      const data = {
        Collection: 'cfg_f_min_custom',
        Filter: {
          $or: [
            {
              OrgId: '',
            },
            {
              OrgId: app.globalData.orgId,
            },
          ],
        },
      };
      const res = await getSetting(data);
      if (res.Type === 200) {
        const copyDefaultList = JSON.parse(JSON.stringify(this.defaultList));
        const data = {
          Filter: {
            Enabled: true,
          },
          OrgId: app.globalData.orgId,
        };
        const resData = await getlist(data);
        if (resData.Type === 200) {
          const filterData = resData.Data.filter(
            (p) => p.Code === 'AssistFunc'
          );
          if (filterData.length > 0) {
            const filterDataObj1 = filterData[0];
            const filterDataObj = filterDataObj1.Payload.filter(
              (p) => p.Key === 'AssistFunc'
            )[0];
            copyDefaultList.forEach((m) => {
              if (m.code === 'WDFJ' || m.code === 'GNZAPG' || m.code === 'LY') {
                m.isShow = filterDataObj.Value;
              }
            });
          } else {
            copyDefaultList.forEach((m) => {
              if (m.code === 'WDFJ' || m.code === 'GNZAPG' || m.code === 'LY') {
                m.isShow = false;
              }
            });
          }
        }
        if (res.Data.length > 1) {
          const menuList = res.Data.filter(
            (v) => v.OrgId === app.globalData.orgId
          )[0].Menus;
          menuList.forEach((o) => {
            copyDefaultList.forEach((k) => {
              if (k.code === o.Code) {
                k.isShow = o.IsShow;
              }
            });
          });
        }
        this.list = copyDefaultList;
      }
    },
    getRootFontSize() {
      let fontSize = app.globalData.rootFontSize;
      if (fontSize) return fontSize;
      fontSize = uni.getStorageSync('root_font_size');
      if (fontSize) return fontSize;
      fontSize = '12px';
      this.setRootFontSize(fontSize);
      return fontSize;
    },
    updateUserInfoOrLogin() {
      if (!this.checkIsLogin()) {
        this.openLoginPage();
        return;
      }
      if (config.envVersion == 'release') {
        this.updateUserInfo();
      } else {
        // this.userLogin()
        // uni.navigateTo({
        // 	url: '/subPrescription/payResults'
        // })
      }
    },
    toSetting() {
      if (!this.checkIsLogin()) {
        this.openLoginPage();
        return;
      }
      uni.navigateTo({
        url: '/subPackIndex/user/accountSettings',
      });
    },
    lookInfo() {
      if (!this.checkIsLogin()) {
        this.openLoginPage();
        return;
      }
      uni.navigateTo({
        url: '/subPackIndex/news',
      });
    },
    toUs() {
      uni.navigateTo({
        url: '/subPackIndex/customerService',
      });
    },
    // 判断是否有登录信息
    checkIsLogin() {
      return app.isLoggedIn();
    },
    // 微信一件登录
    async getPhoneNumber(e) {
      const r = await getApp().loginInWithWX(e);
      if (r) {
        this.show = false;
        this.initData();
      }
    },
    // 初始化用户信息
    async initData() {
      if (
        app.globalData.userInfo.Name &&
        app.globalData.userInfo.Name.length > 8
      ) {
        app.globalData.userInfo.Name =
          app.globalData.userInfo.Name.substring(0, 8) + '...';
      }
      if (
        app.globalData.userInfo.NickName &&
        app.globalData.userInfo.NickName.length > 8
      ) {
        app.globalData.userInfo.NickName =
          app.globalData.userInfo.NickName.substring(0, 8) + '...';
      }
      this.userInfo = app.globalData.userInfo;
      this.userInfo.PhoneNumberStr = this.phoneHandel(
        this.userInfo.PhoneNumber
      );
      // 获取用户的来源
      await this.getUserFrom();
      // 获取选择机构对应的菜单信息
      this.getMinAppMenuByOrgId();
    },
    // 手机号脱敏
    phoneHandel(num) {
      if (num) {
        return num.substring(0, 3) + '****' + num.substring(num.length - 4);
      } else {
        return false;
      }
    },
    // 如果检测到没有登录 就提示他去登录
    confirm() {
      this.show = false;
      uni.navigateTo({
        url: '/subPackIndex/user/login',
      });
    },
    confirm2() {
      this.showModal2 = false;
      uni.navigateTo({
        url: '/subPackIndex/user/userUpdate?type=smxx',
      });
    },
    // 取消去登录
    cancel() {
      this.show = false;
    },
    // 手动去登陆
    userLogin() {
      uni.navigateTo({
        url: '/subPackIndex/user/login',
      });
    },
    // 去修改用户信息
    updateUserInfo() {
      uni.navigateTo({
        url: '/subPackIndex/user/index',
      });
    },
    // 三个按钮的点击事件
    listGoPage(type) {
      if (!this.checkIsLogin()) {
        this.openLoginPage();
        return;
      }
      switch (type) {
        case 'wz':
          uni.navigateTo({
            url: '/subPackInquiry/inquiryOrder',
          });
          break;
        case 'zl':
          uni.navigateTo({
            url: '/subPackIndex/user/treatmentOrder',
          });
          break;
        case 'sb':
          uni.navigateTo({
            url: '/subPackIndex/user/deviceBack',
          });
          break;
      }
    },
    // 点击下方的list去某个页面
    goToUrl(item, index) {
      if (!this.checkIsLogin()) {
        this.openLoginPage();
      } else if (item.code === 'LY' && item.value) {
        uni.showModal({
          content: '您已有来源,无法修改',
          showCancel: false,
        });
        return;
        // uni.navigateTo({
        // 	url: item.url
        // })
      } else if (item.code === 'WDFJ') {
        uni.navigateToMiniProgram({
          appId: 'wxe0b835dfcd017835',
          path: '/ec_orderDetail/orderlist?productInstanceId=2147401813&vid=0',
        });
        return;
      } else if (
        app.globalData.userInfo.WorkflowStatus != 2 &&
        item.needCertification
      ) {
        this.showModal2 = true;
      } else {
        uni.navigateTo({
          url: item.url,
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .u-avatar .u-avatar__image {
  border-radius: 0;
}

/deep/ .u-image__image {
  border-radius: 50% !important;
}

.u-demo-block__content {
  @include flex;
  align-items: center;
}

.u-avatar-item {
  margin-right: 30upx;
  text-align: center;
}

.u-avatar-item1 {
  width: 30%;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.user {
  background-color: #f7f7f7;

  /deep/ .u-cell {
    background-color: white;
    padding: 10px 0;
  }

  .cl-bg {
    width: 100%;
    height: 373upx;
    background-image: linear-gradient(to right, #62bf7e, #00aeb7);
    position: relative;
    padding: 40upx;

    .icon-top {
      width: 100%;
      display: flex;
      justify-content: space-between;
      position: absolute;
    }
  }

  .userInfo {
    /deep/ .u-button {
      margin: 0;
      width: 120px !important;
    }

    display: flex;
    position: absolute;
    align-items: center;
    justify-content: flex-start;
    width: 90%;

    .icon-all {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      flex: 1;
      position: relative !important;

      /deep/ .u-badge {
        background-color: red;
        position: absolute !important;
        top: 2px !important;
        right: 45px !important;
      }
    }
  }

  .botton-list {
    height: 210upx;
    background-color: white;
    width: 90%;
    margin: 0 auto;
    border-radius: 20upx;
    transform: translateY(-70upx);
    box-shadow: 0 0 8rpx -2rpx rgba(0, 0, 0, 0.3);
    padding: 10upx 20upx;
    display: flex;
    justify-content: space-around;
    align-items: center;
  }
}
</style>
