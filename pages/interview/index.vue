<template>
  <!-- <page-meta :root-font-size="getRootFontSize()"></page-meta> -->
  <!--  @touchmove="handletouchmove($event)" @touchstart="handletouchstart"
		@touchend="handletouchendFun" -->
  <view class="container">
    <u-index-list v-if="itemArr.length > 0">
      <u-list
        @scrolltolower="scrolltolower"
        :scrollWithAnimation="true"
        :preLoadScreen="1"
      >
        <u-list-item v-for="(item, index) in itemArr" :key="index">
          <u-index-item :key="index">
            <!-- #ifndef APP-NVUE -->
            <u-index-anchor :text="indexList[index]"> </u-index-anchor>
            <!-- #endif -->
            <view
              class="container-right-box"
              v-for="(o, index2) in item"
              @click="toIM(o)"
              :class="'index' + index"
              :key="index2"
            >
              <u-cell :border="false">
                <u-avatar
                  :src="o.DocUserHeadImg"
                  size="60"
                  slot="icon"
                ></u-avatar>
                <view
                  slot="title"
                  style="margin-left: 4px; font-size: 1rem"
                  :style="o.State == 3 ? 'color:#999999' : ''"
                >
                  <p
                    style="
                      font-size: 1rem;
                      overflow: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                    "
                    v-if="o.State == 1 && o.ConsultWay === 1"
                  >
                    咨询时间：{{ o.dateFormaTime }}
                  </p>
                  <p
                    style="
                      font-size: 1rem;
                      overflow: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                    "
                    v-if="o.State != 1 && o.ConsultWay === 1"
                  >
                    咨询时间：{{ o.dateFormaTime2 }}
                  </p>
                  <p
                    style="
                      font-size: 1rem;
                      overflow: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                    "
                    v-if="o.ConsultWay !== 1 && o.State == 1"
                  >
                    咨询时间：{{ o.dateFormaTime }}
                  </p>
                  <p
                    style="
                      font-size: 1rem;
                      overflow: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                    "
                    v-if="o.ConsultWay !== 1 && o.State != 1"
                  >
                    咨询时间：{{ o.dateFormaTime2 }}
                  </p>
                  <p style="margin-bottom: 4px" v-if="o.ConsultWay === 1">
                    咨询医生：{{ o.DocUserName }}
                  </p>
                  <p style="margin-bottom: 4px" v-else-if="o.ConsultWay === 2">
                    治疗师：{{ o.DocUserName }}
                  </p>
                  <p style="margin-bottom: 4px" v-else-if="o.ConsultWay === 3">
                    护士：{{ o.DocUserName }}
                  </p>
                  <p
                    v-if="o.Describing"
                    style="
                      overflow: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                      margin-bottom: 4px;
                    "
                  >
                    症状描述：{{ o.Describing }}
                  </p>
                </view>
                <view
                  slot="value"
                  style="font-size: 1rem; margin-left: 5px"
                  @click.stop=""
                >
                  <p
                    :style="
                      o.State == 1
                        ? 'color:#FF9900'
                        : o.State == 3
                          ? 'color:#999999'
                          : 'color:#29B7A3'
                    "
                    style="margin-bottom: 10rpx; text-align: right"
                  >
                    {{
                      o.State == 3
                        ? '已结束'
                        : o.State == 1
                          ? '待确认'
                          : '咨询中'
                    }}
                  </p>
                  <u-button
                    text="治疗方案"
                    size="small"
                    shape="circle"
                    type="primary"
                    v-if="o.PrescriptionId"
                    @click.stop="toseePescript(o.PrescriptionId)"
                  >
                  </u-button>
                </view>
              </u-cell>
              <u-badge
                numberType="overflow"
                :absolute="true"
                :offset="[0, 0]"
                max="99"
                :value="o.unreadCount"
              >
              </u-badge>
            </view>
          </u-index-item>
        </u-list-item>
      </u-list>
    </u-index-list>
    <u-empty
      mode="data"
      icon="http://cdn.uviewui.com/uview/empty/data.png"
      text="当前没有咨询记录"
      v-if="itemArr.length == 0"
    >
    </u-empty>
    <u-modal
      :show="alertIndex == 1"
      :closeOnClickOverlay="true"
      @close="alertIndex = 0"
      title="温馨提示"
      content="您还没有登录,请登录之后再查看"
    >
      <button type="primary" slot="confirmButton" @click="onUserLogin">
        点击登录
      </button>
    </u-modal>
  </view>
</template>

<script>
const app = getApp();
import { getMyConsults } from '@/api/consult.js';
import { arrayNotEmpty } from '@/utils/utils';
import { ConsultClientEvent, SessionClientEvent } from '@/utils/eventKeys.js';
import Move from '@/mixin/move.js';
import { IMApi, IMManager } from 'kfx-im';
import { LOGIN_FINISH } from '../../services/LoginService';
export default {
  mixins: [Move],
  data() {
    return {
      showArr: 'index0',
      loading: false,
      indexList: [],
      itemArr: [],
      query: {
        year: 2022,
        month: 10,
        pageIndex: 1,
        pageSize: 9,
      },
      alertIndex: 0,
      checkOnTabItemTap: true,
      getRest: false,
      current: 0,
      lastX: 0,
      /**
       * 是否需要网络请求会话未读数
       *
       * 初始化时候需要通过网络请求来同步消息未读数，之后医生端发送消息，
       * 未读数变化，IMManager 内部会自动缓存本 session，不需要再网络请求
       */
      needRequestUnreadCount: true,
    };
  },
  /**
   * 2024-01-13 做风险预估的时候 产品说的要进入页面就刷新一次
   */
  onShow() {
    if (!app.globalData.userInfo.Id) {
      this.loading = true;
      this.alertIndex = 1;
      return;
    }
    if (app.globalData.userInfo.Id) {
      this.rest();
    }
  },
  onHide() {
    this.getRest = true;
  },
  async onLoad({ type }) {
    await getApp().isLaunchFinish();

    // 问诊状态改变
    let that = this;
    this.consultStateChangedHandler = () => {
      that.rest();
    };
    uni.$on(
      ConsultClientEvent.consultStateChanged,
      this.consultStateChangedHandler
    );

    uni.$on(LOGIN_FINISH, () => {
      this.alertIndex = 0;
    });
    this.getRest = false;

    // 获取会话未读消息数量
    this.sessionListChangedHandler = async (sessions) => {
      await that.refreshUnReadCount(that.itemArr);
      that.$forceUpdate();
    };
    uni.$on(
      SessionClientEvent.sessionListChanged,
      this.sessionListChangedHandler
    );
  },

  onUnload() {
    uni.$off(
      ConsultClientEvent.consultStateChanged,
      this.consultStateChangedHandler
    );
    uni.$off(
      SessionClientEvent.sessionListChanged,
      this.sessionListChangedHandler
    );
  },
  methods: {
    onUserLogin() {
      getApp().openLoginPage();
    },
    sectionChange(index) {
      this.SlidingDirection = null;
      this.StopDirection = null;
      this.rest();
    },
    getRootFontSize() {
      let fontSize = app.globalData.rootFontSize;
      if (fontSize) return fontSize;
      fontSize = uni.getStorageSync('root_font_size');
      if (fontSize) return fontSize;
      fontSize = '12px';
      this.setRootFontSize(fontSize);
      return fontSize;
    },
    clickAnchor(e) {
      if (this.indexList.length == 1) return;
      if (e == this.showArr) {
        this.showArr = '';
        return;
      }
      this.showArr = e;
    },
    // 微信一件登录
    async getPhoneNumber(e) {
      const r = await getApp().loginInWithWX(e);
      if (r) {
        this.getInfo();
      }
    },
    // 去看处方
    toseePescript(id) {
      uni.navigateTo({
        url: '/subPrescription/detail?id=' + id,
      });
    },

    rest() {
      this.alertIndex = 0;
      this.query.pageIndex = 1;
      this.itemArr = [];
      this.indexList = [];
      this.getInfo();
    },
    onSure() {
      this.alertIndex = 0;
      uni.switchTab({
        url: '/pages/user/index',
      });
    },
    async getInfo() {
      var complaters = {};
      this.checkOnTabItemTap = false;
      uni.showLoading({
        title: this.$loadingMsg,
      });
      let res = await getMyConsults(this.query);
      if (res.Type == 200 && res.Data != null) {
        // 获取副本值，用于获取 unreadCount 后，重新组合，赋值显示
        var indexList = this.indexList.slice();
        var itemArr = this.itemArr.slice();

        res.Data.IndexList.forEach((n, i) => {
          let year = n.slice(0, 4);
          n = year + '年' + n.replace(year, '') + '月';
          const index = this.indexList.indexOf(n);
          if (index == -1) {
            //没找到
            this.indexList.push(n);
            res.Data.ItemArr[i].forEach((e) => {
              e.dateFormaTime = this.$dateFormat(e.CreateDate, 'MM-DD HH:mm');
              e.dateFormaTime2 = this.$dateFormat(e.VistDate, 'MM-DD HH:mm');
              e.dateFormaTime3 = this.$dateFormat(e.VistDate, 'MM-DD HH:mm');
            });
            this.itemArr.push(res.Data.ItemArr[i]);
          } else {
            //找到了
            res.Data.ItemArr[i].forEach((k) => {
              k.dateFormaTime = this.$dateFormat(k.CreateDate, 'MM-DD HH:mm');
              k.dateFormaTime2 = this.$dateFormat(k.VistDate, 'MM-DD HH:mm');
              this.itemArr[index].push(k);
            });
          }
        });

        this.needRequestUnreadCount = true;
        this.refreshUnReadCount(res.Data.ItemArr).then((value) => {
          if (value == null) return;

          res.Data.IndexList.forEach((n, i) => {
            let year = n.slice(0, 4);
            n = year + '年' + n.replace(year, '') + '月';
            const index = indexList.indexOf(n);
            if (index == -1) {
              indexList.push(n);
              itemArr.push(value[i]);
            } else {
              itemArr[index].push(...value[i]);
            }
          });
          this.indexList = indexList;
          this.itemArr = itemArr;
        });
      }
      this.$nextTick(() => {
        uni.hideLoading();
      });
      this.loading = true;
    },
    toIM(o) {
      getApp().toChatPage(o.Id);
    },
    scrolltolower() {
      this.query.pageIndex++;
      this.getInfo();
    },
    // 刷新有效咨询记录列表的会话未读数
    async refreshUnReadCount(itemArr) {
      if (!arrayNotEmpty(itemArr)) return null;
      let that = this;

      // 先从会话列表中获取
      var roomIds = [];
      let sessions = IMManager.instance.getSessionList();
      itemArr.forEach((list) => {
        list.forEach((consult) => {
          let session = sessions.find((s) => s.sessionId == consult.RoomId);
          if (session) {
            if (session.unreadCount != consult.unreadCount) {
              consult.unreadCount = session.unreadCount;
            }
          } else {
            roomIds.push(consult.RoomId);
          }
        });
      });

      if (arrayNotEmpty(roomIds) && this.needRequestUnreadCount) {
        // 未获取到，直接请求未读数
        let userId = app.globalData.userInfo.Id;
        let imApi = new IMApi();
        this.needRequestUnreadCount = false;
        let r = await imApi.getRoomUnreadCount(userId, roomIds);
        if (r.isFailure) {
          this.needRequestUnreadCount = true;
          uni.showToast({
            title: r.msg,
            icon: 'none',
          });
        }
        if (r.isSuccess) {
          this.needRequestUnreadCount = false;
          itemArr.forEach((list) => {
            list.forEach((consult) => {
              if (roomIds.indexOf(consult.RoomId) < 0) return;

              let unreadCount = r.data[consult.RoomId];
              if (consult && consult.unreadCount != unreadCount) {
                consult.unreadCount = unreadCount;
              }
            });
          });
        }
      }

      return itemArr;
    },
  },
  onPullDownRefresh() {
    if (this.SlidingDirection && this.StopDirection) {
      return;
    }
    this.SlidingDirection = null;
    this.StopDirection = null;
    this.query.pageIndex = 1;
    this.rest();
    uni.stopPullDownRefresh();
  },
};
</script>

<style scoped lang="scss">
.container {
  padding: 10px;

  /deep/ .u-index-list {
    width: 100%;
  }

  /deep/ .u-button {
    font-size: 0.92rem !important;
    transform: translate(3px, 2px);
  }

  /deep/ .u-index-list__letter {
    display: none;
  }

  /deep/ .u-cell__title {
    flex: 0 !important;
    width: 78%;
  }

  /deep/ .u-cell__body__content {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  /deep/ .u-cell__body {
    padding: 10px 6px !important;
  }

  /deep/ .u-index-anchor {
    border-radius: 12rpx;
    height: 80rpx !important;
    background-color: #3aceb9 !important;
    margin-bottom: 10rpx !important;
  }

  /deep/ .u-index-anchor__text {
    color: white !important;
    font-size: 1.28rem !important;
  }

  /deep/ .u-button--small {
    margin-top: 10px;
  }

  .list {
    &__item {
      @include flex;
      padding: 6px 12px;
      align-items: center;

      &__avatar {
        height: 35px;
        width: 35px;
        border-radius: 3px;
      }

      &__user-name {
        font-size: 1.14rem;
        margin-left: 10px;
        color: $u-main-color;
      }
    }

    &__footer {
      color: $u-tips-color;
      font-size: 1rem;
      text-align: center;
      margin: 15px 0;
    }
  }

  /deep/ .u-line {
    transform: none !important;
    // border-color: #848484 !important
  }

  .container-right-box {
    margin-bottom: 10rpx;
    // box-shadow: 5px 6px 18px -12px rgba(0, 0, 0, 0.3);
    border-radius: 20rpx;
    // background-color: rgba(255,255,255,8%);;
    position: relative;
    // padding: 20rpx;

    /deep/ .u-cell {
      background-color: white;
      border-radius: 10rpx;
      box-shadow: -1px -1px 4px -2px rgba(0, 0, 0, 0.3) inset;
    }
  }

  .container-right {
    flex: 1;
  }
}
</style>
