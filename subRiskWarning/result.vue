<template>
  <view class="container">
    <view class="container-box">
      <view class="container-box-top">
        <text class="container-box-top-name">基本信息</text>
      </view>
      <view class="container-box-user" style="margin-top: 28rpx">
        <view class="container-box-user-box"> 姓名：{{ userInfo.Name }} </view>
        <view class="container-box-user-box"> 性别：{{ userInfo.Sex }} </view>
      </view>
      <view class="container-box-user" style="margin-top: 20rpx">
        <view class="container-box-user-box"> 年龄：{{ userInfo.Age }} </view>
        <!-- <view class="container-box-user-box">
					学历：初中
				</view> -->
      </view>
    </view>
    <view class="container-box">
      <view class="container-box-top">
        <text class="container-box-top-name">认知功能</text>
        <view
          class="container-box-top-base container-box-top-has"
          v-if="handleGetIsHaveProblem()"
        >
          有风险
        </view>
        <view class="container-box-top-base container-box-top-no" v-else>
          无风险
        </view>
      </view>
      <view
        class="container-box-item-right"
        style="margin-left: 0; margin-top: 24rpx"
      >
        {{ handleGetSingOneResult(cognitiveResult) }}
      </view>
      <view class="container-box-top" style="margin-top: 40rpx">
        <text class="container-box-top-name">运动功能</text>
        <view
          class="container-box-top-base container-box-top-has"
          v-if="motionResult.length"
        >
          有风险
        </view>
        <view class="container-box-top-base container-box-top-no" v-else>
          无风险
        </view>
      </view>
      <view
        class="container-box-item"
        v-for="(i, index) in motionResult"
        :key="index"
      >
        <view class="container-box-item-left">
          {{ i.ShowName }}
        </view>
        <text class="container-box-item-right">{{
          i.Report.map((v) => v.Result).join('')
        }}</text>
      </view>
    </view>
    <view class="container-box">
      <view class="container-box-title">
        <u-icon name="bell" color="#00BFA6" size="24"></u-icon>
        <text class="container-box-title-recommendations">建议</text>
      </view>
      <u-divider />
      <text
        class="container-box-recommendations"
        v-if="motionResult.length || handleGetIsHaveProblem()"
        >您可能存在一些身体功能障碍，请进行下一步的详细评估</text
      >
      <text class="container-box-recommendations" v-else
        >经筛查，您的身体健康状况良好！</text
      >
    </view>
    <text class="container-time">筛查时间：{{ CreatedTime }}</text>
    <view style="height: 180rpx"></view>
    <view
      class="btnButtomStyle"
      @click="handleFurtherClick"
      v-if="handelShowNextBtn()"
    >
      进一步评估
    </view>
  </view>
</template>

<script>
import config from '@/config';
const app = getApp();
import { getRiskWarningWriteReport } from '@/api/tenant.js';
import { setOrCanDoc } from '@/api/passport.js';
import { getDocInfoMation, getPayOrderInfo } from '@/api/consult.js';
export default {
  data() {
    return {
      Id: '',
      cognitiveResult: [],
      motionResult: [],
      CreatedTime: '',
      userInfo: {},
      scen: 'subRiskWarningIndex',
      from: '',
      doctorId: '',
      docInfo: {},
    };
  },
  onLoad(option) {
    this.Id = option.Id;
    if (option.scen) this.scen = option.scen;
    this.userInfo = app.globalData.userInfo;
    if (option.from) this.from = option.from;
    if (option.doctorId) this.doctorId = option.doctorId;
  },
  onShow() {
    // this.onGetReportDetail();
    // 获取报告详情
    this.onGetReportDetail();
  },
  methods: {
    getParms(url) {
      if (url.indexOf('?') != -1) {
        let obj = {};
        let arr = url.slice(url.indexOf('?') + 1).split('&');
        arr.forEach((item) => {
          let param = item.split('=');
          obj[param[0]] = param[1];
        });
        return obj;
      } else {
        console.log('没有参数');
        return null;
      }
    },
    handelShowNextBtn() {
      if (this.scen !== 'subRiskWarningIndex') {
        return false;
      }
      return this.motionResult.length || this.handleGetIsHaveProblem();
    },
    async onGetReportDetail() {
      uni.showLoading({
        title: '加载中',
        mask: true,
      });
      const res = await getRiskWarningWriteReport({
        UserId: app.globalData.userInfo.Id,
        ReportId: this.Id,
        Signs: [1, 2],
      });
      if (res.Type !== 200) {
        uni.hideLoading();
        uni.showModal({
          title: '温馨提示',
          content: res.Content,
          showCancel: false,
          success: (res) => {
            uni.navigateBack();
          },
        });
        return;
      }
      if (!res.Data) {
        return;
      }
      const cognitiveResult = res.Data.Report.filter((s) => s.Sign === 1)
        .map((v) => v.Report)
        .flat();
      const motionResult = res.Data.Report.filter((s) => s.Sign === 2)
        .map((v) => v.Report)
        .flat();
      this.cognitiveResult = cognitiveResult;
      this.motionResult = motionResult;
      this.CreatedTime = this.$dateFormat(res.Data.CreatedTime, 'YYYY-MM-DD');
      uni.hideLoading();
    },
    handleFurtherClick() {
      uni.navigateTo({
        url:
          './treatmentMode?doctorId=' + this.doctorId + '&reportId=' + this.Id,
      });
      // if (this.from === 'fxyg') {
      //   this.handleToSessionChatPage();
      //   return;
      // }
      // // fxyg风险预估
      // uni.navigateTo({
      //   url: `/subPackIndex/seeDoc?scenType=fxyg&reportId=${this.Id}`,
      // });
    },
    async handleToSessionChatPage() {
      uni.showLoading({
        title: '加载中',
        mask: true,
      });
      let res1 = await getDocInfoMation({
        doctorId: this.doctorId,
      });
      if (res1.Type === 200) {
        this.docInfo = res1.Data;
      }
      getApp().subscribeMessage(async () => {
        let query = {
          UserId: app.globalData.userInfo.Id,
          UserName: app.globalData.userInfo.Name,
          Sex: app.globalData.userInfo.Sex,
          DocUserId: this.docInfo.Doctor.UserId,
          Organization: this.docInfo.Doctor.OrganizationId,
          OrganizationName: this.docInfo.Doctor.OrganizationName,
          DepartmentId: this.docInfo.Doctor.DepartmentId,
          DepartmentName: this.docInfo.Doctor.DepartmentName,
          CostState: 1,
          PayAlias: 'com.kangfx.wx.mp.patient' + `-${config.resources}`,
          ConsultWay: 1, // 1 问诊  2 咨询
          Source: 1,
          CreatorId: app.globalData.userInfo.Id,
          AutoCreateMedical: true, // 医生就是true 治疗师就是false
          Describing: '',
          OfflineDate: this.$dateFormat(
            new Date(),
            'YYYY-MM-DD HH:mm:ss',
            false
          ),
          HospitName: this.docInfo.Doctor.OrganizationName,
          ConsultReportInputs: [],
          Extend: {
            Extend: JSON.stringify({
              Type: 'HxUnion',
              Data: this.Id,
            }),
          },
          IsValidateDct: false,
        };
        let res = await getPayOrderInfo(query);
        if (res.Type == 200 && res.Data.Amount == 0) {
          // 不需要支付费用
          let consultId = res.Data.ConsultId;
          let backPath = '/pages/interview/index';
          uni.navigateTo({
            url:
              '/subPackChat/sessionChatPage?consultId=' +
              consultId +
              '&backPath=' +
              backPath +
              '&fromAddInquiry=true',
            complete: () => {
              uni.hideLoading();
            },
          });
        } else if (res.Type == 200 && res.Data.Amount > 0) {
          // 需要支付费用
          uni.navigateTo({
            url: `/subPrescription/cashier?orderId=${res.Data.OrderNo}&docName=${this.docInfo.Doctor.Name}&needMoeny=${res.Data.Amount}&consultId=${res.Data.ConsultId}&PaymentId=${res.Data.Payment.Id}`,
            complete: () => {
              uni.hideLoading();
            },
          });
        } else {
          // uni.showModal({
          // 	title: '温馨提示',
          // 	content: res.Content,
          // 	showCancel: false,
          // })
          // fxyg风险预估
          uni.navigateTo({
            url: `/subPackIndex/seeDoc?scenType=fxyg&reportId=${this.Id}`,
          });
          uni.hideLoading();
        }
        let data1 = {
          UserId: app.globalData.userInfo.Id,
          FollowUserId: this.docInfo.Doctor.UserId,
        };
        await setOrCanDoc('setfollow', data1);
      });
    },
    handleGetSingOneResult(list) {
      console.log('list', list);
      if (!list.length) {
        return '';
      }
      const source = (list[0].Report[0].Result || 0) * 1;
      return '得分：' + source.toFixed(3) + '，' + list[0].ShowTips;
    },
    handleGetIsHaveProblem() {
      if (!this.cognitiveResult.length) {
        return false;
      }
      return this.cognitiveResult[0].ExistProblem;
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding: 32rpx;

  &-box {
    background: #ffffff;
    border-radius: 12rpx 12rpx 12rpx 12rpx;
    padding: 28rpx;
    margin-bottom: 20rpx;

    &-top {
      display: flex;
      justify-content: flex-start;
      align-items: center;

      &-name {
        font-weight: 500;
        font-size: 32rpx;
        color: #323233;
      }

      &-base {
        width: 115rpx;
        height: 38rpx;
        border-radius: 4rpx 4rpx 4rpx 4rpx;
        font-weight: 400;
        font-size: 23rpx;
        color: #ffffff;
        margin-left: 30rpx;
        text-align: center;
        line-height: 38rpx;
      }

      &-has {
        background: #ff9d24;
      }

      &-no {
        background: #00bfa6;
      }
    }

    &-user {
      display: flex;
      justify-content: flex-start;
      align-items: center;

      &-box {
        width: 50%;
        font-weight: 400;
        font-size: 28rpx;
        color: #323233;
        text-align: left;
      }
    }

    &-item {
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;
      margin-top: 38rpx;

      &-left {
        width: 183rpx;
        height: 38rpx;
        color: #fa9923;
        background: rgba(255, 157, 36, 0.15);
        border-radius: 4rpx 4rpx 4rpx 4rpx;
        text-align: center;
        padding: 2rpx 0;
        font-weight: 400;
        font-size: 24rpx;
      }

      &-right {
        margin-left: 28rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: #323233;
        flex: 1;
      }
    }

    &-title {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin-bottom: 28rpx;

      &-recommendations {
        font-weight: 500;
        font-size: 32rpx;
        color: #323233;
        margin-left: 12rpx;
      }
    }

    &-recommendations {
      font-weight: 400;
      font-size: 28rpx;
      color: #323233;
    }
  }

  &-time {
    font-weight: 400;
    font-size: 24rpx;
    color: #3f3d56;
  }
}
</style>
