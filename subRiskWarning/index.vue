<template>
  <view class="container">
    <view class="container-top">
      以下信息对于预测判断非常重要，有助于预测的完整度及准确度，信息越准确详细，预测结果越准确，请认真如实填写。
    </view>
    <view class="container-bottom">
      <view class="container-base">
        <text class="container-base-titleBox">
          <text class="container-base-titleBox-title">基本信息</text>
          <text class="container-base-titleBox-require">*</text>
        </text>
        <view class="container-base-list">
          <block v-if="userInfo.WorkflowStatus != 2">
            <view class="container-base-list-item">
              <text class="container-base-list-item-left">
                <text class="container-base-list-item-left-title">姓名</text>
                <text class="container-base-titleBox-require">*</text>
              </text>
              <view class="container-base-list-item-right">
                <u-input
                  v-model="baseParams.Name"
                  placeholder="请输入姓名"
                  border="surround"
                  clearable
                  inputAlign="right"
                />
              </view>
            </view>
            <view class="container-base-list-item">
              <text class="container-base-list-item-left">
                <text class="container-base-list-item-left-title">性别</text>
                <text class="container-base-titleBox-require">*</text>
              </text>
              <view class="container-base-list-item-right">
                <u-radio-group placement="row" v-model="baseParams.Sex">
                  <u-radio activeColor="#29B7A3" label="男" name="男"></u-radio>
                  <u-radio activeColor="#29B7A3" label="女" name="女"></u-radio>
                </u-radio-group>
              </view>
            </view>
            <view class="container-base-list-item">
              <text class="container-base-list-item-left">
                <text class="container-base-list-item-left-title">年龄</text>
                <text class="container-base-titleBox-require">*</text>
              </text>
              <view class="container-base-list-item-right">
                <u-input
                  v-model="baseParams.Age"
                  placeholder="请输入年龄"
                  type="number"
                  border="surround"
                  clearable
                  inputAlign="right"
                />
              </view>
            </view>
          </block>
          <view class="container-base-list-item">
            <text class="container-base-list-item-left">
              <text class="container-base-list-item-left-title">文化程度</text>
              <text class="container-base-titleBox-require">*</text>
            </text>
            <view
              class="container-base-list-item-right"
              @click="handleDegreeClick"
            >
              <view class="container-base-list-item-right-rightItem">
                <text
                  class="container-base-list-item-right-rightItem-notValue"
                  v-if="!baseParams.Education"
                  >请选择</text
                >
                <text
                  class="container-base-list-item-right-rightItem-hasValue"
                  v-else
                  >{{ baseParams.Education }}</text
                >
                <u-icon
                  name="arrow-down-fill"
                  size="24"
                  color="#D9D9D9"
                ></u-icon>
              </view>
            </view>
          </view>
          <view class="container-base-list-itemLast" @click="handleMoreClick">
            <u-icon name="plus-circle" size="20" color="#00BFA6"></u-icon>
            <text style="margin-left: 10rpx">更多信息</text>
          </view>
        </view>
      </view>
      <view class="container-base">
        <text class="container-base-titleBox">
          <text class="container-base-titleBox-title">认知功能筛查</text>
          <text class="container-base-titleBox-require">*</text>
        </text>
        <view
          class="container-base-gauge"
          v-for="item in cognitiveGaugeList"
          :key="item.Id"
          @click="handleCognitiveScreeningClick(item)"
        >
          <text class="container-base-gauge-name text-max2">{{
            item.Name
          }}</text>
          <view class="container-base-gauge-btn" v-if="item.IsFinish">
            <u-icon name="checkbox-mark" size="20" color="#FFFFFF"></u-icon>
            <text class="container-base-gauge-btn-name">已填写</text>
          </view>
          <view class="container-base-gauge-btnNo" v-else> 未填写 </view>
        </view>
      </view>
      <view class="container-base">
        <text class="container-base-titleBox">
          <text class="container-base-titleBox-title">运动功能筛查</text>
          <text class="container-base-titleBox-require">*</text>
        </text>
        <view
          class="container-base-gauge"
          v-for="item in motionGaugeList"
          :key="item.Id"
          @click="handleCognitiveScreeningClick(item)"
        >
          <text class="container-base-gauge-name text-max2">{{
            item.Name
          }}</text>
          <view class="container-base-gauge-btn" v-if="item.IsFinish">
            <u-icon name="checkbox-mark" size="20" color="#FFFFFF"></u-icon>
            <text class="container-base-gauge-btn-name">已填写</text>
          </view>
          <view class="container-base-gauge-btnNo" v-else> 未填写 </view>
        </view>
      </view>
    </view>
    <view class="btnButtomStyle" @click="handleSubmitClick"> 提交 </view>
    <u-action-sheet
      :actions="degreeList"
      @select="selectDegreeClick"
      title="请选择文化程度"
      :show="showDegreeActionSheet"
      @close="showDegreeActionSheet = false"
    ></u-action-sheet>
  </view>
</template>

<script>
import actionSheet from '@/subPackIndex/user/data/localData.js';
import {
  getRiskWarningGaugePage,
  riskWarningWriteGauge,
} from '@/api/tenant.js';
import { setUserInfo, getUserInfoArch } from '@/api/record.js';
import { AssessClientEvent } from '@/utils/eventKeys.js';
const app = getApp();
export default {
  data() {
    return {
      baseParams: {
        Name: '',
        Age: null,
        Sex: '',
        Education: '',
      },
      showDegreeActionSheet: false,
      degreeList: [],
      cognitiveGaugeList: [],
      motionGaugeList: [],
      userInfo: {},
      doctorId: '',
      from: '',
    };
  },
  async onLoad() {
    this.userInfo = app.globalData.userInfo;
    // 获取文化程度
    this.baseParams = {
      ...this.baseParams,
      Name: app.globalData.userInfo.Name,
      Age: app.globalData.userInfo.Age,
      Sex: app.globalData.userInfo.Sex,
    };
    this.degreeList = actionSheet.Education;
    // 获取风险预警的两个特殊评估
    this.onGetRiskWarningGaugePage();
    uni.$on(AssessClientEvent.updateRiskAssess, this.getSelectAnswer);
    this.handleGetFormByWx();
    // 获取学历
    this.onGetEducation();
  },
  methods: {
    async onGetEducation() {
      const res = await getUserInfoArch({ userId: app.globalData.userInfo.Id });
      if (res.Type === 200) {
        this.baseParams.Education = res.Data.Education;
      }
    },
    handleGetFormByWx() {
      const res = uni.getEnterOptionsSync();
      console.log('res', res);
      let localScene = uni.getStorageSync('localScene') || {};
      uni.setStorageSync('localScene', {
        scene: res.scene,
        params: res.query,
      });
      if (res.scene === 1011 || res.scene === 1012) {
        localScene.scene = res.scene;
        localScene.params = res.query;
      }
      console.log('localScene', localScene.scene);
      switch (localScene.scene) {
        case 1011: // 扫描二维码进入
        case 1012: // 长按二维码进入
          if (!localScene.params) return;
          const q = localScene.params.q;
          if (!q) return;
          const decodeUrl = decodeURIComponent(q);
          const obj = this.getParms(decodeUrl);
          if (obj && obj.docId) {
            this.doctorId = obj.docId;
            this.from = 'fxyg';
          }
          break;
      }
    },
    getParms(url) {
      if (url.indexOf('?') != -1) {
        let obj = {};
        let arr = url.slice(url.indexOf('?') + 1).split('&');
        arr.forEach((item) => {
          let param = item.split('=');
          obj[param[0]] = param[1];
        });
        return obj;
      } else {
        console.log('没有参数');
        return null;
      }
    },
    async onGetRiskWarningGaugePage() {
      const res = await getRiskWarningGaugePage({
        PageIndex: 1,
        PageSize: 999,
        Signs: [1, 2],
        OrgId: app.globalData.orgId,
        ReferenceId: null,
      });
      if (res.Type === 200 && res.Data.Data.length) {
        this.cognitiveGaugeList = res.Data.Data.filter((s) => s.Sign === 1);
        this.motionGaugeList = res.Data.Data.filter((s) => s.Sign === 2);
      }
    },
    handleMoreClick() {
      uni.navigateTo({
        url: '/subPackIndex/user/improveArchives',
      });
    },
    handleDegreeClick() {
      this.showDegreeActionSheet = true;
    },
    selectDegreeClick(item) {
      this.baseParams.Education = item.name;
      this.showDegreeActionSheet = false;
    },
    getSelectAnswer(item) {
      console.log('item', item);
      const listMapping = {
        2: this.motionGaugeList,
        1: this.cognitiveGaugeList,
      };
      const list = listMapping[item.Sign];
      if (!list?.length) return;
      const index = list.findIndex((s) => s.Id === item.Id);
      if (index === -1) return;
      list[index].Questions = item.Questions;
      list[index].IsFinish = item.IsFinish;
      list[index].StartDate = item.StartDate;
      list[index].EndDate = item.EndDate;
      // 直接更新相应列表
      if (item.Sign === 2) {
        this.motionGaugeList = [...list];
      } else if (item.Sign === 1) {
        this.cognitiveGaugeList = [...list];
      }
    },
    async handleSubmitClick() {
      // 检查基本信息是否填写完整
      let isBaseParamsComplete = true;
      for (let key in this.baseParams) {
        if (!this.baseParams[key]) {
          isBaseParamsComplete = false;
          break;
        }
      }
      if (!isBaseParamsComplete) {
        uni.showToast({
          title: '请填写完整基本信息',
          icon: 'none',
        });
        return;
      }
      if (this.baseParams.Age <= 0) {
        uni.showToast({
          title: '年龄必须大于0岁',
          icon: 'none',
        });
        return;
      }
      // 判断认知功能筛查和运动功能筛查是否都填写了
      if (this.cognitiveGaugeList.some((s) => !s.IsFinish)) {
        uni.showToast({
          title: '请将认知功能筛查完成',
          icon: 'none',
        });
        return;
      }
      if (this.motionGaugeList.some((s) => !s.IsFinish)) {
        uni.showToast({
          title: '请将运动功能筛查完成',
          icon: 'none',
        });
        return;
      }
      const copyMotionGauge = JSON.parse(JSON.stringify(this.motionGaugeList));
      const copyCognitiveGauge = JSON.parse(
        JSON.stringify(this.cognitiveGaugeList)
      );
      const allGaugeList = [...copyMotionGauge, ...copyCognitiveGauge];
      allGaugeList.forEach((v) => {
        v.Questions.forEach((s) => {
          if ((s.Type === 1 || s.Type === 0) && s.Answer) {
            s.Answer = JSON.stringify(s.Answer);
          }
          if (s.Type === 3 && s.Answer !== null) {
            s.Answer = String(s.Answer);
          }
        });
        v.UserId = app.globalData.userInfo.Id;
        if (v.Id) {
          v.BaseGaugeId = v.Id;
          delete v.Id;
        }
      });
      console.log('所有的筛查数据', allGaugeList);
      // 发送请求
      uni.showLoading({
        title: '提交中',
        mask: true,
      });
      if (this.userInfo.WorkflowStatus !== 2) {
        try {
          app.changeUserInfo('Name', this.baseParams.Name);
          app.changeUserInfo('Sex', this.baseParams.Sex);
          app.changeUserInfo('Age', this.baseParams.Age);
          // 修改当前用户的信息
          await setUserInfo({
            UserId: app.globalData.userInfo.Id,
            Name: this.baseParams.Name,
            Birthday: app.getBirthdayByAge(this.baseParams.Age),
            Sex: this.baseParams.Sex,
            Education: this.baseParams.Education,
          });
        } catch (e) {}
      }
      riskWarningWriteGauge(allGaugeList)
        .then((res) => {
          if (res.Type === 200 && res.Data) {
            uni.showToast({
              title: '提交成功',
              icon: 'none',
            });
            uni.navigateTo({
              url: `./result?Id=${res.Data}&from=${this.from}&doctorId=${this.doctorId}`,
            });
          } else {
            uni.showModal({
              title: '温馨提示',
              content: res.Content,
              showCancel: false,
            });
          }
        })
        .finally(() => {
          uni.hideLoading();
        });
    },
    handleCognitiveScreeningClick(item) {
      if (!item.Id) {
        uni.showToast({
          title: '无法加载该评估的数据',
          icon: 'none',
        });
        return;
      }
      uni.navigateTo({
        url: './screeningGauge?Id=' + item.Id,
      });
    },
  },
  onUnload() {
    const copyMotionGauge = JSON.parse(JSON.stringify(this.motionGaugeList));
    const copyCognitiveGauge = JSON.parse(
      JSON.stringify(this.cognitiveGaugeList)
    );
    const allGaugeIds = [...copyMotionGauge, ...copyCognitiveGauge].map(
      (s) => s.Id
    );
    allGaugeIds.forEach((s) => {
      uni.removeStorageSync(s);
    });
    uni.$off(AssessClientEvent.updateRiskAssess, this.getSelectAnswer);
  },
};
</script>

<style lang="scss" scoped>
.container {
  &-top {
    height: 115rpx;
    background: rgba(255, 195, 0, 0.2);
    border-radius: 0rpx 0rpx 0rpx 0rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #545353;
    line-height: 38rpx;
    padding: 20rpx;
  }

  &-bottom {
    padding: 0 32rpx;
  }

  &-base {
    margin-top: 42rpx;

    &-titleBox {
      &-title {
        font-weight: 500;
        font-size: 32rpx;
        color: #000000;
        line-height: 34rpx;
      }

      &-require {
        font-weight: 400;
        font-size: 28rpx;
        color: #ff4040;
        line-height: 34rpx;
      }
    }

    &-list {
      margin-top: 28rpx;
      background: #ffffff;
      border-radius: 12rpx 12rpx 12rpx 12rpx;

      &-item {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        padding: 0 28rpx;
        height: 90rpx;
        border-bottom: 1rpx solid rgba(159, 159, 159, 0.45);

        &-left {
          &-title {
            font-weight: 400;
            font-size: 28rpx;
            color: #000000;
            line-height: 34rpx;
          }
        }

        &-right {
          flex: 1;

          &-rightItem {
            display: flex;
            justify-content: flex-end;
            align-items: center;

            &-notValue {
              font-weight: 400;
              font-size: 28rpx;
              color: #808080;
              line-height: 34rpx;
              margin-right: 28rpx;
            }

            &-hasValue {
              font-weight: 400;
              font-size: 28rpx;
              color: #000000;
              line-height: 34rpx;
              margin-right: 28rpx;
            }
          }
        }
      }

      &-itemLast {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 90rpx;
        font-weight: 400;
        font-size: 27rpx;
        color: #00bfa6;
        line-height: 34rpx;
      }
    }

    &-gauge {
      background: #ffffff;
      border-radius: 12rpx 12rpx 12rpx 12rpx;
      padding: 28rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 28rpx;

      &-name {
        font-weight: 400;
        font-size: 28rpx;
        color: #000000;
        line-height: 46rpx;
        flex: 1;
        margin-right: 24rpx;
      }

      &-btn {
        width: 140rpx;
        height: 58rpx;
        background: #00bfa6;
        border-radius: 12rpx 12rpx 12rpx 12rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 10rpx;

        &-name {
          font-weight: 400;
          font-size: 24rpx;
          color: #ffffff;
          color: white;
        }
      }

      &-btnNo {
        width: 140rpx;
        height: 58rpx;
        border-radius: 12rpx 12rpx 12rpx 12rpx;
        border: 2rpx solid #ff9d24;
        font-weight: 400;
        font-size: 23rpx;
        color: #ff9d24;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 10rpx;
      }
    }
  }
}

/deep/ .u-input {
  border: none !important;
}

/deep/ .u-radio-group--row {
  justify-content: flex-end;
}

/deep/ .u-radio {
  margin-left: 50rpx !important;
  margin-right: 0 !important;
}
</style>
