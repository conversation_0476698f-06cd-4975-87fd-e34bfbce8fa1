<template>
  <view class="container">
    <block v-for="(item, index) in list" :key="index">
      <view
        @click="handleSelectTreatmentMode(item)"
        class="container-wapper"
        :style="{
          'backgroundImage': 'url(' + item.url + ')',
          'width': '100%',
          'background-size': '100% 100%',
          'background-repeat': 'no-repeat',
        }"
      >
        {{ item.title }}
      </view>
    </block>
  </view>
</template>

<script>
import config from '@/config';
import { getCurrentScheduleDoctor } from '@/api/tenant.js';
import { getDocInfoMation, getPayOrderInfo } from '@/api/consult.js';
import { setOrCanDoc } from '@/api/passport.js';
const app = getApp();
export default {
  data() {
    return {
      doctorId: '',
      reportId: '',
      list: [
        {
          title: '线下诊疗',
          url: 'https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/static/frontend/mp-patient/treatmentMode_icon3.png',
        },
        {
          title: '线上诊疗',
          url: 'https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/static/frontend/mp-patient/treatmentMode_icon2.png',
        },
        {
          title: '无人诊疗',
          url: 'https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/static/frontend/mp-patient/treatmentMode_icon1.png',
        },
      ],
      orgId: '',
      docInfo: {},
    };
  },
  onLoad(option) {
    if (option.doctorId) {
      this.doctorId = option.doctorId;
      // 获取当前扫描二维码医生的信息
      this.onGetDoctorOrgInfo(option.doctorId);
    } else {
      // 获取当前用户选择的医院
      this.orgId = app.globalData.orgId;
    }
    this.reportId = option.reportId;
  },
  methods: {
    async onGetDoctorOrgInfo(doctorId) {
      let res = await getDocInfoMation({
        doctorId,
      });
      if (res.Type === 200) {
        this.docInfo = res.Data;
        this.doctorId = res.Data.Doctor.UserId;
        this.orgId = res.Data.Doctor.OrganizationId;
      }
    },
    handleSelectTreatmentMode(type) {
      switch (type.title) {
        case '线下诊疗':
          this.handleToSessionChatPage();
          uni.removeStorageSync('localScene');
          break;
        case '线上诊疗':
          this.onGetCurrentScheduleDoctor();
          uni.removeStorageSync('localScene');
          break;
        case '无人诊疗':
          uni.showModal({
            title: '提示',
            content: '功能开发中',
            showCancel: false,
          });
          break;
      }
    },
    async onGetCurrentScheduleDoctor() {
      const res = await getCurrentScheduleDoctor({
        OrgId: this.orgId,
      });
      if (res.Type === 200) {
        if (res.Data && res.Data.DoctorId) {
          await this.onGetDoctorOrgInfo(res.Data.DoctorId);
          this.handleToSessionChatPage();
        } else {
          this.handleToSelectDoctorPage();
        }
      }
    },
    handleToSelectDoctorPage() {
      uni.navigateTo({
        url: `/subPackIndex/seeDoc?scenType=fxyg&reportId=${this.reportId}`,
      });
    },
    async handleToSessionChatPage() {
      uni.showLoading({
        title: '加载中',
        mask: true,
      });
      if (!this.doctorId) {
        this.handleToSelectDoctorPage();
        return;
      }
      getApp().subscribeMessage(async () => {
        let query = {
          UserId: app.globalData.userInfo.Id,
          UserName: app.globalData.userInfo.Name,
          Sex: app.globalData.userInfo.Sex,
          DocUserId: this.docInfo.Doctor.UserId,
          Organization: this.docInfo.Doctor.OrganizationId,
          OrganizationName: this.docInfo.Doctor.OrganizationName,
          DepartmentId: this.docInfo.Doctor.DepartmentId,
          DepartmentName: this.docInfo.Doctor.DepartmentName,
          CostState: 1,
          PayAlias: 'com.kangfx.wx.mp.patient' + `-${config.resources}`,
          ConsultWay: 1, // 1 问诊  2 咨询
          Source: 1,
          CreatorId: app.globalData.userInfo.Id,
          AutoCreateMedical: true, // 医生就是true 治疗师就是false
          Describing: '',
          OfflineDate: this.$dateFormat(
            new Date(),
            'YYYY-MM-DD HH:mm:ss',
            false
          ),
          HospitName: this.docInfo.Doctor.OrganizationName,
          ConsultReportInputs: [],
          Extend: {
            Extend: JSON.stringify({
              Type: 'HxUnion',
              Data: this.reportId,
            }),
          },
          IsValidateDct: false,
        };
        let res = await getPayOrderInfo(query);
        if (res.Type == 200 && res.Data.Amount == 0) {
          // 不需要支付费用
          let consultId = res.Data.ConsultId;
          let backPath = '/pages/interview/index';
          uni.navigateTo({
            url:
              '/subPackChat/sessionChatPage?consultId=' +
              consultId +
              '&backPath=' +
              backPath +
              '&fromAddInquiry=true',
            complete: () => {
              uni.hideLoading();
            },
          });
        } else if (res.Type == 200 && res.Data.Amount > 0) {
          // 需要支付费用
          uni.navigateTo({
            url: `/subPrescription/cashier?orderId=${res.Data.OrderNo}&docName=${this.docInfo.Doctor.Name}&needMoeny=${res.Data.Amount}&consultId=${res.Data.ConsultId}&PaymentId=${res.Data.Payment.Id}`,
            complete: () => {
              uni.hideLoading();
            },
          });
        } else {
          // fxyg风险预估
          uni.navigateTo({
            url: `/subPackIndex/seeDoc?scenType=fxyg&reportId=${this.reportId}`,
          });
          uni.hideLoading();
        }
        let data1 = {
          UserId: app.globalData.userInfo.Id,
          FollowUserId: this.docInfo.Doctor.UserId,
        };
        await setOrCanDoc('setfollow', data1);
      });
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  padding: 26rpx 22rpx;
  background-image: url('https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/static/frontend/mp-patient/treatmentMode-bg.png');
  width: 100%;
  height: 100vh;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  &-wapper {
    display: flex;
    font-weight: 600;
    font-size: 32rpx;
    color: #333333;
    line-height: 34rpx;
    height: 172rpx;
    padding: 59rpx 187rpx 82rpx 187rpx;
  }
}
</style>
