<template>
  <view
    class="container"
    :class="[animationClass]"
    @animationend="animationEnd"
  >
    <view class="container-question">
      <text class="container-question-title"
        >{{ questions.Text }}
        <text style="color: red" v-if="questions.Required">*</text></text
      >
      <view
        hover-class="press-down-style"
        hover-stay-time="70"
        class="container-question-voice"
        @click="handleReadQuestion(questions.Text, -2)"
      >
        <!-- <u-icon name="volume-fill" size="20" color="#29b7a3"></u-icon> -->
        <image
          :src="
            clickIndex === -2 ? '../static/voice.gif' : '../static/voice.png'
          "
          style="width: 20px; height: 20px"
        ></image>
        <text style="margin-left: 20rpx">语音读题</text>
      </view>
      <!-- 是否需要显示额外的数据展示 -->
      <view
        class="container-question-ext"
        v-if="questions.Ext && questions.Ext.Diagram === 'HumanBody'"
      >
        <image
          src="../static/icon-body.png"
          class="container-question-ext-body"
        ></image>
        <block v-for="(item, index) in bodyPoints" :key="index">
          <image
            @click="handlePointClick(item.Ext)"
            :style="{ top: item.Top, left: item.Left }"
            :src="
              item.IsSelect
                ? '../static/icon-select.png'
                : '../static/icon-default.png'
            "
            class="container-question-ext-point"
          ></image>
        </block>
      </view>
      <!-- 选择项 -->
      <view class="container-selection">
        <!-- 单选0或者多选1  因为单选组件无法取消勾选 -->
        <block v-if="questions.Type === 0 || questions.Type === 1">
          <view
            class="container-selection-box"
            v-if="!questions.Ext || !questions.Ext.Diagram"
          >
            <u-checkbox-group
              @change="handleCheckBoxGroupChange"
              :borderBottom="true"
              :shape="questions.Type ? 'square' : 'circle'"
              v-model="questions.Answer"
              iconPlacement="right"
              placement="column"
            >
              <view
                style="
                  display: flex;
                  align-items: flex-start;
                  justify-content: flex-start;
                "
                v-for="(item, index) in questions.Options"
                :key="item.Text"
              >
                <view
                  hover-class="press-down-style"
                  hover-stay-time="70"
                  style="width: 20px; height: 20px"
                  :style="index === 0 ? '' : 'padding-top: 16px;'"
                >
                  <image
                    :src="
                      clickIndex === index
                        ? '../static/voice.gif'
                        : '../static/voice.png'
                    "
                    style="width: 20px; height: 20px"
                    @click="handleReadQuestion(item.Text, index)"
                  ></image>
                </view>
                <u-checkbox
                  :customStyle="{
                    marginLeft: '12px',
                    flex: 1,
                    ...(index === 0 ? {} : { paddingTop: '16px' }),
                  }"
                  :name="item.Value !== undefined ? item.Value : item.Text"
                  :label="item.Text"
                  activeColor="#29b7a3"
                >
                </u-checkbox>
              </view>
            </u-checkbox-group>
          </view>
          <view
            class="container-selection-box"
            v-if="questions.Ext && questions.Ext.Diagram === 'HumanBody'"
          >
            <u-checkbox-group
              @change="handleCheckBoxGroupChange"
              :borderBottom="true"
              :shape="questions.Type ? 'square' : 'circle'"
              v-model="questions.Answer"
              iconPlacement="right"
              placement="column"
            >
              <view
                style="
                  display: flex;
                  align-items: center;
                  justify-content: flex-start;
                  width: 100%;
                  flex-wrap: wrap;
                "
              >
                <view
                  style="
                    display: flex;
                    align-items: flex-start;
                    justify-content: flex-start;
                    width: 50%;
                    margin-bottom: 32rpx;
                  "
                  v-for="(item, index) in questions.Options"
                  :key="item.Text"
                >
                  <!--  @click="handleReadQuestion(item.Text)" -->
                  <!-- <u-icon name="volume-fill" size="20" color="#29b7a3"></u-icon> -->
                  <u-checkbox
                    :customStyle="{ flex: 1 }"
                    :name="item.Value !== undefined ? item.Value : item.Text"
                    :label="item.Text"
                    activeColor="#29b7a3"
                  >
                  </u-checkbox>
                </view>
              </view>
            </u-checkbox-group>
          </view>
        </block>
        <!-- 文本2 -->
        <block v-else-if="questions.Type === 2">
          <view class="container-selection-box">
            <u-textarea
              v-model="questions.Answer"
              autoHeight
              placeholder="请输入内容"
            ></u-textarea>
          </view>
        </block>
        <!-- 数值3 -->
        <block v-else-if="questions.Type === 3">
          <view class="container-selection-box">
            <u-input
              v-model="questions.Answer"
              type="number"
              placeholder="请输入分值"
              border="surround"
              clearable
            ></u-input>
          </view>
        </block>
      </view>
    </view>
  </view>
</template>

<script>
const WechatSI = requirePlugin('WechatSI');
import bodyPoints from './bodySelect.js';
import { screeningGauge } from '../localData.js';
export default {
  props: {
    questionId: {
      type: Number,
      required: true,
      default: 0,
    },
    selectedGaugeParams: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    direction: {
      type: String,
      required: true,
      default: 'right',
    },
  },
  watch: {
    questionId: {
      handler(newValue, oldValue) {
        if (newValue !== oldValue) {
          this.onGetQuestions(newValue);
          // 将页面滚动到顶部
          uni.pageScrollTo({
            scrollTop: 0,
            duration: 0,
          });
          this.setAnimationClass();
        }
      },
      immediate: true,
    },
  },
  data() {
    return {
      questions: {},
      bodyPoints: [],
      innerAudioContext: null,
      clickIndex: -1,
      animationClass: [],
    };
  },
  created() {
    this.innerAudioContext = uni.createInnerAudioContext({
      useWebAudioImplement: false, // 是否使用 WebAudio 作为底层音频驱动，默认关闭。对于短音频、播放频繁的音频建议开启此选项，开启后将获得更优的性能表现。由于开启此选项后也会带来一定的内存增长，因此对于长音频建议关闭此选项
    });
  },
  destroyed() {
    console.log('destroyed');
    this.innerAudioContext.destroy();
    this.innerAudioContext = null;
  },
  methods: {
    // 设置动画类
    setAnimationClass() {
      this.animationClass =
        this.direction === 'left' ? 'slide-in-left' : 'slide-in-right';
    },
    // 动画结束后清除类名
    animationEnd() {
      this.animationClass = '';
    },
    handlePointClick(ext) {
      // 如果存在就删除
      if (this.questions.Answer && this.questions.Answer.includes(ext)) {
        this.questions.Answer = this.questions.Answer.filter((s) => s != ext);
      } else {
        this.questions.Answer = this.questions.Answer || [];
        this.questions.Answer.push(ext);
      }
      const pointIndex = this.bodyPoints.findIndex((s) => s.Ext == ext);
      this.bodyPoints[pointIndex].IsSelect =
        !this.bodyPoints[pointIndex].IsSelect;
    },
    handleReadQuestion(text, index) {
      this.innerAudioContext.stop();
      WechatSI.textToSpeech({
        lang: 'zh_CN',
        content: text,
        success: (r) => {
          console.log('success', r);
          if (r.retcode !== 0) return;
          this.innerAudioContext.src = r.filename;
          this.innerAudioContext.play(); // 播放
          this.innerAudioContext.onPlay(() => {
            console.log('开始播放');
            this.clickIndex = index;
          });
          this.innerAudioContext.onStop(() => {
            console.log('停止播放');
            this.clickIndex = -1;
            // this.innerAudioContext.destroy();
          });
          this.innerAudioContext.onEnded(() => {
            console.log('播放完成');
            this.clickIndex = -1;
          });
          this.innerAudioContext.onError((res) => {
            console.log(res.errMsg);
            console.log(res.errCode);
          });
        },
        fail: (e) => {
          console.log('fail', e);
          console.timeEnd('a');
        },
      });
    },
    generateArray(list) {
      const start = list[0];
      const end = list[1];
      const step = list[2];
      const result = [];
      for (let i = start; i <= end; i += step) {
        result.push(i);
      }
      return result;
    },
    onGetQuestions(questionsId) {
      this.questions = this.selectedGaugeParams.Questions.filter(
        (s) => s.Key === questionsId
      )[0];
      if (this.questions.List && this.questions.List.length) {
        let Options = [];
        const list = this.generateArray(this.questions.List);
        list.forEach((s) => {
          const obj = {
            Value: s,
            Text: s + this.questions.Unit,
            Jump: null, // 单选跳转下一题Key, null 表示 questions 中的下一个
            isDone: false, // 是否完成
          };
          Options.push(obj);
        });
        this.questions.Options = Options;
      }
      this.$nextTick(() => {
        if (this.questions.Ext && this.questions.Ext.Diagram === 'HumanBody') {
          this.bodyPoints = JSON.parse(JSON.stringify(bodyPoints));
        }
        if (
          this.questions.Type === 1 &&
          this.questions.Answer !== null &&
          this.questions.Ext &&
          this.questions.Ext.Diagram === 'HumanBody'
        ) {
          const selectPoints = this.questions.Answer;
          this.bodyPoints.forEach((s) => {
            if (selectPoints.some((v) => s.Ext == v)) {
              s.IsSelect = true;
            } else {
              s.IsSelect = false;
            }
          });
        }
      });
    },
    handleCheckBoxGroupChange(e) {
      const value = this.questions.Answer;
      if (this.questions.Type === 0) {
        this.$nextTick(() => {
          if (value) {
            if (!e.length) {
              this.questions.Answer = value;
            } else {
              if (value.length !== e.length) {
                const beforeValue = value[0];
                this.questions.Answer = e.filter((s) => s !== beforeValue);
              } else {
                this.questions.Answer = value;
              }
            }
          } else {
            this.questions.Answer = e;
          }
          this.$emit('getFinishSelectQuestions', {
            question: this.questions,
          });
        });
      } else if (
        this.questions.Type === 1 &&
        this.questions.Ext &&
        this.questions.Ext.Diagram === 'HumanBody'
      ) {
        this.bodyPoints.forEach((s) => {
          if (e.some((v) => s.Ext == v)) {
            s.IsSelect = true;
          } else {
            s.IsSelect = false;
          }
        });
      }
    },
    onGetFinishQuestions() {
      if (this.questions.Required) {
        switch (this.questions.Type) {
          case 0:
          case 1:
            if (!this.questions.Answer || !this.questions.Answer.length) {
              uni.showToast({
                title: '请选择',
                icon: 'none',
              });
              return null;
            }
          case 2:
          case 3:
            if (this.questions.Answer === null) {
              uni.showToast({
                title: '请输入',
                icon: 'none',
              });
              return null;
            }
        }
      }
      return {
        question: this.questions,
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  height: auto;
  &-question {
    &-title {
      font-weight: 400;
      font-size: 32rpx;
      color: #000000;
      display: block;
    }

    &-voice {
      width: 195rpx;
      height: 58rpx;
      border-radius: 12rpx 12rpx 12rpx 12rpx;
      border: 2rpx solid #29b7a3;
      color: #29b7a3;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 400;
      font-size: 26rpx;
      color: #29b7a3;
      margin-top: 20rpx;
    }
    &-ext {
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 688rpx;
      height: 822rpx;
      margin: 0 auto;
      margin-top: 24rpx;
      &-body {
        width: 688rpx;
        height: 822rpx;
      }
      &-point {
        position: absolute;
        width: 56rpx;
        height: 56rpx;
        z-index: 9;
      }
    }
  }

  &-selection {
    margin-top: 24rpx;

    &-box {
      padding: 32rpx;
      background: #ffffff;
      border-radius: 32rpx;
    }
  }
}
/* 向左滑入 */
.slide-in-left {
  animation: slideInLeft 0.5s ease-in-out forwards;
}

/* 向右滑入 */
.slide-in-right {
  animation: slideInRight 0.5s ease-in-out forwards;
}

/* 动画定义 */
@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}
</style>
