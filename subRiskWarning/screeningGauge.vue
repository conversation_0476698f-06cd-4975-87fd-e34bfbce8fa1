<template>
  <view class="container">
    <view class="container-progress">
      <view style="flex: 1">
        <u-line-progress
          :percentage="percentage"
          :showText="false"
          activeColor="#29b7a3"
        ></u-line-progress>
      </view>
      <view class="container-progress-text">
        <text class="container-progress-text-finish">{{ finishCount }}</text> /
        {{ totleCount }}
      </view>
    </view>
    <GaugeItem
      ref="gaugeItem"
      v-if="questionId"
      :direction="direction"
      :question-id="questionId"
      @getFinishSelectQuestions="getFinishSelectQuestions"
      :selected-gauge-params="selectedGaugeParams"
    />
    <view style="height: 140rpx"></view>
    <view class="container-btn2Box">
      <view class="base previous" @click="handleBeforeClick"> 上一题 </view>
      <view class="base last" @click="handleNextClick" v-if="!questionsFinish">
        下一题
      </view>
      <view class="base last" @click="handleFinishClick" v-else> 完成 </view>
    </view>
  </view>
</template>

<script>
import { getRiskWarningGaugeDetailById } from '@/api/tenant.js';
import GaugeItem from './components/gaugeItem.vue';
import { AssessClientEvent } from '@/utils/eventKeys.js';
export default {
  components: {
    GaugeItem,
  },
  data() {
    return {
      questionId: '',
      percentage: 0,
      selectedGaugeParams: {},
      finishCount: 1,
      totleCount: 0,
      nextQuestionId: '',
      beforeQuestionId: '',
      selectQuestionId: [],
      questionsFinish: false,
      gaugeId: '',
      isFinishCurrent: false,
      direction: 'right',
    };
  },
  watch: {
    questionsFinish(newValue) {
      if (newValue) {
        this.totleCount = this.finishCount;
        this.percentage = 100;
      } else {
        this.totleCount = this.selectedGaugeParams.Questions.length;
        const index = this.selectedGaugeParams.Questions.findIndex(
          (s) => s.Key === this.questionId
        );
        this.finishCount = index + 1;
      }
    },
    percentage(newValue) {
      if (newValue == 100) {
        this.questionsFinish = true;
      } else {
        this.questionsFinish = false;
      }
    },
  },
  onLoad(option) {
    this.gaugeId = option.Id;
    // 获取所有的题目
    this.initGaugeList();
  },
  methods: {
    async initGaugeList() {
      let selectedGaugeParams = uni.getStorageSync(this.gaugeId);
      if (
        !selectedGaugeParams ||
        !selectedGaugeParams.Questions ||
        !selectedGaugeParams.Questions.length
      ) {
        const res = await getRiskWarningGaugeDetailById({ id: this.gaugeId });
        if (res.Type === 200) {
          selectedGaugeParams = res.Data;
          uni.setNavigationBarTitle({
            title: res.Data.Name,
          });
        }
      }
      this.selectedGaugeParams = selectedGaugeParams;
      if (!this.selectedGaugeParams.StartDate) {
        this.selectedGaugeParams.StartDate = this.$dateFormat(
          new Date(),
          'YYYY-MM-DD HH:mm:ss',
          false
        );
      }
      this.$nextTick(() => {
        // 获取当前应该显示哪一道题
        this.getShowQuestionId();
        this.totleCount = this.selectedGaugeParams.Questions.length;
      });
    },
    getShowQuestionId() {
      const allHasAnswerQuestionsList =
        this.selectedGaugeParams.Questions.filter((s) => s.Answer !== null);
      const lastAnswerId = allHasAnswerQuestionsList.length
        ? allHasAnswerQuestionsList[allHasAnswerQuestionsList.length - 1].Key
        : null;
      // 获取当前应该显示哪一道题
      this.questionId =
        lastAnswerId || this.selectedGaugeParams.Questions[0].Key;
      // 获取lastAnswerId的下标
      const index = this.selectedGaugeParams.Questions.findIndex(
        (s) => s.Key === this.questionId
      );
      // 判断当前题目是不是单选题 并且 选择的项 是不是IsDone为true  如果是true 则是最后一道题
      const answer = this.selectedGaugeParams.Questions[index].Answer;
      if (this.selectedGaugeParams.Questions[index].Type === 0) {
        if (answer) {
          // v.Value !== undefined 这个的意思是  配置的Value可能没有 如果没有就用Text 作为Value  这是是为了兼容 Value为 0 的情况
          const selectItem = this.selectedGaugeParams.Questions[
            index
          ].Options.find(
            (v) => (v.Value !== undefined ? v.Value : v.Text) === answer[0]
          );
          if (selectItem && selectItem.IsDone) {
            this.questionsFinish = true;
          }
        }
      }
      this.finishCount = index + 1;
      this.percentage = (
        (this.finishCount / this.selectedGaugeParams.Questions.length) *
        100
      ).toFixed(2);
      if (allHasAnswerQuestionsList.length) {
        let selectQuestionId = allHasAnswerQuestionsList.map((s) => s.Key);
        selectQuestionId.pop();
        this.selectQuestionId = selectQuestionId;
      }
    },
    handleFinishClick() {
      this.handleNextClick();
      if (!this.isFinishCurrent) {
        return;
      }
      this.selectQuestionId.push(this.questionId);
      // 目的是把最后一条数据保存下来(单选题默认已经调用了)
      const index = this.selectedGaugeParams.Questions.findIndex(
        (s) => s.Key === this.questionId
      );
      // 将本次没有选择过的题的答案置为null (有可能第一次选了第二题 但是下一次进来不选择第二题了)
      this.selectedGaugeParams.Questions.forEach((s) => {
        if (!this.selectQuestionId.includes(s.Key)) {
          s.Answer = null;
        }
      });
      uni.setStorageSync(this.gaugeId, this.selectedGaugeParams);
      this.selectedGaugeParams.IsFinish = true;
      this.selectedGaugeParams.EndDate = this.$dateFormat(
        new Date(),
        'YYYY-MM-DD HH:mm:ss',
        false
      );
      uni.$emit(AssessClientEvent.updateRiskAssess, this.selectedGaugeParams);
      uni.navigateBack();
    },
    handleBeforeClick() {
      if (!this.selectQuestionId.length) return;
      this.questionsFinish = false;
      this.direction = 'left';
      this.questionId = this.selectQuestionId[this.selectQuestionId.length - 1];
      const index = this.selectedGaugeParams.Questions.findIndex(
        (s) => s.Key === this.questionId
      );
      this.finishCount = index + 1;
      this.percentage = (
        (this.finishCount / this.selectedGaugeParams.Questions.length) *
        100
      ).toFixed(2);
      this.selectQuestionId.pop();
      this.direction = 'left';
    },
    handleNextClick() {
      const index = this.selectedGaugeParams.Questions.findIndex(
        (s) => s.Key === this.questionId
      );
      if (this.nextQuestionId) {
        this.selectQuestionId.push(this.questionId);
        this.direction = 'right';
        this.questionId = this.nextQuestionId;
        const nextIndex = this.selectedGaugeParams.Questions.findIndex(
          (s) => s.Key === this.questionId
        );
        this.finishCount = nextIndex + 1;
        this.percentage = (
          (this.finishCount / this.selectedGaugeParams.Questions.length) *
          100
        ).toFixed(2);
        this.nextQuestionId = '';
        // 如果是最后一道题就表示题目做完了
        const isLast =
          nextIndex === this.selectedGaugeParams.Questions.length - 1;
        if (isLast) {
          this.questionsFinish = true;
          this.totleCount = this.selectedGaugeParams.Questions.length;
        }
        return;
      }
      const questions = this.$refs.gaugeItem.onGetFinishQuestions();
      console.log('questions', questions);
      if (questions) {
        switch (this.selectedGaugeParams.Questions[index].Type) {
          case 0:
            this.getFinishSelectQuestions(questions, 0);
            break;
          case 1:
            this.getFinishSelectQuestions(questions, 1);
            break;
          case 2:
            this.getFinishSelectQuestions(questions, 2);
            break;
          case 3:
            this.getFinishSelectQuestions(questions, 3);
            break;
          default:
            break;
        }
        this.isFinishCurrent = true;
      } else {
        this.isFinishCurrent = false;
      }
    },
    getFinishSelectQuestions(obj, type = 0) {
      const { question } = obj;
      const index = this.selectedGaugeParams.Questions.findIndex(
        (s) => s.Key === this.questionId
      );
      if (index > -1) {
        this.selectedGaugeParams.Questions[index] = question;
        uni.setStorageSync(this.gaugeId, this.selectedGaugeParams);
      }
      let nextQuestionId = '';
      const defulatNextQuestionId =
        index === this.selectedGaugeParams.Questions.length - 1
          ? ''
          : this.selectedGaugeParams.Questions[index + 1].Key;
      let answer = null;
      let isRequired = question.Required;
      if (isRequired) {
        switch (type) {
          case 0:
            answer = question.Answer[0];
            if (answer) {
              // 如果有选择项
              // 如果选择项是否有IsDone = true
              const isDone = question.Options.find(
                (s) => (s.Value || s.Text) === answer
              )?.IsDone;
              // 如果选择项有IsDone = true 就表示题目做完了
              if (isDone) {
                nextQuestionId = '';
              } else {
                // 如果选择项没有IsDone = true 那就跳转指定题或者默认下一题
                nextQuestionId =
                  question.Options.find((s) => (s.Value || s.Text) === answer)
                    ?.Jump || defulatNextQuestionId;
              }
            } else {
              // 如果没有选择 默认下一题
              nextQuestionId = defulatNextQuestionId;
            }
            break;
          case 1:
            answer = question.Answer;
            nextQuestionId =
              this.checkNextQuestionId(answer, question.Jumps) ||
              defulatNextQuestionId;
            break;
          case 2:
            nextQuestionId = defulatNextQuestionId;
            break;
          case 3:
            answer = question.Answer;
            nextQuestionId =
              this.checkNumberQuestionId(answer, question.Conditions) ||
              defulatNextQuestionId;
            break;
          default:
            break;
        }
      } else {
        nextQuestionId = defulatNextQuestionId;
      }
      this.nextQuestionId = nextQuestionId;
      if (nextQuestionId) {
        this.questionsFinish = false;
        this.handleNextClick();
      } else {
        this.questionsFinish = true;
      }
    },
    // 数值题检测下一项的Jump
    checkNumberQuestionId(answer, jumpsList) {
      // 分值判断左开右闭
      if (!jumpsList || !jumpsList.length) {
        return null;
      }
      const range = this.getRange(answer, jumpsList);
      if (!range) {
        return null;
      }
      return range.Jump || null;
    },
    getRange(value, jumpsList) {
      const ranges = jumpsList;
      for (const range of ranges) {
        const { Range } = range;
        const Min = Range[0];
        const Max = Range[1];
        const isInRange =
          (Min === null || value > Min) && (Max === null || value <= Max);

        if (isInRange) {
          return range;
        }
      }
      return null; // 如果不在任何区间内
    },
    // 多选检测下一项的Jump
    checkNextQuestionId(selectList, JumpsList) {
      if (!JumpsList || !JumpsList.length) {
        return null;
      }
      const sortedSelectList = [...selectList].sort();
      const match = JumpsList.find(
        ({ Values }) =>
          Values.length === sortedSelectList.length &&
          Values.sort().every(
            (value, index) => value === sortedSelectList[index]
          )
      );
      return match ? match.Jump : null; // Return the Jump or null if no match found
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding: 32rpx;

  &-progress {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 32rpx;

    &-text {
      margin-left: 24rpx;

      &-finish {
        color: #29b7a3;
      }
    }
  }
}

.base {
  width: 48%;
  text-align: center;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);
  font-size: 32rpx;
  background: #14b593;
  color: #fff;
}

.last {
}

.previous {
}
</style>
