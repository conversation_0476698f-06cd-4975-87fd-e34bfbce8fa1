<template>
  <view class="container">
    <!-- 标签栏 -->
    <u-sticky bgColor="#fff">
      <u-tabs
        lineColor="#29B7A3"
        lineWidth="80rpx"
        :current="currentIndex"
        :activeStyle="{
          fontWeight: '500',
          fontSize: '28rpx',
          color: '#323233',
        }"
        :inactiveStyle="{
          fontWeight: '400',
          fontSize: '28rpx',
          color: '#969799',
        }"
        :list="tabList"
        @click="onChange"
      ></u-tabs>
    </u-sticky>
    <view class="warpper">
      <u-list :height="listHeight + 'px'">
        <!-- 头空白 -->
        <u-list-item>
          <view class="warpper-header"></view>
        </u-list-item>
        <!-- 当前得分细则 -->
        <u-list-item v-if="currentGauge.Id">
          <view class="container-top">
            <text class="container-top-time"
              >评估时间：{{ currentGauge.CreatedTime }}</text
            >
            <block v-if="handleGetIsGroup(currentGauge.Sign)">
              <view
                class="container-top-box"
                v-for="(item, index) in currentGauge.Result"
                :key="index"
              >
                <text class="container-top-box-left"
                  >{{ item.ShowName }}：</text
                >
                <text class="container-top-box-left">{{
                  item.Report[0].Result
                }}</text>
                <text
                  class="container-top-box-right"
                  style="display: block"
                  v-if="item.Report && item.Report[0].Suggest"
                  >{{ item.Report[0].Suggest }}</text
                >
              </view>
            </block>
            <view
              class="container-top-point-warpper"
              v-if="handleGetIsScore(currentGauge.Sign)"
            >
              <!-- <text class="container-top-title">最终得分</text> -->
              <view class="container-top-point" v-if="onGetTotalScore()">
                <text>{{ onGetTotalScore() }}</text>
                <text class="container-top-point-unit">分</text>
              </view>
            </view>
            <block
              v-if="
                !handleGetIsScore(currentGauge.Sign) && conclusionList.length
              "
            >
              <view
                class="container-top-box"
                v-for="(item, index) in conclusionList"
                :key="index"
              >
                <text class="container-top-box-left">{{ item.Name }}：</text>
                <text class="container-top-box-left">{{ item.Score }}分</text>
                <text
                  class="container-top-box-right"
                  style="display: block"
                  v-if="item.Conclusion"
                  >{{ item.Conclusion }}</text
                >
              </view>
            </block>
            <text v-else class="container-top-box-left">{{
              onGetConclusion()
            }}</text>
          </view>
        </u-list-item>
        <!-- 量表题目列表 -->
        <u-list-item
          v-for="(item, problemIndex) in currentGauge.Questions"
          :key="problemIndex"
        >
          <view class="item-title">
            <text>{{ problemIndex + 1 + '、' + item.Text }}</text>
            <text class="item-title-required" v-if="item.Required">*</text>
          </view>
          <!-- 单选 -->
          <view class="item-select-box" v-if="item.Type === 0">
            <view
              class="item-select-box-option"
              v-for="(o, index) in item.Options"
              :key="Id"
            >
              <view
                :class="[
                  'item-icon-radius',
                  getIsSelectRadio(
                    o.Value !== undefined ? o.Value : o.Text,
                    item.Answer
                  )
                    ? 'item-icon-selected'
                    : 'item-icon-unselected',
                ]"
              >
                <u-icon
                  v-if="getIsSelectRadio()"
                  color="#fff"
                  name="checkbox-mark"
                ></u-icon>
              </view>
              <text style="flex: 1">{{ o.Text }}</text>
            </view>
          </view>
          <!-- 多选 -->
          <view class="item-select-box" v-else-if="item.Type === 1">
            <view
              class="item-select-box-option"
              v-for="(o, index) in item.Options"
              :key="Id"
            >
              <view
                :class="[
                  getIsSelectMultiple(
                    o.Value !== undefined ? o.Value : o.Text,
                    item.Answer
                  )
                    ? 'item-icon-selected'
                    : 'item-icon-unselected',
                ]"
              >
                <u-icon
                  v-if="
                    getIsSelectMultiple(
                      o.Value !== undefined ? o.Value : o.Text,
                      item.Answer
                    )
                  "
                  color="#fff"
                  name="checkbox-mark"
                ></u-icon>
              </view>
              <text style="flex: 1">{{ o.Text }}</text>
            </view>
          </view>
          <!-- 输入框/数值 -->
          <view class="item-input-box" v-else>
            <text>{{ item.Answer }}</text>
          </view>
        </u-list-item>
        <!-- 尾部空白 -->
        <u-list-item>
          <view class="warpper-footer"></view>
        </u-list-item>
      </u-list>
    </view>
    <view
      class="container-bomBtn"
      @click="handleReassessClick"
      v-if="fillType == 2"
    >
      重新评估
    </view>
  </view>
</template>

<script>
const app = getApp();
import { getPatGauge, getPatGaugeById } from '@/api/tenant.js';
import { AssessClientEvent } from '@/utils/eventKeys.js';
import riskWarningManager from '@/services/RiskWarningManager.js';
export default {
  data() {
    return {
      baseGaugeId: '',
      reportId: '',
      // 用于显示 Tabs
      tabList: {},

      // 已评估列表
      gaugeList: [],
      // 列表可滚动高度(单位px)
      listHeight: 680,

      // 当前显示的下标
      currentIndex: 0,
      // 当前显示的评估
      currentGauge: {},
      Ids: [],
      conclusionList: [],
      fillType: 0,
    };
  },
  async onLoad(option) {
    this.baseGaugeId = option.BaseGaugeId;
    this.reportId = option.ReportId;
    this.fillType = option.FillType;
    await this.handleGetSetting();
    this.loadGetGaugeDataByPatGaugeId();
    uni.$on(AssessClientEvent.updateRiskAssess, this.handlePageBack);
  },
  onReady() {
    const window = uni.getWindowInfo();
    console.debug('window', window);
    this.listHeight = window.windowHeight - 44;
  },
  methods: {
    async handleGetSetting() {
      const res = await riskWarningManager.requestRiskWarningConfig();
      if (res.Type === 200) {
        this.settingList = res.Data;
      }
    },
    handleGetIsScore(sgin) {
      return riskWarningManager && riskWarningManager.isScore(sgin);
    },
    handleGetIsGroup(sgin) {
      return riskWarningManager && riskWarningManager.isGroup(sgin);
    },
    onGetConclusion() {
      let conclusion = '';
      if (this.currentGauge.Result && this.currentGauge.Result.length) {
        conclusion = this.currentGauge.Result[0].Conclusion || '';
      }
      return conclusion;
    },
    onGetTotalScore() {
      if (!riskWarningManager) return '';
      if (riskWarningManager.isGroup(this.currentGauge.Sign)) {
        return '';
      }
      let result = '';
      if (this.currentGauge.Result && this.currentGauge.Result.length) {
        result = this.currentGauge.Result[0].Report[0].Result;
      }
      return result;
    },
    getIsSelectRadio(item, answer) {
      if (!answer) return false;
      if (item === JSON.parse(answer)[0]) return true;
      return false;
    },
    getIsSelectMultiple(item, answer) {
      if (!answer) return false;
      if (JSON.parse(answer).some((s) => s === item)) return true;
      return false;
    },
    handleReassessClick() {
      uni.navigateTo({
        url: `./screeningGauge?Id=` + this.baseGaugeId,
      });
    },
    // 请求已填写的评估结果列表
    async loadGetGaugeDataByPatGaugeId() {
      const res = await getPatGauge({
        UserId: app.globalData.userInfo.Id,
        ReportId: this.reportId,
        BaseGaugeId: this.baseGaugeId,
        DtoName: 'PatGaugePageOutputDto',
      });
      if (res.Type === 200) {
        const newData = res.Data.Data.reverse();
        this.tabList = newData.map((_, index) => {
          return {
            name: `第${index + 1}次评估`,
            index: index,
          };
        });
        // 当前显示值设置
        this.currentIndex = newData.length - 1;
        this.Ids = newData.map((s) => s.Id);
        // 获取评估数据
        this.onGetGaugeDetails(newData[newData.length - 1].Id);
      }
    },
    async onGetGaugeDetails(Id) {
      uni.showLoading({
        title: '加载中',
        mask: true,
      });
      // 获取评估数据
      const res = await getPatGaugeById({
        Id,
      });
      if (res.Type === 200) {
        res.Data.CreatedTime = this.$dateFormat(
          res.Data.CreatedTime,
          'YYYY-MM-DD HH:mm:ss',
          false
        );
        this.currentGauge = res.Data;
        // 获取结论
        if (riskWarningManager.isConclusion(res.Data.Sign)) {
          this.onGetConclusionList();
        }
        // 修改标题
        uni.setNavigationBarTitle({
          title: res.Data.Name,
        });
      }
      uni.hideLoading();
    },
    onGetConclusionList() {
      const questions = JSON.parse(
        JSON.stringify(this.currentGauge)
      ).Questions.filter((s) => s.Result);
      let conclusionList = [];
      questions.forEach((s) => {
        if (s.Answer) s.Answer = JSON.parse(s.Answer);
        s.Result = JSON.parse(s.Result);
        let item = {
          Name: s.Result.ShowName,
          Score: s.Answer.reduce(
            (accumulator, current) => accumulator * 1 + current * 1,
            0
          ),
          Conclusion: s.Result.Report.map((s) => s.Result).join(''),
        };
        conclusionList.push(item);
      });
      this.conclusionList = conclusionList;
    },
    onChange(event) {
      const index = event.index;
      this.currentIndex = index;
      this.onGetGaugeDetails(this.Ids[index]);
    },
    handlePageBack() {
      uni.navigateBack();
    },
  },
  onUnload() {
    uni.$off(AssessClientEvent.updateRiskAssess, this.handlePageBack);
  },
};
</script>

<style scoped lang="scss">
.warpper {
  padding: 0 32rpx;
  flex: 1;

  &-header {
    height: 32rpx;
  }

  &-footer {
    height: calc(env(safe-area-inset-bottom) + 24rpx);
  }
}

// 分数分析结果
.container-top {
  background: #ffffff;
  box-shadow: 0rpx 4rpx 24rpx 0rpx rgba(100, 101, 102, 0.12);
  border-radius: 32rpx;
  padding: 32rpx;
  position: relative;

  &-title {
    font-weight: 400;
    font-size: 36rpx;
    color: #323233;
  }

  &-time {
    // position: absolute !important;
    // top: 42rpx;
    // right: 32rpx;
    margin-bottom: 12rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #969799;
  }

  &-point-warpper {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }

  &-point {
    font-weight: bold;
    font-size: 72rpx;
    color: #323233;

    &-unit {
      margin-left: 24rpx;
      font-weight: 400;
      font-size: 36rpx;
      color: #969699;
    }
  }

  &-box {
    margin-top: 20rpx;
    text-align: left !important;

    &-left {
      font-weight: 400;
      font-size: 24rpx;
      color: #969799;
    }

    &-right {
      font-weight: 500;
      font-size: 24rpx;
      color: #323233;
    }
  }
}

.item {
  &-title {
    margin-top: 32rpx;
    margin-bottom: 24rpx;
    font-weight: 600;
    font-size: 28rpx;
    color: #323233;

    &-required {
      font-weight: 600;
      font-size: 28rpx;
      color: #ff5656;
      margin-left: 4rpx;
    }
  }

  // 单选、多选
  &-select-box {
    background-color: #ffffff;
    border-radius: 32rpx;

    // 选项
    &-option {
      display: flex;
      flex-direction: row;
      justify-content: start;
      align-items: center;
      margin: 0 32rpx;
      padding: 24rpx 0;
      border-bottom: 1rpx solid #ebedf0;
    }
  }

  // 选项图标
  &-icon-selected {
    width: 32rpx;
    height: 32rpx;
    background-color: #29b7a3;
    box-sizing: border-box;
    margin-right: 18rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  &-icon-unselected {
    width: 32rpx;
    height: 32rpx;
    border: 2rpx solid #969799;

    box-sizing: border-box;
    margin-right: 18rpx;
  }

  &-icon-radius {
    border-radius: 32rpx;
  }

  &-input-box {
    padding: 32rpx;
    background: #ffffff;
    border-radius: 32rpx;
    font-weight: 400;
    word-break: break-all;
    font-size: 24rpx;
    color: #646566;
  }
}

::v-deep .u-radio {
  padding: 28rpx 32rpx !important;
}

::v-deep .u-radio__icon-wrap {
  margin-right: 18rpx !important;
}
</style>
