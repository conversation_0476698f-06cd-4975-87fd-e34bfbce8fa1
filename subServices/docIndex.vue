<template>
  <view class="container">
    <view class="box">
      <view class="box-top">
        <u-avatar :src="docInfo.HeadImg" size="55"></u-avatar>
        <view class="box-top-right">
          <view class="box-top-right-top">
            <span
              style="font-size: 20px; font-weight: 700; margin-bottom: 4px"
              >{{ docInfo.Name }}</span
            >
            <span
              style="
                font-size: 14px;
                color: #666666;
                margin-bottom: 4px;
                margin-left: 20px;
              "
              >{{ docInfo.WorkerTitle }}</span
            >
            <p
              style="
                font-size: 14px;
                color: #666666;
                margin-bottom: 4px;
                margin-top: 10px;
              "
            >
              {{ docInfo.OrganizationName }} {{ docInfo.DepartmentName }}
            </p>
          </view>
        </view>
      </view>
    </view>
    <view class="container-type">
      <!-- 评分 -->
      <view
        class="container-type-start"
        v-for="item in EvaluateObj"
        :key="item.Id"
      >
        <p v-if="ConsultWay == 1">{{ item.Name }}</p>
        <p v-if="ConsultWay == 2">满意度</p>
        <u-rate
          :minCount="1"
          :touchable="false"
          :count="5"
          v-model="item.Score"
          activeColor="#29B7A3"
          :size="24"
          @change="scoreChange"
        ></u-rate>
      </view>

      <!-- 词条 -->
      <view class="container-type-entry" v-if="showEntry">
        <view
          class="container-type-entry-list"
          :class="item.choose ? 'container-type-entry-choose' : ''"
          v-for="(item, index) in Entry"
          :key="index"
          @click="onChooseItem(item, index)"
        >
          {{ item.Key }}
        </view>
      </view>
    </view>
    <!-- 问诊评价 -->
    <view class="container-evaluation">
      <p style="padding: 0 10px">文字评价</p>
      <u-textarea
        v-model="reqData.OtherAdvice"
        placeholder="咨询过程还满意吗？说说你的感受和建议吧。"
        count
        :maxlength="200"
        :height="160"
        confirmType="return"
      >
      </u-textarea>
    </view>
    <view class="bomm-btn-style" style="height: 100px"></view>
    <u-button
      type="success"
      @click="onSubmit"
      shape="circle"
      text="提交"
      customStyle="width:90%;bottom: 30px;position: fixed; left: 50%;transform: translateX(-50%);fontSize:16px;z-index:99"
    >
    </u-button>
    <u-modal
      :show="show1"
      title="温馨提示"
      content="您确定要提交吗？"
      @confirm="confirm"
      @cancel="show1 = false"
      :showCancelButton="true"
    ></u-modal>
    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script>
const app = getApp();
import {
  SendEvaluation,
  GetOrgEvaContentByOrgIds,
} from '@/api/satisfactory.js';
import { DictItemReadStd, GetDiseaseList } from '@/api/dictionary.js';
import { getConsultRecordInfo } from '@/api/consult.js';
export default {
  data() {
    return {
      loadingType: 'loading',
      show1: false,
      EvaluateObj: [],
      docInfo: {},
      reqData: {
        UserName: app.globalData.userInfo.Name || '默认患者',
        VisitNo: '',
        Age: '',
        OrganizationId: '',
        OtherAdvice: '',
        Phone: '',
        Type: 2,
        DoctorId: '',
        OrganizationName: '',
        PatId: app.globalData.userInfo.Id || '',
      },
      consultId: '',
      Entry: [],
      showEntry: true,
      ConsultWay: null,
    };
  },
  onLoad({ item, consultId, ConsultWay }) {
    this.getData(consultId);
  },
  methods: {
    async getData(consultId) {
      uni.showLoading({
        title: this.$loadingMsg,
      });
      const res = await getConsultRecordInfo(consultId);
      if (res.Type === 200) {
        this.docInfo = res.Data.Doctor;
        this.reqData.OrganizationId = res.Data.Doctor.OrganizationId;
        this.consultId = consultId;
        this.ConsultWay = res.Data.Consult.ConsultWay;
        this.reqData.VisitNo = consultId;
        this.reqData.DoctorId = res.Data.Doctor.UserId;
        this.reqData.OrganizationName = res.Data.Doctor.OrganizationName;
        // 获取问诊评价的词条
        this.onGetDiseaseCode();
        this.onGetSatisfactionId();
        this.loadingType = 'success';
        uni.hideLoading();
      } else {
        this.$refs.uToast.show({
          message: res.Content,
          type: 'error',
        });
        setTimeout(() => {
          uni.navigateBack();
        }, 2000);
      }
    },
    async onGetDiseaseCode() {
      const req = {
        PageCondition: {
          PageIndex: 1,
          PageSize: 99,
          SortConditions: [
            {
              SortField: 'Id',
              ListSortDirection: 1,
            },
          ],
        },
        FilterGroup: {
          Rules: [],
          Groups: [
            {
              Rules: [
                {
                  Field: 'Code',
                  Value: 'EvaluateTag',
                  Operate: 11,
                },
              ],
              Operate: 2,
            },
          ],
          Operate: 1,
        },
      };
      let res = await GetDiseaseList(req);
      if (res.Type === 200) {
        this.onGetEntry(res.Data?.Rows[0].Id * 1);
      }
    },
    scoreChange(e) {
      console.log('e', e);
      // if (e < 3) {
      // 	this.showEntry = false
      // 	this.Entry.forEach(e => {
      // 		e.choose = false
      // 	})
      // } else {
      // 	this.showEntry = true
      // }
    },
    confirm() {
      this.show1 = false;
      this.onSubmit();
    },
    async onGetSatisfactionId() {
      let res = await GetOrgEvaContentByOrgIds({
        orgIds: [this.docInfo.OrganizationId],
        isEnabled: true,
      });
      if (res.Type === 200) {
        res.Data.forEach((e) => {
          e.Score = 5;
        });
        this.EvaluateObj = res.Data;
      }
    },
    async onSubmit() {
      const satisfactionSurveyDetails = [];
      this.EvaluateObj.forEach((e) => {
        satisfactionSurveyDetails.push({
          Score: e.Score,
          EvaluateContentId: e.Id,
        });
      });
      const SatisfactionTags = [];
      // this.Entry.forEach(e => {
      // 	SatisfactionTags.push({
      // 		Tag: e.Key
      // 	})
      // })
      const chooseData = this.Entry.filter((e) => e.choose);
      chooseData &&
        chooseData.length > 0 &&
        chooseData.forEach((e) => {
          SatisfactionTags.push({
            Tag: e.Key,
          });
        });
      const data = {
        satisfactionSurveyDetails,
        ...this.reqData,
        SatisfactionTags,
        DeptId: this.docInfo.DepartmentId,
        DeptName: this.docInfo.DepartmentName,
      };
      console.log('data', data);
      let res = await SendEvaluation(data);
      if (res.Type === 200) {
        this.$refs.uToast.show({
          message: res.Content,
          type: 'success',
        });
        let pages = getCurrentPages();
        const prePage = pages[pages.length - 2]; //上一个页面
        const prePage1 = pages[pages.length - 3]; //上上一个页面
        // setTimeout(() => {
        // 	if (prePage.$vm.getList) {
        // 		prePage.$vm.getList()
        // 		uni.navigateBack()
        // 	}
        // 	// uni.redirectTo({
        // 	// 	url: '/subPackInquiry/inquiryOrder'
        // 	// })
        // }, 1500)
        if (prePage.$vm.getList) {
          setTimeout(() => {
            uni.navigateBack({
              delta: 1,
              success: () => {
                prePage.$vm.getList(1);
              },
            });
          }, 1500);
        } else if (prePage.$vm.loadConsultRecordInfo) {
          setTimeout(() => {
            uni.navigateBack({
              delta: 1,
              success: () => {
                prePage.$vm.loadConsultRecordInfo(this.consultId);
              },
            });
          }, 1500);
        } else if (prePage.$vm.getDetail) {
          setTimeout(() => {
            uni.navigateBack({
              delta: 1,
              success: () => {
                prePage.$vm.getDetail(this.consultId);
                if (prePage1.$vm.getList) {
                  prePage1.$vm.getList(1);
                }
              },
            });
          }, 1500);
        } else {
          uni.navigateBack();
        }
      } else {
        this.$refs.uToast.show({
          message: res.Content,
          type: 'error',
        });
      }
    },
    // 选择词条
    onChooseItem(item, index) {
      // console.log(item)
      this.Entry[index].choose = !this.Entry[index].choose;
    },
    async onGetEntry(id) {
      const data = {
        PageCondition: {
          PageIndex: 1,
          PageSize: 100,
          SortConditions: [
            {
              SortField: 'OrderNumber',
              ListSortDirection: 1,
            },
          ],
        },
        FilterGroup: {
          Groups: [
            {
              Rules: [
                {
                  Field: 'OrgId',
                  Value: this.docInfo.OrganizationId,
                  Operate: 3,
                },
                {
                  Field: 'OrgId',
                  Value: null,
                  Operate: 3,
                },
              ],
              Operate: 2,
            },
          ],
          Rules: [
            {
              Field: 'DictId',
              Value: id,
              Operate: 3,
            },
            {
              Field: 'IsEnabled',
              Value: true,
              Operate: 3,
            },
            {
              Field: 'IsPublish',
              Value: true,
              Operate: 3,
            },
          ],
          Operate: 1,
        },
      };
      const res = await DictItemReadStd(data);
      if (res.Type !== 200) {
        this.loadingType = res.Content;
        return;
      }
      res.Data.Rows.forEach((e) => {
        e.choose = false;
      });
      this.Entry = res.Data.Rows;
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  /deep/ .u-cell {
    background-color: white;
  }

  /deep/ .u-line {
    border: none !important;
  }

  /deep/ .u-border {
    border-style: none !important;
    border-color: #fafafa !important;
  }

  /deep/ .u-textarea__field {
    background-color: #fafafa !important;
    padding: 10px;
  }

  &-evaluation {
    background-color: white;
  }

  &-type {
    margin-top: 20rpx;
    background-color: white;
    padding: 10px;

    &-entry {
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;

      &-list {
        padding: 8rpx 48rpx;
        background: #e5e5e5;
        border-radius: 50rpx 50rpx 50rpx 50rpx;
        text-align: left;
        margin-top: 15px;
        margin-left: 20rpx;
        border-radius: 15px;
        font-size: 15px;
      }

      &-choose {
        background-color: #29b7a3;
        border: 1px solid #29b7a3;
        color: white;
      }

      &-list:nth-of-type(3n) {
        margin-right: 0;
      }
    }

    &-start {
      margin-top: 30rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .box {
    background-color: white;
    margin-bottom: 20upx;
    border-radius: 12upx;
    padding: 20upx;

    .box-top {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .box-top-right {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex: 1;
        margin-left: 12px;
        margin-top: 2px;

        .follow-style {
          width: 60px;
          height: 30px;
          text-align: center;
          line-height: 30px;
          color: white;
          font-size: 12px;
          background-color: #29b7a3;
          border-radius: 20px;
          float: right;
          transform: translateY(-10px);
          margin-top: 18px;
        }

        .box-top-right-top {
          .botton-style {
            width: 138upx;
            height: 60upx;
            background-color: #29b7a3;
            font-size: 15px;
            text-align: center;
            line-height: 60upx;
            border-radius: 30px;
          }
        }
      }
    }
  }
}
</style>
