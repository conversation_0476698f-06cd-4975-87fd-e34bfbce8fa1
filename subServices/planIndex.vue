<template>
  <view class="container">
    <view class="box">
      <view class="box-title" v-if="ProgramName">
        <view class="box-title-box"></view>
        <span>{{ ProgramName }}</span>
      </view>
      <view class="box-top">
        <u-avatar :src="docInfo.HeadImg" size="55"></u-avatar>
        <view class="box-top-right">
          <view class="box-top-right-top">
            <span
              style="font-size: 20px; font-weight: 700; margin-bottom: 4px"
              >{{ docInfo.Name }}</span
            >
            <span
              style="
                font-size: 14px;
                color: #666666;
                margin-bottom: 4px;
                margin-left: 20px;
              "
              >{{ docInfo.WorkerTitle }}</span
            >
            <p
              style="
                font-size: 14px;
                color: #666666;
                margin-bottom: 4px;
                margin-top: 10px;
              "
            >
              {{ docInfo.OrganizationName }} {{ docInfo.DepartmentName }}
            </p>
          </view>
        </view>
      </view>
    </view>

    <view class="container-type">
      <!-- 评分 -->
      <view
        class="container-type-start"
        v-for="item in EvaluateObj"
        :key="item.Id"
      >
        <p>{{ item.Name }}</p>
        <u-rate
          v-if="item.Id"
          :minCount="1"
          :touchable="false"
          :count="5"
          v-model="item.Score"
          activeColor="#29B7A3"
          :size="24"
          @change="scoreChange"
        ></u-rate>
      </view>

      <!-- 词条 -->
      <view class="container-type-entry" v-if="showEntry">
        <view
          class="container-type-entry-list"
          :class="item.choose ? 'container-type-entry-choose' : ''"
          v-for="(item, index) in Entry"
          :key="index"
          @click="onChooseItem(item, index)"
        >
          {{ item.Key }}
        </view>
      </view>
    </view>

    <!-- 问诊评价 -->
    <view class="container-evaluation">
      <p style="padding: 0 10px">文字评价</p>
      <u-textarea
        v-model="reqData.OtherAdvice"
        placeholder="康复过程还满意吗？说说你的感受和建议吧。"
        count
        :maxlength="200"
        :height="160"
        confirmType="return"
      >
      </u-textarea>
    </view>

    <view class="bomm-btn-style" style="height: 100px"></view>
    <u-button
      type="success"
      @click="onSubmit"
      shape="circle"
      text="提交"
      customStyle="width:90%;bottom: 30px;position: fixed; left: 50%;transform: translateX(-50%);fontSize:16px;z-index:99"
    >
    </u-button>
    <u-modal
      :show="show1"
      title="温馨提示"
      content="您确定要提交吗？"
      @confirm="confirm"
      @cancel="show1 = false"
      :showCancelButton="true"
    ></u-modal>
    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script>
const app = getApp();
import { SendEvaluation } from '@/api/satisfactory.js';
import { DictItemReadStd, GetDiseaseList } from '@/api/dictionary.js';
import { GetEvaTrainingInitData } from '@/api/training.js';
export default {
  data() {
    return {
      loadingType: 'loading',
      show1: false,
      EvaluateObj: [],
      docInfo: {},
      reqData: {
        UserName: app.globalData.userInfo.Name || '默认患者',
        VisitNo: '',
        Age: '',
        OrganizationId: '',
        OtherAdvice: '',
        Phone: '',
        Type: 3,
        DoctorId: '',
        OrganizationName: '',
        PatId: app.globalData.userInfo.Id || '',
      },
      consultId: '',
      Entry: [],
      showEntry: true,
      ConsultWay: null,
      ProgramName: '',
    };
  },
  onLoad({ Id }) {
    console.log('服务评价', Id);
    if (!getApp().isLoggedIn()) {
      console.warn('用户未登录');
      getApp().openLoginPage({
        reLaunch: true,
        redirect: `/subServices/planIndex?Id=${Id}`,
      });
      return;
    }

    this.getData(Id);
  },
  methods: {
    async getData(trainingId) {
      uni.showLoading({
        title: this.$loadingMsg,
      });
      const res = await GetEvaTrainingInitData({
        trainingId,
      });

      if (res.Type !== 200) {
        uni.showToast({
          icon: 'error',
          title: res.Content,
        });
        setTimeout(() => {
          uni.navigateBack();
        }, 2000);
        return;
      }

      if (res.Data.TrainingIsEva) {
        uni.showModal({
          title: '提示',
          content: '您已评价过，无须再次评价',
          showCancel: false,
          success: (res) => {
            uni.reLaunch({
              url: '/pages/index/index',
            });
          },
        });
        return;
      }

      this.docInfo = res.Data.Dct;
      res.Data.Eva.forEach((e) => {
        e.Score = 5;
      });
      this.EvaluateObj = res.Data.Eva;
      this.reqData.OrganizationId = res.Data.Dct.OrganizationId;
      this.reqData.VisitNo = trainingId;
      this.reqData.DoctorId = res.Data.Dct.UserId;
      this.reqData.OrganizationName = res.Data.Dct.OrganizationName;
      this.ProgramName = res.Data.ProgramName;
      // // 获取问诊评价的词条
      await this.onGetDiseaseCode();
      uni.hideLoading();
      this.loadingType = 'success';
    },
    async onGetDiseaseCode() {
      const req = {
        PageCondition: {
          PageIndex: 1,
          PageSize: 99,
          SortConditions: [
            {
              SortField: 'Id',
              ListSortDirection: 1,
            },
          ],
        },
        FilterGroup: {
          Rules: [],
          Groups: [
            {
              Rules: [
                {
                  Field: 'Code',
                  Value: 'TrainingTag',
                  Operate: 11,
                },
              ],
              Operate: 2,
            },
          ],
          Operate: 1,
        },
      };
      let res = await GetDiseaseList(req);
      if (res.Type === 200) {
        this.onGetEntry(res.Data?.Rows[0].Id * 1);
      }
    },
    scoreChange(e) {
      console.log('e', e);
    },
    confirm() {
      this.show1 = false;
      this.onSubmit();
    },
    async onSubmit() {
      const satisfactionSurveyDetails = [];
      this.EvaluateObj.forEach((e) => {
        satisfactionSurveyDetails.push({
          Score: e.Score,
          EvaluateContentId: e.Id,
        });
      });
      const SatisfactionTags = [];
      const chooseData = this.Entry.filter((e) => e.choose);
      chooseData &&
        chooseData.length > 0 &&
        chooseData.forEach((e) => {
          SatisfactionTags.push({
            Tag: e.Key,
          });
        });
      const data = {
        satisfactionSurveyDetails,
        ...this.reqData,
        SatisfactionTags,
        DeptId: this.docInfo.DepartmentId,
        DeptName: this.docInfo.DepartmentName,
      };
      console.log('data', data);
      let res = await SendEvaluation(data);
      if (res.Type === 200) {
        this.$log.info(
          `${this.$envVersion}:用户${this.reqData.UserName}评价了${this.ProgramName}`
        );
        this.$refs.uToast.show({
          message: res.Content,
          type: 'success',
        });
        let pages = getCurrentPages();
        const prePage = pages[pages.length - 2]; //上一个页面
        if (prePage && prePage.$vm && prePage.$vm.loadData) {
          // 如果是一层一层点进来的
          setTimeout(() => {
            uni.navigateBack({
              delta: 1,
              success: () => {
                prePage.$vm.loadData();
              },
            });
          }, 1500);
        } else {
          // 如果是通过模板跳转过来的
          setTimeout(() => {
            uni.reLaunch({
              url: '/pages/index/index',
            });
          }, 1500);
        }
        this.$log.info(
          `${this.$envVersion}:用户${this.reqData.UserName}评价了${this.ProgramName}治疗方案`
        );
      } else {
        this.$refs.uToast.show({
          message: res.Content,
          type: 'error',
        });
      }
    },
    // 选择词条
    onChooseItem(item, index) {
      // console.log(item)
      this.Entry[index].choose = !this.Entry[index].choose;
    },
    async onGetEntry(id) {
      const data = {
        PageCondition: {
          PageIndex: 1,
          PageSize: 100,
          SortConditions: [
            {
              SortField: 'OrderNumber',
              ListSortDirection: 1,
            },
          ],
        },
        FilterGroup: {
          Groups: [
            {
              Rules: [
                {
                  Field: 'OrgId',
                  Value: this.docInfo.OrganizationId,
                  Operate: 3,
                },
                {
                  Field: 'OrgId',
                  Value: null,
                  Operate: 3,
                },
              ],
              Operate: 2,
            },
          ],
          Rules: [
            {
              Field: 'DictId',
              Value: id,
              Operate: 3,
            },
            {
              Field: 'IsEnabled',
              Value: true,
              Operate: 3,
            },
            {
              Field: 'IsPublish',
              Value: true,
              Operate: 3,
            },
          ],
          Operate: 1,
        },
      };
      const res = await DictItemReadStd(data);
      console.log('res', res);
      if (res.Type !== 200) {
        this.loadingType = res.Content;
        return;
      }
      res.Data.Rows.forEach((e) => {
        e.choose = false;
      });
      this.Entry = res.Data.Rows;
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  /deep/ .u-cell {
    background-color: white;
  }

  /deep/ .u-line {
    border: none !important;
  }

  /deep/ .u-border {
    border-style: none !important;
    border-color: #fafafa !important;
  }

  /deep/ .u-textarea__field {
    background-color: #fafafa !important;
    padding: 10px;
  }

  &-evaluation {
    background-color: white;
  }

  &-type {
    margin-top: 20rpx;
    background-color: white;
    padding: 10px;

    &-entry {
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;

      &-list {
        padding: 8rpx 48rpx;
        background: #e5e5e5;
        border-radius: 50rpx 50rpx 50rpx 50rpx;
        text-align: left;
        margin-top: 15px;
        margin-left: 20rpx;
        border-radius: 15px;
        font-size: 15px;
      }

      &-choose {
        background-color: #29b7a3;
        border: 1px solid #29b7a3;
        color: white;
      }

      &-list:nth-of-type(3n) {
        margin-right: 0;
      }
    }

    &-start {
      margin-top: 30rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .box {
    background-color: white;
    margin-bottom: 20upx;
    border-radius: 12upx;
    padding: 20upx;

    &-title {
      padding: 20rpx;
      display: flex;
      align-items: center;

      &-box {
        width: 14rpx;
        height: 40rpx;
        background: #31d65a;
        margin-right: 10rpx;
      }
    }

    .box-top {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .box-top-right {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex: 1;
        margin-left: 12px;
        margin-top: 2px;

        .follow-style {
          width: 60px;
          height: 30px;
          text-align: center;
          line-height: 30px;
          color: white;
          font-size: 12px;
          background-color: #29b7a3;
          border-radius: 20px;
          float: right;
          transform: translateY(-10px);
          margin-top: 18px;
        }

        .box-top-right-top {
          .botton-style {
            width: 138upx;
            height: 60upx;
            background-color: #29b7a3;
            font-size: 15px;
            text-align: center;
            line-height: 60upx;
            border-radius: 30px;
          }
        }
      }
    }
  }
}
</style>
