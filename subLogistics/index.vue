<template>
  <view class="container">
    <view class="container-box" v-for="i in showLogistics" :key="i.ExpressNum">
      <view class="container-box-title" v-if="i.Company">
        <view class="container-box-title-left">
          <span class="container-box-title-left-one">{{ i.Company }}:</span>
          <span class="container-box-title-left-two">{{ i.ExpressNum }}</span>
        </view>
        <view class="container-box-title-right" @click="copy(i.ExpressNum)">
          复制
        </view>
      </view>
      <view style="margin: 20px 0" v-if="i.Remark"> 备注：{{ i.Remark }} </view>
      <u-steps
        dot
        :current="0"
        activeColor="#29B7A3"
        direction="column"
        v-if="i.Company"
      >
        <u-steps-item v-for="item in i.Track" :key="Time">
          <view slot="title">
            <span style="font-size: 28rpx; color: #29b7a3; font-weight: 600">{{
              item.Status
            }}</span>
            <span
              style="
                font-size: 24rpx;
                color: #29b7a3;
                font-weight: 400;
                margin-left: 20rpx;
              "
              >{{ item.FTime }}</span
            >
          </view>
          <view slot="desc" style="margin-top: 16rpx">
            {{ item.Context }}
          </view>
        </u-steps-item>
      </u-steps>
      <u-empty mode="data" :text="i.Message" v-else />
    </view>
    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script>
import { expressQuery } from '@/api/order.js';
export default {
  data() {
    return {
      showLogistics: [],
    };
  },
  onLoad({ OrderExpresses, phone }) {
    this.getLogistics(OrderExpresses, phone);
  },
  methods: {
    async getLogistics(StrOrderExpresses, phone) {
      const OrderExpresses = JSON.parse(decodeURIComponent(StrOrderExpresses));
      const numsList = OrderExpresses.map((v) => v.ExpressNum);
      const sendData = [];
      numsList.forEach((v) => {
        sendData.push({
          ExpressNum: v,
          Phone: phone,
        });
      });
      const res = await expressQuery(sendData);
      if (res.Type === 200) {
        res.Data.forEach((v) => {
          v.Remark = OrderExpresses.filter(
            (s) => s.ExpressNum === v.ExpressNum
          )[0].Remark;
        });
        this.showLogistics = res.Data;
      }
    },
    copy(i) {
      uni.setClipboardData({
        data: String(i),
        fail: (err) => {
          console.log('err', err);
        },
      });
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  padding: 20rpx;

  &-box {
    background: white;
    padding: 32rpx;
    margin-bottom: 32rpx;
    box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);
    border-radius: 16rpx;

    &-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24rpx;

      &-left {
        &-one {
          font-size: 28rpx;
          font-family:
            PingFangSC-Semibold,
            PingFang SC;
          font-weight: 600;
          color: #333333;
        }

        &-two {
          font-size: 28rpx;
          font-family:
            PingFangSC-Semibold,
            PingFang SC;
          font-weight: 600;
          color: #29b7a3;
          margin-left: 20rpx;
        }
      }

      &-right {
        font-size: 28rpx;
        font-family:
          PingFangSC-Semibold,
          PingFang SC;
        font-weight: 600;
        color: #343434;
      }
    }
  }
}
</style>
