<template>
  <view class="container">
    <view class="display-style container-top" style="margin-bottom: 20rpx">
      <p>历史数据</p>
      <view class="display-style1" @click="showPicker = true">
        <span style="margin-right: 10rpx">{{ query.date }}</span>
        <u-icon name="arrow-down" color="#ffffff" size="16"></u-icon>
      </view>
    </view>

    <view class="container-list" v-for="(item, index) in list" :key="index">
      <view class="container-list-date">
        <p style="color: #333333; font-size: 18px; margin-bottom: 24rpx">
          {{ item.type }}
        </p>
        <p style="color: #999999; font-size: 13px" v-if="type == 0">
          平均收缩压：{{ item.SystolicBloodPressure }}mmHg 平均舒张压：{{
            item.DiastolicPressure
          }}mmHg
        </p>
        <p style="color: #999999; font-size: 13px" v-if="type == 1">
          平均血糖：{{ item.BloodSugar }}mmol/L
        </p>
        <p style="color: #999999; font-size: 13px" v-if="type == 2">
          平均心率：{{ item.HeartRate }}bpm
        </p>
        <p style="color: #999999; font-size: 13px" v-if="type == 3">
          平均体重：{{ item.Weight }}kg 平均身高：{{ item.Height }}cm
        </p>
      </view>
      <u-divider></u-divider>
      <view class="container-list-detail">
        <view class="" v-for="(o, index2) in item.data" :key="index2">
          <u-cell :border="false">
            <view class="container-icon" slot="icon" style="width: 60px">
              <u--image :src="defaultSrc" width="60px" height="60px"></u--image>
            </view>
            <view slot="title" style="margin-bottom: 30rpx; font-size: 16px">
              <view v-if="type == 0">
                <p style="margin-bottom: 20px">
                  收缩压：{{ o.Data.SystolicBloodPressure }}mmHg
                </p>
                <p>舒张压：{{ o.Data.DiastolicPressure }}mmHg</p>
              </view>
              <view v-if="type == 1">
                <p style="margin-bottom: 20px">{{ o.Data.BloodGlucoseType }}</p>
                <p>血糖：{{ o.Data.BloodSugar }}mmol/L</p>
              </view>
              <view v-if="type == 2">
                <p>心率：{{ o.Data.HeartRate }}bpm</p>
              </view>
              <view v-if="type == 3">
                <p style="margin-bottom: 20px">体重：{{ o.Data.Height }}cm</p>
                <p>身高：{{ o.Data.Weight }}kg</p>
              </view>
            </view>
            <view slot="value" style="font-size: 20px">
              {{ o.detailTime }}
            </view>
          </u-cell>
          <u-divider></u-divider>
        </view>
      </view>
    </view>

    <u-empty v-if="list.length === 0" mode="list" text="暂无数据" />

    <u-datetime-picker
      :show="showPicker"
      v-model="value1"
      mode="year-month"
      :maxDate="Number(new Date())"
      @close="showPicker = false"
      @confirm="showPickerChooseDate"
      @cancel="showPicker = false"
    >
    </u-datetime-picker>
  </view>
</template>

<script>
const app = getApp();
import { getDataEntryListByUserId } from '@/api/record.js';
import { ItemGroupBy, dateData } from '@/utils/utils';
export default {
  data() {
    return {
      defaultSrc: '/subPhysical/static/xy.png',
      list: [],
      type: '',
      query: {
        date: null,
        userId: app.globalData.userInfo.Id,
        type: null,
      },
      showPicker: false,
      value1: Number(new Date()),
    };
  },
  onLoad({ type, time }) {
    this.type = type;
    this.getList();
    this.query.date = this.$dateFormat(time, 'YYYY-MM');
  },
  methods: {
    showPickerChooseDate({ value, mode }) {
      console.log('value', this.$dateFormat(value, 'YYYY-MM', false));
      console.log('mode', mode);
      this.query.date = this.$dateFormat(value, 'YYYY-MM', false);
      this.showPicker = false;
      this.getList();
    },
    async getList() {
      this.query.type = this.type;
      let res = await getDataEntryListByUserId(this.query);
      if (res.Type == 200) {
        res.Data.forEach((e) => {
          e.Data = JSON.parse(e.Data);
          e.Time = this.$dateFormat(e.SignTime, 'YYYY-MM-DD');
          e.detailTime = this.$dateFormat(e.SignTime, 'HH:mm');
        });
        // res.Data.sort(dateData('', true)
        const newResData = res.Data.sort(dateData('SignTime', false));
        const newResData2 = newResData.sort(dateData('CreatedTime', false));
        // 将数据通过日期进行分组
        const newArr = ItemGroupBy(newResData2, 'Time');
        console.log(newArr);
        // 求取平均值
        newArr.forEach((e) => {
          const avgList = [];
          e.data.forEach((k) => {
            Object.keys(k.Data).map((m) => {
              avgList.push({
                [m]: k.Data[m],
              });
            });
          });
          const finAvgList = this.averageCal(avgList);
          Object.keys(finAvgList).map((m) => {
            e[m] = finAvgList[m];
          });
          const newArr = ItemGroupBy(avgList, 'Time');
        });
        this.list = newArr;
      }
    },
    averageCal(arr, fixed = 2) {
      let dataobj = {};
      arr.map((obj) => {
        Object.keys(obj).map((item) => {
          dataobj[item] || (dataobj[item] = []);
          if (
            obj[item] !== undefined &&
            obj[item] !== null &&
            obj[item] !== '' &&
            !isNaN(obj[item])
          ) {
            // 有值且为数字
            dataobj[item].push(+obj[item]);
          }
        });
      });
      Object.keys(dataobj).map((item) => {
        if (dataobj[item] && dataobj[item].length) {
          dataobj[item] = (
            dataobj[item].reduce((a, b) => a + b, 0) / dataobj[item].length
          ).toFixed(fixed);
        } else {
          dataobj[item] = '';
        }
      });

      return dataobj;
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding: 32rpx;

  &-top {
    background-color: #29b7a3;
    padding: 16rpx;
    color: white;
    border-radius: 8rpx;
    position: fixed !important;
    top: 0;
    width: calc(100% - 32px);
    z-index: 99;
  }

  &-list {
    margin-top: 30px;
    padding: 28rpx 32rpx 0 32rpx;
    background-color: white;

    &-date {
      margin-bottom: 24rpx;
    }
  }

  /deep/ .u-cell {
    background-color: white;
  }

  /deep/ .u-collapse-item__content {
    background-color: white;
  }

  /deep/ .u-collapse-item__content__text {
    padding: 12px 0 !important;
  }

  /deep/ .u-empty {
    margin-top: 100px !important;
  }
}
</style>
