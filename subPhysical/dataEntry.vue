<template>
  <view class="container">
    <!-- 血压相关 -->
    <view v-if="type === '血压'">
      <p>收缩压</p>
      <view style="padding: 20rpx">
        <slideChoose
          id="inputId"
          ref="refswiper"
          :max-value="ssymaxValue"
          ruleType="mmHg"
          :current="ssyCurrent"
          @finishRuler="finishRuler($event, 'ssyValue')"
        />
        <u-input
          type="number"
          v-model="info.ssyValue"
          @change="onInputChange($event, 'ssyCurrent')"
          customStyle="width:50%;margin:0 auto;height:50px"
        >
          <template slot="suffix"> mmHg </template>
        </u-input>
      </view>
      <p>舒张压</p>
      <view style="padding: 20rpx">
        <slideChoose
          ref="refswiper"
          :max-value="szymaxValue"
          ruleType="mmHg"
          :current="szyCurrent"
          @finishRuler="finishRuler($event, 'szyValue')"
        />
        <u-input
          type="number"
          v-model="info.szyValue"
          @change="onInputChange($event, 'szyCurrent')"
          customStyle="width:50%;margin:0 auto;height:50px"
        >
          <template slot="suffix"> mmHg </template>
        </u-input>
      </view>
    </view>

    <!-- 血糖相关 -->
    <view v-if="type === '血糖'">
      <view class="u-demo-block__content">
        <view
          class="u-page__tag-item"
          v-for="(item, index) in BloodGlucoseEntryPeriod"
          :key="index"
        >
          <u-tag
            :text="item.name"
            :plain="!item.checked"
            size="large"
            type="primary"
            :name="index"
            @click="radioClick"
          >
          </u-tag>
        </view>
      </view>
      <p>血糖值</p>
      <view style="padding: 20rpx">
        <slideChoose
          id="inputId"
          ref="refswiper"
          :max-value="xtmaxValue"
          ruleType="mmol/L"
          :current="xtCurrent"
          @finishRuler="finishRuler($event, 'xtValue')"
          :decimal="true"
        />
        <u-input
          type="digit"
          v-model="info.xtValue"
          @change="onInputChange($event, 'xtCurrent')"
          customStyle="width:50%;margin:0 auto;height:50px"
          @blur="onInputBlur($event, 'xtCurrent')"
        >
          <template slot="suffix"> mmol/L </template>
        </u-input>
      </view>
    </view>

    <!-- 心率相关 -->
    <view v-if="type === '心率'">
      <p>心率值</p>
      <view style="padding: 20rpx">
        <slideChoose
          id="inputId"
          ref="refswiper"
          :max-value="xlmaxValue"
          ruleType="mmol/L"
          :current="xlCurrent"
          @finishRuler="finishRuler($event, 'xlValue')"
        />
        <u-input
          type="number"
          v-model="info.xlValue"
          @change="onInputChange($event, 'xlCurrent')"
          customStyle="width:50%;margin:0 auto;height:50px"
        >
          <template slot="suffix"> bpm </template>
        </u-input>
      </view>
    </view>

    <!-- 体重、身高相关 -->
    <view v-if="type === '体重'">
      <p>体重</p>
      <view style="padding: 20rpx">
        <slideChoose
          id="inputId"
          ref="refswiper"
          :max-value="tzmaxValue"
          ruleType="kg"
          :current="tzCurrent"
          @finishRuler="finishRuler($event, 'tzValue')"
        />
        <u-input
          type="number"
          v-model="info.tzValue"
          @change="onInputChange($event, 'tzCurrent')"
          customStyle="width:40%;margin:0 auto;height:50px"
        >
          <template slot="suffix"> kg </template>
        </u-input>
      </view>
      <p>身高</p>
      <view style="padding: 20rpx">
        <slideChoose
          ref="refswiper"
          :max-value="sgmaxValue"
          ruleType="mmHg"
          :current="sgCurrent"
          @finishRuler="finishRuler($event, 'sgValue')"
        />
        <u-input
          type="number"
          v-model="info.sgValue"
          @change="onInputChange($event, 'sgCurrent')"
          customStyle="width:40%;margin:0 auto;height:50px"
        >
          <template slot="suffix"> cm </template>
        </u-input>
      </view>
    </view>

    <u-cell-group>
      <u-cell @click="onChooseTime('date')" :border="false" :isLink="true">
        <view slot="title">
          <p>日期<span style="color: red">*</span></p>
        </view>
        <span slot="value">{{ info.date }}</span>
      </u-cell>
    </u-cell-group>

    <u-cell-group>
      <u-cell @click="onChooseTime('time')" :border="false" :isLink="true">
        <view slot="title">
          <p>测量时间<span style="color: red">*</span></p>
        </view>
        <span slot="value">{{ info.time }}</span>
      </u-cell>
    </u-cell-group>

    <view class="bomm-btn-style" style="height: 100px"></view>
    <u-button
      type="success"
      @click="onSave"
      shape="circle"
      text="提交"
      customStyle="width:90%;bottom: 30px;position: fixed; left: 50%;transform: translateX(-50%);fontSize:16px;z-index:99"
    >
    </u-button>
    <u-datetime-picker
      :show="showpicker"
      mode="date"
      @confirm="dateChoose"
      v-model="pickerValue"
      @cancel="showpicker = false"
      :maxDate="Date.now()"
    ></u-datetime-picker>
    <u-datetime-picker
      :show="showpicker1"
      mode="time"
      v-model="timeObj"
      @confirm="dateChoose1"
      @cancel="showpicker1 = false"
    >
    </u-datetime-picker>

    <u-modal
      :show="upload"
      title="温馨提示"
      content="确定要提交吗？"
      showConfirmButton="true"
      @confirm="onSave"
      showCancelButton="true"
      @cancel="upload = false"
    >
    </u-modal>
    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script>
const app = getApp();
import slideChoose from '@/components/mp-dlm-slide-choose/mp-dlm-slide-choose.vue';
import { dataEntry } from '@/api/record.js';
import { dateFormat } from '@/utils/validate.js';
export default {
  components: {
    slideChoose,
  },
  onLoad({ type }) {
    this.type = type;
    uni.showLoading({
      title: '加载数据中',
    });
  },
  onReady() {
    console.log('渲染完成');
    this.setIntervalId = setInterval(() => {
      this.getIsonPage();
    }, 500);
  },
  data() {
    const currentValue = 155;
    const currentValue1 = 65;
    const date = dateFormat(new Date(), 'YYYY-MM-DD');
    const time = dateFormat(new Date(), 'HH:mm', false);
    return {
      upload: false,
      BloodGlucoseEntryPeriod: [
        {
          name: '空腹',
          checked: true,
        },
        {
          name: '早餐后',
          checked: false,
        },
        {
          name: '午餐前',
          checked: false,
        },
        {
          name: '午餐后',
          checked: false,
        },
        {
          name: '晚餐前',
          checked: false,
        },
        {
          name: '晚餐后',
          checked: false,
        },
      ],
      ssyCurrent: 135, // 收缩压卡尺选择的刻度
      szyCurrent: 95, // 舒张压卡尺选择的刻度
      xtCurrent: 75, // 默认值+15
      xlCurrent: 85,
      tzCurrent: 70,
      sgCurrent: 175,
      szymaxValue: 210, // 舒张压最大的刻度 这里的值是最大值 + 30
      ssymaxValue: 330, // 收缩压最大的刻度 这里的值是最大值 + 30
      xtmaxValue: 330,
      xlmaxValue: 310,
      tzmaxValue: 230,
      sgmaxValue: 230,
      showpicker: false,
      showpicker1: false,
      pickerValue: Number(new Date()),
      info: {
        date,
        time,
        szyValue: 80, // 舒张压发送请求的值
        ssyValue: 120, // 收缩压发送请求的值
        xtValue: 6.0,
        xlValue: 70,
        tzValue: 55,
        sgValue: 160,
      },
      timeObj: time,
      type: '',
      show: true,
      BloodGlucoseType: '空腹',
      setIntervalId: null,
    };
  },
  methods: {
    onSubmit() {
      if (
        this.type === '血压' &&
        (this.info.ssyValue > 300 || this.info.ssyValue < 0)
      ) {
        this.$refs.uToast.show({
          message: '收缩压的范围为0-300，请重新输入',
          type: 'error',
        });
        return;
      } else if (
        this.type === '血压' &&
        (this.info.szyValue > 180 || this.info.szyValue < 0)
      ) {
        this.$refs.uToast.show({
          message: '舒张压的范围为0-180，请重新输入',
          type: 'error',
        });
        return;
      } else if (
        this.type === '血糖' &&
        (this.info.xtValue > 30 || this.info.xtValue < 0)
      ) {
        this.$refs.uToast.show({
          message: '血糖的范围为0-30，请重新输入',
          type: 'error',
        });
        return;
      } else if (
        this.type === '心率' &&
        (this.info.xlValue > 280 || this.info.xlValue < 0)
      ) {
        this.$refs.uToast.show({
          message: '心率的范围为0-280，请重新输入',
          type: 'error',
        });
        return;
      } else if (
        this.type === '体重' &&
        (this.info.tzValue > 200 || this.info.tzValue < 0)
      ) {
        this.$refs.uToast.show({
          message: '体重的范围为0-200，请重新输入',
          type: 'error',
        });
        return;
      } else if (
        this.type === '体重' &&
        (this.info.sgValue > 200 || this.info.sgValue < 0)
      ) {
        this.$refs.uToast.show({
          message: '身高的范围为0-200，请重新输入',
          type: 'error',
        });
        return;
      }
      this.upload = true;
    },
    getIsonPage() {
      const query = uni.createSelectorQuery().in(this);
      query
        .select('#inputId')
        .boundingClientRect((data) => {
          if (data) {
            clearInterval(this.setIntervalId);
            uni.hideLoading();
          }
        })
        .exec();
    },
    radioClick(name) {
      console.log(name);
      this.BloodGlucoseEntryPeriod.map((item, index) => {
        item.checked = index === name ? true : false;
      });
      this.BloodGlucoseType = this.BloodGlucoseEntryPeriod[name].name;
    },
    onChooseTime(mode) {
      if (mode == 'date') {
        this.showpicker = true;
      } else {
        this.showpicker1 = true;
      }
    },
    async onSave() {
      let req = {
        UserId: app.globalData.userInfo.Id,
        Organization: app.globalData.orgId || '',
        Type: null, // 0：血压 1：血糖 2：心率 3：体重 ,
        SignTime: this.info.date + ' ' + this.info.time + ':00',
        Data: {},
      };
      if (this.type === '血压') {
        req.Type = 0;
        req.Data = {
          SystolicBloodPressure: this.info.ssyValue,
          DiastolicPressure: this.info.szyValue,
        };
      } else if (this.type === '血糖') {
        req.Type = 1;
        req.Data = {
          BloodSugar: this.info.xtValue,
          BloodGlucoseType: this.BloodGlucoseType,
        };
      } else if (this.type === '心率') {
        req.Type = 2;
        req.Data = {
          HeartRate: this.info.xlValue,
        };
      } else if (this.type === '体重') {
        req.Type = 3;
        req.Data = {
          Height: this.info.sgValue,
          Weight: this.info.tzValue,
        };
      }
      console.log('reqData', req);
      let res = await dataEntry([req]);
      if (res.Type === 200) {
        this.$refs.uToast.show({
          message: '录入数据成功',
          type: 'success',
        });
        setTimeout(() => {
          const pages = getCurrentPages();
          const prePage = pages[pages.length - 2];
          if (prePage.$vm.getData) {
            prePage.$vm.getData();
          }
          uni.navigateBack();
        }, 1000);
      } else {
        this.$refs.uToast.show({
          message: res.Content,
          type: 'error',
        });
      }
      this.upload = false;
    },
    onInputChange($event, type) {
      // this.current = e * 1 + 15
      console.log($event, type);
      if (this.type != '血糖') {
        this[type] = $event * 1 + 15;
      } else {
        this[type] = $event * 10 + 15;
      }
    },
    onInputBlur($event, type) {
      console.log($event, type);
      this.info.xtValue = ($event * 1).toFixed(1);
      this[type] = this.info.xtValue * 10 + 15;
    },
    finishRuler($event, type) {
      this.info[type] = $event;
    },
    dateChoose1(e) {
      console.log('分秒选中', e);
      this.info.time = e.value;
      this.showpicker1 = false;
    },
    // 选择时间
    dateChoose({ value, mode }) {
      console.log('value', value);
      this.showpicker = false;
      let newTime = this.$dateFormat(value, 'YYYY-MM-DD', false);
      console.log('newTime', newTime);
      this.info.date = newTime;
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding: 32rpx;

  .u-page__tag-item {
    margin-right: 20rpx;
    margin-bottom: 20rpx;
  }

  .u-demo-block__content {
    flex-direction: row;
    flex-wrap: wrap;
    align-items: center;
    display: flex;
  }

  /deep/ .u-cell {
    background-color: white;
  }

  /deep/ .u-input__content__field-wrapper__field {
    font-size: 30px !important;
    height: 40px !important;
  }

  /deep/ .u-input {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  /deep/ .u-border {
    background: #ffffff !important;
    box-shadow: 0px 3px 4px 0px rgba(0, 0, 0, 0.08) !important;
    border-radius: 8rpx 8rpx 8rpx 8rpx !important;
    opacity: 1 !important;
    border: none;
  }
}
</style>
