import config from '../config';

export const HW_DEVICE_AUTH_STATE_CHANGE = 'HW_DEVICE_AUTH_STATE_CHANGE';

let deviceAuthState = null;
/**
 * 是否正在授权中
 */
let authInProgress = false;

// export const setHWAuthInProgress = () => {
//   authInProgress = true;
// };

export const clearHWAuthInProgress = () => {
  authInProgress = false;
};

export const isHWAuthInProgress = () => {
  return authInProgress;
};

export const BloodPressureDataUnAuthTip = '您没有血压数据访问权限，请授权后再试';
export const HeartRateDataUnAuthTip = '您没有心率数据访问权限，请授权后再试';

uni.$on(HW_DEVICE_AUTH_STATE_CHANGE, (res) => {
  console.warn(res.Content);
  switch (res.Type) {
    case 401: {
      if (deviceAuthState) {
        return;
      }
      deviceAuthState = true;

      uni.showModal({
        content: '华为授权已失效，请重新授权',
        confirmText: '确定',
        cancelText: '取消',
        success: ({
          confirm
        }) => {
          deviceAuthState = false;
          if (confirm) {
            gotoHWHealthDataAuth();
          }
        },
        fail: (e) => {
          uni.$log.warn(e);
          deviceAuthState = false;
        }
      });
    }
    break;
    case 403: {
      if (deviceAuthState) {
        return;
      }
      deviceAuthState = true;

      uni.showModal({
        content: res.Content,
        confirmText: '确定',
        cancelText: '取消',
        success: ({
          confirm
        }) => {
          deviceAuthState = false;
          if (confirm) {
            gotoHWHealthDataAuth();
          }
        },
        fail: (e) => {
          uni.$log.warn(e);
          deviceAuthState = false;
        }
      });
    }
    break;
    case 503: {
      uni.showToast({
        icon: 'none',
        title: '服务器繁忙，请稍后再试',
      });
    }
    break;
    default: {
      uni.showToast({
        icon: 'none',
        title: res.Content,
      });
    }
  }
});

export const gotoHWHealthDataAuth = async () => {
  authInProgress = true;
  const r = await wx.navigateToMiniProgram({
    appId: 'wxa6c04f899577d944',
    path: 'pages/authLogin/authLogin',
    extraData: {
      lang: 'zh-CN',
      client_id: config.hwHealthKitAppId,
      // https://developer.huawei.com/consumer/cn/doc/HMSCore-Guides/health-sampling-data-0000001131423778
      scope: [
        'https://www.huawei.com/healthkit/heartrate.read',
        'https://www.huawei.com/healthkit/bloodpressure.read',
      ],
      state: '1234',
      envVersion: config.envVersion,
    },
  });
  if (r.errMsg != 'navigateToMiniProgram:ok') {
    uni.$log.warn('hw 返回异常', r);
    authInProgress = false;
  }
};
