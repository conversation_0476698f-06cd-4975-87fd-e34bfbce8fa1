import {
  bindHWDevice,
  getHWRefreshToken
} from '../api/supplier';
import {
  isHWAuthInProgress,
  clearHWAuthInProgress,
  gotoHWHealthDataAuth,
} from './hw-state';

const DEVICE_BIND_STATE_UPDATE = 'DEVICE_BIND_STATE_UPDATE';

export default {
  data() {
    return {
      isBind: null,
      needLoad: false,
    };
  },
  created() {
    uni.$on(DEVICE_BIND_STATE_UPDATE, this.onBindChanged);
  },
  destroyed() {
    uni.$off(DEVICE_BIND_STATE_UPDATE, this.onBindChanged);
  },
  onLoad() {
    console.debug('HWDevice onLoad');
    getHWRefreshToken({
      userId: getApp().getAccountId(),
    }).then((r) => {
      if (r.Type != 200) {
        console.warn('获取华为refreshToken 失败');
        return;
      }
      uni.$emit(DEVICE_BIND_STATE_UPDATE, {
        binding: !!r.Data,
        update: false
      });
    });
  },
  onShow() {
    console.debug('HWDevice onShow');

    if (!isHWAuthInProgress()) return;
    clearHWAuthInProgress();

    const {
      code,
      error,
      state
    } =
    wx.getEnterOptionsSync().referrerInfo.extraData ?? {};
    console.debug(wx.getEnterOptionsSync());
    const redirectUri =
      'https://h5hosting.dbankcdn.com/cch5/healthkit/oauth-h5/oauth-callback.html';
    uni.$log.info({
      code,
      state,
      error,
      redirectUri,
    });
    if (code && state) {
      bindHWDevice({
          RedirectUri: redirectUri,
          Code: code,
        })
        .then((r) => {
          if (r.Type != 200) {
            uni.$log.warn(r.Content);
          } else {
            uni.$log.info('绑定成功');
            r.Content = '绑定成功';
            uni.$emit(DEVICE_BIND_STATE_UPDATE, {
              binding: true,
              update: true
            });
          }
          wx.showToast({
            icon: 'none',
            title: r.Content ?? `${r.Type}`,
          });
        });
    } else if (error) {
      uni.$log.warn(error);

      uni.showToast({
        title: error?.msg || '绑定失败',
        icon: 'none'
      })
    }
  },
  methods: {
    onBindChanged({
      binding,
      update
    }) {
      this.isBind = binding;
      if (update && this.loadData) {
        this.loadData();
      }
    },
    async bindHWDevice() {
      gotoHWHealthDataAuth();
    },
  },
};
