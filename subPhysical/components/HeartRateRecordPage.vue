<template>
  <view class="root">
    <view v-if="isBind == false">
      <view class="latest card">
        <view class="latest-top">
          <view class="latest-value">
            <text class="latest-avg">{{ latest.value || '--' }}</text>
            <text>次/分钟</text>
          </view>
          <view>{{ $formateDate(latest.time, 'YYYY.MM.DD HH:mm') }}</view>
        </view>
        <text>心率</text>
      </view>

      <!-- 历史数据 -->
      <view>
        <view class="history">历史数据</view>
        <view class="record-container" :key="item.date" v-for="item in list">
          <Record :value="item">
            <template #title>
              <view style="padding-top: 19rpx"
                >平均心率：{{ item.avg.toFixed(2) }}KG</view
              >
            </template>

            <view
              class="record"
              :key="record.time"
              v-for="record in item.value"
            >
              <view class="record-text"> 心率：{{ record.value }}次/分钟 </view>
              <view class="record-time">
                {{ $formateDate(record.time, 'HH:mm') || '-' }}
              </view>
            </view>
          </Record>
        </view>
      </view>
    </view>

    <view v-if="isBind" class="card">
      <view
        class="heart-type"
        @click="handleRestingHeartClick"
        :class="showRestingHeart ? 'selected' : ''"
      >
        <view style="font-size: 28rpx">静息心率</view>
        <view
          style="
            font-size: 22rpx;
            display: flex;
            flex-direction: row;
            align-items: center;
          "
        >
          {{ restingHeart || '--' }}
          <text>次/分钟</text>
        </view>
      </view>

      <view v-if="showRestingHeart" class="heart-value">
        <view class="heart-value-latest">
          <view class="center-align"
            ><text style="font-size: 50rpx; font-weight: bold">{{
              restingHeart || '--'
            }}</text
            >次/分钟
          </view>
          静息心率
        </view>
      </view>
      <view v-else class="heart-value">
        <view class="heart-value-range">
          <view class="center-align"
            ><text style="font-size: 50rpx; font-weight: bold">{{
              heartRange
            }}</text
            >次/分钟
          </view>
          心率范围
        </view>
        <view class="heart-value-latest">
          <view class="center-align"
            ><text style="font-size: 50rpx; font-weight: bold">{{
              heartLatest
            }}</text
            >次/分钟
          </view>
          最新值{{ heartLatestTime }}
        </view>
      </view>

      <view class="gap"></view>

      <view class="chart-container">
        <view class="top">
          <view style="color: #333; font-size: 26rpx" @click="show = true">
            {{ dateString }}
            <uni-icons type="down"></uni-icons>
          </view>
          <view style="width: 150rpx">
            <u-subsection
              v-if="isShow"
              :list="dateTypes"
              :current="current"
              @change="sectionChange"
            ></u-subsection>
          </view>
        </view>

        <view class="chart">
          <qiun-data-charts
            :reshow="isShow"
            type="line"
            canvas2d
            :opts="opts"
            :chartData="chartData"
            :optsWatch="false"
            tooltipFormat="tlineTooltip"
            :animation="false"
          />
        </view>

        <u-datetime-picker
          v-if="mode"
          :show="show"
          v-model="date"
          :mode="mode"
          closeOnClickOverlay
          @close="show = false"
          @cancel="show = false"
          @confirm="handleDateChange"
        ></u-datetime-picker>
      </view>
    </view>

    <view v-if="!isBind" class="bottom-container">
      <view class="button-outside" @click="addRecord">手动记录</view>
      <view class="button" @click="bindHWDevice">绑定设备</view>
    </view>
    <view v-else class="bottom-container">
      <view class="bind-status"> 已绑定设备 </view>
    </view>
  </view>
</template>

<script>
import { getDataEntryListByUserId } from '../../api/record';
import { getSampleSet } from '../../api/supplier';

import {
  getEndOfDay,
  getFirstDayOfMonth,
  getLastDayOfMonth,
  getStartOfDay,
} from '../../utils/validate';
import { PHYSICAL_RECORD_ADDED } from '../PhysicalEvents';
import hwDevice from '../hw-device';
import {
  HW_DEVICE_AUTH_STATE_CHANGE,
  HeartRateDataUnAuthTip,
} from '../hw-state';
import Record from './Record.vue';

/**
 * 心率记录
 * @typedef {{
  date:string,
    avg:number,
    value:Array<{
      time:Date,
      value:number,
    }>
  }} Record
*/

export default {
  components: {
    Record,
  },
  mixins: [hwDevice],
  props: ['isShow'],
  data() {
    return {
      type: 2,
      needLoad: false,
      /**
       * @type {Array<Record>}
       */
      list: [],
      dateTypes: ['日', '月'],
      current: 0,
      show: false,
      date: Date.now(),
      requestId: 0,
      //您可以通过修改 config-ucharts.js 文件中下标为 ['line'] 的节点来配置全局默认参数，如都是默认参数，此处可以不传 opts 。实际应用过程中 opts 只需传入与全局默认参数中不一致的【某一个属性】即可实现同类型的图表显示不同的样式，达到页面简洁的需求。
      opts: {
        color: ['#3BD4BF', '#eee'],
        padding: [0, 20, 0, 0],
        // canvas2d: true,
        dataPointShape: false,
        enableScroll: false,
        dataLabel: false,
        enableMarkLine: true,
        legend: {
          position: 'top',
          float: 'right',
        },
        xAxis: {
          disableGrid: true,
          splitNumber: 3,
          boundaryGap: 'justify',
          format: 'xAxisTime',
          fontColor: '#999999',
          fontSize: 9,
          gridColor: '#EEE',
          axisLineColor: '#EEE',
        },
        yAxis: {
          showTitle: true,
          gridType: 'solid',
          splitNumber: 4,
          gridColor: '#EEE',
          data: [
            {
              title: '心率（bpm）',
              titleFontSize: 12,
              titleFontColor: '#333',
              min: 20,
              max: 220,
              textAlign: 'center',
              titleOffsetX: 16,
              titleOffsetY: -16,
              fontColor: '#999999',
              fontSize: 9,
              axisLineColor: '#EEE',
            },
          ],
        },
        extra: {
          line: {
            //   type: "straight",
            width: 1,
            activeType: 'none',
          },
          // markLine: {
          //   type: 'dash',
          //   dashLength: 2,
          //   data: [{
          //     value: 80,
          //     lineColor: '#E82C52',
          //   }]
          // },
          // tooltip: {
          //   legendShape: 'line',
          //   yAxisLabel: true,
          // }
        },
      },
      chartData: {
        series: [],
      },
      /**
       * 图表中是否显示静息心率
       */
      showRestingHeart: false,
      restingHeartList: null,
      heart: {
        /**
           * @type {{
             min:number,
             max:number
           }}
           */
        range: null,
        /**
           * @type {{
             time:number,
             value:number,
           }}
           */
        latest: null,
      },
    };
  },
  watch: {
    isShow: function (value, oldValue) {
      console.debug('isShow', value, oldValue);
      if (!this.needLoad) return;
      this.needLoad = false;
      this.loadData();
    },
    isBind: function (value, oldValue) {
      console.debug('isBind', value, oldValue);
      if (this.isShow) {
        this.loadData();
      } else {
        this.needLoad = true;
      }
    },
  },
  computed: {
    latest() {
      return this.list.length > 0 ? this.list[0].value[0] : {};
    },
    mode() {
      return this.current === 0 ? 'date' : 'year-month';
    },
    dateString() {
      return this.$formateDate(
        this.date,
        this.current === 0 ? 'YYYY年MM月DD日' : 'YYYY年MM月'
      );
    },
    heartRange() {
      if (this.heart.range) {
        return `${this.heart.range.min}-${this.heart.range.max}`;
      }
      return '--';
    },
    heartLatest() {
      if (this.heart.latest) {
        return this.heart.latest.value;
      }
      return '--';
    },
    heartLatestTime() {
      if (this.heart.latest) {
        return this.$formateDate(
          this.heart.latest.time,
          this.current == 0 ? 'MM-DD HH:mm' : 'MM-DD'
        );
      }
      return '--';
    },
    restingHeart() {
      if (this.restingHeartList && this.restingHeartList.length > 0) {
        if (this.current == 0) {
          return this.restingHeartList[0].value;
        } else {
          let min, max;
          this.restingHeartList.forEach((item) => {
            min ??= item.value;
            max ??= item.value;
            if (min > item.value) {
              min = item.value;
            }
            if (max < item.value) {
              max = item.value;
            }
          });
          return `${min}-${max}`;
        }
      }
      return '--';
    },
  },
  created() {
    uni.$on(PHYSICAL_RECORD_ADDED, this.onRecordAdded);
    if (this.isBind != null && this.isShow) {
      this.loadData();
    }
  },
  destroyed() {
    uni.$off(PHYSICAL_RECORD_ADDED, this.onRecordAdded);
  },
  methods: {
    sectionChange(index) {
      this.current = index;
      this.loadData();
    },
    handleDateChange(dateTime) {
      const { value, mode } = dateTime;

      this.show = false;
      setTimeout(() => {
        this.loadData();
        // console.debug(dateTime, this.date);
      });
    },
    handleRestingHeartClick() {
      this.showRestingHeart = !this.showRestingHeart;

      let startDate, endDate;
      let date = new Date(this.date);
      if (this.current === 0) {
        startDate = getStartOfDay(date);
        endDate = getEndOfDay(date);
      } else {
        startDate = getFirstDayOfMonth(date);
        endDate = getLastDayOfMonth(date);
      }
      const chartData = JSON.parse(JSON.stringify(this.chartData));
      const heartChartData = chartData.series.find((e) => e.name == '心率');
      chartData.series = [];
      if (this.showRestingHeart) {
        const s = startDate.getTime() / 1000;
        let data = [];
        if (this.restingHeartList && this.restingHeartList.length > 0) {
          if (this.current == 0) {
            for (let i = 0; i < 24 * 60; i++) {
              data.push([s + i * 60, this.restingHeartList[0].value]);
            }
          } else {
            data = this.restingHeartList.map((item) => {
              return [item.date, item.value];
            });
          }
        }
        chartData.series.push({
          name: '静息心率',
          connectNulls: true,
          data,
        });
      }
      chartData.series.push(heartChartData);
      this.chartData = chartData;
    },
    async loadData() {
      if (this.isBind) {
        await this.loadHWData();
      } else {
        await this.loadKFXData();
      }
    },
    async loadHWData() {
      this.requestId++;
      const requestId = this.requestId;
      let startDate, endDate;
      let date = new Date(this.date);
      if (this.current === 0) {
        startDate = getStartOfDay(date);
        endDate = getEndOfDay(date, true);
      } else {
        startDate = getFirstDayOfMonth(date);
        endDate = getLastDayOfMonth(date);
      }

      this.opts.xAxis.format = this.current === 0 ? 'xAxisTime' : 'xAxisDate';
      this.opts.xAxis.min = startDate.getTime() / 1000;
      this.opts.xAxis.max = endDate.getTime() / 1000;
      this.chartData.series = [];

      const res = await getSampleSet({
        UserId: getApp().getAccountId(),
        StartDate: this.$formateDate(startDate, 'YYYY-MM-DD HH:mm:ss'),
        EndDate: this.$formateDate(endDate, 'YYYY-MM-DD HH:mm:ss'),
        Type: this.current + 1,
        DataTypeName: [
          'com.huawei.instantaneous.heart_rate',
          'com.huawei.instantaneous.resting_heart_rate',
        ],
      });

      if (this.requestId != requestId) return;

      if (res.Type !== 200) {
        if (res.Type === 403) {
          res.Content = HeartRateDataUnAuthTip;
        }
        uni.$emit(HW_DEVICE_AUTH_STATE_CHANGE, res);
      }
      // console.debug(res.Data);

      let min, max;
      const st = startDate.getTime() / 1000;
      const et = endDate.getTime() / 1000;

      if (res.Data?.RestingHeartRate && res.Data?.RestingHeartRate.length > 0) {
        const value = res.Data.RestingHeartRate.map((item) => {
          return {
            date: new Date(item.Time).getTime() / 1000,
            value: item.Value[0].Bpm ?? item.Value[0].Avg,
          };
        }).filter((e) => e.date >= st && e.date < et);
        this.restingHeartList = value;
      } else {
        this.restingHeartList = null;
      }

      /**
       * @type {Array<Array<number>}
       */
      const list = (res.Data?.HeartRate ?? [])
        .map((item) => {
          // 0: {Time: "2025-03-31T23:59:00", Value: [{Avg: 84, Min: 59, Max: 138, Last: 75}]}
          // Time: "2025-03-31T23:59:00"
          // Value: [{Avg: 84, Min: 59, Max: 138, Last: 75}]
          // 0: {Avg: 84, Min: 59, Max: 138, Last: 75}
          // Avg: 84
          // Last: 75
          // Max: 138
          // Min: 59
          const { Bpm, Avg, Min, Max, Last } = item.Value[0];
          const value = Bpm ?? Avg;
          const dateTime = new Date(item.Time).getTime() / 1000;

          if (dateTime > st) {
            max ??= value;
            min ??= value;
            if (value > max) {
              max = value;
            }
            if (value < min) {
              min = value;
            }
          }
          return [dateTime, value];
        })
        .filter((e) => e[0] >= st && e[0] < et);

      // console.debug(list);
      if (list.length > 0) {
        this.heart = {
          range: {
            min,
            max,
          },
          latest: {
            value: list[list.length - 1][1],
            time: list[list.length - 1][0] * 1000,
          },
        };
      }

      const chartData = {
        series: [],
      };

      if (this.showRestingHeart) {
        const s = startDate.getTime() / 1000;
        let data = [];
        if (this.restingHeartList && this.restingHeartList.length > 0) {
          if (this.current == 0) {
            for (let i = 0; i < 24 * 60; i++) {
              data.push([s + i * 60, this.restingHeartList[0].value]);
            }
          } else {
            data = this.restingHeartList.map((item) => {
              return [item.date, item.value];
            });
          }
        }
        chartData.series.push({
          name: '静息心率',
          connectNulls: true,
          data,
        });
      }
      chartData.series.push({
        name: '心率',
        connectNulls: true,
        data: list.length > 0 ? list : [[startDate.getTime() / 1000, 0]],
      });

      this.chartData = chartData;
    },
    async loadKFXData() {
      const res = await getDataEntryListByUserId({
        userId: getApp().getAccountId(),
        type: this.type,
      });

      if (res.Type != 200) {
        uni.showToast({
          icon: 'error',
          title: res.Content,
        });
      }
      // console.debug(res.Data);

      const list = (res.Data ?? [])
        .map((item) => {
          const { HeartRate } = JSON.parse(item.Data);
          const dateTime = new Date(item.SignTime);
          return {
            date: this.$formateDate(dateTime, 'YYYY.MM.DD'),
            value: [
              {
                time: dateTime,
                value: HeartRate,
              },
            ],
          };
        })
        .sort((a, b) => {
          return a.date > b.date;
        });

      // console.debug(list);

      let previous;
      /**
       * @type {Array<Record>}
       */
      const result = [];
      for (let item of list) {
        if (item.date == previous?.date) {
          previous.value.push(item.value[0]);
        } else {
          result.push(item);
          previous = item;
        }
      }
      for (let s of result) {
        if (s.value.length > 0) {
          const total = s.value.reduce((a, c) => a + c.value, 0);
          s.avg = total / s.value.length;
        } else {
          s.avg = 0;
        }
      }

      // console.debug(result);
      this.list = result;
    },
    addRecord() {
      // console.debug('添加血糖记录');
      uni.navigateTo({
        url: `/subPhysical/AddRecord?type=${this.type}`,
      });
    },
    /**
     * @param {{Type:number,SignTime:string,Data:{ HeartRate:number}}} record
     */
    onRecordAdded(record) {
      // console.debug('onRecordAdded', record);
      if (record.Type == this.type) {
        const dateTime = new Date(record.SignTime);
        const date = this.$formateDate(dateTime, 'YYYY.MM.DD');
        const { HeartRate } = record.Data;
        const item = {
          time: dateTime,
          value: HeartRate,
        };
        // 添加 record 到列表中
        const r = this.list.find((e) => e.date == date);
        if (r) {
          r.value = [item, ...r.value].sort((a, b) => a.time > b.time);
          const total = r.value.reduce((a, c) => a + c.value, 0);
          r.avg = total / r.value.length;
        } else {
          this.list = [
            {
              date: this.$formateDate(dateTime, 'YYYY.MM.DD'),
              avg: HeartRate,
              value: [item],
            },
            ...this.list,
          ].sort((a, b) => a.date > b.date);
        }
      }
    },
  },
};
</script>

<style>
.root {
  padding: 0 32rpx;
}

.card {
  background-color: white;
  border-radius: 16rpx;
  box-shadow: 0rpx 3rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);
  padding: 32rpx;
}

.latest {
  color: #666666;
  font-size: 24rpx;
}

.latest-top {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.latest-value {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.latest-avg {
  font-family: DINPro;
  font-weight: bold;
  font-size: 50rpx;
  color: #333333;
  margin-right: 8rpx;
}

.history {
  padding: 16rpx;
  color: #333333;
}

.bottom-container {
  display: flex;
  flex-direction: row;
  gap: 32rpx;
  width: 100%;
  padding: 0 32rpx;
  bottom: 50px;
  position: fixed;
  left: 50%;
  transform: translateX(-50%);
  color: #fff;
}

.button {
  border-radius: 48rpx;
  padding: 8rpx 16rpx;
  min-width: 120rpx;
  min-height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 500;
  font-size: 28rpx;
  box-sizing: border-box;
  color: white;
  background-color: #29b7a3;
  flex: 1;
}

.button:hover {
  background-color: #1e8a7a;
  color: #eee;
}

/* 镂空按钮 */
.button-outside {
  border-radius: 48rpx;
  padding: 8rpx 16rpx;
  min-width: 120rpx;
  min-height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 500;
  font-size: 28rpx;
  box-sizing: border-box;
  border: 2rpx solid #29b7a3;
  color: #23b4a3;
  background-color: white;
  flex: 1;
}

.button-outside:hover {
  background-color: #eee;
}

.bind-status {
  background-color: #e3e6e7;
  border-radius: 40px;
  width: 100%;
  height: 40px;
  color: #29b7a3;
  display: flex;
  font-size: 14px;
  justify-content: center;
  align-items: center;
}

.record-container {
  margin-bottom: 20rpx;
}

.record-container:last-child {
  padding-bottom: 200rpx;
}

.record {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  color: #333333;
  border-bottom: 1px solid #eeeeee;
  padding: 24rpx 0;
}

.record:first-child {
  margin-top: 22rpx;
  padding-top: 25rpx;
}

.record:last-child {
  padding-bottom: 25rpx;
  border-bottom: none;
}

.record-time {
  color: #666666;
}

.top {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.heart-type {
  background-color: #f4f5f5;
  border-radius: 16rpx;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 24rpx;
  color: #333;
}

.selected {
  background-color: #2ed1bb;
  color: white;
}

.heart-value {
  display: flex;
  flex-direction: row;
  padding: 16rpx 0;
  color: #666666;
  font-size: 24rpx;
}

.gap {
  height: 1px;
  margin: 16rpx 0;
  background-color: #eeeeee;
}

.center-align {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8rpx;
}

.heart-value-range {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.heart-value-latest {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.chart {
  width: 100%;
  height: 400rpx;
}
</style>
