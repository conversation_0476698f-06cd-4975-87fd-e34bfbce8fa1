<template>
  <view class="root">
    <view class="top">
      <view style="color: #333; font-size: 26rpx" @click="show = true">
        {{ dateString }}
        <uni-icons type="down"></uni-icons>
      </view>
      <view style="width: 150rpx">
        <u-subsection
          :list="list"
          :current="current"
          @change="sectionChange"
        ></u-subsection>
      </view>
    </view>

    <view class="chart">
      <qiun-data-charts
        :reshow="reshow"
        type="tline"
        canvas2d
        :opts="opts"
        :chartData="chartData"
        :optsWatch="false"
        tooltipFormat="tooltipBloodPressure"
        :animation="false"
      />
    </view>

    <u-datetime-picker
      v-if="mode"
      :show="show"
      v-model="date"
      :mode="mode"
      closeOnClickOverlay
      @close="show = false"
      @cancel="show = false"
      @confirm="handleDateChange"
    ></u-datetime-picker>
  </view>
</template>

<script>
import { getDataEntryListByUserId } from '../../api/record';
import { getSampleSet } from '../../api/supplier';
import {
  getEndOfDay,
  getFirstDayOfMonth,
  getLastDayOfMonth,
  getStartOfDay,
} from '../../utils/validate';
import hwDevice from '../hw-device';
import {
  BloodPressureDataUnAuthTip,
  HW_DEVICE_AUTH_STATE_CHANGE,
} from '../hw-state';

export default {
  props: ['reshow'],
  mixins: [hwDevice],
  data() {
    return {
      list: ['日', '月'],
      current: 0,
      show: false,
      date: Date.now(),
      requestId: 0,
      //您可以通过修改 config-ucharts.js 文件中下标为 ['line'] 的节点来配置全局默认参数，如都是默认参数，此处可以不传 opts 。实际应用过程中 opts 只需传入与全局默认参数中不一致的【某一个属性】即可实现同类型的图表显示不同的样式，达到页面简洁的需求。
      opts: {
        padding: [0, 20, 0, 0],
        animation: false,
        enableScroll: false,
        dataLabel: false,
        enableMarkLine: true,
        legend: {
          position: 'top',
          float: 'right',
        },
        xAxis: {
          disableGrid: true,
          splitNumber: 3,
          boundaryGap: 'justify',
          format: 'xAxisTime',
          fontColor: '#999999',
          fontSize: 9,
          gridColor: '#EEE',
          axisLineColor: '#EEE',
        },
        yAxis: {
          showTitle: true,
          gridType: 'solid',
          splitNumber: 4,
          gridColor: '#EEE',
          data: [
            {
              title: '血压（mmHg）',
              titleFontSize: 12,
              titleFontColor: '#333',
              min: 40,
              max: 200,
              // calibration: true,
              textAlign: 'center',
              titleOffsetX: 70,
              titleOffsetY: -16,
              fontColor: '#999999',
              fontSize: 9,
              axisLineColor: '#EEE',
            },
          ],
        },
        extra: {
          line: {
            type: 'curve',
            width: 1,
            activeType: 'hollow',
            // animation: "horizontal",
          },
          markLine: {
            type: 'dash',
            dashLength: 2,
            data: [
              {
                value: 90,
                lineColor: '#25a796',
                showLabel: true,
                labelAlign: 'right',
                labelFontSize: 9,
                labelFontColor: '#999999',
                labelBgOpacity: 0,
                labelBgColor: '#fff',
              },
              {
                value: 140,
                lineColor: '#ba8d12',
                showLabel: true,
                labelAlign: 'right',
                labelFontSize: 9,
                labelFontColor: '#999999',
                labelBgOpacity: 0,
                labelBgColor: '#fff',
              },
            ],
          },
          tooltip: {
            legendShape: 'line',
            yAxisLabel: true,
          },
        },
      },
      chartData: {
        series: [],
      },
    };
  },
  computed: {
    mode() {
      return this.current === 0 ? 'date' : 'year-month';
    },
    dateString() {
      return this.$formateDate(
        this.date,
        this.current === 0 ? 'YYYY年MM月DD日' : 'YYYY年MM月'
      );
    },
  },
  created() {
    if (this.isBind != null) {
      // console.debug('created loadData');
      this.loadData();
    }
    this.$watch('isBind', () => {
      // console.debug('watch loadData');
      this.loadData();
    });
  },
  methods: {
    sectionChange(index) {
      this.current = index;
      this.loadData();
    },
    handleDateChange(dateTime) {
      const { value, mode } = dateTime;

      this.show = false;
      setTimeout(() => {
        this.loadData();
        // console.debug(dateTime, this.date);
      });
    },
    async loadData() {
      this.requestId++;
      let startDate, endDate;
      let date = new Date(this.date);
      if (this.current === 0) {
        startDate = getStartOfDay(date);
        endDate = getEndOfDay(date);
      } else {
        startDate = getFirstDayOfMonth(date);
        endDate = getLastDayOfMonth(date);
      }

      this.opts.xAxis.format = this.current === 0 ? 'xAxisTime' : 'xAxisDate';
      this.opts.xAxis.min = startDate.getTime() / 1000;
      this.opts.xAxis.max = endDate.getTime() / 1000;
      this.chartData.series = [];

      if (this.isBind) {
        await this.loadHWData(startDate, endDate, this.requestId);
      } else {
        await this.loadKFXData(startDate, endDate, this.requestId);
      }
    },
    async loadKFXData(startDate, endDate, requestId) {
      const res = await getDataEntryListByUserId({
        userId: getApp().getAccountId(),
        type: 0,
        beginTime: this.$formateDate(startDate, 'YYYY-MM-DD HH:mm:ss'),
        endTime: this.$formateDate(endDate, 'YYYY-MM-DD HH:mm:ss'),
      });

      if (this.requestId != requestId) return;

      if (res.Type != 200) {
        console.warn(res.Content);
        return;
      }

      /**
       * @type {Array<{date:number,value:number,value2:number}>}
       */
      const list = res.Data.map((item) => {
        const { SystolicBloodPressure, DiastolicPressure } = JSON.parse(
          item.Data
        );
        const dateTime = new Date(item.SignTime);
        return {
          date: dateTime.getTime() / 1000,
          value2: SystolicBloodPressure,
          value: DiastolicPressure,
        };
      });

      this.updateData(list, startDate, endDate);
    },
    async loadHWData(startDate, endDate, requestId) {
      const res = await getSampleSet({
        UserId: getApp().getAccountId(),
        StartDate: this.$formateDate(startDate),
        EndDate: this.$formateDate(endDate),
        Type: this.current + 1,
        DataTypeName: ['com.huawei.instantaneous.blood_pressure'],
      });

      if (this.requestId != requestId) return;

      if (res.Type != 200) {
        if (res.Type === 403) {
          res.Content = BloodPressureDataUnAuthTip;
        }
        uni.$emit(HW_DEVICE_AUTH_STATE_CHANGE, res);
      }

      /**
       * @type {Array<{date:number,value:number,value2:number}>}
       */
      const list = (res.Data?.BloodPressure ?? []).map((item) => {
        const {
          SystolicPressure,
          SystolicPressureAvg,
          DiastolicPressure,
          DiastolicPressureAvg,
        } = item.Value[0];
        const dateTime = new Date(item.Time);
        return {
          date: dateTime.getTime() / 1000,
          value2: SystolicPressure ?? SystolicPressureAvg,
          value: DiastolicPressure ?? DiastolicPressureAvg,
        };
      });

      this.updateData(list, startDate, endDate);
    },
    updateData(list, startDate, endDate) {
      // console.debug('list', list);
      this.chartData = {
        series: [
          {
            name: '低压',
            color: '#3BD4BF',
            connectNulls: true,
            data:
              list.length > 0
                ? list.map((e) => [e.date, e.value])
                : [[startDate.getTime() / 1000, null]],
          },
          {
            name: '高压',
            color: '#EBB82F',
            connectNulls: true,
            data:
              list.length > 0
                ? list.map((e) => [e.date, e.value2])
                : [[startDate.getTime() / 1000, null]],
          },
        ],
      };

      // if (list.length == 0) {
      //   this.$nextTick(() => {
      //     this.chartData = {
      //       series: [{
      //           name: "低压",
      //           color: "#3BD4BF",
      //           connectNulls: true,
      //           data: [],
      //         },
      //         {
      //           name: "高压",
      //           color: "#EBB82F",
      //           connectNulls: true,
      //           data: [],
      //         },
      //       ]
      //     };
      //   })
      // }
    },
  },
};
</script>

<style>
.root {
  padding-top: 32rpx;
}

.top {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart {
  width: 100%;
  height: 400rpx;
  /* position: relative; */
}
</style>
