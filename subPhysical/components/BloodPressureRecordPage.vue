<template>
  <view class="root">
    <view class="latest card">
      <view class="latest-top">
        <view class="latest-value">
          <text class="latest-avg"
            >{{ latest.systolicPressure || '--' }}/{{
              latest.diastolicPressure || '--'
            }}</text
          >
          <text>mmHg</text>
        </view>
        <text>{{ $formateDate(latest.time, 'YYYY.MM.DD HH:mm') || '--' }}</text>
      </view>
      <view class="">高压/低压</view>
      <view
        style="height: 1rpx; background-color: #f4f4f4; margin-top: 16rpx"
      ></view>
      <BloodPressureChart
        :isBind="isBind"
        :reshow="isShow"
      ></BloodPressureChart>
    </view>
    <view>
      <view class="history">历史数据</view>
      <view class="record-container" :key="item.date" v-for="item in list">
        <Record :value="item">
          <template #title>
            <view style="margin-top: 19rpx"
              >平均收缩压：{{ item.systolicPressureAvg.toFixed(2) }}mmHg</view
            >
            <view style="margin-top: 8rpx"
              >平均舒张压：{{ item.diastolicPressureAvg.toFixed(2) }}mmHg</view
            >
          </template>

          <view class="record" :key="record.time" v-for="record in item.value">
            <view class="record-text">
              <view> 收缩压:{{ record.systolicPressure }}mmHg </view>
              <view> 舒张压:{{ record.diastolicPressure }}mmHg </view>
            </view>
            <view class="record-time">
              {{ $formateDate(record.time, 'HH:mm') || '-' }}
            </view>
          </view>
        </Record>
      </view>
    </view>

    <view v-if="!isBind" class="bottom-container">
      <view class="button-outside" @click="addRecord">手动记录</view>
      <view class="button" @click="bindHWDevice">绑定设备</view>
    </view>
    <view v-else class="bottom-container">
      <view class="bind-status"> 已绑定设备 </view>
    </view>

    <!-- <u-loading-page :loading="true"></u-loading-page> -->
  </view>
</template>

<script>
import { getDataEntryListByUserId } from '../../api/record';
import { getSampleSet } from '../../api/supplier';
import { PHYSICAL_RECORD_ADDED } from '../PhysicalEvents';
import hwDevice from '../hw-device';
import {
  BloodPressureDataUnAuthTip,
  HW_DEVICE_AUTH_STATE_CHANGE,
} from '../hw-state';
import BloodPressureChart from './BloodPressureChart.vue';
import Record from './Record.vue';

/**
   * 血压记录
   * @typedef {{
     date:string,
     systolicPressureAvg:number,
     diastolicPressureAvg:number,
     value:Array<{
       time:Date,
       systolicPressure:number,
       diastolicPressure:number
     }>
    }} Record
   */

export default {
  components: {
    BloodPressureChart,
    Record,
  },
  mixins: [hwDevice],
  props: ['isShow'],
  data() {
    return {
      type: 0,
      needLoad: false,
      /**
       * @type {Array<Record>}
       */
      list: [],
    };
  },
  watch: {
    isShow: function (value, oldValue) {
      console.debug('isShow', value, oldValue);
      if (this.needLoad) {
        this.needLoad = false;
        this.loadData();
      }
    },
    isBind: function (value, oldValue) {
      console.debug('isBind', value, oldValue);
      if (this.isShow) {
        this.loadData();
      } else {
        this.needLoad = true;
      }
    },
  },
  computed: {
    latest() {
      return this.list.length > 0 ? this.list[0].value[0] : {};
    },
  },
  created() {
    uni.$on(PHYSICAL_RECORD_ADDED, this.onRecordAdded);
    if (this.isBind != null && this.isShow) {
      this.loadData();
    }
  },
  destroyed() {
    uni.$off(PHYSICAL_RECORD_ADDED, this.onRecordAdded);
  },
  methods: {
    addRecord() {
      console.debug('添加血压记录');
      uni.navigateTo({
        url: `/subPhysical/AddRecord?type=${this.type}`,
      });
    },
    async loadData() {
      if (this.isBind) {
        await this.loadHWData();
      } else {
        await this.loadKFXData();
      }
    },
    /**
     * @param {Array<Optional<Record>>} list
     */
    updateData(list) {
      // console.debug('list', list);
      let previous;
      /**
       * @type {Array<Record>}
       */
      const result = [];
      for (let item of list) {
        if (item.date == previous?.date) {
          previous.value.push(item.value[0]);
        } else {
          result.push(item);
          previous = item;
        }
      }
      for (let s of result) {
        if (s.value.length > 0) {
          const totalS = s.value.reduce((a, c) => a + c.systolicPressure, 0);
          const totalD = s.value.reduce((a, c) => a + c.diastolicPressure, 0);
          s.systolicPressureAvg = totalS / s.value.length;
          s.diastolicPressureAvg = totalD / s.value.length;
        } else {
          s.systolicPressureAvg = 0;
          s.diastolicPressureAvg = 0;
        }
      }

      // console.debug(result);
      this.list = result;
    },
    async loadHWData() {
      const endDate = new Date();
      const startDate = new Date(endDate.getTime() - 30 * 24 * 60 * 60 * 1000);
      const res = await getSampleSet({
        UserId: getApp().getAccountId(),
        StartDate: this.$formateDate(startDate, 'YYYY-MM-DD HH:mm:ss'),
        EndDate: this.$formateDate(endDate, 'YYYY-MM-DD HH:mm:ss'),
        Type: 1,
        DataTypeName: ['com.huawei.instantaneous.blood_pressure'],
      });
      if (res.Type !== 200) {
        if (res.Type === 403) {
          res.Content = BloodPressureDataUnAuthTip;
        }
        uni.$emit(HW_DEVICE_AUTH_STATE_CHANGE, res);
      }
      // console.debug('hwList', res.Data.BloodPressure);

      /**
       * @type {Optional<Record>[]}
       */
      const list = (res.Data?.BloodPressure ?? []).reverse().map((item) => {
        const { SystolicPressure, DiastolicPressure } = item.Value[0];
        const dateTime = new Date(item.Time);
        return {
          date: this.$formateDate(dateTime, 'YYYY.MM.DD'),
          value: [
            {
              time: dateTime,
              systolicPressure: SystolicPressure,
              diastolicPressure: DiastolicPressure,
            },
          ],
        };
      });

      this.updateData(list);
    },
    async loadKFXData() {
      const res = await getDataEntryListByUserId({
        userId: getApp().getAccountId(),
        type: this.type,
      });

      if (res.Type != 200) {
        uni.showToast({
          icon: 'error',
          title: res.Content,
        });
        return;
      }
      // console.debug(res.Data);

      /**
       * @type {Record[]}
       */
      const list = res.Data.map((item) => {
        const { SystolicBloodPressure, DiastolicPressure } = JSON.parse(
          item.Data
        );
        const dateTime = new Date(item.SignTime);
        return {
          date: this.$formateDate(dateTime, 'YYYY.MM.DD'),
          value: [
            {
              time: dateTime,
              systolicPressure: SystolicBloodPressure,
              diastolicPressure: DiastolicPressure,
            },
          ],
        };
      }).sort((a, b) => {
        return a.date > b.date;
      });

      // console.debug(list);
      this.updateData(list);
    },
    /**
       * @param {{Type:number,SignTime:string,Data:{SystolicBloodPressure:number,
            DiastolicPressure:number}}} record
       */
    onRecordAdded(record) {
      // console.debug('onRecordAdded', record);
      if (record.Type == this.type) {
        const dateTime = new Date(record.SignTime);
        const date = this.$formateDate(dateTime, 'YYYY.MM.DD');
        const { SystolicBloodPressure, DiastolicPressure } = record.Data;
        const item = {
          time: dateTime,
          systolicPressure: SystolicBloodPressure,
          diastolicPressure: DiastolicPressure,
        };
        // 添加 record 到列表中
        const s = this.list.find((e) => e.date == date);
        if (s) {
          s.value = [item, ...s.value].sort((a, b) => a.time > b.time);
          const totalS = s.value.reduce((a, c) => a + c.systolicPressure, 0);
          const totalD = s.value.reduce((a, c) => a + c.diastolicPressure, 0);
          s.systolicPressureAvg = totalS / s.value.length;
          s.diastolicPressureAvg = totalD / s.value.length;
        } else {
          this.list = [
            {
              date: this.$formateDate(dateTime, 'YYYY.MM.DD'),
              systolicPressureAvg: SystolicBloodPressure,
              diastolicPressureAvg: DiastolicPressure,
              value: [item],
            },
            ...this.list,
          ].sort((a, b) => a.date > b.date);
        }
      }
    },
  },
};
</script>

<style>
.root {
  padding: 0 32rpx;
}

.card {
  background-color: white;
  border-radius: 16rpx;
  box-shadow: 0rpx 3rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);
  padding: 32rpx;
}

.latest {
  color: #666666;
  font-size: 24rpx;
}

.latest-top {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.latest-value {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.latest-avg {
  font-family: DINPro;
  font-weight: bold;
  font-size: 50rpx;
  color: #333333;
  margin-right: 8rpx;
}

.history {
  padding: 30rpx 0 15rpx 30rpx;
  color: #333333;
}

.bottom-container {
  display: flex;
  flex-direction: row;
  gap: 32rpx;
  width: 100%;
  padding: 0 32rpx;
  bottom: 50px;
  position: fixed;
  left: 50%;
  transform: translateX(-50%);
  /* backgroundColor: #29B7A3; */
  color: #fff;
}

.button {
  border-radius: 48rpx;
  padding: 8rpx 16rpx;
  min-width: 120rpx;
  min-height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 500;
  font-size: 28rpx;
  box-sizing: border-box;
  color: white;
  background-color: #29b7a3;
  flex: 1;
}

.button:hover {
  background-color: #1e8a7a;
  color: #eee;
}

/* 镂空按钮 */
.button-outside {
  border-radius: 48rpx;
  padding: 8rpx 16rpx;
  min-width: 120rpx;
  min-height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 500;
  font-size: 28rpx;
  box-sizing: border-box;
  border: 2rpx solid #29b7a3;
  color: #23b4a3;
  background-color: white;
  flex: 1;
}

.button-outside:hover {
  background-color: #eee;
}

.bind-status {
  background-color: #e3e6e7;
  border-radius: 40px;
  width: 100%;
  height: 40px;
  color: #29b7a3;
  display: flex;
  font-size: 14px;
  justify-content: center;
  align-items: center;
}

.record-container {
  margin-bottom: 20rpx;
}

.record-container:last-child {
  padding-bottom: 200rpx;
}

.record {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  color: #333333;
  border-bottom: 1px solid #eeeeee;
  padding: 24rpx 0;
}

.record-text {
  display: flex;
  flex-direction: column;
  gap: 13rpx;
}

.record:first-child {
  margin-top: 22rpx;
  padding-top: 25rpx;
}

.record:last-child {
  padding-bottom: 25rpx;
  border-bottom: none;
}

.record-time {
  color: #666666;
}
</style>
