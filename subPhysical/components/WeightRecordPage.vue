<template>
  <view class="root">
    <view class="latest card">
      <view class="latest-top">
        <view class="latest-value">
          <text class="latest-avg">{{ latest.weight || '--' }} KG</text>
          <text>体重</text>
        </view>
        <view class="latest-value">
          <text class="latest-avg">{{ latest.height || '--' }} CM</text>
          <text>身高</text>
        </view>
        <view>{{ $formateDate(latest.time, 'YYYY.MM.DD HH:mm') }}</view>
      </view>
    </view>

    <!-- 历史数据 -->
    <view>
      <view class="history">历史数据</view>
      <view class="record-container" :key="item.date" v-for="item in list">
        <Record :value="item">
          <template #title>
            <view style="padding-top: 19rpx"
              >平均体重：{{ item.avg.toFixed(2) }}KG</view
            >
          </template>

          <view class="record" :key="record.time" v-for="record in item.value">
            <view class="record-text">
              <view> 体重：{{ record.weight }}KG </view>
              <view> 身高：{{ record.height }}CM </view>
            </view>
            <view class="record-time">
              {{ $formateDate(record.time, 'HH:mm') || '-' }}
            </view>
          </view>
        </Record>
      </view>
    </view>

    <view class="bottom-container">
      <view class="button-outside" @click="addRecord">手动记录</view>
    </view>
  </view>
</template>

<script>
import { getDataEntryListByUserId } from '../../api/record';
import { times } from '../../uni_modules/uview-ui/libs/function/digit';
import { dateFormat } from '../../utils/validate';
import { PHYSICAL_RECORD_ADDED } from '../PhysicalEvents';
import hwDevice from '../hw-device';
import Record from './Record.vue';

/**
 *
 */

export default {
  components: {
    Record,
  },
  data() {
    return {
      type: 3,
      /**
       * @type {Array<{date:string,avg:number,value:Array<{time:Date,weight:number,height:number}>}>}
       */
      list: [],
    };
  },
  computed: {
    latest() {
      return this.list.length > 0 ? this.list[0].value[0] : {};
    },
  },
  created() {
    uni.$on(PHYSICAL_RECORD_ADDED, this.onRecordAdded);
    this.loadData();
  },
  destroyed() {
    uni.$off(PHYSICAL_RECORD_ADDED, this.onRecordAdded);
  },
  methods: {
    async loadData() {
      const res = await getDataEntryListByUserId({
        userId: getApp().getAccountId(),
        type: this.type,
      });

      if (res.Type != 200) {
        uni.showToast({
          icon: 'error',
          title: res.Content,
        });
        return;
      }
      // console.debug(res.Data);

      const list = res.Data.map((item) => {
        const { Height, Weight } = JSON.parse(item.Data);
        const dateTime = new Date(item.SignTime);
        return {
          date: this.$formateDate(dateTime, 'YYYY.MM.DD'),
          value: [
            {
              time: dateTime,
              height: Height,
              weight: Weight,
            },
          ],
        };
      }).sort((a, b) => {
        return a.date > b.date;
      });

      // console.debug(list);

      let previous;
      /**
       * @type {Array<{date:string,avg:number,value:Array<{time:Date,height:number,weight:number}>}>}
       */
      const result = [];
      for (let item of list) {
        if (item.date == previous?.date) {
          previous.value.push(item.value[0]);
        } else {
          result.push(item);
          previous = item;
        }
      }
      for (let s of result) {
        if (s.value.length > 0) {
          const total = s.value.reduce((a, c) => a + c.weight, 0);
          s.avg = total / s.value.length;
        } else {
          s.avg = 0;
        }
      }

      // console.debug(result);
      this.list = result;
    },
    addRecord() {
      // console.debug('添加血糖记录');
      uni.navigateTo({
        url: `/subPhysical/AddRecord?type=${this.type}`,
      });
    },
    /**
       * @param {{Type:number,SignTime:string,Data:{ Height:number,
            Weight:number}}}} record
       */
    onRecordAdded(record) {
      // console.debug('onRecordAdded', record);
      if (record.Type == this.type) {
        const dateTime = new Date(record.SignTime);
        const date = this.$formateDate(dateTime, 'YYYY.MM.DD');
        const { Height, Weight } = record.Data;
        const item = {
          time: dateTime,
          weight: Weight,
          height: Height,
        };
        // 添加 record 到列表中
        const r = this.list.find((e) => e.date == date);
        if (r) {
          r.value = [item, ...r.value].sort((a, b) => a.time > b.time);
          const total = r.value.reduce((a, c) => a + c.weight, 0);
          r.avg = total / r.value.length;
        } else {
          this.list = [
            {
              date: this.$formateDate(dateTime, 'YYYY.MM.DD'),
              avg: Weight,
              value: [item],
            },
            ...this.list,
          ].sort((a, b) => a.date > b.date);
        }
      }
    },
  },
};
</script>

<style>
.root {
  padding: 0 32rpx;
}

.card {
  background-color: white;
  border-radius: 16rpx;
  box-shadow: 0rpx 3rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);
  padding: 32rpx;
}

.latest {
  color: #666666;
  font-size: 24rpx;
}

.latest-top {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.latest-value {
  display: flex;
  flex-direction: column;
}

.latest-avg {
  font-family: DINPro;
  font-weight: bold;
  font-size: 50rpx;
  color: #333333;
  margin-right: 8rpx;
}

.history {
  padding: 30rpx 0 15rpx 30rpx;
  color: #333333;
}

.bottom-container {
  width: 100%;
  padding: 0 32rpx;
  bottom: 50px;
  position: fixed;
  left: 50%;
  transform: translateX(-50%);
  /* backgroundColor: #29B7A3; */
  color: #fff;
}

/* 镂空按钮 */
.button-outside {
  border-radius: 48rpx;
  padding: 8rpx 16rpx;
  min-width: 120rpx;
  min-height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 500;
  font-size: 28rpx;
  box-sizing: border-box;
  border: 2rpx solid #29b7a3;
  color: #23b4a3;
  background-color: white;
}

.button-outside:hover {
  background-color: #eee;
}

.record-container {
  margin-bottom: 20rpx;
}

.record-container:last-child {
  padding-bottom: 200rpx;
}

.record {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  color: #333333;
  border-bottom: 1px solid #eeeeee;
  padding: 24rpx 0;
}

.record:first-child {
  margin-top: 22rpx;
  padding-top: 25rpx;
}

.record:last-child {
  padding-bottom: 25rpx;
  border-bottom: none;
}

.record-time {
  color: #666666;
}
</style>
