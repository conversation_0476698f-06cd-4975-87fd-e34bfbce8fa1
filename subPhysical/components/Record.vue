<template>
  <view class="item card">
    <view class="item-top">
      <view class="item-title-tag">
        <view class="item-title-tag-inner"></view>
      </view>
      <view class="item-top-center">
        <view class="item-title">{{ value.date }}</view>
        <slot name="title"></slot>
      </view>
      <view class="expend-icon" @click="handleIconClick"
        ><uni-icons :type="expendIcon || ''"></uni-icons
      ></view>
    </view>

    <view class="record-container" v-show="isExpend">
      <slot></slot>
    </view>
  </view>
</template>

<script>
export default {
  props: ['value'],
  data() {
    return {
      isExpend: false,
    };
  },
  computed: {
    expendIcon() {
      return this.isExpend ? 'up' : 'down';
    },
  },
  methods: {
    handleIconClick() {
      this.isExpend = !this.isExpend;
    },
  },
};
</script>

<style>
.card {
  background-color: white;
  border-radius: 16rpx;
  box-shadow: 0rpx 3rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);
  padding: 32rpx;
}

.item {
  font-weight: 400;
  font-size: 0.75rem;
  color: #666666;
  line-height: 34rpx;
}

.item-top {
  display: flex;
  gap: 13rpx;
}

.item-top-center {
  flex: 1;
}

.item-title {
  font-weight: 500;
  font-size: 1rem;
  color: #333333;
  line-height: 34rpx;
  display: flex;
}

.item-title-tag {
  background-color: #29b7a3;
  width: 24rpx;
  height: 24rpx;
  border-radius: 12rpx;
  position: relative;
  overflow: hidden;
}

.item-title-tag-inner {
  position: absolute;
  background-color: #31dbc3;
  width: 14rpx;
  height: 14rpx;
  border-radius: 6rpx;
  right: 0;
  bottom: 0;
}

.expend-icon {
  min-width: 44px;
  min-height: 44px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.record-container {
  background: #f5f6fa;
  border-radius: 16rpx;
  padding: 0 20rpx 0 40rpx;
}
</style>
