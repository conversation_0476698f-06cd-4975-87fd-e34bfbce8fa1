<template>
  <view>
    <view class="sub-container">
      <u-tabs
        :list="list1"
        lineColor="#29B7A3"
        :scrollable="false"
        @click="sectionChange"
      ></u-tabs>
    </view>

    <view class="page-container">
      <BloodPressureRecordPage
        class="page"
        v-show="current === 0"
        :is-show="current === 0"
      />
      <HeartRateRecord
        class="page"
        v-show="current === 1"
        :is-show="current === 1"
      />
      <BloodSugarRecordPage class="page" v-show="current === 2" />
      <WeightRecord class="page" v-show="current === 3" />
    </view>
  </view>
</template>

<script>
const app = getApp();
import BloodPressureRecordPage from './components/BloodPressureRecordPage.vue';
import BloodSugarRecordPage from './components/BloodSugarRecordPage.vue';
import HeartRateRecord from './components/HeartRateRecordPage.vue';
import WeightRecord from './components/WeightRecordPage.vue';
import HWDevice from './hw-device';
import { getRecentRecord } from '@/api/record.js';

export default {
  mixins: [HWDevice],
  components: {
    BloodPressureRecordPage,
    BloodSugarRecordPage,
    HeartRateRecord,
    WeightRecord,
  },
  data() {
    return {
      list: ['血压', '心率', '血糖', '体重'],
      list1: ['血压', '心率', '血糖', '体重'].map((e) => {
        return {
          name: e,
        };
      }),
      current: 0,
    };
  },
  methods: {
    sectionChange({ index }) {
      this.current = index;
    },
  },
};
</script>

<style scoped lang="scss">
.sub-container {
  background-color: white;
  margin-bottom: 32rpx;
}

.page-container {
  flex: 1;
  position: relative;
}

.page {
  position: absolute;
  inset: 0;
}
</style>
