<template>
  <view class="container">
    <block v-if="userInfo.WorkflowStatus !== 2">
      <view
        :style="{ top: (showImagePos.top || 0) + 'px' }"
        style="
          z-index: 99;
          width: 266rpx;
          height: 56rpx;
          position: absolute;
          right: 32rpx;
        "
        @click="showCamraImage = false"
      >
        <image
          src="./static/tic.png"
          style="width: 100%; height: 100%; position: absolute"
        >
        </image>
        <span
          style="
            color: white;
            text-align: center;
            line-height: 56rpx;
            font-size: 12px;
            left: 0;
            right: 0;
            position: absolute;
          "
          >点击相机可自动识别<span style="margin-left: 5px">x</span>
        </span>
      </view>
      <u-cell-group>
        <u-cell>
          <view slot="title">
            <p>姓名<span style="color: red">*</span></p>
          </view>
          <u-input
            slot="value"
            placeholder="请输入真实姓名"
            border="none"
            v-model="smQuery.Name"
            inputAlign="right"
          ></u-input>
        </u-cell>
        <u-cell :border="false" customStyle="justify-content: space-between">
          <view slot="title">
            <p>性别<span style="color: red">*</span></p>
          </view>
          <view slot="value" style="display: flex">
            <view
              class="u-page__tag-item"
              style="margin-right: 10rpx"
              v-for="(item, index) in sexList"
              :key="index"
            >
              <MPTag
                shape="circle"
                iconPosition="right"
                :icon="item.name === '男' ? 'man' : 'woman'"
                :text="item.name"
                :plain="!item.checked"
                type="success"
                :name="index"
                @click="radioClick"
              >
              </MPTag>
            </view>
          </view>
        </u-cell>
        <u-cell>
          <view slot="title" id="targetComponent">
            <p>证件号码</p>
          </view>
          <u-input
            slot="value"
            placeholder="请输入真实证件号码"
            type="idcard"
            border="none"
            v-model="smQuery.UserCertificates[0].CertificateValue"
            inputAlign="right"
            @blur="IdCardblur"
          >
          </u-input>
          <ocr-navigator
            slot="right-icon"
            @onSuccess="ocrSuccess"
            certificateType="idCard"
            :opposite="false"
          >
            <u-icon name="camera" color="#24BAA3" size="28"></u-icon>
          </ocr-navigator>
        </u-cell>
        <u-cell>
          <view slot="title">
            <p>年龄<span style="color: red">*</span></p>
          </view>
          <u-input
            slot="value"
            placeholder="请输入真实年龄"
            border="none"
            v-model="smQuery.Age"
            inputAlign="right"
            type="number"
            @blur="Ageblur"
          ></u-input>
        </u-cell>
      </u-cell-group>
    </block>
    <block v-else>
      <u-cell-group>
        <u-cell :border="false">
          <p slot="title">
            <span style="color: #29b7a3; margin-right: 24rpx">{{
              userInfo.Name
            }}</span>
            (本人 {{ userInfo.Sex }} {{ userInfo.Age + '岁' }})
          </p>
        </u-cell>
      </u-cell-group>
      <view style="height: 24rpx"></view>
    </block>
    <block v-if="scene === 'planUpdateUser'">
      <view
        class="container-diseases"
        @click="showAddDiseases = !showAddDiseases"
        v-if="!showAddDiseases"
      >
        <span>
          <span>完善线下就诊信息</span>
          <span class="container-diseases-text">（选填）</span>
        </span>
        <u-icon name="arrow-rightward"></u-icon>
      </view>
      <view class="container-diseases1" v-if="showAddDiseases">
        <text class="container-diseases-title">请选择就诊疾病</text>
        <view class="u-demo-block__content">
          <view
            class="u-page__tag-item"
            v-for="(item, index) in Entry"
            :key="item.Id"
          >
            <u-tag
              v-if="showMore ? true : index < showCount"
              :text="item.Key"
              :plain="!item.checked"
              size="large"
              :name="item.Id"
              @click="checkboxClick"
              type="success"
            >
            </u-tag>
          </view>
          <text
            @click="showMore = !showMore"
            style="color: #29b7a3"
            v-if="Entry.length > showCount"
            >{{ showMore ? '收起' : '更多' }}</text
          >

          <view
            class="u-demo-block__content-select"
            style="margin-top: 20rpx"
            v-show="isInEntryList.length > 0"
          >
            <u-cell
              :title="o.Key"
              v-for="o in isInEntryList"
              @click="cellClick(o)"
              :key="o.Id"
            ></u-cell>
          </view>
        </view>
        <u-textarea
          v-model="query.Describing"
          placeholder="请填写其他的疾病"
          :maxlength="999"
          :height="100"
          confirmType="return"
          @input="describingChange"
        >
        </u-textarea>
        <text class="container-diseases-title">线下病例资料(最多上传9张)</text>
        <u-upload
          :fileList="fileList5"
          @afterRead="afterRead"
          @delete="deletePic"
          name="5"
          multiple
          :maxCount="9"
          width="100"
          height="100"
          customStyle="marginLeft:30rpx"
        ></u-upload>
      </view>
    </block>
    <block v-if="scene === 'returningHospital'">
      <u-cell-group>
        <u-cell
          is-link
          @click="onCellClick('outTime')"
          v-if="dataObj.HaveDischarged"
        >
          <view slot="title">
            <p>
              出院时间<span style="color: red" v-if="dataObj.HaveDischarged"
                >*</span
              >
            </p>
          </view>
          <text slot="value">{{
            returningHospitalParams.showOutTime || '请选择'
          }}</text>
        </u-cell>
        <u-cell
          is-link
          @click="onCellClick('operationTime')"
          v-if="dataObj.Postoperative"
        >
          <view slot="title">
            <p>
              手术时间<span style="color: red" v-if="dataObj.Postoperative"
                >*</span
              >
            </p>
          </view>
          <text slot="value">{{
            returningHospitalParams.showOperationTime || '请选择'
          }}</text>
        </u-cell>
        <u-cell is-link @click="onCellClick('doctor')">
          <view slot="title">
            <p>就诊医生<span style="color: red">*</span></p>
          </view>
          <text slot="value">{{
            returningHospitalParams.doctorName || '请选择'
          }}</text>
        </u-cell>
      </u-cell-group>
    </block>
    <view v-if="!notShowSureBtn">
      <view style="height: 70px"></view>
      <view class="container-bomBtn" @click="onSave"> 确定 </view>
    </view>
    <u-toast ref="uToast"></u-toast>
    <u-datetime-picker
      :show="showOutTimePicker"
      v-model="returningHospitalParams.outTime"
      mode="date"
      :maxDate="Date.now()"
      @cancel="showOutTimePicker = false"
      :formatter="formatter"
      ref="datetimePicker"
      :defaultIndex="datetimePickerDefaultIndex"
      @confirm="(e) => onGetSelectDay(e, 'outTime')"
    ></u-datetime-picker>
    <u-datetime-picker
      :show="showOperationTimePicker"
      v-model="returningHospitalParams.operationTime"
      mode="date"
      :maxDate="Date.now()"
      @cancel="showOperationTimePicker = false"
      :formatter="formatter"
      ref="datetimePicker"
      :defaultIndex="datetimePickerDefaultIndex"
      @confirm="(e) => onGetSelectDay(e, 'operationTime')"
      useType="max"
    ></u-datetime-picker>
  </view>
</template>

<script>
import { newReadDict, GetDiseaseList } from '@/api/dictionary.js';
import { SaveTags, GetTags, setUserCase } from '@/api/record.js';
import { guid } from '@/utils/validate.js';

import {
  briGetAge,
  idCardTrue,
  idCardGetSex,
  idCardGetBri,
} from '@/utils/utils';
import {
  updateConsultRecord,
  updateConsultReport,
  queryRxTempBackRemind,
  backRemindQrCode,
} from '@/api/consult.js';

import { SetUserClaims } from '@/api/passport.js';
import MPTag from '@/components/mp-tag/u-tag.vue';
import { uploadFileToServer } from '../services/UploadService';
const app = getApp();
export default {
  components: {
    MPTag,
  },
  data() {
    return {
      datetimePickerDefaultIndex: [],
      showOutTimePicker: false,
      showOperationTimePicker: false,
      isInEntryList: [],
      showCount: 20,
      showMore: false,
      dataObj: {},
      showImagePos: {},
      showCamraImage: true,
      showImagePos: {},
      sexList: [
        {
          name: '男',
          checked: false,
        },
        {
          name: '女',
          checked: false,
        },
      ],
      smQuery: {
        UserId: app.globalData.userInfo.Id,
        Name: '',
        Sex: '',
        Birthday: '',
        Age: null,
        UserCertificates: [
          {
            CertificateType: 'idCard',
            CertificateValue: '',
          },
        ],
      },
      showAddDiseases: true,
      Entry: [],
      query: {
        Describing: '',
      },
      fileList5: [],
      okNextBer: true,
      notShowSureBtn: false,
      scene: '',
      userInfo: {},
      returningHospitalParams: {
        outTime: '',
        showOutTime: '',
        operationTime: '',
        showOperationTime: '',
        DctId: '',
        doctorName: '',
      },
    };
  },
  onLoad(option) {
    const Scene = option.Scene;
    this.scene = option.Scene;
    this.notShowSureBtn = option.notShowBtn;
    this.userInfo = app.globalData.userInfo;
    this.smQuery.Name = this.userInfo.Name;
    this.smQuery.Sex = this.userInfo.Sex;
    if (this.userInfo.Sex) {
      this.sexList.map((s) =>
        s.name === this.userInfo.Sex ? (s.checked = true) : false
      );
    }
    this.smQuery.Age = this.userInfo.Age;
    this.dataObj = option;
    let showTitle =
      this.userInfo.WorkflowStatus === 2 ? this.userInfo.Name : '完善个人信息';
    uni.setNavigationBarTitle({
      title: showTitle,
    });
    // 选择时间默认
    const nowData = this.$dateFormat(Date.now(), 'YYYY-MM-DD');
    this.datetimePickerDefaultIndex = [
      10,
      nowData.split('-')[1] - 1,
      nowData.split('-')[2] - 1,
    ];
    switch (Scene) {
      case 'planUpdateUser':
        // 获取疾病相关的信息
        this.onGetDisData();
        break;
      case 'returningHospital':
        if (!this.dataObj.GroupId) {
          uni.showModal({
            title: '温馨提示',
            content: '当前没有数据信息，请联系管理员',
            showCancel: false,
          });
          this.$log.error(`${this.$envVersion || ''}:没有GroupId`);
          return;
        }
        // 判断是否需要填写出院时间和手术时间
        this.onGetIsNeedFullTime();
        break;
      default:
        break;
    }
  },
  onReady() {
    this.$nextTick(() => {
      const query = uni.createSelectorQuery().in(this);
      query
        .select('#targetComponent')
        .boundingClientRect((rect) => {
          // rect 包含了组件的位置信息
          console.log('组件位置信息：', rect);
          if (rect && rect.id) {
            this.showImagePos = {
              top: rect.top - 30,
            };
          }
        })
        .exec();
    });
    this.$refs.datetimePicker.setFormatter(this.formatter);
  },
  methods: {
    async onGetIsNeedFullTime() {
      const data = {
        PageIndex: 1,
        PageSize: 100,
        GroupId: this.dataObj.GroupId,
        IsGroup: false,
      };
      if (app.globalData.orgId) {
        data.OrgId = app.globalData.orgId;
      }
      const res = await queryRxTempBackRemind(data);
      if (res.Type !== 200) {
        uni.showModal({
          title: '温馨提示',
          content: res.Content,
          showCancel: false,
          success: () => {
            uni.reLaunch({
              url: '/pages/index/index',
            });
          },
        });
        return;
      }
      const list = res.Data.Rows;
      if (!list.length) {
        uni.showModal({
          title: '温馨提示',
          content: '二维码已失效',
          showCancel: false,
          success: () => {
            uni.reLaunch({
              url: '/pages/index/index',
            });
          },
        });
        return;
      }
      console.log('list', list);
      this.$set(
        this.dataObj,
        'HaveDischarged',
        list.some((s) => s.Type === 1)
      );
      if (this.dataObj.HaveDischarged) {
        this.returningHospitalParams.showOutTime = this.$dateFormat(
          new Date(),
          'YYYY-MM-DD',
          false
        );
        this.returningHospitalParams.outTime = Date.now();
      }
      this.$set(
        this.dataObj,
        'Postoperative',
        list.some((s) => s.Type === 2)
      );
    },
    handleUpdateSelectDoctor(item) {
      this.returningHospitalParams.DctId = item.Id;
      this.returningHospitalParams.doctorName = item.Name;
      // 获取医生的默认科室Id
      const defaultDeptId = item.Departments.filter((s) => s.IsMain)[0].Id;
      this.returningHospitalParams.DeptId = defaultDeptId;
    },
    onGetDisData() {
      const data = {
        UserId: app.globalData.userInfo.Id,
        Type: 'disease',
      };
      GetTags(data).then((res) => {
        if (res.code === 200) {
          this.Entry = res.data;
          this.Entry.forEach((e) => {
            this.isInEntryList.push(false);
          });
        }
      });
    },
    onGetSelectDay(e, type) {
      if (type === 'outTime') {
        this.returningHospitalParams.showOutTime = this.$dateFormat(
          e.value,
          'YYYY-MM-DD',
          false
        );
      } else if (type === 'operationTime') {
        this.returningHospitalParams.showOperationTime = this.$dateFormat(
          e.value,
          'YYYY-MM-DD',
          false
        );
      }
      this.showOutTimePicker = false;
      this.showOperationTimePicker = false;
    },
    formatter(type, value) {
      if (type === 'year') {
        return `${value}年`;
      }
      if (type === 'month') {
        return `${value}月`;
      }
      if (type === 'day') {
        return `${value}日`;
      }
      return value;
    },
    onCellClick(type) {
      switch (type) {
        case 'outTime':
          this.showOutTimePicker = true;
          break;
        case 'operationTime':
          this.showOperationTimePicker = true;
          break;
        case 'doctor':
          uni.navigateTo({
            url: `/subPackIndex/selectDoctor?roleType=doctor&orgId=${this.dataObj.OrgId}`,
          });
          break;
        default:
          break;
      }
    },
    IdCardblur(e) {
      if (e && !idCardTrue(e)) {
        this.$refs.uToast.show({
          message: '身份证格式输入错误',
          type: 'error',
        });
        return;
      } else if (e && idCardTrue(e)) {
        this.smQuery.Sex = idCardGetSex(e);
        this.smQuery.Birthday = idCardGetBri(e);
        this.smQuery.Age = briGetAge(this.smQuery.Birthday);
      }
    },
    async onAddSelfBuiltMedicalRecord() {
      const uid = guid();
      let VisitReportDetails = [];
      this.fileList5.forEach((e) => {
        VisitReportDetails.push({
          VisitReportId: uid,
          ReportUrl: e.url,
        });
      });
      let arr = this.Entry.filter((e) => e.checked);
      let VisitTags = [];
      let Codes = '';
      let Names = '';
      arr.forEach((k, index) => {
        VisitTags.push({
          UserId: app.globalData.userInfo.Id,
          OrganizationId: this.dataObj.OrganizationId,
          DoctorId: '',
          Source: 0,
          Class: '疾病标签',
          Tag: k.Key,
          From: 0,
        });
        Codes += k.Key + (index === arr.length - 1 ? '' : ',');
        Names += k.Key + (index === arr.length - 1 ? '' : ',');
      });
      if (this.query.Describing) {
        const DescribingTage = this.query.Describing.split(/[,， ]+/);
        DescribingTage.forEach((v) => {
          VisitTags.push({
            UserId: app.globalData.userInfo.Id,
            OrganizationId: this.dataObj.OrganizationId,
            DoctorId: '',
            Source: 0,
            Class: '疾病标签',
            Tag: v,
            From: 1,
          });
        });
        Names = Names
          ? Names + ',' + this.query.Describing
          : this.query.Describing;
      }
      let data = {
        Vist: {
          Source: '0',
          PatientId: app.globalData.userInfo.Id,
          Name: this.smQuery.Name,
          Age: this.smQuery.Age,
          Sex: this.smQuery.Sex,
          DepartmentName: this.dataObj.DepartmentName,
          DoctorName: this.dataObj.DoctorName,
          InDate: this.$dateFormat(new Date(), 'YYYY-MM-DD'),
          OrganizationName: this.dataObj.OrganizationName,
          IsSelfBuild: true,
        },
        CheckExists: true,
        VisitReports: [
          {
            Id: uid,
          },
        ],
        VisitDiagnoses: [
          {
            DiagnoseTypeName: '诊断',
            DiagnoseName: Names,
            IsMain: true,
          },
          {
            DiagnoseTypeName: '症状描述',
            DiagnoseName: Names,
          },
        ],
        VisitReportDetails,
      };
      // 1、添加自建病历 // 2、保存疾病标签 // 3、完善问诊信息
      const reqList = [
        setUserCase(data),
        SaveTags(VisitTags),
        this.onImproveConsultationInformation(VisitTags),
      ];
      // 4、把上传的图片保存到问诊的疾病图片中
      if (this.fileList5.length > 0) {
        reqList.push(this.onUpdateConsultReport());
      }
      const res = await Promise.all(reqList);
    },
    onImproveConsultationInformation(VisitTags) {
      const data = {
        ConsultId: this.dataObj.Id,
        Describing: VisitTags.map((s) => s.Tag).join(','),
      };
      updateConsultRecord(data);
    },
    onUpdateConsultReport() {
      let Urls = [];
      this.fileList5.forEach((e) => {
        Urls.push(e.url);
      });
      updateConsultReport({
        ConsultId: this.dataObj.Id,
        Urls,
      });
    },
    async onCertification() {
      this.$log.info(
        `${this.$envVersion || ''}:onCertification开始`,
        this.smQuery
      );
      try {
        await getApp().saveAuthen(this.smQuery);
        this.$log.info(
          `${this.$envVersion || ''}:onCertification成功`,
          this.smQuery
        );
      } catch (e) {
        this.$log.info(`${this.$envVersion || ''}:onCertification失败`, e);
      }
    },
    async onSave() {
      if (this.userInfo.WorkflowStatus !== 2) {
        const flag = this.getPatUserInfo();
        if (!flag) {
          return;
        }
        // 修改用户信息
        await this.onCertification();
      }
      uni.showLoading({
        title: '保存中...',
        mask: true,
      });
      switch (this.scene) {
        case 'planUpdateUser':
          if (!this.okNextBer) {
            uni.showToast({
              title: '请等待图片上传完成',
              icon: 'none',
            });
            return;
          }
          this.onSaveByPlanUpdateUser();
          break;
        case 'returningHospital':
          this.onSaveByReturningHospital();
          break;
        default:
          break;
      }
    },
    onSaveByReturningHospital() {
      // 判断是否填写完整
      if (
        this.dataObj.HaveDischarged &&
        !this.returningHospitalParams.outTime
      ) {
        uni.showToast({
          title: '请选择出院时间',
          icon: 'none',
        });
        return;
      }
      if (
        this.dataObj.Postoperative &&
        !this.returningHospitalParams.operationTime
      ) {
        uni.showToast({
          title: '请选择手术时间',
          icon: 'none',
        });
        return;
      }
      if (!this.returningHospitalParams.DctId) {
        uni.showToast({
          title: '请选择就诊医生',
          icon: 'none',
        });
        return;
      }
      const sendParams = {
        GroupId: this.dataObj.GroupId, //列表上的groupId
        UserId: app.globalData.userInfo.Id,
        LeaveHospitalDate: this.returningHospitalParams.showOutTime, //出院时间，根据模板明细内，有没有出院提醒
        PostoperativeDate: this.returningHospitalParams.showOperationTime, //手术时间，根据模板明细内，有没有术后提醒
        DctId: this.returningHospitalParams.DctId,
        DeptId: this.returningHospitalParams.DeptId,
        OrgId: this.dataObj.OrgId,
      };
      backRemindQrCode(sendParams)
        .then((res) => {
          if (res.Type === 200) {
            this.$nextTick(() => {
              uni.showToast({
                title: '保存成功',
                icon: 'none',
              });
              // 向这个医生报道并且不发送消息提示
              this.onReportDoctor();
              setTimeout(() => {
                uni.reLaunch({
                  url: '/pages/index/index',
                });
                uni.hideLoading();
              }, 1000);
            });
          } else {
            uni.hideLoading();
            uni.showModal({
              title: '温馨提示',
              content: res.Content,
              showCancel: false,
            });
          }
        })
        .catch((err) => {
          uni.hideLoading();
          uni.showModal({
            title: '温馨提示',
            content: err,
            showCancel: false,
          });
        });
    },
    onReportDoctor() {
      let sendInfo = {
        DiseaseLabel: '',
        UserId: app.globalData.userInfo.Id,
        UserName: app.globalData.userInfo.Name,
        InviterType: 8,
      };
      let params = [
        {
          UserId: app.globalData.userInfo.Id, //患者UserId
          ClaimType: 'VisitedRegister',
          ClaimValue: this.returningHospitalParams.DctId, //医生UserId
          AdditionInfo: {
            Stateful: true,
            NotifyClientAlways: true,
            Info: JSON.stringify(sendInfo),
          },
        },
        {
          UserId: app.globalData.userInfo.Id,
          ClaimType: 'Follow',
          ClaimValue: this.returningHospitalParams.DctId,
        },
      ];
      SetUserClaims(params);
    },
    async onSaveByPlanUpdateUser() {
      let arr = this.Entry.filter((e) => e.checked).length > 0;
      let describing = !!this.query.Describing;
      let pic = this.fileList5.length > 0;
      if (arr || describing || pic) {
        // 1、添加自建病历 2、保存疾病标签 3、完善问诊信息 4、修改问诊图片资料
        await this.onAddSelfBuiltMedicalRecord();
      }
      uni.hideLoading();
      this.$nextTick(() => {
        uni.showToast({
          title: '保存成功',
          icon: 'none',
        });
        setTimeout(() => {
          uni.navigateBack();
        }, 1000);
      });
    },
    getPatUserInfo() {
      if (!this.smQuery.Name) {
        this.$refs.uToast.show({
          message: '请输入您的名字',
          type: 'error',
        });
        return false;
      }
      if (!this.smQuery.Sex) {
        this.$refs.uToast.show({
          message: '请选择您的性别',
          type: 'error',
        });
        return false;
      }
      if (!this.smQuery.Age) {
        this.$refs.uToast.show({
          message: '请输入您的年龄',
          type: 'error',
        });
        return false;
      }
      if (
        this.smQuery.UserCertificates.length > 0 &&
        this.smQuery.UserCertificates[0].CertificateValue &&
        !idCardTrue(this.smQuery.UserCertificates[0].CertificateValue)
      ) {
        this.$refs.uToast.show({
          message: '身份证格式输入错误',
          type: 'error',
        });
        return false;
      }
      return true;
    },
    // 删除图片
    deletePic(event) {
      this[`fileList${event.name}`].splice(event.index, 1);
    },
    // 新增图片
    async afterRead(event) {
      // 当设置 mutiple 为 true 时, file 为数组格式，否则为对象格式
      let lists = [].concat(event.file);
      console.log('lists', lists, event);
      let fileListLen = this[`fileList${event.name}`].length;
      lists.map((item) => {
        this[`fileList${event.name}`].push({
          ...item,
          status: 'uploading',
          message: '上传中',
        });
        this.okNextBer = false;
      });
      for (let i = 0; i < lists.length; i++) {
        const result = await uploadFileToServer(lists[i].url);
        if (result) {
          if (i === lists.length - 1) {
            this.okNextBer = true;
          }
        }
        let item = this[`fileList${event.name}`][fileListLen];
        this[`fileList${event.name}`].splice(
          fileListLen,
          1,
          Object.assign(item, {
            status: 'success',
            message: '',
            url: result.data.Data,
          })
        );
        fileListLen++;
      }
    },
    describingChange(key) {
      // 以空格或者逗号分割
      const lastKeyWord = key.split(/[,， ]+/)[key.split(/[,， ]+/).length - 1];
      console.log('lastKeyWord', lastKeyWord);
      if (lastKeyWord !== '' && lastKeyWord !== ',' && lastKeyWord !== '，') {
        const isInEntryList = this.Entry.filter((v) =>
          v.Key.includes(lastKeyWord)
        );
        this.isInEntryList = isInEntryList;
        if (isInEntryList.length > 0) {
          console.log('找到le');
        } else {
          console.log('没有找到');
        }
      } else {
        this.isInEntryList = [];
      }
    },
    checkboxClick(Id, flag, cb) {
      const index = this.Entry.findIndex((v) => v.Id === Id);
      console.log(index);
      if (index > -1) {
        this.Entry[index].checked = flag ? true : !this.Entry[index].checked;
        cb && cb();
      }
    },
    cellClick(e) {
      console.log('选中', e);
      const index = this.Entry.findIndex((v) => v.Id === e.Id);
      if (index > -1) {
        this.showCount++;
        this.checkboxClick(e.Id, true, () => {
          const index1 = this.Entry.findIndex((v) => v.Id === e.Id);
          this.$nextTick(() => {
            this.Entry.splice(
              this.showCount - 1,
              0,
              this.Entry.splice(index1, 1)[0]
            );
          });
        });
      }
      this.isInEntryList = [];
      this.query.Describing = this.query.Describing.split(/[,， ]+/)
        .slice(0, this.query.Describing.split(/[,， ]+/).length - 1)
        .join(' ');
    },
    // 对疾病标签进行数据处理
    handleDisData(platData, userData) {
      platData.forEach((e) => {
        e.checked = false;
      });
      this.Entry = platData;
      const userSet = [];
      userData.forEach((e) => {
        const isHave = this.Entry.some((v) => v.Key === e.Tag);
        if (isHave) {
          const index = this.Entry.findIndex((o) => o.Key === e.Tag);
          this.Entry[index].checked = true;
          this.Entry.sort((obj1, obj2) => {
            if (obj1.checked && !obj2.checked) {
              return -1;
            } else if (!obj1.checked && obj2.checked) {
              return 1;
            } else {
              return 0;
            }
          });
        } else {
          userSet.push(e.Tag);
        }
      });
      let uniqueArr = [...new Set(userSet)];
      this.query.Describing = uniqueArr.join(' ');
    },
    async onGetDiseaseCode() {
      const req = {
        PageCondition: {
          PageIndex: 1,
          PageSize: 99,
          SortConditions: [
            {
              SortField: 'Id',
              ListSortDirection: 1,
            },
          ],
        },
        FilterGroup: {
          Rules: [],
          Groups: [
            {
              Rules: [
                {
                  Field: 'Code',
                  Value: 'DiseaseTag',
                  Operate: 11,
                },
              ],
              Operate: 2,
            },
          ],
          Operate: 1,
        },
      };
      let res = await GetDiseaseList(req);
      if (res.Type !== 200) return res;
      const data = {
        OrganizationId: this.dataObj.OrganizationId,
        DepartmentId: this.dataObj.DepartmentId,
        Codes: [res.Data.Rows[0].Code],
        LoadDefaultOrganization: false, //=true  会匹配 OrganizationId=null的 项
        LoadDefaultDepartment: true, //=true  会匹配 DepartmentId=null的 项
        PageIndex: 1,
        PageSize: 99999,
      };
      return newReadDict(data);
    },
    async GetTagsInfo() {
      const data = {
        UserIds: [app.globalData.userInfo.Id],
        OrganizationId: this.dataObj.OrganizationId,
        Source: 0,
      };
      return GetTags(data);
    },
    async onGetDisData() {
      const res = await Promise.all([
        this.onGetDiseaseCode(),
        this.GetTagsInfo(),
      ]);
      const errorList = res.filter((s) => !s.Type || s.Type !== 200);
      if (errorList.length > 0) {
        uni.showToast({
          title: errorList[0].Content,
          icon: 'none',
        });
        return;
      }
      // 对疾病标签进行数据处理
      this.handleDisData(res[0].Data, res[1].Data);
    },
    Ageblur(e) {
      if (Number(e) > 150 || Number(e) < 0) {
        this.smQuery.Age = null;
        uni.showToast({
          title: '年龄范围为0-150岁',
          icon: 'none',
        });
        return;
      }
    },
    ocrSuccess(e) {
      this.$log.info('使用了身份证识别，并识别成功');
      if (e.type === 'onSuccess') {
        const data = e.detail;
        this.smQuery.UserCertificates[0].CertificateValue = data.id.text;
        this.smQuery.Sex = data.gender.text;
        this.smQuery.Name = data.name.text;
        this.smQuery.Age = briGetAge(data.birth.text);
      }
    },
    radioClick(name) {
      this.sexList.map((item, index) => {
        item.checked = index === name ? true : false;
      });
      this.smQuery.Sex = this.sexList[name].name;
    },
  },
};
</script>

<style scoped lang="scss">
.u-page__tag-item {
  margin-right: 20rpx;
  margin-bottom: 10rpx;
}

.u-demo-block__content {
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  display: flex;
  position: relative;
  margin-top: 32rpx;

  &-select {
    position: absolute;
    bottom: -20px;
    left: 0;
    z-index: 99;
    box-shadow: 2.5px 3px 9px 0px rgba(0, 0, 0, 0.08);
    max-height: 200rpx;
    overflow-y: auto;
    width: 100%;
  }
}

.container {
  height: 100vh;
  background-color: #f7f7f7;
  // padding-top: 208rpx;
  padding: 0 32rpx;
  padding-top: 20rpx;
  position: relative;

  &-diseases {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    background: #ffffff;
    box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);
    border-radius: 16rpx;
    padding: 32rpx;
    margin-top: 32rpx;

    &-text {
      color: #999999;
    }

    &-title {
      font-weight: 600;
      font-size: 28rpx;
      color: #333333;
    }
  }

  &-diseases1 {
    width: 100%;
    background: #ffffff;
    box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);
    border-radius: 16rpx;
    padding: 32rpx;
    margin-top: 32rpx;
  }

  &-topTip {
    margin-bottom: 10rpx;
    background: rgba(255, 195, 0, 0.2);
    padding: 10rpx;
    align-items: center;
    justify-content: center;
  }

  &-bomBtn {
    z-index: 99 !important;
  }

  // /deep/ .u-cell__body {
  // 	justify-content: space-between !important;
  // }

  /deep/ .u-cell {
    background-color: #fff !important;
  }

  /deep/ .u-line {
    border: none !important;
  }

  // /deep/ .u-cell__right-icon-wrap {
  // 	text-align: right;
  // }

  /deep/ .u-radio-group {
    flex: 0 !important;
  }

  /deep/ .u-list {
    height: auto !important;
  }

  /deep/ .u-radio {
    margin-right: 0 !important;
  }

  /deep/ .u-radio-group {
    background: '#f7f7f7' !important;
    padding: 20rpx !important;
  }
}
</style>
