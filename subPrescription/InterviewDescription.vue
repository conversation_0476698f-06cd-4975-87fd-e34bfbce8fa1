<template>
  <view class="container">
    <u-navbar title="问诊描述" @rightClick="rightClick" @leftClick="leftClick">
    </u-navbar>
    <!-- 问诊秒速 -->
    <u-cell :border="false" customStyle="borderRadius:16rpx">
      <u-avatar :src="userInfo.HeadImg" slot="icon"></u-avatar>
      <span slot="title"
        ><span style="font-size: 32rpx; margin-left: 24rpx">{{
          userInfo.Name
        }}</span>
        <span style="margin-left: 16rpx; color: #333333; font-size: 24rpx"
          >{{ age }}岁</span
        >
        <span style="margin-left: 16rpx; font-size: 24rpx; color: #333333">{{
          userInfo.Sex
        }}</span></span
      >
      <span slot="value" @click="importInfo">导入病历</span>
    </u-cell>

    <u-cell-group>
      <u-cell
        :border="false"
        customStyle="borderRadius:16rpx 16rpx 0 0;marginTop:32rpx"
      >
        <view slot="title">
          <p>是否就该病情到医院就诊过<span style="color: red">*</span></p>
        </view>
        <view
          class="display-style"
          slot="value"
          style="transform: translateX(10px)"
        >
          <view
            :class="['cell-each-one', isActive == 1 ? 'active' : '']"
            @click="changeActive(1)"
            >是</view
          >
          <view
            :class="['cell-each-one', isActive == 2 ? 'active' : '']"
            @click="changeActive(2)"
            >否</view
          >
        </view>
      </u-cell>
    </u-cell-group>
    <view
      v-show="isActive == 1"
      style="margin-top: 32rpx; background-color: white"
    >
      <u-cell-group>
        <u-cell @click="showpicker = true" :border="false" :isLink="true">
          <view slot="title">
            <p>就诊时间<span style="color: red">*</span></p>
          </view>
          <span slot="value" style="flex: 1; text-align: right">{{
            reqdata.date
          }}</span>
        </u-cell>
      </u-cell-group>

      <u-cell-group>
        <u-cell :border="false">
          <view slot="title">
            <p>就诊医院<span style="color: red">*</span></p>
          </view>
          <span slot="value" style="flex: 1; text-align: right">
            <u--input
              inputAlign="right"
              placeholder="请填写(必填)"
              border="none"
              v-model="reqdata.hospi"
            >
            </u--input>
          </span>
        </u-cell>
      </u-cell-group>

      <!-- <u-cell-group >
				<u-cell :border="false">
					<view slot="title">
						<p>就诊科室<span style="color: red;">*</span></p>
					</view>
					<span slot="value">
						<u--input inputAlign="right" placeholder="请填写(必填)" border="none" v-model="reqdata.dep"></u--input>
					</span>
				</u-cell>
			</u-cell-group> -->

      <p style="margin: 32rpx 0 24rpx 30rpx; font-size: 16px; color: #303133">
        诊断<span style="color: red">*</span>
      </p>
      <u-textarea
        v-model="reqdata.diagnosis"
        :disabled="show || show1 || show3"
        @input="textareaInput2($event)"
        placeholder="请描述您上次就诊时医生给您下达的主要诊断"
        :maxlength="999"
        :height="50"
        confirmType="return"
      >
      </u-textarea>
      <!-- <u-icon name="mic" color="#29B7A3" customStyle="transform: translateY(-30rpx);z-index: 99;"
				size="30px;transform: translateX(40rpx)" :isBlock="true" @touchstart="streamRecord(1)" ></u-icon> -->
      <p style="margin: 32rpx 0 25rpx 30rpx; font-size: 16px; color: #303133">
        症状描述<span style="color: red">*</span>
      </p>
      <view style="position: relative">
        <u-textarea
          v-model="reqdata.dec"
          :disabled="show || show1 || show3"
          @input="textareaInput($event)"
          placeholder=" 请描述您的患病时间、主要症状、治疗过程、目前状况等."
          :maxlength="999"
          :height="160"
          confirmType="return"
        >
        </u-textarea>
        <view
          style="
            position: absolute;
            bottom: 0;
            right: 0;
            width: 30px;
            height: 30px;
            z-index: 99;
          "
        >
          <u-icon
            name="mic"
            color="#29B7A3"
            size="30px;"
            :isBlock="true"
            @touchstart="streamRecord(2)"
          ></u-icon>
        </view>
      </view>

      <p style="margin: 32rpx 0 10rpx 30rpx; font-size: 16px">
        上传图片(最多上传9张)<span style="color: red">*</span>
      </p>
      <span style="margin: 0 0 25rpx 30rpx; font-size: 14px; color: gray"
        >请上传具有明确诊断的病历资料，如门诊病历、住院病历、出院小结、诊断证明等</span
      >

      <u-upload
        :fileList="fileList5"
        @afterRead="afterRead"
        @delete="deletePic"
        name="5"
        multiple
        :maxCount="9"
        width="100"
        height="100"
        customStyle="marginLeft:30rpx"
      ></u-upload>
    </view>

    <view class="bomm-btn-style" style="height: 100px"></view>
    <u-button
      :loading="payMoenyDisabled"
      type="success"
      @click="payMoeny"
      shape="circle"
      text="提交"
      customStyle="width:90%;bottom: 30px;position: fixed; left: 50%;transform: translateX(-50%);fontSize:16px;z-index:99"
    >
    </u-button>
    <u-modal
      :show="show"
      title="温馨提示"
      content="返回后将不保存已填写信息,确定返回吗？"
      @confirm="confirm1"
      @cancel="cancel"
      :showCancelButton="true"
    ></u-modal>
    <u-modal
      :show="show1"
      title="温馨提示"
      content="是否需要导入病历？"
      @confirm="confirm"
      @cancel="cancel"
      :showCancelButton="true"
    ></u-modal>
    <u-modal
      :show="show2"
      title="温馨提示"
      content="根据国家互联网诊疗相关规定,线上只能复诊,您未去线下医院就诊过,无法提供医疗咨询服务"
      @confirm="confirm2"
      @cancel="cancel"
      :showCancelButton="true"
    ></u-modal>
    <u-modal
      :show="show3"
      title="温馨提示"
      @confirm="confirm3"
      @cancel="cancelSplk"
      :showCancelButton="true"
      confirmText="说完了"
      confirmColor="#29B7A3"
    >
      <SpeakCss slot="default" style="padding: 30px" />
    </u-modal>
    <u-datetime-picker
      :show="showpicker"
      mode="date"
      @confirm="dateChoose"
      v-model="pickerValue"
      @cancel="showpicker = false"
      :maxDate="Date.now()"
    ></u-datetime-picker>
    <u-toast ref="uToast"></u-toast>
    <u-popup
      :show="showPrivacyPopup"
      :closeOnClickOverlay="false"
      :round="10"
      mode="bottom"
      @close="closePrivacyPopup()"
    >
      <view style="padding: 40rpx">
        <p
          style="
            text-align: center;
            margin-bottom: 40rpx;
            font-size: 20px;
            font-weight: 700;
          "
        >
          提示
        </p>
        <text>
          在你使用服务之前，请仔细阅读<text
            @click="onClickPrivacyPopupTitle()"
            style="color: #29b7a3"
            >{{ PrivacyPopupTitle }}</text
          >。如果你同意{{ PrivacyPopupTitle }}，请点击“同意”开始使用。
        </text>
        <view class="display-style" style="margin-top: 40rpx">
          <u-button
            text="拒绝"
            @click="handleRefusePrivacyAuthorization()"
          ></u-button>
          <!-- <u-button type="primary" id="agree-btn" text="同意" openType="agreePrivacyAuthorization"
						@agreeprivacyauthorization="handleAgreePrivacyAuthorization()"></u-button> -->
          <button
            id="agree-btn"
            class="btn-agree"
            open-type="agreePrivacyAuthorization"
            @agreeprivacyauthorization="handleAgreePrivacyAuthorization()"
          >
            同意
          </button>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
const app = getApp();
import { getPayOrderInfo, getDocInfoMation } from '@/api/consult.js';
var plugin = requirePlugin('WechatSI');
let manager = plugin.getRecordRecognitionManager();
import { getUserCaseInfo } from '@/api/record.js';
import { dateFormat } from '@/utils/validate.js';
import { setUserCase } from '@/api/record.js';
import { guid } from '@/utils/validate.js';
import SpeakCss from '../components/mp-speakCss/speakCss.vue';
import config from '@/config';
import { uploadFileToServer } from '../services/UploadService';
export default {
  data() {
    return {
      docInfo: {},
      fileList5: [],
      reqdata: {
        date: '', //选择的时间
        hospi: '', //就诊页面
        dep: '', //就诊科室
        dec: '', //症状描述
        diagnosis: '', // 诊断
      },
      showpicker: false,
      isActive: 1,
      userInfo: {},
      show: false,
      age: app.globalData.userInfo.Age,
      show1: false,
      show2: false,
      show3: false,
      okNextBer: false,
      pickerValue: Number(new Date()),
      isGiveText: false,
      chooseDate: '',
      setBLdata: false,
      listenersType: 0,
      payMoenyDisabled: false,
    };
  },
  components: {
    SpeakCss,
  },
  methods: {
    textareaInput(e) {
      if (e.length > 999) {
        this.reqdata.dec = e.slice(0, 999);
      }
    },
    textareaInput2(e) {
      if (e.length > 999) {
        this.reqdata.diagnosis = e.slice(0, 999);
      }
    },
    // 新增图片
    async afterRead(event) {
      // 当设置 mutiple 为 true 时, file 为数组格式，否则为对象格式
      let lists = [].concat(event.file);
      let fileListLen = this[`fileList${event.name}`].length;
      lists.map((item) => {
        this[`fileList${event.name}`].push({
          ...item,
          status: 'uploading',
          message: '上传中',
        });
        this.okNextBer = false;
      });
      for (let i = 0; i < lists.length; i++) {
        const result = await uploadFileToServer(lists[i].url);
        if (result) {
          if (i === lists.length - 1) {
            this.okNextBer = true;
          }
        }
        let item = this[`fileList${event.name}`][fileListLen];
        this[`fileList${event.name}`].splice(
          fileListLen,
          1,
          Object.assign(item, {
            status: 'success',
            message: '',
            url: result.data.Data,
          })
        );
        fileListLen++;
      }
    },
    // 删除图片
    deletePic(event) {
      this[`fileList${event.name}`].splice(event.index, 1);
    },
    // 选择时间
    dateChoose({ value, mode }) {
      this.showpicker = false;
      let newTime = dateFormat(value, 'YYYY-MM-DD', false);
      this.reqdata.date = newTime;
      this.chooseDate = dateFormat(value, 'YYYY-MM-DD HH:mm:ss', false);
    },
    //选择是还是否
    changeActive(num) {
      this.isActive = num;
    },
    // 获取患者信息
    async getPatInfo() {
      let data = {
        userId: app.globalData.userInfo.Id,
        dateSerachType: 0,
        type: 0,
        pageindex: 1,
        pagesize: 2,
        IsSelfBuild: true,
      };
      let res = await getUserCaseInfo(data);
      if (res.Type == 200 && res.Data.length > 0) {
        this.show1 = true;
      }
    },
    async payMoeny() {
      if (this.isActive == 2) {
        this.show2 = true;
        return;
      }
      let okNext = true;
      if (!this.reqdata.date) {
        okNext = false;
        this.$refs.uToast.show({
          message: '请选择就诊时间',
          type: 'error',
        });
        return;
      }
      if (!this.reqdata.hospi) {
        okNext = false;
        this.$refs.uToast.show({
          message: '请填写就诊医院',
          type: 'error',
        });
        return;
      }
      if (!this.reqdata.diagnosis) {
        okNext = false;
        this.$refs.uToast.show({
          message: '请填写诊断',
          type: 'error',
        });
        return;
      }
      if (!this.reqdata.dec) {
        okNext = false;
        this.$refs.uToast.show({
          message: '请填写症状描述',
          type: 'error',
        });
        return;
      }
      if (!this.fileList5.length) {
        this.$refs.uToast.show({
          message: '请上传图片',
          type: 'error',
        });
        return;
      }
      if (this.fileList5.length > 0 && !this.okNextBer) {
        this.$refs.uToast.show({
          message: '请等待图片上传完成再点击',
          type: 'error',
        });
        return;
      }
      let ConsultReportInputs = [];
      this.fileList5.forEach((e) => {
        ConsultReportInputs.push({
          Url: e.url,
        });
      });
      let query = {
        UserId: app.globalData.userInfo.Id,
        UserName: app.globalData.userInfo.Name,
        Sex: app.globalData.userInfo.Sex,
        DocUserId: this.docInfo.Doctor.UserId,
        Organization: this.docInfo.Doctor.OrganizationId, // app.globalData.orgId ||
        OrganizationName: this.docInfo.Doctor.OrganizationName, // app.globalData.orgName ||
        DepartmentId: this.docInfo.Doctor.DepartmentId,
        DepartmentName: this.docInfo.Doctor.DepartmentName,
        CostState: 1,
        Describing: this.reqdata.dec,
        IsHospital: true,
        OfflineDate: this.chooseDate,
        CreateDate: null,
        HospitName: this.reqdata.hospi,
        HospitDepartmentName: this.reqdata.dep,
        ConsultReportInputs: ConsultReportInputs,
        PayCompany: 'yankang',
        PayAlias:
          config.clientId ||
          'com.kangfx.wx.mp.patient' + `-${config.resources}`,
        OfflineDiagnosis: this.reqdata.diagnosis,
        AutoCreateMedical: true,
        Source: 1,
        CreatorId: app.globalData.userInfo.Id,
        ConsultWay: 1, // 1 问诊  2 咨询
      };
      this.payMoenyDisabled = true;
      let res = await getPayOrderInfo(query);
      if (res.Type !== 200) {
        this.payMoenyDisabled = false;
        this.$log.error(
          `${this.$envVersion || ''}:患者${app.globalData.userInfo.Name}发起问诊了，问诊的医生是:${this.docInfo.Doctor.Name}调用LaunchConsult接口报错了`
        );
        return;
      }
      console.log('生成订单成功res', res);

      const orgId = this.docInfo.Doctor.OrganizationId,
        orgName = this.docInfo.Doctor.OrganizationName;
      getApp().changeOrgAndMark({
        orgId,
        orgName,
      });

      const msgTemplatesList = app.globalData.msgTemplates;
      const LaunchConsultNew = msgTemplatesList.LaunchConsult;
      let newArrTemplatesList = [];
      if (LaunchConsultNew instanceof Array && LaunchConsultNew.length > 0) {
        newArrTemplatesList = this.ItemGroupBy(
          LaunchConsultNew,
          'SubscribeType'
        );
        console.log('newArr', newArrTemplatesList);
      }
      const itemTemplates = newArrTemplatesList.find(
        (e) => e.type === 'LongTerm'
      );
      const tmplIds = [];
      if (
        itemTemplates &&
        itemTemplates.data &&
        itemTemplates.data.length > 0
      ) {
        itemTemplates.data.forEach((e) => {
          tmplIds.push(e.TemplateId);
        });
      }
      uni.requestSubscribeMessage({
        tmplIds,
        success: (res) => {
          console.log('长期订阅消息', res);
        },
        complete: () => {
          if (res.Type !== 200) {
            uni.showToast({
              title: res.Content,
              icon: 'none',
            });
            this.payMoenyDisabled = false;
            return;
          }
          if (res.Data.Amount > 0) {
            uni.navigateTo({
              url: `./cashier?orderId=${res.Data.OrderNo}&docName=${this.docInfo.Doctor.Name}&needMoeny=${res.Data.Amount}&consultId=${res.Data.ConsultId}&PaymentId=${res.Data.Payment.Id}`,
              complete: () => {
                this.payMoenyDisabled = false;
              },
            });
          } else if (res.Data.Amount == 0) {
            let consultId = res.Data.ConsultId;
            let backPath = '/pages/interview/index';

            uni.navigateTo({
              url:
                '/subPackChat/sessionChatPage?consultId=' +
                consultId +
                '&backPath=' +
                backPath +
                '&fromAddInquiry=true',
              complete: () => {
                this.payMoenyDisabled = false;
              },
            });
          }
          this.$log.info(
            `${this.$envVersion || ''}:患者${app.globalData.userInfo.Name}发起问诊了，问诊的医生是:${this.docInfo.Doctor.Name}`
          );
        },
      });
    },
    // js分组
    ItemGroupBy(arr, key) {
      let newArr = [],
        types = {},
        newItem,
        i,
        j,
        cur;
      for (i = 0, j = arr.length; i < j; i++) {
        cur = arr[i];
        if (!(cur[key] in types)) {
          types[cur[key]] = {
            type: cur[key],
            data: [],
          };
          newArr.push(types[cur[key]]);
        }
        types[cur[key]].data.push(cur);
      }
      return newArr;
    },
    async setCase() {
      let id = guid();
      let arr = [];
      this.fileList5.forEach((e) => {
        arr.push({
          VisitReportId: id,
          ReportUrl: e.url,
        });
      });
      let data = {
        Vist: {
          Source: '0',
          PatientId: app.globalData.userInfo.Id,
          Name: app.globalData.userInfo.Name,
          Age: app.globalData.userInfo.Age,
          Sex: app.globalData.userInfo.Sex,
          DepartmentName: this.reqdata.dep,
          InDate: this.chooseDate,
          OrganizationName: this.reqdata.hospi,
          IsSelfBuild: true,
        },
        VisitDiagnoses: [
          {
            DiagnoseTypeName: '症状描述',
            DiagnoseName: this.reqdata.dec,
          },
        ],
        VisitReports: [
          {
            Id: id,
          },
        ],
        VisitReportDetails: arr,
      };
      await setUserCase(data);
    },
    // 右边按钮监听
    rightClick() {},
    // 左边按钮监听
    leftClick() {
      this.show = true;
    },
    confirm1() {
      this.show = false;
      uni.navigateBack();
    },
    confirm() {
      this.show1 = false;
      uni.navigateTo({
        url: './medicalRecord',
      });
    },
    confirm2() {
      this.show2 = false;
    },
    cancel() {
      this.show1 = false;
      this.show = false;
      this.show2 = false;
    },
    // 导入病历
    importInfo() {
      uni.navigateTo({
        url: './medicalRecord',
      });
    },
    checkIsActive() {
      this.isActive = 1;
    },
    // 病历页面传过来的数据 需要渲染到页面上
    getNextPageInfo(item) {
      console.log('病历页面的数据', item);
      this.reqdata.date = dateFormat(item.InDate, 'YYYY-MM-DD');
      this.chooseDate = dateFormat(item.InDate, 'YYYY-MM-DD HH:mm:ss');
      this.reqdata.hospi = item.OrganizationName || '';
      this.reqdata.dep = item.DepartmentName || '';
      this.reqdata.dec = item.diagnoseObj2?.DiagnoseName || '';
      this.reqdata.diagnosis = item.diagnoseObj?.DiagnoseName || '';
      if (item.VisitReportDetails.length > 0) {
        this.okNextBer = true;
        this.fileList5 = [];
        item.VisitReportDetails.forEach((e) => {
          this.fileList5.push({
            url: e.ReportUrl,
          });
        });
      }
    },
    initRecord() {
      //有新的识别内容返回，则会调用此事件
      manager.onRecognize = (res) => {
        let text = res.result;
        if (this.isGiveText && this.listenersType == 2) {
          this.reqdata.dec = this.reqdata.dec + text;
        }
        if (this.isGiveText && this.listenersType == 1) {
          this.reqdata.diagnosis = this.reqdata.diagnosis + text;
        }
      };
      // 识别结束事件
      manager.onStop = (res) => {
        let text = res.result;
        if (text == '') {
          console.log('没有说话');
          return;
        }
        if (this.isGiveText && this.listenersType == 2) {
          this.reqdata.dec = this.reqdata.dec + text;
        }
        if (this.isGiveText && this.listenersType == 1) {
          this.reqdata.diagnosis = this.reqdata.diagnosis + text;
        }
      };
    },
    streamRecord(type) {
      this.listenersType = type;
      uni.getSetting({
        success: (res) => {
          console.log('res.authSetting', res.authSetting['scope.record']);
          if (res.authSetting['scope.record'] == false) {
            // uni.showToast({
            // 	title: '请先开启麦克风授权',
            // 	icon: 'none'
            // })
            // return false
            uni.authorize({
              scope: 'scope.record',
              success() {
                //1.1 允许授权
                // recorderManager.start();
                this.show3 = true;
                manager.start({
                  lang: 'zh_CN',
                });
              },
              fail() {
                //1.2 拒绝授权
                uni.showModal({
                  content: '检测到您没打开录音功能权限，是否去设置打开？',
                  confirmText: '确认',
                  cancelText: '取消',
                  success: (res) => {
                    if (res.confirm) {
                      uni.openSetting({
                        success: (res) => {
                          // console.log(res.authSetting);
                          if (res.authSetting == true) {
                            // recorderManager.start();
                            this.show3 = true;
                            manager.start({
                              lang: 'zh_CN',
                            });
                          } else {
                            // console.log("什么也不做");
                            return;
                          }
                        },
                      });
                    } else {
                      // console.log('取消');
                      return false;
                    }
                  },
                });
              },
            });
          } else {
            this.show3 = true;
            manager.start({
              lang: 'zh_CN',
            });
          }
        },
      });
      // this.show3 = true;
      console.log('=======开始====');
      // manager.start({
      //   lang: "zh_CN",
      // });
    },
    endStreamRecord() {
      console.log('=======结束====');
      manager.stop();
    },
    confirm3() {
      this.endStreamRecord();
      this.show3 = false;
      this.isGiveText = true;
    },
    cancelSplk() {
      this.isGiveText = false;
      this.show3 = false;
      manager.stop();
    },
    /**
     * 获取通过医生Id获取医生信息
     */
    async onGetDoctorInfoById(doctorId) {
      let res = await getDocInfoMation({
        doctorId,
      });
      if (res.Type === 200) {
        this.docInfo = res.Data;
      }
    },
  },
  onLoad(option) {
    this.saveWXCallBack();
    this.initRecord();
    if (option.show1 === undefined) {
      this.getPatInfo();
    } else {
      this.show1 =
        option.show1 == 'true' || option.show1 == true ? true : false;
    }
    // this.docInfo = (JSON.parse(decodeURIComponent(option.docInfo)));
    // 获取医生信息
    this.onGetDoctorInfoById(option.doctorId);
    this.userInfo = app.globalData.userInfo;
  },
};
</script>

<style scoped lang="scss">
.container {
  height: 100vh;
  background-color: #f7f7f7;
  // padding-top: 208rpx;
  padding: 0 32rpx;
  padding-top: 208rpx;

  /deep/ .hideHhowPlace {
    color: red;
  }

  /deep/ .u-cell {
    background-color: #fff !important;
  }

  /deep/ .u-upload__button {
    background-color: #fafafa !important;
  }

  /deep/ .u-upload {
    border-radius: 0 0 16rpx 16rpx !important;
  }

  /deep/ .u-cell__title {
    font-size: 16px;
  }

  /deep/ .u-line {
    border: none !important;
  }

  /deep/ .u-border {
    border-style: none !important;
    border-color: #fafafa !important;
  }

  /deep/ .u-textarea__field {
    background-color: #fafafa !important;
    padding: 10px;
  }

  .cell-each-one {
    border-radius: 30upx;
    height: 30px;
    line-height: 30px;
    width: 50px;
    text-align: center;
    font-size: 14px;
    margin-right: 10upx;
    color: #979797;
    border: 1px solid #979797;
  }

  .active {
    background-color: #29b7a3;
    color: white;
    border: none;
  }

  /deep/ .u-cell__body__content {
    flex: none !important;
  }

  /deep/ ._span {
    flex: 1 !important;
    text-align: right;
  }
}
</style>
