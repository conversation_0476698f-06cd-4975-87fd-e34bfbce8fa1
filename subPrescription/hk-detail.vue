<template>
  <view class="container">
    <MpLoadingPage :loadingType="loadingType">
      <view class="container-top">
        <text class="container-top-title">{{ dataObj.PrescriptionName }}</text>
        <view class="container-top-box">
          <text class="container-top-box-left">套餐价格</text>
          <text class="container-top-box-right container-top-box-money"
            >¥ {{ dataObj.TotalAmount }}</text
          >
        </view>
        <u-divider />
        <view class="container-top-box">
          <text class="container-top-box-left">适应症</text>
          <text class="container-top-box-right">{{
            onGetDiagnosContent()
          }}</text>
        </view>
        <u-divider />
        <view class="container-top-box">
          <text class="container-top-box-left">服务周期</text>
          <text class="container-top-box-right">{{ dataObj.ExecutDay }}天</text>
        </view>
        <u-divider />
        <view class="container-top-box">
          <text class="container-top-box-left">套餐内容</text>
          <text class="container-top-box-right">{{
            onGetPackageContent()
          }}</text>
        </view>
        <u-divider />
      </view>
      <block v-if="Object.keys(doctorInfo).length">
        <view class="container-server">
          <image
            class="container-server-img"
            src="./static/left.png"
            mode=""
          ></image>
          <text class="container-server-introduce">指导医生</text>
          <image
            class="container-server-img"
            src="./static/right.png"
            mode=""
          ></image>
        </view>
        <view class="container-server-doctor" @click="handleToDoctorDetail">
          <image
            class="container-server-doctor-img"
            :src="doctorInfo.HeadImg"
            mode=""
          />
          <view class="container-server-doctor-info">
            <view class="container-server-doctor-info-name">
              {{ doctorInfo.Name }}
            </view>
            <view
              style="
                height: 160rpx;
                box-sizing: border-box;
                padding: 20rpx 26rpx;
                margin-top: 20rpx;
                background: #f7f8fa;
                border-radius: 16rpx 16rpx 16rpx 16rpx;
              "
            >
              <view class="container-server-doctor-info-skilled">
                {{ doctorInfo.Skilled }}
              </view>
            </view>
          </view>
        </view>
      </block>
      <block v-if="imgList.length">
        <view class="container-server">
          <image
            class="container-server-img"
            src="./static/left.png"
            mode=""
          ></image>
          <text class="container-server-introduce">服务介绍</text>
          <image
            class="container-server-img"
            src="./static/right.png"
            mode=""
          ></image>
        </view>
        <image
          v-for="(o, index) in imgList"
          :key="index"
          :src="o"
          style="width: 100%; margin-top: 20rpx; z-index: -1"
          mode="widthFix"
        >
        </image>
      </block>
      <u-gap height="80" bgColor="#f6f6f6"></u-gap>
      <u-button
        :loading="payBtnLoading"
        @click="handlePayBtnClick"
        type="success"
        shape="circle"
        text="购买"
        customStyle="width:90%;bottom: 30px;position: fixed; left: 50%;transform: translateX(-50%);background: linear-gradient(270deg, #FF5F5C 0%, #FE8368 100%);color:#fff;border: none"
      >
      </u-button>
    </MpLoadingPage>
    <u-popup :show="showPopup" mode="bottom" @close="showPopup = false">
      <view class="popup">
        <view class="popup-top">
          <text class="popup-top-title">设备使用保证金</text>
          <text class="popup-top-label">(可退)</text>
        </view>
        <view class="popup-mid">
          本次治疗将使用到以下设备，需要缴纳设备保证金，保证金会在设备返还后退回付款账户
        </view>

        <view v-if="rentDataInfos.length > 0">
          <view
            class="display-style"
            v-for="(item, index2) in rentDataInfos"
            :key="item.RentDataName"
          >
            <text>{{ item.RentDataName }}</text>
            <text>￥{{ item.RentDataAmount }}</text>
          </view>
        </view>

        <view
          class="popup-agreements display-style1"
          v-if="rentAgreements.length > 0"
        >
          <u-checkbox-group v-model="agreementsChecked" placement="row">
            <u-checkbox shape="circle" />
          </u-checkbox-group>
          <p style="font-size: 14px">我已阅读并同意</p>
          <view
            class="display-style1"
            style="margin-left: 2px"
            v-for="(k, index2) in rentAgreements"
            @click="seeAgreements(k)"
          >
            <u--text type="success" :text="'《 ' + k.Key + '》'"></u--text>
          </view>
        </view>
        <view style="height: 100rpx"></view>
        <u-button
          v-if="payTotalAmount"
          @click="handlePayBtnSecondClick"
          :loading="payBtnLoading"
          :text="'￥' + payTotalAmount"
          customStyle="width:90%;bottom: 20rpx;position: fixed; left: 50%;transform: translateX(-50%);backgroundColor:#29B7A3;color:#fff"
        >
        </u-button>
        <u-button
          v-else
          @click="handlePayBtnSecondClick"
          :loading="payBtnLoading"
          text="确定"
          customStyle="width:90%;bottom: 20rpx;position: fixed; left: 50%;transform: translateX(-50%);backgroundColor:#29B7A3;color:#fff"
        >
        </u-button>
      </view>
    </u-popup>
    <u-modal
      :show="showContents"
      confirmText="确定"
      confirmColor="#29B7A3"
      @confirm="showContents = false"
      :heigth="300"
    >
      <view
        v-html="contentArg"
        slot="default"
        :style="'max-height:' + windowHeight + 'px;' + 'text-align:left'"
      ></view>
    </u-modal>
  </view>
</template>

<script>
import {
  getToExecute,
  getRxTemplateById,
  getTreatOrderInfo,
  quickPrescription,
} from '@/api/consult.js';
import {
  payNoNeedMoeny,
  getWxPayInfo,
  getUserAddrInfo,
  setOrderPaying,
} from '@/api/order.js';
import { sumOfTwoNumbers } from '@/utils/utils.js';
import { getUserProfile } from '@/api/passport.js';
const app = getApp();
export default {
  data() {
    return {
      payBtnLoading: false,
      loadingType: 'loading',
      windowHeight: 0,
      defaultAddress: [],
      option: {
        id: '',
      },
      imgList: [],
      prescriptionDetails: [],
      dataObj: {},
      payTotalAmount: 0,
      rentAgreements: [],
      rentDataInfos: [],
      orderInfo: {},
      showPopup: false,
      agreementsChecked: [],
      contentArg: '',
      showContents: false,
      rxTemplateId: '',
      targetMiniProgramsParams: {
        appId: '',
        extraData: {},
      },
      doctorInfo: {},
    };
  },
  onLoad(option) {
    console.log('option', option);
    const res = uni.getLaunchOptionsSync();
    if (res.referrerInfo && Object.keys(res.referrerInfo).length) {
      this.targetMiniProgramsParams = res.referrerInfo;
    }
    console.log('getLaunchOptionsSync', res);
    this.getSysInfo();
    // 获取用户地址信息
    this.getUserAddressList();
    if (option.id) {
      this.rxTemplateId = option.id;
      this.getPrescriptionDetails();
    }
  },
  methods: {
    handleToDoctorDetail() {
      uni.navigateTo({
        url: `/subPackIndex/docDetail?docId=${this.doctorInfo.Id}`,
      });
    },
    // 查看协议
    seeAgreements(item) {
      this.contentArg = item.Remark;
      this.showContents = true;
    },
    handlePayBtnClick() {
      // 切换首页机构
      getApp().changeOrgAndMark({
        orgId: this.dataObj.OrganizationId,
        orgName: this.dataObj.OrgName,
      });
      uni.showLoading({
        mask: true,
        title: '加载中...',
      });
      quickPrescription({
        RelatedId: this.rxTemplateId,
        CreatorId: this.dataObj.CreatorId,
        PatId: app.globalData.userInfo.Id,
        Type: 1,
        Extend: JSON.stringify({
          Type: ['partner'],
          Name: 'HK',
          Data: this.targetMiniProgramsParams.extraData,
        }),
      })
        .then((res) => {
          if (res.Type !== 200) {
            uni.showModal({
              title: '提示',
              content: res.Content,
              showCancel: false,
            });
            return;
          }
          this.option.id = res.Data;
          this.getToExecute();
        })
        .catch((err) => {
          uni.hideLoading();
          uni.showModal({
            title: '提示',
            content: err,
            showCancel: false,
          });
        });
    },
    async getToExecute() {
      let obj = {
        PayCompany: 'kangfx',
        TreatPayCompany: 'kangfx',
        PrescriptionId: this.option.id,
        IsChecked: false,
      };
      let rsp = await getToExecute(obj);
      if (rsp.Type !== 200) {
        uni.hideLoading();
        uni.showModal({
          title: '提示',
          content: rsp.Content,
          showCancel: false,
        });
        return;
      }
      let resData = await getTreatOrderInfo(this.option.id);
      if (resData.Type !== 200) {
        uni.hideLoading();
        uni.showModal({
          title: '提示',
          content: resData.Content,
          showCancel: false,
        });
        return;
      }
      this.orderInfo = resData.Data;
      const payTotalAmount = sumOfTwoNumbers(
        resData.Data.RentDataMoney,
        resData.Data.TreatOrder.Price
      );
      this.payTotalAmount = payTotalAmount;
      const RentAgreements = resData.Data.RentAgreements;
      const RentDataInfos = resData.Data.RentDataInfos;
      console.log('RentAgreements', RentAgreements);
      if (RentDataInfos && RentDataInfos.length > 0) {
        this.rentAgreements = RentAgreements;
        this.rentDataInfos = RentDataInfos;
        this.showPopup = true;
        if (!RentAgreements || !RentAgreements.length) {
          this.agreementsChecked = [true];
        }
        uni.hideLoading();
      } else {
        this.handlePayBtnSecondClick();
      }
    },
    handlePayBtnSecondClick() {
      if (this.showPopup && !this.agreementsChecked.length) {
        uni.showToast({
          title: '请勾选相关协议',
          icon: 'none',
        });
        return;
      }
      this.payBtnLoading = true;
      if (this.payTotalAmount) {
        let data = {
          OrderNo: this.orderInfo.TreatOrderNo,
          PayDescription: '订单支付',
          PaymentId: this.orderInfo.TreatOrder.Payment.Id,
          OpenId: app.globalData.openId,
          TradeType: 1,
        };
        if (this.defaultAddress.length && this.orderInfo.IsDelivery) {
          data.OrderAddresss = this.defaultAddress;
        }
        getWxPayInfo(data)
          .then((orderInfo) => {
            if (orderInfo.Type === 200) {
              uni.requestPayment({
                provider: 'wxpay',
                timeStamp: orderInfo.Data.timeStamp, // 时间戳（单位：秒）
                nonceStr: orderInfo.Data.nonceStr, // 随机字符串
                package: orderInfo.Data.package, // 固定值
                signType: orderInfo.Data.signType, //固定值
                paySign: orderInfo.Data.paySign, //签名
                success: (resss) => {
                  if ((resss.errMsg = 'requestPayment:ok')) {
                    this.$log.info(
                      `${this.$envVersion || ''}:有钱到账啦!!! 来源是：处方支付，患者是：${this.patName}`
                    );
                    this.onToResultPage();
                    // 将这单设置为支付中（后端这样设计的 别管为什么)
                    setOrderPaying({
                      OrderNo: this.orderInfo.OrderNo,
                    });
                  }
                },
                complete: () => {
                  uni.hideLoading();
                  this.payBtnLoading = false;
                },
              });
            } else {
              uni.showModal({
                title: '温馨提示',
                content: orderInfo.Content,
                showCancel: false,
              });
            }
          })
          .catch((err) => {
            uni.showModal({
              title: '温馨提示',
              content: res.Content,
              showCancel: false,
            });
          })
          .finally(() => {
            this.payBtnLoading = false;
          });
      } else {
        let data = {
          OrderNo: this.orderInfo.TreatOrderNo,
        };
        if (this.defaultAddress.length && this.orderInfo.IsDelivery) {
          data.OrderAddresss = this.defaultAddress;
        }
        payNoNeedMoeny(data)
          .then((res) => {
            if (res.Type === 200) {
              this.onToResultPage();
            } else {
              uni.showModal({
                title: '温馨提示',
                content: res.Content,
                showCancel: false,
              });
            }
          })
          .catch((err) => {
            uni.showModal({
              title: '温馨提示',
              content: res.Content,
              showCancel: false,
            });
          })
          .finally(() => {
            this.payBtnLoading = false;
          });
      }
    },
    onToResultPage() {
      const orderInfo = {
        IsDelivery: this.orderInfo.IsDelivery,
        Id: this.orderInfo.Id,
        TreatType: this.orderInfo.TreatType,
        OrderNo: this.orderInfo.OrderNo,
      };
      getApp().subscribeMessage(() => {
        let url =
          './payResults?orderInfo=' +
          encodeURIComponent(JSON.stringify(orderInfo));
        if (this.defaultAddress && this.defaultAddress.length > 0) {
          url +=
            '&address=' +
            encodeURIComponent(JSON.stringify(this.defaultAddress[0]));
        }
        uni.redirectTo({
          url,
        });
      });
    },
    getSysInfo() {
      uni.getSystemInfo({
        success: (res) => {
          this.windowHeight = res.windowHeight * 0.75;
        },
      });
    },
    async getUserAddressList() {
      let obj = {
        PageCondition: {
          PageIndex: 1,
          PageSize: 1000,
        },
        FilterGroup: {
          Rules: [
            {
              Field: 'UserId',
              Value: app.globalData.userInfo.Id,
              Operate: 3,
            },
          ],
          Operate: 1,
        },
      };
      let rsp = await getUserAddrInfo(obj);
      if (rsp.Type === 200 && rsp.Data.Rows.length > 0) {
        rsp.Data.Rows.forEach((s) => {
          if (s.Id) delete s.Id;
        });
        const isDef = rsp.Data.Rows.filter((s) => s.IsDefault);
        this.defaultAddress =
          isDef.length > 0 ? [isDef[0]] : [rsp.Data.Rows[0]];
      }
    },
    async getPrescriptionDetails() {
      let res = await getRxTemplateById(this.rxTemplateId);
      if (res.Type == 200) {
        this.initResData(res);
        this.loadingType = 'success';
      } else {
        uni.showModal({
          showCancel: false,
          content: res.Content,
        });
        this.loadingType = res.Content;
      }
    },
    initResData(res) {
      uni.setNavigationBarTitle({
        title: res.Data.PrescriptionName,
      });
      // 对医嘱信息进行格式化展现数据
      res.Data.RxDetailTemplateOutputDtos.map((v, index) => {
        if (v.MoItemMedia) {
          if (!Array.isArray(v.MoItemMedia)) {
            v.MoItemMedia = JSON.parse(v.MoItemMedia);
          }
          this.imgList.push(...v.MoItemMedia);
        }
      });
      this.prescriptionDetails = res.Data.RxDetailTemplateOutputDtos;
      this.dataObj = res.Data;
      // 关闭 loading 遮罩
      this.loadingSkeleton = false;
      // 获取医生的信息
      this.onGetCreatorInfo();
      this.$nextTick(() => {
        uni.hideLoading();
      });
    },
    async onGetCreatorInfo() {
      const doctorId = this.dataObj.CreatorId;
      const res = await getUserProfile({
        Ids: [doctorId],
        DtoTypeName: 'QueryUserFullOutputDto',
      });
      if (res.Type === 200) {
        const orgList = res.Data.Organizations.filter((v) => v.IsDefault);
        if (orgList.length) {
          this.doctorInfo = orgList[0];
          this.doctorInfo.Name = res.Data.Name;
          this.doctorInfo.HeadImg = res.Data.HeadImg;
          this.doctorInfo.Id = res.Data.Id;
        } else {
          this.doctorInfo = {};
        }
      }
    },
    onGetPackageContent() {
      let conten = '';
      this.prescriptionDetails.forEach((s, index) => {
        conten +=
          s.MoName + (index < this.prescriptionDetails.length - 1 ? ',' : '');
      });
      return conten;
    },
    onGetDiagnosContent() {
      let conten = '';
      this.dataObj.RxTempDiagnosisOutputDtos &&
        this.dataObj.RxTempDiagnosisOutputDtos.map((s, index) => {
          conten +=
            s.DiagnoseName +
            (index < this.dataObj.RxTempDiagnosisOutputDtos.length - 1
              ? ','
              : '');
        });
      return conten;
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .u-divider {
  margin: 0 !important;
}

.popup {
  padding: 32rpx;
  position: relative;
  &-top {
    &-title {
      font-weight: 600;
      font-size: 16px;
      margin-bottom: 10px;
    }
    &-label {
      color: #999999;
    }
  }
  &-mid {
    font-size: 14px;
    color: #999999;
    margin-bottom: 10px;
  }
  &-agreements {
    margin: 10px 0;
    flex-wrap: wrap;
  }
}
/deep/ .u-modal__content {
  max-height: 600px;
  overflow-y: auto;
}
/deep/ button {
  z-index: 2 !important;
}

/deep/ .u-radio-group {
  flex: 0 !important;
}

/deep/ .u-radio {
  margin-right: 0 !important;
}

.container {
  position: relative;
  padding-top: 32rpx;

  &-server {
    margin-top: 32rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 24rpx;
    &-doctor {
      margin: 0 32rpx;
      padding: 40rpx 32rpx;
      background: #ffffff;
      border-radius: 32rpx 32rpx 32rpx 32rpx;
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      &-img {
        width: 88rpx;
        height: 88rpx;
        border-radius: 50%;
      }
      &-info {
        margin-left: 32rpx;
        flex: 1;
        &-name {
          font-weight: 600;
          font-size: 28rpx;
          color: #333333;
          line-height: 40rpx;
        }
        &-skilled {
          font-weight: 400;
          font-size: 28rpx;
          color: #333333;
          display: -webkit-box; /* 必须结合 */
          overflow: hidden; /* 隐藏超出的部分 */
          text-overflow: ellipsis; /* 文字超出部分显示省略号 */
          -webkit-box-orient: vertical; /* 设置盒子的排列方向 */
          -webkit-line-clamp: 3; /* 限制行数为 3 */
        }
      }
    }

    &-img {
      width: 24rpx;
      height: 24rpx;
    }

    &-introduce {
      font-weight: 600;
      font-size: 32rpx;
      color: #333333;
      line-height: 44rpx;
      margin: 0 8rpx;
    }
  }

  &-top {
    background: #ffffff;
    border-radius: 24rpx;
    width: calc(100% - 64rpx);
    margin: 0 auto;
    padding: 32rpx;
    padding-bottom: 24rpx;

    &-title {
      display: block;
      text-align: center;
      font-weight: 600;
      font-size: 32rpx;
      color: #333333;
      line-height: 44rpx;
      margin-bottom: 32rpx;
    }

    &-box {
      min-height: 88rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;

      &-left {
        font-weight: 400;
        font-size: 28rpx;
        color: #999999;
        line-height: 40rpx;
      }

      &-right {
        font-weight: 400;
        font-size: 28rpx;
        color: #333333;
        line-height: 40rpx;
        flex: 1;
        text-align: right;
        margin-left: 64rpx;
      }

      &-money {
        font-weight: 600 !important;
        font-size: 32rpx !important;
        color: #ff4273 !important;
        line-height: 34rpx !important;
      }
    }
  }
}
/deep/ .u-popup__content {
  z-index: 5 !important;
}
/deep/ .u-transition {
  z-index: 3 !important;
}
</style>
