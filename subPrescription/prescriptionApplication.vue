<template>
  <view class="container">
    <view class="flex-center-center container-top">
      <image src="./static/sqm-bg.png" style="width: 278rpx; height: 164rpx" />
      <view class="container-top-text">
        <text>康复方案需求提交成功 稍后医生助手与您联系</text>
      </view>
    </view>
    <view class="payResults-box-assbox" v-if="assessInfo.Name">
      <view class="payResults-box-assbox-assinfo">
        <image src="static/ass.png" style="width: 96rpx; height: 96rpx"></image>
        <view class="payResults-box-assbox-assinfo-right">
          <p>
            <text class="payResults-box-assbox-assinfo-right-left"
              >若有疑问请联系：</text
            >
            <text
              class="payResults-box-assbox-assinfo-right-left"
              style="font-weight: 600"
              >{{ assessInfo.Name }}</text
            >
          </p>
          <p v-if="assessInfo.Phone">
            <text class="payResults-box-assbox-assinfo-right-left">电话：</text>
            <text
              @click="onPhoneCall"
              class="payResults-box-assbox-assinfo-right-left"
              style="font-weight: 600; color: #2ab7a3"
              >{{ assessInfo.Phone }}</text
            >
          </p>
        </view>
      </view>
      <u-image
        v-if="assessInfo.AssistantWeChatQrCode"
        :showLoading="true"
        :src="assessInfo.AssistantWeChatQrCode"
        width="150px"
        height="150px"
        custom-style="margin: 0 auto;margin-top: 56rpx"
      ></u-image>
      <div
        v-if="assessInfo.AssistantWeChatQrCode"
        class="payResults-box-qrcodetxt"
      >
        长按识别图中二维码，添加医生助手微信
      </div>
      <view
        style="height: 100rpx"
        v-if="assessInfo.AssistantWeChatQrCode"
      ></view>
    </view>
  </view>
</template>

<script>
const app = getApp();
import { insertPreApplyRecord } from '@/api/consult.js';
import { getUserProfile } from '@/api/passport.js';
export default {
  data() {
    return {
      pageParams: {},
      assessInfo: {
        Name: '',
        Phone: '',
        AssistantWeChatQrCode: '',
      },
    };
  },
  onLoad(option) {
    const params = JSON.parse(decodeURI(option.info));
    this.pageParams = params;
    // 方案申请
    this.onGetAddPrescriptionApplication();
  },
  methods: {
    async onGetAddPrescriptionApplication() {
      uni.showLoading({
        title: '加载中',
      });
      const res = await insertPreApplyRecord({
        RecommendId: this.pageParams.RecommendId,
        DctId: this.pageParams.CreatorId,
        UserId: app.globalData.userInfo.Id,
        UserPhoneNumber: app.globalData.userInfo.PhoneNumber,
        RecommendName: this.pageParams.RecommendName,
      });
      if (res.Type === 200 && res.Data && res.Data.AssistantId) {
        // 获取医助相关的信息
        this.onGetAssessInfo(res.Data.AssistantId);
      }
    },
    async onGetAssessInfo(Id) {
      const res = await getUserProfile({
        RoleType: 'assistant',
        Ids: [Id],
        DtoTypeName: 'OrganizationDoctorListOutputDto',
      });
      if (res.Type === 200) {
        this.assessInfo.Name = res.Data.Name;
        this.assessInfo.Phone = res.Data.PhoneNumber;
        this.assessInfo.AssistantWeChatQrCode =
          res.Data.UserExternalIdentify?.WeChatQrCode || '';
      }
      uni.hideLoading();
    },
    onPhoneCall() {
      uni.makePhoneCall({
        phoneNumber: this.assessInfo.Phone,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding: 0 30rpx;

  &-top {
    padding-top: 162rpx;
    flex-direction: column;

    &-text {
      width: 300rpx;
      font-weight: 600;
      font-size: 30rpx;
      color: #646464;
      line-height: 42rpx;
      margin-top: 32rpx;
    }
  }
}

.payResults {
  &-box {
    padding: 40rpx;

    &-assbox {
      background: #f6fcfb;
      border-radius: 16rpx;
      padding: 40rpx 32rpx;
      margin-top: 32rpx;

      &-assinfo {
        display: flex;
        justify-content: flex-start;
        align-items: center;

        &-right {
          margin-left: 24rpx;

          &-left {
            font-weight: 400;
            font-size: 28rpx;
            color: #333333;
            line-height: 40rpx;
          }
        }
      }
    }

    &-qrcodetxt {
      text-align: center;
      font-weight: 600;
      font-size: 28rpx;
      color: #999999;
      line-height: 40rpx;
      margin-top: 32rpx;
      margin-bottom: 72rpx;
    }

    &-label {
      font-weight: 600;
      font-size: 30rpx;
      color: #646464;
      line-height: 42rpx;
      text-align: left;
      margin-left: 16rpx;
    }

    &-labels {
      font-weight: 600;
      font-size: 24rpx;
      line-height: 34rpx;
      margin-left: 8rpx;
    }

    &-top {
      display: flex;
      justify-content: center;
      align-items: center;

      &-right {
        font-weight: 600;
        font-size: 50rpx;
        color: #323232;
        line-height: 72rpx;
        margin-left: 26rpx;
      }
    }

    &-title {
      background: #faead2;
      border-radius: 16rpx;
      color: #ea6800;
      padding: 16rpx 32rpx;
      margin-top: 32rpx;
    }

    &-title1 {
      background: #ffeaef;
      border-radius: 16rpx;
      color: #ff4273;
      padding: 16rpx 32rpx;
      margin-top: 32rpx;
    }
  }
}
</style>
