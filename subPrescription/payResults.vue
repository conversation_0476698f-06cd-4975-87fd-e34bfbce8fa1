<template>
  <div class="payResults">
    <div class="payResults-box">
      <view class="payResults-box-top">
        <image
          src="static/result.png"
          style="width: 80rpx; height: 80rpx"
        ></image>
        <!-- <span class="payResults-box-top-right">执行成功</span> -->
        <view class="payResults-box-label">{{ payContent.title }}</view>
      </view>

      <view class="payResults-box-title flex-start-start" v-if="!userInfo.Name">
        <u-icon name="info-circle" :top="3" color="#EA6800" size="12"></u-icon>
        <text class="payResults-box-labels"
          >为了更好地为您提供服务,建议完善个人信息</text
        >
      </view>

      <u-cell-group v-if="!userInfo.Name">
        <u-cell>
          <view slot="title">
            <p>姓名<span style="color: red">*</span></p>
          </view>
          <u-input
            slot="value"
            @change="onUserNameChange"
            placeholder="请输入真实姓名"
            border="none"
            v-model="smQuery.Name"
            inputAlign="right"
          ></u-input>
        </u-cell>
        <u-cell :border="false" customStyle="justify-content: space-between">
          <view slot="title">
            <p>性别<span style="color: red">*</span></p>
          </view>
          <view
            slot="value"
            style="display: flex; flex: 1; justify-content: flex-end"
          >
            <view
              class="u-page__tag-item"
              style="margin-right: 10rpx"
              v-for="(item, index) in sexList"
              :key="index"
            >
              <MPTag
                shape="circle"
                iconPosition="right"
                :icon="item.name === '男' ? 'man' : 'woman'"
                :text="item.name"
                :plain="!item.checked"
                type="success"
                :name="index"
                @click="radioClick"
              >
              </MPTag>
            </view>
          </view>
        </u-cell>
        <u-cell>
          <span slot="title" id="targetComponent">身份证</span>
          <u-input
            style="flex: 1"
            slot="value"
            placeholder="请输入真实身份证"
            type="idcard"
            border="none"
            v-model="smQuery.UserCertificates[0].CertificateValue"
            inputAlign="right"
            @blur="IdCardblur"
          >
          </u-input>
          <ocr-navigator
            slot="right-icon"
            @onSuccess="ocrSuccess"
            certificateType="idCard"
            :opposite="false"
          >
            <u-icon name="camera" color="#24BAA3" size="28"></u-icon>
          </ocr-navigator>
        </u-cell>
        <u-cell>
          <view slot="title">
            <p>年龄<span style="color: red">*</span></p>
          </view>
          <u-input
            slot="value"
            placeholder="请输入真实年龄"
            border="none"
            v-model="smQuery.Age"
            inputAlign="right"
            type="number"
            @blur="Ageblur"
          ></u-input>
        </u-cell>
      </u-cell-group>

      <block v-if="isHavePostoperative">
        <u-cell-group>
          <u-cell is-link @click="() => (showPostoperativePicker = true)">
            <view slot="title">
              <p>手术时间<span style="color: red">*</span></p>
            </view>
            <text style="flex: 1; text-align: right" slot="value">{{
              postoperativeTime || '请选择'
            }}</text>
          </u-cell>
        </u-cell-group>
      </block>

      <view
        class="payResults-box-title1 flex-start-start"
        v-if="payContent.label"
      >
        <u-icon name="info-circle" :top="3" color="#FF4273" size="12"></u-icon>
        <text class="payResults-box-labels">{{ payContent.label }}</text>
      </view>
      <u-cell-group v-if="orderInfo.IsDelivery">
        <u-cell>
          <view slot="title">
            <p class="title-name-style">
              收货方式<span style="color: red">*</span>
            </p>
          </view>
          <view style="flex: 1" slot="value">
            <u-radio-group
              activeColor="#25ae99"
              v-model="deliveryMethod"
              placement="row"
            >
              <u-radio label="邮寄" name="邮寄"></u-radio>
              <u-radio label="自提" name="自提"></u-radio>
            </u-radio-group>
          </view>
        </u-cell>
        <block v-if="deliveryMethod === '邮寄'">
          <block v-if="userAddress.Name">
            <view class="address-box" @click="handleAddressChange">
              <view class="address-box-top">收货信息</view>
              <view class="flex-between-center" style="margin-top: 24rpx">
                <view class="address-box-text">
                  <view
                    >{{ userAddress.Name }}, {{ userAddress.Tel }}
                    {{ userAddress.ProvinceName }} {{ userAddress.CityName }}
                    {{ userAddress.CountyName }}
                  </view>
                  <view>
                    {{ userAddress.Address }}
                  </view>
                </view>
                <u-icon name="arrow-right" size="24"></u-icon>
              </view>
            </view>
          </block>
          <block v-else>
            <MpAddress
              :on-mp-address-user-name="onMpAddressUserName"
              ref="address"
              @changeAddress="onGetAddress"
              :need-address="false"
            />
          </block>
        </block>
      </u-cell-group>
      <view class="payResults-box-assbox" v-if="AssessInfo.Name">
        <view class="payResults-box-assbox-assinfo">
          <image
            src="static/ass.png"
            style="width: 96rpx; height: 96rpx"
          ></image>
          <view class="payResults-box-assbox-assinfo-right">
            <p>
              <text class="payResults-box-assbox-assinfo-right-left"
                >若有疑问请联系：</text
              >
              <text
                class="payResults-box-assbox-assinfo-right-left"
                style="font-weight: 600"
                >{{ AssessInfo.Name }}</text
              >
            </p>
            <p v-if="AssessInfo.Phone">
              <text class="payResults-box-assbox-assinfo-right-left"
                >电话：</text
              >
              <text
                @click="onPhoneCall"
                class="payResults-box-assbox-assinfo-right-left"
                style="font-weight: 600; color: #2ab7a3"
                >{{ AssessInfo.Phone }}</text
              >
            </p>
          </view>
        </view>
        <u-image
          v-if="AssessInfo.AssistantWeChatQrCode"
          :showLoading="true"
          :src="AssessInfo.AssistantWeChatQrCode"
          width="150px"
          height="150px"
          custom-style="margin: 0 auto;margin-top: 56rpx"
        ></u-image>
        <div
          v-if="AssessInfo.AssistantWeChatQrCode"
          class="payResults-box-qrcodetxt"
        >
          长按识别图中二维码，添加医生助手微信
        </div>
        <view
          style="height: 100rpx"
          v-if="AssessInfo.AssistantWeChatQrCode"
        ></view>
      </view>
    </div>
    <div class="container-bomBtn" v-if="showJUJIA" @click="toPlat">
      <text>确定</text>
    </div>
    <div class="container-bomBtn" v-else @click="toTreatment">
      <text>去治疗</text>
    </div>
    <u-toast ref="uToast"></u-toast>
    <u-datetime-picker
      :show="showPostoperativePicker"
      v-model="postoperativePickerTime"
      mode="date"
      :maxDate="Date.now()"
      @cancel="showPostoperativePicker = false"
      :formatter="formatter"
      ref="datetimePicker"
      @confirm="(e) => onGetSelectDay(e)"
      :defaultIndex="datetimePickerDefaultIndex"
      useType="max"
    ></u-datetime-picker>
  </div>
</template>

<script>
const app = getApp();
import MpAddress from '@/components/mp-address/mp-address.vue';
import { TrainingClientEvent } from '@/utils/eventKeys.js';
import { getPrescriptionInfo, fillUserStartDate } from '../api/consult.js';
import { payOverGetPlatId } from '@/api/training.js';
import { addAddress, setOrderAddress } from '@/api/order.js';
import {
  briGetAge,
  idCardTrue,
  idCardGetSex,
  idCardGetBri,
} from '@/utils/utils';
import MPTag from '@/components/mp-tag/u-tag.vue';
export default {
  components: {
    MPTag,
    MpAddress,
  },
  data() {
    return {
      datetimePickerDefaultIndex: [],
      isHavePostoperative: null,
      postoperativeTime: '',
      postoperativePickerTime: '',
      showPostoperativePicker: false,
      onMpAddressUserName: '',
      deliveryMethod: '邮寄',
      userAddress: {},
      userInfo: {},
      smQuery: {
        UserId: app.globalData.userInfo.Id,
        Name: '',
        Sex: '',
        Birthday: '',
        Age: null,
        UserCertificates: [
          {
            CertificateType: 'idCard',
            CertificateValue: '',
          },
        ],
      },
      sexList: [
        {
          name: '男',
          checked: false,
        },
        {
          name: '女',
          checked: false,
        },
      ],
      orderInfo: {},
      payOverGetToPlatId: '',
      payContent: {
        title: '您可以前往附近治疗点,出示医嘱二维码进行治疗',
        label: '',
      },
      id: '',
      showJUJIA: true,
      CustomerServicePhone: '',
      AssistantQrCode: '',
      AssessInfo: {
        Name: '',
        Phone: '',
        AssistantWeChatQrCode: '',
      },
      orgTestList: [
        {
          HeadImg:
            'https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/5420127094451419136.png',
          Name: '大安和平老年养护院',
          Address: '自贡市大安区马冲口街人民路341号',
          LatLon: '104.771890,29.374015',
        },
        {
          HeadImg:
            'https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/5418290738234338304.png',
          Name: '自贡市自流井区新街社区卫生服务中心',
          Address: '四川省自贡市自流井区滨江路74号',
          LatLon: '104.768913,29.348245',
        },
        {
          HeadImg:
            'https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/5251073264670496768.png',
          Name: '自贡市第四人民医院',
          Address: '四川省自贡市自流井区檀木林街19号',
          LatLon: '104.777049,29.351535',
        },
      ],
    };
  },
  onLoad({ orderInfo, address, isHavePostoperative }) {
    uni.setNavigationBarTitle({
      title: '执行成功',
    });
    this.isHavePostoperative = false; // isHavePostoperative 现在直接不显示  后续如果需要恢复 就写isHavePostoperative
    this.changeOrderStatus();
    this.userInfo = app.globalData.userInfo;
    if (orderInfo) {
      this.orderInfo = JSON.parse(decodeURIComponent(orderInfo));
      this.userAddress = address ? JSON.parse(decodeURIComponent(address)) : {};
      if (!this.userAddress.Name) {
        this.onMpAddressUserName = app.globalData.userInfo.Name;
      }
      this.getAssessWxCode();
      this.orderInfo.Id && this.getPlatInfo();
      this.id = this.orderInfo.Id;
    }
    // 选择时间默认
    const nowData = this.$dateFormat(Date.now(), 'YYYY-MM-DD');
    this.datetimePickerDefaultIndex = [
      10,
      nowData.split('-')[1] - 1,
      nowData.split('-')[2] - 1,
    ];
  },
  onShow() {
    this.getAssessWxCode();
  },
  methods: {
    onGetSelectDay(e) {
      this.postoperativeTime = this.$dateFormat(e.value, 'YYYY-MM-DD', false);
      this.showPostoperativePicker = false;
    },
    formatter(type, value) {
      if (type === 'year') {
        return `${value}年`;
      }
      if (type === 'month') {
        return `${value}月`;
      }
      if (type === 'day') {
        return `${value}日`;
      }
      return value;
    },
    onUserNameChange(e) {
      this.onMpAddressUserName = e;
    },
    onGetAddress(info) {
      console.log('输入的地址', info);
      if (info.Name) {
        const defaultAddress = {
          Name: '',
          Tel: '',
          Province: '',
          City: '',
          County: '',
          ProvinceName: '',
          CityName: '',
          CountyName: '',
          Address: '',
          UserId: app.globalData.userInfo.Id,
          IsDefault: true,
        };
        if (this.$refs.address.onCheckIsAddress()) {
          addAddress('create', {
            ...defaultAddress,
            ...info,
          });
          this.userAddress = info;
          this.onSetOrderAddress(info);
        }
      }
      uni.hideLoading();
      this.onToPage();
    },
    handleAddressChange() {
      uni.navigateTo({
        url: '/subPackIndex/user/addressMgr?isChange=no',
      });
    },
    // 通过选择回来的数据更新地址
    getBeforData(item) {
      console.log('item', item);
      this.userAddress = JSON.parse(JSON.stringify(item));
    },
    onPhoneCall() {
      uni.makePhoneCall({
        phoneNumber: this.AssessInfo.Phone,
      });
    },
    IdCardblur(e) {
      if (e && !idCardTrue(e)) {
        this.$refs.uToast.show({
          message: '身份证格式输入错误',
          type: 'error',
        });
        return;
      } else if (e && idCardTrue(e)) {
        this.smQuery.Sex = idCardGetSex(e);
        this.smQuery.Birthday = idCardGetBri(e);
        this.smQuery.Age = briGetAge(this.smQuery.Birthday);
      }
    },
    Ageblur(e) {
      if (Number(e) > 150 || Number(e) < 0) {
        this.smQuery.Age = null;
        uni.showToast({
          title: '年龄范围为0-150岁',
          icon: 'none',
        });
        return;
      }
    },
    ocrSuccess(e) {
      this.$log.info('使用了身份证识别，并识别成功');
      if (e.type === 'onSuccess') {
        const data = e.detail;
        this.smQuery.UserCertificates[0].CertificateValue = data.id.text;
        this.smQuery.Sex = data.gender.text;
        this.smQuery.Name = data.name.text;
        this.smQuery.Age = briGetAge(data.birth.text);
      }
    },
    radioClick(name) {
      this.sexList.map((item, index) => {
        item.checked = index === name ? true : false;
      });
      this.smQuery.Sex = this.sexList[name].name;
    },
    // 防止不要钱不发货的情况直接进入这里 回到上个页面导致仍然可以确认订单的情况
    changeOrderStatus() {
      let pages = getCurrentPages();
      const prePage = pages[pages.length - 2]; //上一个页面
      if (prePage && prePage.$vm.getPrescriptionDetails) {
        prePage.$vm.getPrescriptionDetails();
      }
    },
    async getAssessWxCode() {
      uni.showLoading({
        title: this.$loadingMsg,
      });
      let res = await getPrescriptionInfo({
        PrescriptionId: this.orderInfo.Id,
        DtoTypeName: 'RxInfoOutputDto1',
      });
      if (res.Type == 200) {
        this.AssessInfo.Name = res.Data.AssistantName;
        this.AssessInfo.Phone = res.Data.AssistantPhoneNumber;
        this.AssessInfo.AssistantWeChatQrCode = res.Data.AssistantWeChatQrCode;
      }
      this.$nextTick(() => {
        uni.hideLoading();
      });
    },
    toTreatment() {
      this.onBtnClickHandle();
    },
    async getPlatInfo() {
      let res = await payOverGetPlatId({
        visitId: this.orderInfo.Id,
      });
      if (res.Type == 200 && res.Data) {
        this.payOverGetToPlatId = res.Data;
      }
      if (this.orderInfo.TreatType === 1 || this.orderInfo.TreatType === 3) {
        if (this.orderInfo.IsDelivery) {
          this.payContent = {
            title: '已为您生成居家康复计划,请按计划打卡训练',
            label: '治疗项目中包含的耗材/设备将在3-5天内通过快递邮寄给您',
          };
        } else {
          this.payContent = {
            title: '已为您生成居家康复计划,请按计划执行',
            label: '',
          };
        }
      } else if (this.orderInfo.TreatType === 2) {
        this.showJUJIA = false;
      }
    },
    onSetPostoperativeTime() {
      if (this.isHavePostoperative && this.postoperativeTime) {
        fillUserStartDate({
          UserId: app.globalData.userInfo.Id,
          PreId: this.id,
          StartDate: this.postoperativeTime,
        });
      }
    },
    async onBtnClickHandle() {
      const flag = await this.onNeedUpDateUser();
      if (!flag) {
        return;
      }
      if (this.isHavePostoperative && !this.postoperativeTime) {
        uni.showToast({
          title: '请选择手术时间',
          icon: 'none',
        });
        return;
      }
      uni.showLoading({
        title: '加载中...',
        mask: true,
      });
      if (this.onChekcIsNeedSendDelivery()) {
        if (this.deliveryMethod !== '邮寄') {
          this.onToPage();
        } else {
          if (this.userAddress.Name) {
            // 说明有默认的地址
            this.onSetOrderAddress(this.userAddress);
            this.onToPage();
          } else {
            // 没有有默认的地址 要为用户添加一个地址
            const flag = this.onCheckIsFinishAddress(); // 判断是否完整填写了信息或者手机号是否错误
            if (!flag.error) {
              // 如果都填写了 并且手机号没问题
              this.$refs.address.onGetSSQByAddress();
            } else {
              if (flag.type === 'phone') {
                // 如果是手机号的问题
                uni.hideLoading();
                return;
              }
              uni.showModal({
                content:
                  '您还未完整填写收货信息，如暂不方便填写，后续将由医助与您联系',
                cancelText: '暂不填写',
                confirmText: '完善地址',
                success: (res) => {
                  if (!res.confirm) {
                    this.onGetAddress({});
                  } else {
                    uni.hideLoading();
                  }
                },
              });
            }
          }
        }
      } else {
        this.onToPage();
      }
    },
    toPlat() {
      this.onBtnClickHandle();
    },
    /**
     * 修改用户收货地址
     */
    onSetOrderAddress(address) {
      let obj = [
        {
          OrderNo: this.orderInfo.OrderNo,
          Name: address.Name,
          Tel: address.Tel,
          Province: address.Province,
          City: address.City,
          County: address.County,
          ProvinceName: address.ProvinceName,
          CityName: address.CityName,
          CountyName: address.CountyName,
          Address: address.Address,
        },
      ];
      setOrderAddress(obj);
    },
    onToPage() {
      // 如果需要填写术后时间 并且填写了 则更新时间
      try {
        this.onSetPostoperativeTime();
      } catch (e) {
        this.$log.error(
          `${this.$envVersion || ''}:支付结果页面修改术后时间失败`,
          e
        );
      }
      if (this.showJUJIA) {
        uni.switchTab({
          url: '/pages/plan/index',
        });
      } else {
        uni.reLaunch({
          url: '/subPackIndex/community/index',
        });
      }
    },
    onChekcIsNeedSendDelivery() {
      return this.orderInfo.IsDelivery;
    },
    onCheckIsFinishAddress() {
      return this.$refs.address.checkIsFinish();
    },
    confirm2() {
      uni.navigateTo({
        url: '/subPackIndex/user/treatmentDetail?id=' + this.id,
      });
    },
    async onNeedUpDateUser() {
      if (!this.userInfo.Name) {
        const flag = this.getPatUserInfo();
        if (!flag) {
          return false;
        }
        try {
          this.$log.info(
            `${this.$envVersion}:用户开始主动提交修改信息成功`,
            this.smQuery
          );
          await getApp().saveAuthen(this.smQuery);
          this.$log.info(
            `${this.$envVersion}:用户主动提交修改信息成功`,
            this.smQuery
          );
          return true;
        } catch (e) {
          this.$log.info(`${this.$envVersion}:用户主动提交修改信息出错`, e);
          return true;
        }
        return true;
      }
      return true;
    },
    getPatUserInfo() {
      if (!this.smQuery.Name) {
        this.$refs.uToast.show({
          message: '请输入您的名字',
          type: 'error',
        });
        return false;
      }
      if (!this.smQuery.Sex) {
        this.$refs.uToast.show({
          message: '请选择您的性别',
          type: 'error',
        });
        return false;
      }
      if (!this.smQuery.Age) {
        this.$refs.uToast.show({
          message: '请输入您的年龄',
          type: 'error',
        });
        return false;
      }
      if (
        this.smQuery.UserCertificates.length > 0 &&
        this.smQuery.UserCertificates[0].CertificateValue &&
        !idCardTrue(this.smQuery.UserCertificates[0].CertificateValue)
      ) {
        this.$refs.uToast.show({
          message: '身份证格式输入错误',
          type: 'error',
        });
        return false;
      }
      return true;
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .u-radio {
  margin-left: 20rpx !important;
}

/deep/ .u-radio-group {
  justify-content: flex-end !important;
}

/deep/ .u-cell__body {
  background: #f6fcfb;
}

.container {
  &-execute {
    padding: 32rpx;
    background-color: white;
    margin-bottom: 24rpx;

    &-list {
      margin: 24rpx 0;

      &-text {
        font-weight: 400;
        font-size: 20rpx;
        color: #999999;
        line-height: 28rpx;
        margin-top: 8rpx;
      }

      &-avtor {
        width: 40rpx;
        height: 40rpx;
      }

      &-mid {
        flex: 1;
        margin-left: 24rpx;

        &-title {
          font-weight: 600;
          font-size: 28rpx;
          color: #333333;
          line-height: 40rpx;
        }

        &-address {
          font-weight: 400;
          font-size: 24rpx;
          color: #999999;
          line-height: 34rpx;
          margin-top: 8rpx;
        }
      }

      &-img {
        width: 104rpx;
        height: 104rpx;
        border-radius: 50%;
      }

      &-org {
        font-weight: 600;
        font-size: 28rpx;
        color: #333333;
        line-height: 40rpx;
        margin-left: 14rpx;
      }
    }
  }
}

.payResults {
  position: relative;
  background: white !important;

  &-box {
    padding: 40rpx;

    &-assbox {
      background: #f6fcfb;
      border-radius: 16rpx;
      padding: 40rpx 32rpx;
      margin-top: 32rpx;

      &-assinfo {
        display: flex;
        justify-content: flex-start;
        align-items: center;

        &-right {
          margin-left: 24rpx;

          &-left {
            font-weight: 400;
            font-size: 28rpx;
            color: #333333;
            line-height: 40rpx;
          }
        }
      }
    }

    &-qrcodetxt {
      text-align: center;
      font-weight: 600;
      font-size: 28rpx;
      color: #999999;
      line-height: 40rpx;
      margin-top: 32rpx;
      margin-bottom: 72rpx;
    }

    &-label {
      font-weight: 600;
      font-size: 30rpx;
      color: #646464;
      line-height: 42rpx;
      text-align: left;
      margin-left: 16rpx;
    }

    &-labels {
      font-weight: 600;
      font-size: 24rpx;
      line-height: 34rpx;
      margin-left: 8rpx;
    }

    &-top {
      display: flex;
      justify-content: center;
      align-items: center;

      &-right {
        font-weight: 600;
        font-size: 50rpx;
        color: #323232;
        line-height: 72rpx;
        margin-left: 26rpx;
      }
    }

    &-title {
      background: #faead2;
      border-radius: 16rpx;
      color: #ea6800;
      padding: 16rpx 32rpx;
      margin-top: 32rpx;
    }

    &-title1 {
      background: #ffeaef;
      border-radius: 16rpx;
      color: #ff4273;
      padding: 16rpx 32rpx;
      margin-top: 32rpx;
    }
  }
}

/deep/ .u-text__value {
  color: blue !important;
}

/deep/ .u-text {
  flex: 0 !important;
}

/deep/ .u-cell {
  background-color: #fff !important;
}

/deep/ .u-line {
  border: none !important;
}

// /deep/ .u-cell__right-icon-wrap {
// 	text-align: right;
// }

/deep/ .u-radio-group {
  flex: 0 !important;
}

/deep/ .u-list {
  height: auto !important;
}

/deep/ .u-radio {
  margin-right: 0 !important;
}

/deep/ .u-radio-group {
  background: '#f7f7f7' !important;
  padding: 20rpx !important;
}

/deep/ .u-cell__body__content {
  flex: none !important;
}

/deep/ .u-cell__title {
  flex: none !important;
}

.address-box {
  padding: 32rpx;
  background: #f5fcfa;

  &-top {
    font-weight: 600;
    font-size: 28rpx;
    color: #333333;
    line-height: 40rpx;
  }

  &-text {
    flex: 1;
    font-weight: 400;
    font-size: 24rpx;
    color: #343434;
    line-height: 34rpx;
  }
}
</style>
