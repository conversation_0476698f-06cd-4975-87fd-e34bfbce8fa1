<template>
  <view class="box">
    <u-navbar title="收银台" @leftClick="leftClick"> </u-navbar>
    <u-cell-group v-if="userInfo.WorkflowStatus != 2">
      <u-cell>
        <view slot="title">
          <p>姓名<span style="color: red">*</span></p>
        </view>
        <u-input
          slot="value"
          placeholder="请输入真实姓名"
          border="none"
          v-model="smQuery.Name"
          inputAlign="right"
          @blur="onNameChange"
        ></u-input>
      </u-cell>
      <u-cell :border="false" customStyle="justify-content: space-between">
        <view slot="title">
          <p>性别<span style="color: red">*</span></p>
        </view>
        <view slot="value" style="display: flex">
          <view
            class="u-page__tag-item"
            style="margin-right: 10rpx"
            v-for="(item, index) in sexList"
            :key="index"
          >
            <MPTag
              shape="circle"
              iconPosition="right"
              :icon="item.name === '男' ? 'man' : 'woman'"
              :text="item.name"
              :plain="!item.checked"
              type="success"
              :name="index"
              @click="radioClick"
            >
            </MPTag>
          </view>
        </view>
      </u-cell>
      <u-cell>
        <view slot="title" id="targetComponent">
          <p>证件号码</p>
        </view>
        <u-input
          slot="value"
          placeholder="请输入真实证件号码"
          type="idcard"
          border="none"
          v-model="smQuery.UserCertificates[0].CertificateValue"
          inputAlign="right"
          @blur="IdCardblur"
        >
        </u-input>
        <ocr-navigator
          slot="right-icon"
          @onSuccess="ocrSuccess"
          certificateType="idCard"
          :opposite="false"
        >
          <u-icon name="camera" color="#24BAA3" size="28"></u-icon>
        </ocr-navigator>
      </u-cell>
      <u-cell>
        <view slot="title">
          <p>年龄<span style="color: red">*</span></p>
        </view>
        <u-input
          slot="value"
          placeholder="请输入真实年龄"
          border="none"
          v-model="smQuery.Age"
          inputAlign="right"
          type="number"
          @blur="Ageblur"
        ></u-input>
      </u-cell>
    </u-cell-group>
    <view v-if="id != ''">
      <div style="background: white" v-if="orderInfo.IsDelivery">
        <u-radio-group
          v-model="mailMethod"
          placement="row"
          @change="mailMethodChange($event)"
          activeColor="#29B7A3"
        >
          <u-radio label="邮寄" name="邮寄"></u-radio>
          <u-radio
            label="自提"
            name="自提"
            customStyle="margin-left: 100rpx;"
          ></u-radio>
        </u-radio-group>
      </div>
      <u-line :hairline="false"></u-line>
      <u-cell-group
        style="margin-top: 44px"
        v-if="orderInfo.IsDelivery && addrInfo.Name && mailMethod === '邮寄'"
      >
        <u-cell
          @click="toChangeAddr"
          :label="
            '收货地址：' +
            addrInfo.ProvinceName +
            addrInfo.CityName +
            addrInfo.CountyName +
            addrInfo.Address
          "
          :title="'收件人：' + addrInfo.Name + addrInfo.Tel"
          :isLink="true"
        >
        </u-cell>
      </u-cell-group>

      <u-cell-group
        style="margin-top: 44px"
        v-if="orderInfo.IsDelivery && !addrInfo.Name && mailMethod === '邮寄'"
      >
        <u-cell :isLink="true" @click="addAddress">
          <div slot="title">
            <span style="color: red">请添加收货地址</span>
          </div>
        </u-cell>
      </u-cell-group>

      <u-list
        v-if="
          orderInfo.TreatOrderMoOutputDtos &&
          orderInfo.TreatOrderMoOutputDtos.length > 0
        "
      >
        <p style="background-color: white; padding: 10px 16px 6px 16px">
          医嘱明细
        </p>
        <u-list-item
          v-for="(item, index) in orderInfo.TreatOrderMoOutputDtos"
          :key="item.MoItemId"
        >
          <u-cell :border="false">
            <view
              slot="title"
              style="font-weight: 600"
              v-show="item.MoItemChargeMode == 1"
            >
              {{ item.MoName }} {{ '(部位' + item.Part + ')' }}
            </view>
            <view
              slot="title"
              style="font-weight: 600"
              v-show="item.MoItemChargeMode != 1"
            >
              {{ item.MoName }}
            </view>
            <view
              slot="label"
              style="font-size: 12px; color: #999999; margin-top: 24rpx"
              v-show="item.MoItemChargeMode === 3"
            >
              {{ item.FreqDay }}天{{ item.Freq }}次,共{{ item.MoDay }}天
            </view>
            <view
              slot="label"
              style="font-size: 12px; color: #999999; margin-top: 24rpx"
              v-show="item.MoItemChargeMode === 5"
            >
              {{ item.MoMonth }}个月
            </view>
            <view
              slot="label"
              style="font-size: 12px; color: #999999; margin-top: 24rpx"
              v-show="
                item.MoItemChargeMode === 1 || item.MoItemChargeMode === 2
              "
            >
              {{ item.FreqDay }}天{{ item.Freq }}次,共{{ item.TotalCount }}次
            </view>
            <view slot="value" style="text-align: right">
              <p style="font-size: 16px" v-show="item.MoItemChargeMode === 1">
                <span>￥{{ (item.Price * item.Part).toFixed(2) }}</span>
                <span
                  style="text-decoration: line-through; color: #b5b5b5"
                  v-if="item.ShowPrice > item.Price"
                  >￥{{ (item.ShowPrice * item.Part).toFixed(2) }}</span
                >
              </p>
              <p style="font-size: 16px" v-show="item.MoItemChargeMode != 1">
                <span>￥{{ item.Price.toFixed(2) }}</span>
                <span
                  style="text-decoration: line-through; color: #b5b5b5"
                  v-if="item.ShowPrice > item.Price"
                  >￥{{ item.ShowPrice.toFixed(2) }}</span
                >
              </p>
              <p
                style="color: #999999; font-size: 12px"
                v-show="item.MoItemChargeMode === 4"
              >
                x1
              </p>
              <p
                style="color: #999999; font-size: 12px"
                v-show="item.MoItemChargeMode === 3"
              >
                x{{ item.MoDay }}
              </p>
              <p
                style="color: #999999; font-size: 12px"
                v-show="
                  item.MoItemChargeMode === 1 || item.MoItemChargeMode === 2
                "
              >
                x{{ item.TotalCount }}
              </p>
              <p
                style="color: #999999; font-size: 12px"
                v-show="item.MoItemChargeMode === 5"
              >
                x{{ item.MoMonth }}
              </p>
            </view>
          </u-cell>
        </u-list-item>
      </u-list>

      <!-- orderInfo.RentAgreements.length> 0 -->
      <u-cell-group
        style="margin-top: 10upx"
        v-if="orderInfo.RentDataInfos && orderInfo.RentDataInfos.length > 0"
      >
        <u-cell :border="false">
          <view class="" slot="title">
            <p style="font-weight: 600; font-size: 16px; margin-bottom: 10px">
              设备使用保证金<span style="color: #999999">(可退)</span>
            </p>
            <p style="font-size: 14px; color: #999999; margin-bottom: 10px">
              本次治疗将使用到以下设备，需要缴纳设备保证金，保证金会在设备返还后退回付款账户
            </p>
            <view
              class="display-style1"
              style="margin-bottom: 10px; flex-wrap: wrap"
              v-if="orderInfo.RentAgreements.length > 0"
            >
              <u-radio-group @change="groupChange1" v-model="agrCheck">
                <u-radio
                  name="agrCheck"
                  activeColor="#29B7A3"
                  @change="radioChange1"
                >
                </u-radio>
              </u-radio-group>
              <p style="font-size: 14px">我已阅读并同意</p>
              <view
                class="display-style1"
                style="margin-left: 2px"
                v-for="(k, index2) in orderInfo.RentAgreements"
                @click="seeAgreements(k)"
              >
                <u--text type="success" :text="'《 ' + k.Key + '》'"></u--text>
              </view>
            </view>
          </view>
        </u-cell>
      </u-cell-group>

      <u-list
        v-if="orderInfo.RentDataInfos && orderInfo.RentDataInfos.length > 0"
      >
        <u-list-item
          v-for="(item, index2) in orderInfo.RentDataInfos"
          :key="item.RentDataName"
        >
          <u-cell
            :title="item.RentDataName"
            :value="'￥' + item.RentDataAmount"
          >
          </u-cell>
        </u-list-item>
      </u-list>

      <u-cell-group style="margin-top: 10upx">
        <u-cell title="共计" :border="false">
          <p slot="value" style="color: red">￥{{ orderInfo.TotlePriceIn }}</p>
        </u-cell>
      </u-cell-group>
      <u-cell-group style="margin-top: 10upx" v-if="orderInfo.TotlePriceIn > 0">
        <u-cell title="微信">
          <u-radio-group v-model="wxChcek" slot="value" @change="groupChange">
            <u-radio
              ref="radio1_check"
              name="wxChcek"
              activeColor="#29B7A3"
              @change="radioChange"
            >
            </u-radio>
          </u-radio-group>
        </u-cell>
      </u-cell-group>
    </view>

    <view class="" v-if="orderId != ''">
      <u-cell :border="false">
        <p slot="title" style="font-size: 16px">医生姓名</p>
        <p slot="value" style="font-size: 16px">{{ docName }}</p>
      </u-cell>
      <u-cell :border="false">
        <p slot="title" style="font-size: 16px">就诊人</p>
        <p slot="value" style="font-size: 16px">{{ patName }}</p>
      </u-cell>
      <u-cell :border="false">
        <p slot="title" style="font-size: 16px">金额</p>
        <p slot="value" style="font-size: 16px">{{ '￥' + needMoeny }}</p>
      </u-cell>
    </view>

    <view style="height: 80px"></view>

    <!-- 需要钱的治疗订单支付 -->
    <u-button
      v-if="id && orderInfo.TotlePriceIn > 0"
      type="success"
      @click="payMoeny"
      :disabled="disabled"
      shape="circle"
      :text="'支付￥' + orderInfo.TotlePriceIn"
      customStyle="width:90%;bottom: 30px;position: fixed; left: 50%;transform: translateX(-50%);backgroundColor:#29B7A3;color:#fff"
    >
    </u-button>

    <!-- 不要钱的治疗订单支付 -->
    <u-button
      v-if="id && orderInfo.TotlePriceIn == 0"
      type="success"
      @click="sureOrder"
      :disabled="disabled"
      shape="circle"
      text="确认订单"
      customStyle="width:90%;bottom: 30px;position: fixed; left: 50%;transform: translateX(-50%);backgroundColor:#29B7A3;color:#fff"
    >
    </u-button>

    <!-- 问诊支付 -->
    <u-button
      v-if="orderId"
      type="success"
      @click="payMoeny"
      shape="circle"
      :loading="disabled"
      :text="'支付￥' + needMoeny"
      customStyle="width:90%;bottom: 30px;position: fixed; left: 50%;transform: translateX(-50%);backgroundColor:#29B7A3;color:#fff"
    >
    </u-button>

    <u-modal
      :show="show"
      title="温馨提示"
      content="是否放弃支付"
      @confirm="confirm"
      @cancel="cancel"
      :showCancelButton="true"
      confirmText="继续支付"
      cancelText="放弃支付"
    >
    </u-modal>
    <u-modal
      :show="show1"
      title="温馨提示"
      content="恭喜您,支付成功,点击确定即可和医生进行聊天"
      @confirm="confirm1"
    ></u-modal>
    <u-modal
      :show="show2"
      title="支付成功"
      :content="payContent"
      @confirm="confirm2"
      confirmText="查看订单"
      :showCancelButton="true"
      cancelText="确定"
      @cancel="toPlat"
      :showConfirmButton="false"
    >
    </u-modal>
    <u-modal
      :show="show3"
      title="支付成功"
      content="您可以前往附近治疗点，出示医嘱二维码进行治疗"
      @confirm="confirm2"
      confirmText="查看订单"
      :showCancelButton="true"
      cancelText="去治疗"
      @cancel="toTreatment"
    ></u-modal>
    <u-modal
      :show="show4"
      title="温馨提示"
      content="计划正在生成中"
      @confirm="confirm5"
      :showCancelButton="false"
      confirmText="手动刷新"
    ></u-modal>
    <u-notify :message="notifyMessage" :show="showNotifyMessage"></u-notify>
    <u-modal
      :show="showContents"
      confirmText="确定"
      confirmColor="#29B7A3"
      @confirm="showContents = false"
      :heigth="300"
    >
      <view
        v-html="contentArg"
        slot="default"
        :style="'max-height:' + windowHeight + 'px;' + 'text-align:left'"
      >
      </view>
    </u-modal>
    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script>
import { getTreatOrderInfo } from '@/api/consult.js';
import { payNoNeedMoeny, getWxPayInfo, getUserAddrInfo } from '@/api/order.js';
import { TrainingClientEvent } from '@/utils/eventKeys.js';
import { startTrainingProgram, payOverGetPlatId } from '@/api/training';
import MPTag from '@/components/mp-tag/u-tag.vue';
import {
  briGetAge,
  idCardTrue,
  idCardGetSex,
  idCardGetBri,
} from '@/utils/utils';
const app = getApp();
export default {
  components: {
    MPTag,
  },
  data() {
    return {
      mailMethod: '邮寄',
      show4: false,
      show3: false,
      show2: false,
      show1: false,
      showContents: false,
      patName: '',
      docName: '',
      orderId: '', //问诊订单
      id: '', // 处方id
      payType: {
        Id: '',
      },
      orderInfo: {},
      addrInfo: {},
      show: false,
      showNotifyMessage: false,
      notifyMessage: '',
      treatOrderNo: '',
      needMoeny: '',
      disabled: false,
      radioCheck: true,
      wxChcek: 'wxChcek',
      agrCheck: '',
      radioValue: '',
      radioValue1: '',
      num1: 0, //用于区分是否是重复选中
      num: 0,
      contentArg: '',
      ConsultId: '',
      windowHeight: 0,
      payOverGetToPlatId: '',
      testCount: 0,
      payContent: '',
      smQuery: {
        UserId: app.globalData.userInfo.Id,
        Name: '',
        Sex: '',
        Birthday: '',
        Age: null,
        UserCertificates: [
          {
            CertificateType: 'idCard',
            CertificateValue: '',
          },
        ],
      },
      sexList: [
        {
          name: '男',
          checked: false,
        },
        {
          name: '女',
          checked: false,
        },
      ],
      userInfo: {},
      showImagePos: {},
    };
  },
  onLoad(option) {
    this.patName = app.globalData.userInfo.Name;
    this.userInfo = app.globalData.userInfo;
    // 获取处方信息
    if (option.id) {
      this.id = option.id;
      this.getPayOrder();
      this.getPayAddrInfo();
    } else if (option.orderId) {
      // 问诊订单信息
      this.orderId = option.orderId;
      this.docName = option.docName;
      this.needMoeny = option.needMoeny;
      this.ConsultId = option.consultId;
      this.payType.Id = option.PaymentId;
    }
    this.getSysInfo();
    this.onShowUserInfo();
  },
  onReady() {
    if (this.userInfo.WorkflowStatus !== 2) {
      this.$nextTick(() => {
        const query = uni.createSelectorQuery().in(this);
        query
          .select('#targetComponent')
          .boundingClientRect((rect) => {
            // rect 包含了组件的位置信息
            console.log('组件位置信息：', rect);
            if (rect && rect.id) {
              this.showImagePos = {
                top: rect.top - 30,
              };
            }
          })
          .exec();
      });
      this.showCamraImage = true;
    } else {
      this.showCamraImage = false;
    }
  },
  methods: {
    onShowUserInfo() {
      if (this.userInfo.WorkflowStatus != 2) {
        if (this.userInfo.Name) {
          this.smQuery.Name = this.userInfo.Name;
        }
        if (this.userInfo.Sex) {
          this.smQuery.Sex = this.userInfo.Sex;
          this.sexList.map((item, index) => {
            item.checked = item.name === this.userInfo.Sex ? true : false;
          });
        }
        if (this.userInfo.Age) {
          this.smQuery.Age = this.userInfo.Age;
        }
        if (this.userInfo.UserCertificates[0]?.CertificateValue) {
          this.smQuery.UserCertificates[0].CertificateValue =
            this.userInfo.UserCertificates[0].CertificateValue;
        }
      }
    },
    onNameChange(e) {
      getApp().changeUserInfo('Name', e);
    },
    radioClick(name) {
      console.log(name);
      this.sexList.map((item, index) => {
        item.checked = index === name ? true : false;
      });
      this.smQuery.Sex = this.sexList[name].name;
      getApp().changeUserInfo('Sex', this.sexList[name].name);
    },
    IdCardblur(e) {
      getApp().changeUserInfo('idCard', e);
      if (e && !idCardTrue(e)) {
        this.$refs.uToast.show({
          message: '身份证格式输入错误',
          type: 'error',
        });
      } else if (e && idCardTrue(e)) {
        this.smQuery.Sex = idCardGetSex(e);
        this.smQuery.Birthday = idCardGetBri(e);
        this.smQuery.Age = briGetAge(this.smQuery.Birthday);
      }
    },
    ocrSuccess(e) {
      console.log('识别结果', e);
      this.$log.info('使用了身份证识别，并识别成功');
      if (e.type === 'onSuccess') {
        const data = e.detail;
        this.smQuery.UserCertificates[0].CertificateValue = data.id.text;
        this.smQuery.Sex = data.gender.text;
        this.smQuery.Name = data.name.text;
        this.smQuery.Age = briGetAge(data.birth.text);
        getApp().changeUserInfo('Name', data.name.text);
        getApp().changeUserInfo('Sex', data.gender.text);
        getApp().changeUserInfo('Age', this.smQuery.Age);
        getApp().changeUserInfo('idCard', data.id.text);
      }
    },
    Ageblur(e) {
      getApp().changeUserInfo('Age', Number(e));
    },
    mailMethodChange(e) {
      console.log('e', e);
      this.mailMethod = e;
    },
    accAdd(arg1, arg2) {
      var r1, r2, m;
      try {
        r1 = arg1.toString().split('.')[1].length;
      } catch (e) {
        r1 = 0;
      }
      try {
        r2 = arg2.toString().split('.')[1].length;
      } catch (e) {
        r2 = 0;
      }
      m = Math.pow(10, Math.max(r1, r2));
      return (arg1 * m + arg2 * m) / m;
    },
    getSysInfo() {
      uni.getSystemInfo({
        success: (res) => {
          this.windowHeight = res.windowHeight * 0.75;
        },
      });
    },
    // 查看协议
    seeAgreements(item) {
      console.log('item', item);
      this.contentArg = item.Remark;
      this.showContents = true;
    },
    groupChange(n) {
      if (n == this.radioValue && this.num == 0) {
        // 第一次相等即执行以下代码
        this.num++;
      } else {
        // 第一次后相等即执行以下代码
        // 置空 wxChcek 即取消选中的值
        this.wxChcek = '';
        // 初始化 num
        this.num = 0;
      }
    },
    // 当切换其他选项的时候此方法才会执行
    radioChange(n) {
      this.radioValue = n;
      // 切换选项后需要初始化 num
      this.num = 0;
    },

    groupChange1(n) {
      if (n == this.radioValue1 && this.num1 == 0) {
        // 第一次相等即执行以下代码
        this.num1++;
      } else {
        // 第一次后相等即执行以下代码
        // 置空 wxChcek 即取消选中的值
        this.agrCheck = '';
        // 初始化 num
        this.num1 = 0;
      }
    },
    // 当切换其他选项的时候此方法才会执行
    radioChange1(n) {
      this.radioValue1 = n;
      // 切换选项后需要初始化 num
      this.num1 = 0;
    },
    // 重新选择收获地址
    toChangeAddr() {
      uni.navigateTo({
        url: '/subPackIndex/user/addressMgr?isChange=no',
      });
    },
    // 通过选择回来的数据更新地址
    getBeforData(item) {
      console.log('item', item);
      // this.addrInfo = {}
      this.addrInfo = JSON.parse(JSON.stringify(item));
    },
    // 添加收货地址
    addAddress() {
      uni.navigateTo({
        url: '/subPackIndex/user/addOrEditAddress',
      });
    },
    // 获取处方的订单信息
    async getPayOrder() {
      uni.showLoading({
        title: this.$loadingMsg,
      });
      let rsp = await getTreatOrderInfo(this.id);
      if (rsp.Type !== 200) {
        uni.showToast({
          title: rsp.Content,
          icon: 'none',
        });
        uni.hideLoading();
        return;
      }
      rsp.Data.TotlePriceIn = this.accAdd(
        rsp.Data.RentDataMoney,
        rsp.Data.TreatOrder.Price
      );
      this.orderInfo = rsp.Data;
      this.payType = rsp.Data.TreatOrder.Payment;
      this.$nextTick(() => {
        uni.hideLoading();
      });
    },
    // 获取处方用户地址信息
    async getPayAddrInfo() {
      let obj = {
        PageCondition: {
          PageIndex: 1,
          PageSize: 1000,
        },
        FilterGroup: {
          Rules: [
            {
              Field: 'UserId',
              Value: app.globalData.userInfo.Id,
              Operate: 3,
            },
          ],
          Operate: 1,
        },
      };
      let rsp = await getUserAddrInfo(obj);
      if (rsp.Type === 200 && rsp.Data.Rows.length > 0) {
        const isDef = rsp.Data.Rows.filter((s) => s.IsDefault);
        this.addrInfo = isDef.length > 0 ? isDef[0] : rsp.Data.Rows[0];
      }
    },
    // 监听返回事件
    leftClick() {
      this.show = true;
    },
    confirm() {
      this.show = false;
    },
    cancel() {
      if (this.id) {
        // uni.navigateBack()
        uni.reLaunch({
          url: '/subPackIndex/user/treatmentOrder',
        });
      } else {
        uni.reLaunch({
          url: '/subPackInquiry/inquiryOrder',
        });
      }
    },
    confirm1() {
      this.show1 = false;
      // uni.reLaunch({
      // 	url: '/subIM/index'
      // })
      let backPath = '/pages/interview/index';
      uni.navigateTo({
        url:
          '/subPackChat/sessionChatPage?consultId=' +
          this.ConsultId +
          '&backPath=' +
          backPath,
      });
    },
    confirm2() {
      this.toUpdate();
      uni.navigateTo({
        //orderInfo.Id id=5060773640080806917
        url: '/subPackIndex/user/treatmentDetail?id=' + this.id,
      });
    },
    async toPlat() {
      this.show2 = false;

      uni.showLoading({
        title: '请求中...',
      });
      let userId = getApp().globalData.userInfo.Id;
      let r = await startTrainingProgram(userId, this.payOverGetToPlatId);
      uni.hideLoading();
      if (r.Type != 200) {
        uni.showToast({
          title: r.Content,
          icon: 'none',
        });
        return;
      }

      uni.switchTab({
        url: '/pages/plan/index',
      });
    },
    toTreatment() {
      this.show3 = false;
      uni.reLaunch({
        //orderInfo.Id
        url: '/subPackIndex/community/index',
      });
    },
    // 唤起微信支付
    payMoeny() {
      if (!this.orderId && this.wxChcek == '') {
        this.$refs.uToast.show({
          message: '请您勾选支付方式',
          type: 'error',
        });
        return;
      }
      if (
        !this.orderId &&
        !this.agrCheck &&
        this.orderInfo.RentDataInfos.length > 0 &&
        this.orderInfo.RentAgreements.length > 0
      ) {
        this.$refs.uToast.show({
          message: '请您同意设备使用协议',
          type: 'error',
        });
        return;
      }
      if (this.disabled) {
        this.$refs.uToast.show({
          message: '您点太快了，稍等下',
          type: 'error',
        });
        return;
      }
      if (this.userInfo.WorkflowStatus != 2) {
        const flag = this.getPatUserInfo();
        if (!flag) {
          return;
        }
      }
      if (this.id) {
        this.onIsNeedAddress();
      } else {
        this.payWenZhenOrder();
      }
    },
    getPatUserInfo() {
      if (!this.smQuery.Name) {
        this.$refs.uToast.show({
          message: '请输入您的名字',
          type: 'error',
        });
        return false;
      }
      if (!this.smQuery.Sex) {
        this.$refs.uToast.show({
          message: '请选择您的性别',
          type: 'error',
        });
        return false;
      }
      if (!this.smQuery.Age) {
        this.$refs.uToast.show({
          message: '请输入您的年龄',
          type: 'error',
        });
        return false;
      }
      return true;
    },
    // 判断是否需要收获地址
    onIsNeedAddress() {
      if (
        this.mailMethod === '邮寄' &&
        !this.addrInfo?.Address &&
        this.orderInfo.IsDelivery
      ) {
        uni.showModal({
          content:
            '您还未填写收货地址，是否继续支付？（未填写地址后续将由医助与您联系）',
          cancelText: '继续支付',
          confirmText: '完善地址',
          success: (res) => {
            if (res.confirm) {
              this.addAddress();
            } else {
              this.payPrescription();
            }
          },
        });
      } else {
        this.payPrescription();
      }
    },
    //处方的支付
    async payPrescription() {
      if (this.userInfo.WorkflowStatus != 2) {
        getApp().saveAuthen(this.smQuery);
      }
      this.addrInfo && this.addrInfo.Id && delete this.addrInfo.Id;
      let data = {
        OrderNo: this.orderInfo.TreatOrderNo,
        PayDescription: '订单支付',
        PaymentId: this.payType.Id,
        OpenId: app.globalData.openId,
        TradeType: 1,
      };
      if (this.mailMethod === '邮寄') {
        data.OrderAddresss =
          this.addrInfo && this.addrInfo.Name && this.orderInfo.IsDelivery
            ? [this.addrInfo]
            : [];
      }
      this.disabled = true;
      let orderInfo = await getWxPayInfo(data);
      console.log('orderInfo', orderInfo);
      if (orderInfo.Type == 200) {
        uni.requestPayment({
          provider: 'wxpay',
          timeStamp: orderInfo.Data.timeStamp, // 时间戳（单位：秒）
          nonceStr: orderInfo.Data.nonceStr, // 随机字符串
          package: orderInfo.Data.package, // 固定值
          signType: orderInfo.Data.signType, //固定值
          paySign: orderInfo.Data.paySign, //签名
          success: (resss) => {
            console.log('支付成功', resss);
            if ((resss.errMsg = 'requestPayment:ok')) {
              getApp().subscribeMessage(() => {
                uni.redirectTo({
                  url:
                    './payResults?orderInfo=' +
                    encodeURIComponent(JSON.stringify(this.orderInfo)) +
                    '&id=' +
                    this.id,
                });
              });
              this.$log.info(
                `${this.$envVersion || ''}:有钱到账啦!!! 来源是：处方支付，患者是：${this.patName}`
              );
            }
          },
          complete: () => {
            this.disabled = false;
          },
        });
      } else {
        uni.showModal({
          content: orderInfo.Content,
          showCancel: false,
          complete: () => {
            this.showNotifyMessage = true;
            this.notifyMessage = orderInfo.Content;
            this.disabled = false;
          },
        });
      }
    },
    // 问诊的支付
    async payWenZhenOrder() {
      this.disabled = true;
      let data = {
        OrderNo: this.orderId,
        PayDescription: '订单支付',
        PaymentId: this.payType.Id,
        OrderAddresss: [],
        OpenId: app.globalData.openId,
        TradeType: 1,
      };
      let orderInfo = await getWxPayInfo(data);
      console.log('orderInfo', orderInfo);
      if (orderInfo.Type == 200) {
        uni.requestPayment({
          provider: 'wxpay',
          // appId:'wx5ed2e839af97c0ca',
          timeStamp: orderInfo.Data.timeStamp, // 时间戳（单位：秒）
          nonceStr: orderInfo.Data.nonceStr, // 随机字符串
          package: orderInfo.Data.package, // 固定值
          signType: orderInfo.Data.signType, //固定值
          paySign: orderInfo.Data.paySign, //签名
          success: (resss) => {
            console.log('支付成功', resss);
            if ((resss.errMsg = 'requestPayment:ok')) {
              // this.show1 = true
              this.confirm1();
              this.$log.info(
                `${this.$envVersion || ''}:有钱到账啦!!!来源是：问诊或者咨询费用，患者是：${this.patName},，医生是:${this.docName}`
              );
            }
          },
          complete: () => {
            this.disabled = false;
          },
        });
      } else {
        this.showNotifyMessage = true;
        this.notifyMessage = orderInfo.Content;
        this.disabled = false;
      }
    },
    // 获取计划的id
    async getPlatInfo() {
      let res = await payOverGetPlatId({
        visitId: this.orderInfo.ConsultId,
      });
      uni.hideLoading();
      // this.testCount ++
      // if(this.testCount < 3 ){
      // 	res.Data = ''
      // }
      if (res.Type == 200 && res.Data) {
        this.show4 = false;
        this.payOverGetToPlatId = res.Data;
        if (this.orderInfo.IsDelivery) {
          this.payContent =
            '已为您生成居家康复计划，请按计划打卡训练，治疗项目中包含的耗材/设备将在3-5天内通过快递邮寄给您';
        } else {
          this.payContent = '已为您生成居家康复计划，请按计划打卡训练';
        }
        this.show2 = true;
      } else {
        this.show4 = true;
      }
    },
    confirm5() {
      this.getPlatInfo();
    },
    // 不需要钱的处方之支付
    async sureOrder() {
      if (this.userInfo.WorkflowStatus != 2) {
        const flag = this.getPatUserInfo();
        if (!flag) {
          return;
        }
      }
      if (this.userInfo.WorkflowStatus != 2) {
        getApp().saveAuthen(this.smQuery);
      }
      if (this.orderInfo.RentDataInfos.length == 0) {
        this.agrCheck = true;
      } else if (
        this.orderInfo.RentDataInfos.length > 0 &&
        this.orderInfo.RentAgreements == 0
      ) {
        this.agrCheck = true;
      }
      if (!this.orderId && !this.agrCheck) {
        this.$refs.uToast.show({
          message: '请您同意设备使用协议',
          type: 'error',
        });
        return;
      }
      if (this.orderInfo.TreatType == 2) {
        //社区
        this.show3 = true;
      } else {
        if (
          this.mailMethod === '邮寄' &&
          this.orderInfo.IsDelivery &&
          !this.addrInfo?.Address
        ) {
          uni.showModal({
            content:
              '您还未填写收货地址，是否继续支付？（未填写地址后续将由医助与您联系）',
            cancelText: '继续支付',
            confirmText: '完善地址',
            success: (res) => {
              if (res.confirm) {
                this.addAddress();
              } else {
                this.onIsNeedAddressNoMoeny();
              }
            },
          });
        } else {
          this.onIsNeedAddressNoMoeny();
        }
      }
    },
    async onIsNeedAddressNoMoeny() {
      //居家
      if (this.addrInfo && this.addrInfo.Id) {
        delete this.addrInfo.Id;
      }
      let data = {
        OrderNo: this.orderInfo.TreatOrderNo,
      };
      if (this.orderInfo.IsDelivery && this.mailMethod === '邮寄') {
        data.OrderAddresss =
          this.addrInfo && this.addrInfo.Name && this.orderInfo.IsDelivery
            ? [this.addrInfo]
            : [];
      }
      let res = await payNoNeedMoeny(data);
      if (res.Type == 200) {
        getApp().subscribeMessage(() => {
          uni.showLoading({
            title: '正在生成计划，请稍后...',
          });
          uni.redirectTo({
            url:
              './payResults?orderInfo=' +
              encodeURIComponent(JSON.stringify(this.orderInfo)) +
              '&id=' +
              this.id,
          });
        });
      } else {
        this.$refs.uToast.show({
          message: res.Content,
          type: 'error',
        });
      }
    },
    toUpdate() {
      let pages = getCurrentPages();
      const prePage = pages[pages.length - 2]; //上一个页面
      if (prePage.$vm.rest) {
        prePage.$vm.rest();
      }
    },
  },
};
</script>

<style scoped lang="scss">
.u-page__tag-item {
  margin-right: 20rpx;
  margin-bottom: 10rpx;
}

.box {
  height: 100vh;
  background-color: #f7f7f7;
  padding: 200rpx 32rpx;

  /deep/ .u-radio-group {
    background: '#f7f7f7' !important;
    padding: 20rpx !important;
  }

  /deep/ .u-modal__content {
    max-height: 600px;
    overflow-y: auto;
  }

  /deep/ .u-radio-group {
    flex: 0 !important;
  }

  /deep/ .u-radio__icon-wrap {
    // background-color: #29B7A3 !important;
  }

  /deep/ .u-cell {
    background-color: white !important;
  }

  /deep/ .u-list {
    height: auto !important;
  }

  /deep/ .u-radio {
    margin-right: 0 !important;
  }
}
</style>
