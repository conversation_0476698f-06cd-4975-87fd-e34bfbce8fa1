<template>
  <view class="box">
    <u-list @scrolltolower="scrolltolower" v-if="indexList.length > 0">
      <u-list-item v-for="(item, index) in indexList" :key="item.id">
        <u-cell @click="goToSeePrescription(index)" :border="false">
          <p
            slot="value"
            :style="'color:' + item.StateColor"
            style="transform: translateY(-46px); font-size: 16px"
          >
            {{ item.StateName }}
          </p>

          <view
            slot="title"
            style="font-weight: 500; font-size: 16px"
            :style="item.State == 3 ? 'color:#999999' : ''"
          >
            <p>就诊人： {{ item.UserName }}</p>
            <p style="margin: 10px 0">
              诊断：{{ item.UserRxIcds[0].DiagnoseName || '' }}
            </p>
          </view>
          <view
            slot="label"
            class="display-style1"
            :style="item.State == 3 ? 'color:#999999' : ''"
          >
            <u-avatar
              :src="item.Doctor.HeadImg"
              size="50"
              slot="icon"
            ></u-avatar>
            <view
              style="margin-left: 20rpx; color: #666666; font-size: 16px"
              :style="item.State == 3 ? 'color:#999999' : ''"
            >
              <p>下达人：{{ item.Doctor.Name }}</p>
              <p style="margin-top: 20rpx">{{ item.ActualSendTime }}</p>
            </view>
          </view>
        </u-cell>
      </u-list-item>
    </u-list>
    <u-empty
      mode="data"
      icon="http://cdn.uviewui.com/uview/empty/data.png"
      text="当前没有治疗方案"
      v-if="indexList.length == 0"
    >
    </u-empty>
  </view>
</template>

<script>
import { getPrescriptionList } from '@/api/consult.js';
import { dateFormat } from '@/utils/validate';
const debounce = uni.$u.debounce;
export default {
  data() {
    return {
      indexList: [],
      qurey: {
        pageindex: 1,
        pagesize: 10,
      },
    };
  },
  onLoad() {
    this.getList();
  },
  onBackPress(e) {
    console.log('e', e);
  },
  methods: {
    async getList() {
      uni.showLoading({
        title: '正在加载数据',
      });
      let res = await getPrescriptionList(this.qurey);
      console.log('获取数据成功', res);
      if (res.Type == 200) {
        res.Data.forEach((e) => {
          e.ActualSendTime = e.ActualSendTime
            ? dateFormat(e.ActualSendTime)
            : '--';
          // if (e.State == 1 && e.RxExpireMinutes > 0) {
          // 	e.StateName = '未执行'
          // 	e.StateColor = 'red'
          // } else if (e.State == 2) {
          // 	e.StateName = '已执行'
          // 	e.StateColor = '#29B7A3'
          // } else if (e.State == 3 || e.RxExpireMinutes <= 0) {
          // 	e.StateName = '已失效'
          // 	e.StateColor = '#999999'
          // }
          if (e.CalculateState == 1) {
            e.StateName = '未执行';
            e.StateColor = 'red';
          } else if (e.CalculateState == 2) {
            e.StateName = '已执行';
            e.StateColor = '#29B7A3';
          } else if (e.CalculateState) {
            e.StateName = '已失效';
            e.StateColor = '#999999';
          }
          this.indexList.push(e);
        });
        this.$nextTick(() => {
          uni.hideLoading();
        });
      } else {
        this.$nextTick(() => {
          uni.hideLoading();
        });
      }
    },
    scrolltolower() {
      debounce(() => {
        this.qurey.pageindex++;
        this.getList();
      });
    },
    // 处方订单的id
    goToSeePrescription(index) {
      console.log('id', index);
      uni.navigateTo({
        url: `./detail?id=${this.indexList[index].PrescriptionId}&type=${this.indexList[index].CalculateState}`,
      });
    },
    rest() {
      this.qurey.pageindex = 1;
      this.indexList = [];
      this.getList();
    },
  },
};
</script>

<style scoped lang="scss">
.box {
  height: 100vh;
  background-color: #f7f7f7;

  /deep/ .u-cell {
    background-color: white;
    box-shadow: 2.5px 3px 9px 0px rgba(0, 0, 0, 0.08);
    border-radius: 12rpx;
  }

  /deep/ .u-cell__body__content {
    overflow: hidden;
  }

  /deep/ .u-cell__title {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  /deep/ .u-list-item {
    margin-bottom: 32rpx;
    transform: translateY(10px);
  }

  /deep/ .insideStyle {
    padding: 0 32rpx;
  }

  .box-each {
    height: 80px;
    width: 100%;
    background-color: white;
    margin-bottom: 20upx;
    padding: 10upx;
    border-radius: 10upx;
  }
}
</style>
