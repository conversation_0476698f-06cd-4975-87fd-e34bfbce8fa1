<template>
  <view class="container">
    <u-list @scrolltolower="scrolltolower" v-if="indexList.length > 0">
      <u-list-item v-for="(item, index) in indexList" :key="index">
        <u-cell>
          <view class="" slot="title">
            <p
              style="
                font-size: 16px;
                color: #666666;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              "
            >
              {{ item.InDate2 || '' }} {{ item.OrganizationName || '' }}
            </p>
            <p
              style="
                font-size: 16px;
                color: #333333;
                font-weight: 600;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                margin-top: 24rpx;
              "
            >
              {{ item.DoctorName || '' }} {{ item.DepartmentName || '' }}
            </p>
            <p
              style="
                font-size: 16px;
                color: #999999;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                margin-top: 24rpx;
              "
            >
              诊断：{{ item.diagnoseObj.DiagnoseName || '暂无诊断信息' }}
            </p>
          </view>
          <u-button
            customStyle="width:80px;height:40px;line-height:40px"
            shape="circle"
            color="#29B7A3"
            slot="value"
            text="导入病历"
            @click="chooseMedical(item)"
          ></u-button>
        </u-cell>
      </u-list-item>
    </u-list>
    <u-empty
      mode="data"
      icon="http://cdn.uviewui.com/uview/empty/data.png"
      text="没有病历"
      v-else
    >
    </u-empty>
  </view>
</template>

<script>
const app = getApp();
import { getUserCaseInfo } from '@/api/record.js';
import { dateFormat } from '@/utils/validate.js';
import { getVisit } from '@/api/record.js';
export default {
  data() {
    return {
      indexList: [],
      query: {
        userId: app.globalData.userInfo.Id,
        dateSerachType: 0,
        type: 0,
        pageindex: 1,
        pagesize: 20,
        IsSelfBuild: true,
      },
    };
  },
  onLoad() {
    this.getList();
  },
  methods: {
    // 获取病历列表
    async getList() {
      uni.showLoading({
        title: this.$loadingMsg,
      });
      let res = await getUserCaseInfo(this.query);
      if (res.Type == 200 && res.Data.length > 0) {
        res.Data.forEach((e) => {
          e.InDate2 = dateFormat(e.InDate, 'YYYY-MM-DD');
          const index =
            e.VisitDiagnoses &&
            e.VisitDiagnoses.findIndex((k) => {
              return k.DiagnoseTypeName == '诊断';
            });
          const index2 =
            e.VisitDiagnoses &&
            e.VisitDiagnoses.findIndex((k) => {
              return k.DiagnoseTypeName == '症状描述';
            });
          if (index > -1) {
            e.diagnoseObj = e.VisitDiagnoses[index];
          }
          if (index2 > -1) {
            e.diagnoseObj2 = e.VisitDiagnoses[index2];
          }
          if (e.IsSelfBuild) {
            this.indexList.push(e);
          }
        });
      }
      this.$nextTick(() => {
        uni.hideLoading();
      });
    },
    // 滚动加载数据
    scrolltolower() {
      this.query.pageindex++;
      this.getList();
    },
    //选择病历
    async chooseMedical(item) {
      const res = await getVisit({
        visitId: item.Id,
      });
      if (res.Type === 200) {
        item.VisitReportDetails = res.Data.VisitReportDetails;
        let pages = getCurrentPages();
        const prePage = pages[pages.length - 2]; //上一个页面
        prePage.$vm.checkIsActive();
        prePage.$vm.getNextPageInfo(item);
        uni.navigateBack();
      }
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  height: 100vh;
  background-color: #f7f7f7;
  padding-top: 32rpx;

  /deep/ .insideStyle {
    padding: 0 32rpx;
  }

  // /deep/ .u-cell {
  // 	margin-bottom: 32	rpx;
  // 	box-shadow: 2.5px 3px 9px 0px rgba(0, 0, 0, 0.08);
  // 	border-radius: 12rpx;
  // }

  /deep/ .u-list-item {
    background-color: white !important;
    margin-bottom: 32rpx !important;
    box-shadow: 2.5px 3px 9px 0px rgba(0, 0, 0, 0.08);
    border-radius: 12rpx;
  }

  /deep/ .u-cell__body__content {
    width: 70%;
  }

  /deep/ .u-cell__title {
    width: 80%;
  }
}
</style>
