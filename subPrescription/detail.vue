<template>
  <view class="box">
    <MpLoadingPage :loadingType="loadingType">
      <view class="box-top">
        <view
          class="box-top-time"
          v-if="option.type == 1 && (time.hours || time.minutes)"
        >
          <p>方案还有{{ time.hours }}小时{{ time.minutes }}分钟失效</p>
        </view>
        <view
          style="
            width: 100%;
            background-color: #ffffff;
            padding: 24rpx 32rpx;
            box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.1);
            border-radius: 16rpx;
            height: calc(100% - 136px);
          "
        >
          <view
            style="
              font-weight: 700;
              font-size: 18px;
              margin-bottom: 24rpx;
              color: #000;
            "
          >
            {{ dataObj.PrescriptionName }}
          </view>
          <view class="box-top-disposal">
            <p style="font-weight: 700; font-size: 18px">处理意见：</p>
            <p
              v-for="(j, index) in disposal"
              :key="index"
              style="color: #666666"
            >
              {{ j }}
            </p>
          </view>
          <view
            class="box-top-detailbox"
            v-for="(item, index) in prescriptionDetails"
            :key="item.Id"
          >
            <view class="display-style" style="font-weight: 600">
              <p>{{ item.MoName }}</p>
              <!-- <p>
								<span style="color: red;">
									<span>￥{{item.Price}}</span>
									<span v-if="item.MoItemChargeMode === 1">/穴位</span>
									<span v-if="item.MoItemChargeMode === 2">/次</span>
									<span v-if="item.MoItemChargeMode === 3">/天</span>
									<span v-if="item.MoItemChargeMode === 5">/月</span>
								</span>
								<span style="color: #b5b5b5;text-decoration: line-through;" v-if="item.ShowAmount > item.Price">
									<span>￥{{item.ShowAmount}}</span>
									<span v-if="item.MoItemChargeMode === 1">/穴位</span>
									<span v-if="item.MoItemChargeMode === 2">/次</span>
									<span v-if="item.MoItemChargeMode === 3">/天</span>
									<span v-if="item.MoItemChargeMode === 5">/月</span>
								</span>
							</p> -->
            </view>
            <view style="color: #b5b5b5; font-size: 14px">
              <view v-if="item.MoItemMethod === 0">
                <span v-if="item.MoItemChargeMode !== 5"
                  >{{ item.MoDay }}天</span
                >
                <span v-else>{{ item.MoMonth }}个月</span>
              </view>
              <view v-if="item.MoItemMethod === 1">
                {{ item.FreqDay }}天{{ item.Freq }}次 {{ item.MoDay }}天
                {{ item.TotalCount }}次
              </view>
              <view v-if="item.MoItemMethod === 3">
                {{ item.TotalCount }}次
              </view>
              <view v-if="item.MoItemMethod === 5">
                数量： {{ item.TotalCount }}
              </view>
            </view>
            <view
              class="display-style box-top-detailbox-dic"
              @click="lookMore(index)"
              v-if="
                item.MoItemMethod !== 2 &&
                item.MoItemMethod !== 5 &&
                item.MoItemMethod !== 6
              "
            >
              <view
                class="box-top-detailbox-dic-bom"
                v-if="item.MoItemMethod === 1"
                :class="
                  lookMoreIndex == index
                    ? 'box-top-detailbox-dic-lookMoreInfo'
                    : ''
                "
              >
                <span v-for="(o, index2) in item.AcuPoints" :key="o.Name"
                  >{{ o.Name }}
                  <span v-if="index2 != item.AcuPoints.length - 1">、</span>
                </span>
              </view>
              <div v-if="item.MoItemMethod === 0">
                <block v-for="(v, index2) in item.otherTextList" :key="index2">
                  <view
                    class="box-top-detailbox-dic-bom"
                    @click.stop="handleActionClick(index, index2)"
                    style="margin-bottom: 24rpx"
                    :class="
                      lookAction === String(index) + String(index2)
                        ? 'box-top-detailbox-dic-lookMoreInfo'
                        : ''
                    "
                  >
                    {{ v }}
                  </view>
                </block>
              </div>
              <view
                class="box-top-detailbox-dic-bom"
                v-if="item.MoItemMethod === 3"
                :class="
                  lookMoreIndex == index
                    ? 'box-top-detailbox-dic-lookMoreInfo'
                    : ''
                "
              >
                <span v-for="(o, index2) in item.Scales" :key="o.Name"
                  >{{ o.Name }}
                  <span v-if="index2 != item.Scales.length - 1">、</span></span
                >
              </view>
              <view
                class="box-top-detailbox-dic-bom"
                v-if="item.MoItemMethod === 4"
                :class="
                  lookMoreIndex == index
                    ? 'box-top-detailbox-dic-lookMoreInfo'
                    : ''
                "
              >
                <span v-for="(o, index2) in item.Assists" :key="o.Id"
                  >{{ o.AssistInfoObj.ClassifyName }}
                  <span v-if="index2 != item.Assists.length - 1">、</span></span
                >
              </view>
            </view>
          </view>

          <image
            v-for="(o, index) in imgList"
            :key="index"
            :src="o"
            style="width: 100%; margin-top: 20rpx; z-index: -1"
            mode="widthFix"
          >
          </image>
        </view>
      </view>
      <view style="height: 200rpx"></view>
      <view
        style="
          width: 100%;
          background-color: #f7f7f7;
          position: fixed;
          bottom: 40rpx;
        "
      >
        <view
          v-if="
            isHaveConsult &&
            !noPayBtnText &&
            option.type == 1 &&
            dataObj.Source == 2 &&
            dataObj.TreatType == 1 &&
            isShowConsultServeRadio
          "
          style="
            flex-wrap: wrap;
            padding: 0 20rpx;
            width: 100%;
            margin-bottom: 140rpx;
          "
          class="display-style"
          @click="onClickRadioGroupDiv"
        >
          <view class="display-style1">
            <u-radio-group @change="groupChange1" v-model="agrCheck">
              <u-radio
                name="agrCheck"
                activeColor="#29B7A3"
                @change="radioChange1"
              >
              </u-radio>
            </u-radio-group>
            <span>包含与医务人员在线咨询</span>
          </view>
          <p v-if="consultPrice >= 0" style="color: #ff3b30">
            ￥{{ consultPrice }}
          </p>
        </view>
        <view
          v-if="option.type == 1"
          style="
            padding: 48rpx 34rpx;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: #ffffff;
            box-shadow: 0rpx -2rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);
            border-radius: 16rpx 16rpx 0rpx 0rpx;
            bottom: 0px;
            position: fixed;
            width: 100%;
          "
        >
          <view
            v-if="originalPrice > payBtnPrice"
            style="font-size: 30rpx; color: #333333; margin-right: 100rpx"
          >
            <text>原价：</text>
            <text style="text-decoration: line-through"
              >￥{{ originalPrice }}</text
            >
          </view>
          <u-button
            :loading="payBtnLoading"
            @click="handlePayBtnClick"
            type="success"
            shape="circle"
            :text="payBtnText"
            customStyle="flex: 1;background: linear-gradient(270deg, #FF5F5C 0%, #FE8368 100%);color:#fff;border: none"
          >
          </u-button>
        </view>
      </view>
      <u-button
        v-if="option.type !== 1 && noPayBtnText"
        type="success"
        disabled
        shape="circle"
        :text="noPayBtnText"
        customStyle="width:90%;bottom: 30px;position: fixed; left: 50%;transform: translateX(-50%);backgroundColor:#29B7A3;color:#fff;z-index: 9"
      >
      </u-button>
    </MpLoadingPage>
    <u-popup :show="showPopup" mode="bottom" @close="showPopup = false">
      <view class="popup">
        <view class="popup-top">
          <text class="popup-top-title">设备使用保证金</text>
          <text class="popup-top-label">(可退)</text>
        </view>
        <view class="popup-mid">
          本次治疗将使用到以下设备，需要缴纳设备保证金，保证金会在设备返还后退回付款账户
        </view>

        <view v-if="rentDataInfos.length > 0">
          <view
            class="display-style"
            v-for="(item, index2) in rentDataInfos"
            :key="item.RentDataName"
          >
            <text>{{ item.RentDataName }}</text>
            <text>￥{{ item.RentDataAmount }}</text>
          </view>
        </view>

        <view
          class="popup-agreements display-style1"
          v-if="rentAgreements.length > 0"
        >
          <u-checkbox-group v-model="agreementsChecked" placement="row">
            <u-checkbox shape="circle" />
          </u-checkbox-group>
          <p style="font-size: 14px">我已阅读并同意</p>
          <view
            class="display-style1"
            style="margin-left: 2px"
            v-for="(k, index2) in rentAgreements"
            @click="seeAgreements(k)"
          >
            <u--text type="success" :text="'《 ' + k.Key + '》'"></u--text>
          </view>
        </view>
        <view style="height: 100rpx"></view>
        <u-button
          v-if="payTotalAmount"
          @click="handlePayBtnSecondClick"
          :loading="payBtnLoading"
          :text="'￥' + payTotalAmount"
          customStyle="width:90%;bottom: 20rpx;position: fixed; left: 50%;transform: translateX(-50%);backgroundColor:#29B7A3;color:#fff"
        >
        </u-button>
        <u-button
          v-else
          @click="handlePayBtnSecondClick"
          :loading="payBtnLoading"
          text="确定"
          customStyle="width:90%;bottom: 20rpx;position: fixed; left: 50%;transform: translateX(-50%);backgroundColor:#29B7A3;color:#fff"
        >
        </u-button>
      </view>
    </u-popup>
    <u-modal
      :show="showContents"
      confirmText="确定"
      confirmColor="#29B7A3"
      @confirm="showContents = false"
      :heigth="300"
    >
      <view
        v-html="contentArg"
        slot="default"
        :style="'max-height:' + windowHeight + 'px;' + 'text-align:left'"
      ></view>
    </u-modal>
  </view>
</template>

<script>
const app = getApp();
import {
  setPatCheckTime,
  getPrescriptionInfo,
  getToExecute,
  quickPrescription,
  getTreatOrderInfo,
  checkExistPre,
} from '../api/consult.js';

import { dateFormat, toHoursAndMinutes } from '@/utils/validate.js';
import { sumOfTwoNumbers, ItemGroupBy } from '@/utils/utils.js';

import { getHaveConsult } from '@/api/content.js';
import {
  payNoNeedMoeny,
  getWxPayInfo,
  getUserAddrInfo,
  setOrderPaying,
} from '@/api/order.js';
import indexAnchor from '../uni_modules/uview-ui/libs/config/props/indexAnchor';
export default {
  data() {
    return {
      originalPrice: 0,
      isHavePostoperative: false,
      defaultAddress: [],
      payBtnLoading: false,
      orderInfo: {},
      payTotalAmount: 0,
      contentArg: '',
      windowHeight: 0,
      showContents: false,
      agreementsChecked: [],
      rentAgreements: [],
      rentDataInfos: [],
      showPopup: false,
      option: {
        id: '',
        type: null,
      },
      payBtnPrice: 0,
      imgList: [],
      prescriptionDetails: [],
      disposal: [],
      time: {},
      payBtnText: '',
      noPayBtnText: '',
      isShowConsultServeRadio: true,
      dataObj: {},
      isHaveConsult: false, // 是否开启了咨询
      consultPrice: 0,
      agrCheck: '',
      lookMoreIndex: null,
      loadingType: 'loading',
      lookAction: '9999',
    };
  },
  onLoad(option) {
    this.getSysInfo();
    // 获取用户地址信息
    this.getUserAddressList();
    if (option.id) {
      this.option.id = option.id;
      this.getPrescriptionDetails();
    } else if (option.RelatedObj) {
      const data = JSON.parse(decodeURI(option.RelatedObj));
      this.getQuickPrescription(data);
    }
  },
  watch: {
    agrCheck(oldValue, newValue) {
      if (!newValue) {
        this.payBtnPrice =
          (this.payBtnPrice + this.consultPrice * 1).toFixed(2) * 1;
        this.originalPrice =
          (this.originalPrice + this.consultPrice * 1).toFixed(2) * 1;
      } else {
        this.payBtnPrice =
          (this.payBtnPrice - this.consultPrice * 1).toFixed(2) * 1;
        this.originalPrice =
          (this.originalPrice - this.consultPrice * 1).toFixed(2) * 1;
      }
      if (this.payBtnPrice > 0) {
        this.payBtnText = `去支付￥${this.payBtnPrice}`;
      } else {
        this.payBtnText = `下一步`;
      }
    },
  },
  methods: {
    handleActionClick(index, index2) {
      this.lookAction = String(index) + String(index2);
    },
    async getUserAddressList() {
      let obj = {
        PageCondition: {
          PageIndex: 1,
          PageSize: 1000,
        },
        FilterGroup: {
          Rules: [
            {
              Field: 'UserId',
              Value: app.globalData.userInfo.Id,
              Operate: 3,
            },
          ],
          Operate: 1,
        },
      };
      let rsp = await getUserAddrInfo(obj);
      if (rsp.Type === 200 && rsp.Data.Rows.length > 0) {
        rsp.Data.Rows.forEach((s) => {
          if (s.Id) delete s.Id;
        });
        const isDef = rsp.Data.Rows.filter((s) => s.IsDefault);
        this.defaultAddress =
          isDef.length > 0 ? [isDef[0]] : [rsp.Data.Rows[0]];
      }
    },
    handlePayBtnSecondClick() {
      if (this.showPopup && !this.agreementsChecked.length) {
        console.log('请勾选相关协议');
        uni.showToast({
          title: '请勾选相关协议',
          icon: 'none',
        });
        return;
      }
      this.payBtnLoading = true;
      // 判断是否需要支付钱
      if (this.payTotalAmount) {
        let data = {
          OrderNo: this.orderInfo.TreatOrderNo,
          PayDescription: '订单支付',
          PaymentId: this.orderInfo.TreatOrder.Payment.Id,
          OpenId: app.globalData.openId,
          TradeType: 1,
        };
        if (this.defaultAddress.length && this.orderInfo.IsDelivery) {
          data.OrderAddresss = this.defaultAddress;
        }
        getWxPayInfo(data)
          .then((orderInfo) => {
            if (orderInfo.Type === 200) {
              uni.requestPayment({
                provider: 'wxpay',
                timeStamp: orderInfo.Data.timeStamp, // 时间戳（单位：秒）
                nonceStr: orderInfo.Data.nonceStr, // 随机字符串
                package: orderInfo.Data.package, // 固定值
                signType: orderInfo.Data.signType, //固定值
                paySign: orderInfo.Data.paySign, //签名
                success: (resss) => {
                  if ((resss.errMsg = 'requestPayment:ok')) {
                    this.$log.info(
                      `${
                        this.$envVersion || ''
                      }:有钱到账啦!!! 来源是：处方支付，患者是：${this.patName}`
                    );
                    this.onToResultPage();
                    // 将这单设置为支付中（后端这样设计的 别管为什么)
                    setOrderPaying({
                      OrderNo: this.orderInfo.OrderNo,
                    });
                  }
                },
                complete: () => {
                  uni.hideLoading();
                  this.payBtnLoading = false;
                },
              });
            } else {
              uni.showModal({
                title: '温馨提示',
                content: orderInfo.Content,
                showCancel: false,
              });
            }
          })
          .catch((err) => {
            uni.showModal({
              title: '温馨提示',
              content: res.Content,
              showCancel: false,
            });
          })
          .finally(() => {
            this.payBtnLoading = false;
          });
      } else {
        let data = {
          OrderNo: this.orderInfo.TreatOrderNo,
        };
        if (this.defaultAddress.length && this.orderInfo.IsDelivery) {
          data.OrderAddresss = this.defaultAddress;
        }
        payNoNeedMoeny(data)
          .then((res) => {
            if (res.Type === 200) {
              this.onToResultPage();
            } else {
              uni.showModal({
                title: '温馨提示',
                content: res.Content,
                showCancel: false,
              });
            }
          })
          .catch((err) => {
            uni.showModal({
              title: '温馨提示',
              content: res.Content,
              showCancel: false,
            });
          })
          .finally(() => {
            this.payBtnLoading = false;
          });
      }
    },
    onToResultPage() {
      const orderInfo = {
        IsDelivery: this.orderInfo.IsDelivery,
        Id: this.orderInfo.Id,
        TreatType: this.orderInfo.TreatType,
        OrderNo: this.orderInfo.OrderNo,
      };
      getApp().subscribeMessage(() => {
        let url =
          './payResults?orderInfo=' +
          encodeURIComponent(JSON.stringify(orderInfo));
        if (this.defaultAddress && this.defaultAddress.length > 0) {
          url +=
            '&address=' +
            encodeURIComponent(JSON.stringify(this.defaultAddress[0]));
        }
        if (this.isHavePostoperative) {
          url += '&isHavePostoperative=' + this.isHavePostoperative;
        }
        uni.redirectTo({
          url,
        });
      });
    },
    getSysInfo() {
      uni.getSystemInfo({
        success: (res) => {
          console.log('res', res);
          this.windowHeight = res.windowHeight * 0.75;
        },
      });
    },
    // 查看协议
    seeAgreements(item) {
      console.log('item', item);
      this.contentArg = item.Remark;
      this.showContents = true;
    },
    handlePayBtnClick() {
      this.getToExecute();
    },
    async getToExecute() {
      let obj = {
        PayCompany: 'kangfx',
        TreatPayCompany: 'kangfx',
        PrescriptionId: this.option.id,
        IsChecked: this.agrCheck ? true : false,
      };
      let rsp = await getToExecute(obj);
      if (rsp.Type !== 200) {
        uni.hideLoading();
        uni.showModal({
          title: '温馨提示',
          content: rsp.Content,
          showCancel: false,
        });
        return;
      }
      let resData = await getTreatOrderInfo(this.option.id);
      if (resData.Type !== 200) {
        uni.hideLoading();
        uni.showModal({
          title: '温馨提示',
          content: resData.Content,
          showCancel: false,
        });
        return;
      }
      this.orderInfo = resData.Data;
      const payTotalAmount = sumOfTwoNumbers(
        resData.Data.RentDataMoney,
        resData.Data.TreatOrder.Price
      );
      this.payTotalAmount = payTotalAmount;
      const RentAgreements = resData.Data.RentAgreements;
      const RentDataInfos = resData.Data.RentDataInfos;
      console.log('RentAgreements', RentAgreements);
      if (RentDataInfos && RentDataInfos.length > 0) {
        this.rentAgreements = RentAgreements;
        this.rentDataInfos = RentDataInfos;
        this.showPopup = true;
        if (!RentAgreements || !RentAgreements.length) {
          this.agreementsChecked = [true];
        }
      } else {
        this.handlePayBtnSecondClick();
      }
    },
    async getQuickPrescription(data) {
      const reqData = {
        PatId: app.globalData.userInfo.Id,
        ...data,
      };
      reqData.Type = Number(reqData.Type);
      // 判断是否有带执行或者执行中或者已结束的
      const res = await checkExistPre(reqData);
      if (res.Type === 200) {
        if (!res.Data) {
          // 表示不存在方案，可以调用开方接口
          this.onQuickPrescription(reqData);
        } else if (res.Data.Type === 0) {
          // 表示有待执行的同类型方案，会带一个方案ID，PreId
          uni.showModal({
            title: '温馨提示',
            content: '已有待执行方案，是否前往执行？',
            cancelText: '否',
            confirmText: '是',
            success: (res1) => {
              if (res1.confirm) {
                uni.reLaunch({
                  url: `/subPrescription/detail?id=${res.Data.PreId}&type=1`,
                });
              } else {
                this.onQuickPrescription(reqData);
              }
            },
          });
        } else if (res.Data.Type === 1) {
          // 表示有未结束的诊后方案
          uni.showModal({
            title: '温馨提示',
            content: '当前有执行中的康复计划，是否前往查看?',
            cancelText: '否',
            confirmText: '是',
            success: (res1) => {
              if (res1.confirm) {
                uni.reLaunch({
                  url: `/pages/plan/index`,
                });
              } else {
                this.onQuickPrescription(reqData);
              }
            },
          });
        } else if (res.Data.Type === 2) {
          // 表示有未结束的社区方案
          uni.showModal({
            title: '温馨提示',
            content: '有未完成的社区治疗项目，是否前往查看?',
            success: (res1) => {
              if (res1.confirm) {
                uni.reLaunch({
                  url: `/subPackIndex/community/index`,
                });
              } else {
                this.onQuickPrescription(reqData);
              }
            },
          });
        }
      } else {
        uni.showModal({
          title: '温馨提示',
          content: res.Content,
        });
      }
    },
    async onQuickPrescription(reqData) {
      const res = await quickPrescription(reqData);
      if (res.Type == 200) {
        if (res.Data) {
          this.option.id = res.Data;
          this.getPrescriptionDetails();
        } else {
          uni.showModal({
            showCancel: false,
            content: '未查询到当前方案的Id',
          });
          this.loadingType = '未查询到当前方案的Id';
        }
      } else {
        uni.showModal({
          showCancel: false,
          content: res.Content,
        });
        this.loadingType = res.Content;
      }
    },
    async getPrescriptionDetails() {
      let res = await getPrescriptionInfo({
        PrescriptionId: this.option.id,
        DtoTypeName: 'RxInfoOutputDto1',
      });
      if (res.Type == 200) {
        this.initResData(res);
        this.loadingType = 'success';
      } else {
        uni.showModal({
          showCancel: false,
          content: res.Content,
        });
        this.loadingType = res.Content;
      }
    },
    initResData(res) {
      this.option.type = res.Data.State;
      this.option.id = res.Data.Id;
      // 该方案是否含有回院提醒的术后
      const backRemindsList = res.Data.BackReminds;
      if (backRemindsList && backRemindsList.length) {
        this.isHavePostoperative = backRemindsList.some((s) => s.Type === 2);
      }
      // 设置用户已读这一条处方信息
      this.SetPatCheckTimeData(this.option.id);
      // 查看之前患者是否选择过咨询医嘱这个选项
      const userChoose = res.Data.PrescriptionDetails.filter(
        (s) => s.CreatorId === res.Data.UserId
      );
      if (userChoose.length > 0) {
        // 如果选择过 需要将之前选择过的价格去除掉
        this.payBtnPrice =
          res.Data.TotalAmount * 1 - userChoose[0].TotalAmount * 1;
      } else {
        this.payBtnPrice = res.Data.TotalAmount;
      }
      // 对医嘱信息进行格式化展现数据
      res.Data.PrescriptionDetails.map((v, index) => {
        if (v.MoItemMedia) {
          v.MoItemMedia = JSON.parse(v.MoItemMedia);
          this.imgList.push(...v.MoItemMedia);
        }
        if (v.CreatorId === res.Data.UserId) {
          // 删除之前选中的咨询医嘱
          res.Data.PrescriptionDetails.splice(index, 1);
        }
        v.Assists &&
          v.Assists.length > 0 &&
          v.Assists.forEach((o) => {
            o.AssistInfoObj = JSON.parse(o.AssistInfo);
          });
      });
      let originalPrice = 0;
      res.Data.PrescriptionDetails.forEach((s) => {
        if (s.MoItemMethod === 0) {
          let otherText = '';
          const list = ItemGroupBy(s.RxActionTemplateOutputDtos, 'Group');
          list.forEach((s, index) => {
            otherText += s.type + '：' + s.data.map((v) => v.Name).join('、');
            if (index !== list.length - 1) {
              otherText += '；';
            }
          });
          s.otherTextList = otherText ? otherText.split('；') : [];
        }
        originalPrice += s.ShowAmount || 0;
      });
      this.originalPrice = originalPrice;
      this.prescriptionDetails = res.Data.PrescriptionDetails;
      // 格式化诊断的数据格式
      this.disposal = res.Data.Disposal.split(`\n`);
      // 如果当前有倒计时时间限制
      if (res.Data.RxExpireMinutes > 0) {
        this.time = toHoursAndMinutes(res.Data.RxExpireMinutes);
      }
      // 判断当前的处方状态
      if (res.Data.State == 1 && res.Data.RxExpireMinutes > 0) {
        if (this.payBtnPrice * 1 > 0) {
          this.payBtnText = `去支付￥${this.payBtnPrice}`;
        } else {
          this.payBtnText = `下一步`;
        }
      } else if (res.Data.State == 2) {
        this.noPayBtnText = '方案已执行，请联系医务人员重新制定';
      } else if (res.Data.State == 3 || res.Data.RxExpireMinutes < 0) {
        this.noPayBtnText = '方案已失效，请联系医务人员重新制定';
      } else if (res.Data.State == 4) {
        this.noPayBtnText = '方案未通过，请联系医务人员重新制定';
      } else if (res.Data.State == 5) {
        this.noPayBtnText = '方案已作废，可联系医务人员重新制定';
      }
      // 检测当前医嘱中是否有包含了咨询医嘱
      this.checkHaveConsultServe(res.Data.PrescriptionDetails);
      res.Data.ActualSendTime = res.Data.ActualSendTime
        ? dateFormat(res.Data.ActualSendTime)
        : '--';
      const showName = [];
      // 显示的指导医生、治疗师、护士等信息
      if (res.Data.Role === 'doctor') {
        res.Data.ShowIsSuerName = res.Data.DoctorName;
        if (res.Data.TherapistName) showName.push(res.Data.TherapistName);
        if (res.Data.NurseName) showName.push(res.Data.NurseName);
        res.Data.ShowInstructorName = showName.join('、');
        res.Data.ShowDocImage = res.Data.DoctorImg;
      } else if (res.Data.Role === 'therapist') {
        res.Data.ShowIsSuerName = res.Data.TherapistName;
        if (res.Data.DoctorName) showName.push(res.Data.DoctorName);
        if (res.Data.NurseName) showName.push(res.Data.NurseName);
        res.Data.ShowInstructorName = showName.join('、');
        res.Data.ShowDocImage = res.Data.TherapistImg;
      } else if (res.Data.Role === 'nurse') {
        res.Data.ShowIsSuerName = res.Data.NurseName;
        if (res.Data.TherapistName) showName.push(res.Data.TherapistName);
        if (res.Data.DoctorName) showName.push(res.Data.DoctorName);
        res.Data.ShowInstructorName = showName.join('、');
        res.Data.ShowDocImage = res.Data.NurseImg;
      }
      this.dataObj = res.Data;
      // 关闭 loading 遮罩
      this.loadingSkeleton = false;
      // 获取当前开方人的机构的咨询医嘱价格
      this.getHaveConsultation(res.Data.OrganizationId);
      this.$nextTick(() => {
        uni.hideLoading();
      });
    },
    async getHaveConsultation(Id) {
      let res = await getHaveConsult({
        orgId: Id,
      });
      if (res.Type === 200 && res.Data) {
        this.isHaveConsult = true;
        if (res.Data.ChargeMode != 4) {
          let maxDayItem = this.dataObj.PrescriptionDetails.reduce(
            (maxItem, currentItem) => {
              if (currentItem.MoDay > maxItem.MoDay) {
                return currentItem;
              } else {
                return maxItem;
              }
            }
          );
          this.consultPrice = res.Data.MoItemAmount * maxDayItem.MoDay;
        } else {
          this.consultPrice = res.Data.MoItemAmount * 1;
        }
      }
    },
    SetPatCheckTimeData(Id) {
      setPatCheckTime({
        Id,
      });
    },
    checkHaveConsultServe(list) {
      const IsHaveConsult = list.some((item) => item.HaveConsultServe);
      this.isShowConsultServeRadio = !IsHaveConsult;
    },
    // 当切换其他选项的时候此方法才会执行
    radioChange1(n) {
      this.radioValue1 = n;
      // 切换选项后需要初始化 num
      this.num1 = 0;
    },
    groupChange1(n) {
      if (n == this.radioValue1 && this.num1 == 0) {
        // 第一次相等即执行以下代码
        this.num1++;
      } else {
        // 第一次后相等即执行以下代码
        // 置空 wxChcek 即取消选中的值
        this.agrCheck = '';
        // 初始化 num
        this.num1 = 0;
      }
    },
    onClickRadioGroupDiv() {
      this.agrCheck = this.agrCheck ? '' : 'agrCheck';
    },
    lookMore(index) {
      if (this.lookMoreIndex == index) {
        this.lookMoreIndex = null;
        return;
      }
      this.lookMoreIndex = index;
    },
  },
};
</script>

<style lang="scss" scoped>
.popup {
  padding: 32rpx;
  position: relative;

  &-top {
    &-title {
      font-weight: 600;
      font-size: 16px;
      margin-bottom: 10px;
    }

    &-label {
      color: #999999;
    }
  }

  &-mid {
    font-size: 14px;
    color: #999999;
    margin-bottom: 10px;
  }

  &-agreements {
    margin: 10px 0;
    flex-wrap: wrap;
  }
}

/deep/ .u-modal__content {
  max-height: 600px;
  overflow-y: auto;
}

/deep/ button {
  z-index: 2 !important;
}

/deep/ .u-radio-group {
  flex: 0 !important;
}

/deep/ .u-radio {
  margin-right: 0 !important;
}

.box {
  height: 100vh;
  background-color: #f7f7f7;
  position: relative;

  .box-image {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
  }

  .box-top {
    text-align: center;
    z-index: 99;
    color: white;
    font-size: 16px;

    &-doc {
      margin: 24rpx;
      padding: 30rpx 28rpx;
      color: black;
      background: #ffffff;
      box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.1);
      border-radius: 16rpx;
    }

    &-time {
      padding: 20rpx;
      background-color: #29b7a3;
    }

    &-disposal {
      padding: 20rpx;
      text-align: left;
      color: #000;
    }

    &-detailbox {
      margin-bottom: 20rpx;
      color: #000;
      padding: 20rpx;
      text-align: left;

      &-dic {
        background: #f5f6fa;
        padding: 10rpx 10rpx;
        border-radius: 16rpx;

        &-bom {
          overflow: hidden;
          -webkit-line-clamp: 2;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          line-break: anywhere;
        }

        &-lookMoreInfo {
          -webkit-line-clamp: 9999 !important;
          -webkit-box-orient: vertical;
        }
      }
    }
  }
}

/deep/ .u-popup__content {
  z-index: 5 !important;
}

/deep/ .u-transition {
  z-index: 3 !important;
}
</style>
