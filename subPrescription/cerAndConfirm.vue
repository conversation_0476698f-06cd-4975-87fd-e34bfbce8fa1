<template>
  <view class="container">
    <view>
      <view style="background: white" v-if="orderInfo.IsDelivery">
        <u-radio-group
          v-model="mailMethod"
          placement="row"
          @change="mailMethodChange($event)"
          activeColor="#29B7A3"
        >
          <u-radio label="邮寄" name="邮寄"></u-radio>
          <u-radio
            label="自提"
            name="自提"
            customStyle="margin-left: 100rpx;"
          ></u-radio>
        </u-radio-group>
      </view>
      <u-line :hairline="false"></u-line>
      <u-cell-group
        style="margin-top: 44px"
        v-if="orderInfo.IsDelivery && addrInfo.Name && mailMethod === '邮寄'"
      >
        <u-cell
          @click="toChangeAddr"
          :label="
            '收货地址：' +
            addrInfo.ProvinceName +
            addrInfo.CityName +
            addrInfo.CountyName +
            addrInfo.Address
          "
          :title="'收件人：' + addrInfo.Name + addrInfo.Tel"
          :isLink="true"
        >
        </u-cell>
      </u-cell-group>
      <u-cell-group
        style="margin-top: 44px"
        v-if="orderInfo.IsDelivery && !addrInfo.Name && mailMethod === '邮寄'"
      >
        <u-cell :isLink="true" @click="addAddress">
          <div slot="title">
            <span style="color: red">请添加收货地址</span>
          </div>
        </u-cell>
      </u-cell-group>

      <u-cell-group>
        <u-list
          v-if="
            orderInfo.TreatOrderMoOutputDtos &&
            orderInfo.TreatOrderMoOutputDtos.length > 0
          "
        >
          <p style="background-color: white; padding: 10px 16px 6px 16px">
            医嘱明细
          </p>
          <u-list-item
            v-for="(item, index) in orderInfo.TreatOrderMoOutputDtos"
            :key="item.MoItemId"
          >
            <u-cell :border="false">
              <view
                slot="title"
                style="font-weight: 600"
                v-show="item.MoItemChargeMode == 1"
              >
                {{ item.MoName }} {{ '(部位' + item.Part + ')' }}
              </view>
              <view
                slot="title"
                style="font-weight: 600"
                v-show="item.MoItemChargeMode != 1"
              >
                {{ item.MoName }}
              </view>
              <view
                slot="label"
                style="font-size: 12px; color: #999999; margin-top: 24rpx"
                v-show="item.MoItemChargeMode === 3"
              >
                {{ item.FreqDay }}天{{ item.Freq }}次,共{{ item.MoDay }}天
              </view>
              <view
                slot="label"
                style="font-size: 12px; color: #999999; margin-top: 24rpx"
                v-show="item.MoItemChargeMode === 5"
              >
                {{ item.MoMonth }}个月
              </view>
              <view
                slot="label"
                style="font-size: 12px; color: #999999; margin-top: 24rpx"
                v-show="
                  item.MoItemChargeMode === 1 || item.MoItemChargeMode === 2
                "
              >
                {{ item.FreqDay }}天{{ item.Freq }}次,共{{ item.TotalCount }}次
              </view>
              <view slot="value" style="text-align: right">
                <p style="font-size: 16px" v-show="item.MoItemChargeMode === 1">
                  <span>￥{{ (item.Price * item.Part).toFixed(2) }}</span>
                  <span
                    style="text-decoration: line-through; color: #b5b5b5"
                    v-if="item.ShowPrice > item.Price"
                    >￥{{ (item.ShowPrice * item.Part).toFixed(2) }}</span
                  >
                </p>
                <p style="font-size: 16px" v-show="item.MoItemChargeMode != 1">
                  <span>￥{{ item.Price.toFixed(2) }}</span>
                  <span
                    style="text-decoration: line-through; color: #b5b5b5"
                    v-if="item.ShowPrice > item.Price"
                    >￥{{ item.ShowPrice.toFixed(2) }}</span
                  >
                </p>
                <p
                  style="color: #999999; font-size: 12px"
                  v-show="item.MoItemChargeMode === 4"
                >
                  x1
                </p>
                <p
                  style="color: #999999; font-size: 12px"
                  v-show="item.MoItemChargeMode === 3"
                >
                  x{{ item.MoDay }}
                </p>
                <p
                  style="color: #999999; font-size: 12px"
                  v-show="
                    item.MoItemChargeMode === 1 || item.MoItemChargeMode === 2
                  "
                >
                  x{{ item.TotalCount }}
                </p>
                <p
                  style="color: #999999; font-size: 12px"
                  v-show="item.MoItemChargeMode === 5"
                >
                  x{{ item.MoMonth }}
                </p>
              </view>
            </u-cell>
          </u-list-item>
        </u-list>
      </u-cell-group>

      <u-cell-group
        style="margin-top: 10rpx"
        v-if="orderInfo.RentDataInfos && orderInfo.RentDataInfos.length > 0"
      >
        <u-cell :border="false">
          <view class="" slot="title">
            <p style="font-weight: 600; font-size: 16px; margin-bottom: 10px">
              设备使用保证金<span style="color: #999999">(可退)</span>
            </p>
            <p style="font-size: 14px; color: #999999; margin-bottom: 10px">
              本次治疗将使用到以下设备，需要缴纳设备保证金，保证金会在设备返还后退回付款账户
            </p>
            <view
              class="display-style1"
              style="margin-bottom: 10px; flex-wrap: wrap"
              v-if="orderInfo.RentAgreements.length > 0"
            >
              <u-radio-group @change="groupChange1" v-model="agrCheck">
                <u-radio
                  name="agrCheck"
                  activeColor="#29B7A3"
                  @change="radioChange1"
                >
                </u-radio>
              </u-radio-group>
              <p style="font-size: 14px">我已阅读并同意</p>
              <view
                class="display-style1"
                style="margin-left: 2px"
                v-for="(k, index2) in orderInfo.RentAgreements"
                @click="seeAgreements(k)"
              >
                <u--text type="success" :text="'《 ' + k.Key + '》'"></u--text>
              </view>
            </view>
          </view>
        </u-cell>
      </u-cell-group>
      <u-cell-group>
        <u-list
          v-if="orderInfo.RentDataInfos && orderInfo.RentDataInfos.length > 0"
        >
          <u-list-item
            v-for="(item, index2) in orderInfo.RentDataInfos"
            :key="item.RentDataName"
          >
            <u-cell
              :title="item.RentDataName"
              :value="'￥' + item.RentDataAmount"
            >
            </u-cell>
          </u-list-item>
        </u-list>
      </u-cell-group>

      <u-cell-group style="margin-top: 10rpx">
        <u-cell title="共计" :border="false">
          <p slot="value" style="color: red">￥{{ orderInfo.TotlePriceIn }}</p>
        </u-cell>
      </u-cell-group>
      <u-cell-group style="margin-top: 10rpx" v-if="orderInfo.TotlePriceIn > 0">
        <u-cell title="微信">
          <u-radio-group v-model="wxChcek" slot="value" @change="groupChange">
            <u-radio
              ref="radio1_check"
              name="wxChcek"
              activeColor="#29B7A3"
              @change="radioChange"
            >
            </u-radio>
          </u-radio-group>
        </u-cell>
      </u-cell-group>
    </view>
    <u-gap height="100" bgColor="#f7f7f7"></u-gap>
    <!-- 需要钱的治疗订单支付 -->
    <u-button
      v-if="orderInfo.TotlePriceIn > 0"
      type="success"
      @click="payMoeny"
      :loading="disabled"
      shape="circle"
      :text="'支付￥' + orderInfo.TotlePriceIn"
      customStyle="width:90%;bottom: 30px;position: fixed; left: 50%;transform: translateX(-50%);backgroundColor:#29B7A3;color:#fff"
    >
    </u-button>

    <!-- 不要钱的治疗订单支付 -->
    <u-button
      v-if="orderInfo.TotlePriceIn == 0"
      type="success"
      @click="handlePayNoMoenyBtnClick"
      :loading="disabled"
      shape="circle"
      text="确认订单"
      customStyle="width:90%;bottom: 30px;position: fixed; left: 50%;transform: translateX(-50%);backgroundColor:#29B7A3;color:#fff"
    >
    </u-button>

    <u-modal
      :show="showContents"
      confirmText="确定"
      confirmColor="#29B7A3"
      @confirm="showContents = false"
      :heigth="300"
    >
      <view
        v-html="contentArg"
        slot="default"
        :style="'max-height:' + windowHeight + 'px;' + 'text-align:left'"
      ></view>
    </u-modal>

    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script>
const app = getApp();
import { getWxPayInfo, getUserAddrInfo, payNoNeedMoeny } from '@/api/order.js';
import { getTreatOrderInfo } from '@/api/consult.js';
export default {
  data() {
    return {
      windowHeight: 0,
      mailMethod: '邮寄',
      addrInfo: {},
      orderInfo: {},
      payType: {
        Id: '',
      },
      disabled: false,
      option: {},
      dataObj: {},
      agrCheck: '',
      radioValue: '',
      radioValue1: '',
      num1: 0, //用于区分是否是重复选中
      num: 0,
      wxChcek: 'wxChcek',
      showContents: false,
      contentArg: '',
    };
  },
  onLoad(option) {
    this.option.id = option.id;
    this.dataObj = JSON.parse(decodeURIComponent(option.dataObj));
    this.userInfo = app.globalData.userInfo;

    // 获取收货地址
    this.getPayAddrInfo();
    // 初始化页面
    this.initData();
    this.getSysInfo();
  },
  methods: {
    getSysInfo() {
      uni.getSystemInfo({
        success: (res) => {
          this.windowHeight = res.windowHeight * 0.75;
        },
      });
    },
    // 初始化页面
    async initData() {
      uni.showLoading({
        title: this.$loadingMsg,
      });
      // 获取处方信息
      this.getPayOrder();
      uni.hideLoading();
    },
    // 查看协议
    seeAgreements(item) {
      this.contentArg = item.Remark;
      this.showContents = true;
    },
    mailMethodChange(e) {
      console.log('e', e);
      this.mailMethod = e;
    },
    // 重新选择收获地址
    toChangeAddr() {
      uni.navigateTo({
        url: '/subPackIndex/user/addressMgr?isChange=no',
      });
    },
    async payMoeny() {
      if (!this.wxChcek) {
        this.$refs.uToast.show({
          message: '请您勾选支付方式',
          type: 'error',
        });
        return;
      }
      if (
        !this.agrCheck &&
        this.orderInfo.RentDataInfos.length > 0 &&
        this.orderInfo.RentAgreements.length > 0
      ) {
        this.$refs.uToast.show({
          message: '请您同意设备使用协议',
          type: 'error',
        });
        return;
      }
      if (this.disabled) {
        this.$refs.uToast.show({
          message: '您点太快了，稍等下',
          type: 'error',
        });
        return;
      }
      this.onIsNeedAddress();
    },
    // 判断是否需要收获地址
    onIsNeedAddress() {
      if (
        this.mailMethod === '邮寄' &&
        !this.addrInfo?.Address &&
        this.orderInfo.IsDelivery
      ) {
        uni.showModal({
          content:
            '您还未填写收货地址，是否继续支付？（未填写地址后续将由医助与您联系）',
          cancelText: '继续支付',
          confirmText: '完善地址',
          success: (res) => {
            if (res.confirm) {
              this.addAddress();
            } else {
              this.payPrescription();
            }
          },
        });
      } else {
        this.payPrescription();
      }
    }, //处方的支付
    async payPrescription() {
      this.disabled = true;
      this.addrInfo && this.addrInfo.Id && delete this.addrInfo.Id;
      let data = {
        OrderNo: this.orderInfo.TreatOrderNo,
        PayDescription: '订单支付',
        PaymentId: this.payType.Id,
        OpenId: app.globalData.openId,
        TradeType: 1,
      };
      if (this.mailMethod === '邮寄') {
        data.OrderAddresss =
          this.addrInfo && this.addrInfo.Name && this.orderInfo.IsDelivery
            ? [this.addrInfo]
            : [];
      }
      let orderInfo = await getWxPayInfo(data);
      if (orderInfo.Type !== 200) {
        uni.showModal({
          content: orderInfo.Content,
          showCancel: false,
          complete: () => {
            this.showNotifyMessage = true;
            this.notifyMessage = orderInfo.Content;
            this.disabled = false;
          },
        });
        return;
      }
      uni.requestPayment({
        provider: 'wxpay',
        timeStamp: orderInfo.Data.timeStamp, // 时间戳（单位：秒）
        nonceStr: orderInfo.Data.nonceStr, // 随机字符串
        package: orderInfo.Data.package, // 固定值
        signType: orderInfo.Data.signType, //固定值
        paySign: orderInfo.Data.paySign, //签名
        success: (resss) => {
          console.log('支付成功', resss);
          if ((resss.errMsg = 'requestPayment:ok')) {
            getApp().subscribeMessage(() => {
              uni.redirectTo({
                url:
                  './payResults?orderInfo=' +
                  encodeURIComponent(JSON.stringify(this.orderInfo)) +
                  '&id=' +
                  this.option.id,
              });
            });
            this.$log.info(
              `${this.$envVersion || ''}:有钱到账啦!!! 来源是：处方支付，患者是：${app.globalData.userInfo.Name}, 手机号是${app.globalData.userInfo.PhoneNumber}`
            );
          }
        },
        complete: () => {
          this.disabled = false;
        },
      });
    },
    // 不要钱的点击方法
    async handlePayNoMoenyBtnClick() {
      this.disabled = true;
      try {
        await this.sureOrder();
      } finally {
        this.disabled = false;
      }
    },
    // 不要钱
    async sureOrder() {
      if (this.orderInfo.TreatType !== 2) {
        if (this.orderInfo.IsDelivery) {
          // 需要发货
          if (this.orderInfo.RentDataInfos.length == 0) {
            this.agrCheck = true;
          } else if (
            this.orderInfo.RentDataInfos.length > 0 &&
            this.orderInfo.RentAgreements == 0
          ) {
            this.agrCheck = true;
          }
          if (!this.agrCheck) {
            this.$refs.uToast.show({
              message: '请您同意设备使用协议',
              type: 'error',
            });
            return;
          }
          if (this.mailMethod === '邮寄' && !this.addrInfo?.Address) {
            uni.showModal({
              content:
                '您还未填写收货地址，是否继续支付？（未填写地址后续将由医助与您联系）',
              cancelText: '继续支付',
              confirmText: '完善地址',
              success: (res) => {
                if (res.confirm) {
                  this.addAddress();
                } else {
                  this.onIsNeedAddressNoMoeny();
                }
              },
            });
            return;
          }
        }
        this.onIsNeedAddressNoMoeny();
        return;
      }
      uni.redirectTo({
        url:
          './payResults?orderInfo=' +
          encodeURIComponent(JSON.stringify(this.dataObj)) +
          '&id=' +
          this.option.id,
      });
    },
    async onIsNeedAddressNoMoeny() {
      //居家
      if (this.addrInfo && this.addrInfo.Id) {
        delete this.addrInfo.Id;
      }
      let data = {
        OrderNo: this.orderInfo.TreatOrderNo,
      };
      if (this.orderInfo.IsDelivery && this.mailMethod === '邮寄') {
        data.OrderAddresss =
          this.addrInfo.Name && this.orderInfo.IsDelivery
            ? [this.addrInfo]
            : [];
      }
      let res = await payNoNeedMoeny(data);
      if (res.Type !== 200) {
        uni.showToast({
          title: res.Content,
          icon: 'none',
        });
        return;
      }
      getApp().subscribeMessage(() => {
        uni.showLoading({
          title: '正在生成计划，请稍后...',
        });
        uni.redirectTo({
          url:
            './payResults?orderInfo=' +
            encodeURIComponent(JSON.stringify(this.orderInfo)) +
            '&id=' +
            this.option.id,
        });
      });
    },
    // 通过选择回来的数据更新地址
    getBeforData(item) {
      console.log('item', item);
      // this.addrInfo = {}
      this.addrInfo = JSON.parse(JSON.stringify(item));
    },
    // 添加收货地址
    addAddress() {
      uni.navigateTo({
        url: `/subPackIndex/user/addOrEditAddress?needShowUser=yes`,
      });
    },
    groupChange(n) {
      if (n == this.radioValue && this.num == 0) {
        // 第一次相等即执行以下代码
        this.num++;
      } else {
        // 第一次后相等即执行以下代码
        // 置空 wxChcek 即取消选中的值
        this.wxChcek = '';
        // 初始化 num
        this.num = 0;
      }
    },
    // 当切换其他选项的时候此方法才会执行
    radioChange(n) {
      this.radioValue = n;
      // 切换选项后需要初始化 num
      this.num = 0;
    },

    groupChange1(n) {
      if (n == this.radioValue1 && this.num1 == 0) {
        // 第一次相等即执行以下代码
        this.num1++;
      } else {
        // 第一次后相等即执行以下代码
        // 置空 wxChcek 即取消选中的值
        this.agrCheck = '';
        // 初始化 num
        this.num1 = 0;
      }
    },
    // 当切换其他选项的时候此方法才会执行
    radioChange1(n) {
      this.radioValue1 = n;
      // 切换选项后需要初始化 num
      this.num1 = 0;
    },
    accAdd(arg1, arg2) {
      var r1, r2, m;
      try {
        r1 = arg1.toString().split('.')[1].length;
      } catch (e) {
        r1 = 0;
      }
      try {
        r2 = arg2.toString().split('.')[1].length;
      } catch (e) {
        r2 = 0;
      }
      m = Math.pow(10, Math.max(r1, r2));
      return (arg1 * m + arg2 * m) / m;
    },
    // 获取处方的订单信息
    async getPayOrder() {
      let rsp = await getTreatOrderInfo(this.option.id);
      if (rsp.Type == 200) {
        rsp.Data.TotlePriceIn = this.accAdd(
          rsp.Data.RentDataMoney,
          rsp.Data.TreatOrder.Price
        );
        this.orderInfo = rsp.Data;
        this.payType = rsp.Data.TreatOrder.Payment;
      } else {
        this.$refs.uToast.show({
          message: rsp.Content,
          type: 'error',
        });
      }
    },
    // 获取处方用户地址信息
    async getPayAddrInfo() {
      let obj = {
        PageCondition: {
          PageIndex: 1,
          PageSize: 1000,
        },
        FilterGroup: {
          Rules: [
            {
              Field: 'UserId',
              Value: app.globalData.userInfo.Id,
              Operate: 3,
            },
          ],
          Operate: 1,
        },
      };
      let rsp = await getUserAddrInfo(obj);
      if (rsp.Type === 200 && rsp.Data.Rows.length > 0) {
        const isDef = rsp.Data.Rows.filter((s) => s.IsDefault);
        this.addrInfo = isDef.length > 0 ? isDef[0] : rsp.Data.Rows[0];
      }
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  height: 100vh;
  background-color: #f7f7f7;
  // padding-top: 208rpx;
  padding: 0 32rpx;
  padding-top: 20rpx;
  position: relative;

  /deep/ .u-cell {
    background-color: #fff !important;
  }

  /deep/ .u-line {
    border: none !important;
  }

  /deep/ .u-radio-group {
    flex: 0 !important;
  }

  /deep/ .u-list {
    height: auto !important;
  }

  /deep/ .u-radio {
    margin-right: 0 !important;
  }

  /deep/ .u-radio-group {
    background: '#f7f7f7' !important;
    padding: 20rpx !important;
  }
  /deep/ .u-modal__content {
    max-height: 600px;
    overflow-y: auto;
  }
}
</style>
