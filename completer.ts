// completer.ts

class Completer<T> {
  private _future: Promise<T>;
  private _resolve?: (value: T | PromiseLike<T>) => void;
  private _reject?: (reason?: any) => void;

  constructor() {
    this._future = new Promise<T>((resolve, reject) => {
      this._resolve = resolve;
      this._reject = reject;
    });
  }

  get future(): Promise<T> {
    return this._future;
  }

  complete(value: T) {
    this._resolve!(value);
  }

  completeError(error?: any) {
    this._reject!(error);
  }
}

export default Completer;
