<template>
  <MpLoadingPage :loadingType="loadingType">
    <view class="box">
      <!-- 医生相关 -->
      <view class="box-top" style="margin-bottom: 32rpx">
        <u-avatar
          :src="info.DocInfo.HeadImg"
          size="50"
          class="avatar-syyle"
        ></u-avatar>
        <view class="box-top-right">
          <view class="box-top-right-top">
            <p style="font-size: 18px">
              {{ info.DocInfo.Name }}
              <span
                style="font-size: 14px; color: #666666; margin-left: 20rpx"
                >{{ info.DocInfo.WorkerTitle }}</span
              >
            </p>
          </view>
          <view class="box-top-right-top">
            <p style="font-size: 14px; color: #666666">
              {{ info.DocInfo.OrganizationName }}
              <span style="margin-left: 20rpx">{{
                info.DocInfo.DepartmentName
              }}</span>
            </p>
          </view>
        </view>
      </view>
      <!-- 患者相关 -->
      <view
        class="pat"
        style="margin-bottom: 32rpx"
        v-if="info.ConsultWay != 2"
      >
        <p style="font-size: 18px; font-weight: 600">
          {{ info.PatInfo.Name }}
          <span style="margin-left: 20rpx; font-size: 14px">{{
            info.PatInfo.Age
          }}</span>
          <span style="margin-left: 20rpx; font-size: 14px">{{
            info.PatInfo.Sex
          }}</span>
        </p>
        <view class="dirsc">
          <p>病情描述：</p>
          <u-textarea
            style="fexl: 1"
            :value="info.PatInfo.Describing"
            disabled
            :autoHeight="true"
          >
          </u-textarea>
        </view>
      </view>

      <!-- 列表 -->
      <view class="list-style">
        <u-cell title="支付金额" :isLink="false">
          <p slot="value">￥{{ info.OrderInfo.Amount }}</p>
        </u-cell>
        <u-cell
          title="订单编号"
          :isLink="false"
          :value="info.OrderInfo.OrderNo"
        ></u-cell>
        <u-cell
          title="创建时间"
          :isLink="false"
          :value="info.OrderInfo.CreateDate"
          v-if="info.OrderInfo.CreateDate"
        ></u-cell>
        <u-cell
          title="支付时间"
          :isLink="false"
          :value="info.OrderInfo.PayTime"
          v-if="
            info.State !== 0 && !(info.State == 4 && !info.OrderInfo.PayTime)
          "
        >
        </u-cell>
        <u-cell
          title="支付方式"
          :isLink="false"
          :value="info.OrderInfo.PayType"
          v-if="
            info.State !== 0 && !(info.State == 4 && !info.OrderInfo.PayTime)
          "
        >
        </u-cell>
        <u-cell
          :title="info.ConsultWay == 2 ? '确认时间' : '接诊时间'"
          :isLink="false"
          :value="info.OrderInfo.VistDate"
          v-if="
            info.OrderInfo.VistDate && (info.State === 2 || info.State === 3)
          "
        ></u-cell>
        <u-cell
          title="结束时间"
          :isLink="false"
          :value="info.OrderInfo.CompletedTime"
          v-if="info.OrderInfo.CompletedTime"
        ></u-cell>
      </view>
    </view>
    <view class="bottom-gr" v-if="option.type != 4">
      <view
        class="botton-right botton-left"
        v-if="
          (option.type == 0 || option.type == 1) &&
          info.OrderInfo.PayAlias != config.clientId
        "
        @click="cancelOrderShow"
      >
        取消订单
      </view>
      <view
        class="botton-right"
        @click="payMoeny"
        v-if="info.OrderInfo.PayAlias != config.clientId && option.type == 0"
      >
        {{ btnTitle }}
      </view>
      <view
        class="botton-right"
        @click="onServiceEvaluation"
        v-if="option.type == 3 && !info.IsEva && info.Source === 1"
      >
        服务评价
      </view>
      <view class="botton-right" @click="payMoeny" v-if="option.type == 3">
        咨询记录
      </view>
      <view
        class="botton-right botton-left"
        v-if="option.type == 1 || option.type == 2"
        @click="toConsulting"
      >
        去咨询
      </view>
    </view>
    <u-modal
      title="温馨提示"
      :show="show"
      content="您确定要取消吗"
      showConfirmButton="true"
      showCancelButton="true"
      @confirm="onSure"
      @cancel="onCancel"
    ></u-modal>
    <u-modal
      :show="show2"
      title="温馨提示"
      content="恭喜您,支付成功"
      @confirm="confirm2"
    ></u-modal>
    <u-toast ref="uToast"></u-toast>
  </MpLoadingPage>
</template>

<script>
import { cancel, getInquiryDatil } from '@/api/consult.js';
import { dateFormat } from '@/utils/validate.js';
import { getWxPayInfo } from '@/api/order.js';
import config from '@/config';
const app = getApp();
export default {
  data() {
    return {
      loadingType: 'loading',
      option: {},
      info: {},
      btnTitle: '',
      show: false,
      show2: false,
      clickAgin: true,
    };
  },
  async onLoad(option) {
    this.option = option;
    await this.getDetail(option.id);
    if (option.type >= 0) {
      this.setTitle(option.type);
    }
  },
  methods: {
    onServiceEvaluation(index) {
      uni.navigateTo({
        url:
          '/subServices/docIndex?item=' +
          encodeURIComponent(JSON.stringify(this.info.DocInfo)) +
          '&consultId=' +
          this.info.Id +
          '&ConsultWay=' +
          this.info.ConsultWay,
      });
    },
    // 去咨询
    toConsulting() {
      getApp().toChatPage(this.info.Id);
    },
    async getDetail(id) {
      let res = await getInquiryDatil(id);
      if (res.Type == 200) {
        res.Data.OrderInfo.CreateDate =
          res.Data.OrderInfo.CreateDate &&
          dateFormat(res.Data.OrderInfo.CreateDate);
        res.Data.OrderInfo.CompletedTime =
          res.Data.OrderInfo.CompletedTime &&
          dateFormat(res.Data.OrderInfo.CompletedTime);
        res.Data.OrderInfo.ConsultDate =
          res.Data.OrderInfo.ConsultDate &&
          dateFormat(res.Data.OrderInfo.ConsultDate);
        res.Data.OrderInfo.VistDate =
          res.Data.OrderInfo.VistDate &&
          dateFormat(res.Data.OrderInfo.VistDate);
        res.Data.OrderInfo.PayTime =
          res.Data.OrderInfo.PayTime && dateFormat(res.Data.OrderInfo.PayTime);
        this.info = res.Data;
        this.loadingType = 'success';
      } else {
        this.loadingType = res.Content;
      }
    },
    setTitle(type) {
      let title = '';
      let btnTitle = '';
      if (type == 0) {
        title = '待支付';
        btnTitle = '去支付';
      } else if (type == 1) {
        title = '待接诊';
        btnTitle = '取消订单';
      } else if (type == 2) {
        title = '咨询中';
        btnTitle = '去咨询';
      } else if (type == 3) {
        title = '已完成';
        btnTitle = '咨询记录';
      } else if (type == 4) {
        title = '已取消';
      }
      console.log('title', title);
      this.btnTitle = btnTitle;
      let baseTitle = '咨询订单-';
      if (this.info.ConsultWay === 2) {
        baseTitle = '咨询订单-';
      }
      uni.setNavigationBarTitle({
        title: baseTitle + title,
      });
    },
    cancelOrderShow() {
      this.show = true;
    },
    onSure() {
      this.show = false;
      this.cancelOrder();
    },
    onCancel() {
      this.show = false;
    },
    // 取消订单
    async cancelOrder() {
      let type = '';
      type = this.info.State == 1 ? 'CancelPayOrder' : 'CancelNoPayOrder';
      let res = await cancel(type, [this.info.OrderInfo.OrderNo]);
      if (res.Type == 200) {
        this.$refs.uToast.show({
          message: res.Content,
          type: 'success',
        });
        setTimeout(() => {
          uni.navigateBack();
          let pages = getCurrentPages();
          const prePage = pages[pages.length - 2]; //上一个页面
          prePage.$vm.getList(1);
        }, 1500);
      } else {
        this.$refs.uToast.show({
          message: res.Content,
          type: 'error',
        });
      }
    },
    async payMoeny() {
      if (this.option.type == 3) {
        getApp().toChatPage(this.info.Id);
        return;
      }
      if (this.option.type == 1) {
        this.cancelOrderShow();
        return;
      }
      if (this.option.type == 2) {
        this.toConsulting();
        return;
      }
      if (!this.clickAgin) {
        this.$refs.uToast.show({
          message: '您点太快了，稍等下',
          type: 'error',
        });
        return;
      }
      this.clickAgin = false;
      let data = {
        OrderNo: this.info.OrderInfo.OrderNo,
        PayDescription: '订单支付',
        PaymentId: this.info.OrderInfo.PaymentId,
        OrderAddresss: [],
        OpenId: app.globalData.openId,
        TradeType: 1,
      };
      let orderInfo = await getWxPayInfo(data);
      console.log('orderInfo', orderInfo);
      if (orderInfo.Type == 200) {
        uni.requestPayment({
          provider: 'wxpay',
          // appId:'wx5ed2e839af97c0ca',
          timeStamp: orderInfo.Data.timeStamp, // 时间戳（单位：秒）
          nonceStr: orderInfo.Data.nonceStr, // 随机字符串
          package: orderInfo.Data.package, // 固定值
          signType: orderInfo.Data.signType, //固定值
          paySign: orderInfo.Data.paySign, //签名
          success: (resss) => {
            console.log('支付成功', resss);
            if ((resss.errMsg = 'requestPayment:ok')) {
              // this.show2 = true
              this.confirm2();
            }
          },
          complete: () => {
            this.clickAgin = true;
          },
        });
      } else {
        this.$refs.uToast.show({
          message: orderInfo.Content,
          type: 'error',
        });
        this.clickAgin = true;
      }
    },
    confirm2() {
      this.show2 = false;
      // uni.switchTab({
      // 	url: '/pages/user/index'
      // });
      let backPath = '/pages/interview/index';
      uni.navigateTo({
        url:
          '/subPackChat/sessionChatPage?consultId=' +
          this.option.id +
          '&backPath=' +
          backPath,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.bottom-gr {
  height: 50px;
  position: fixed;
  bottom: 0;
  width: 100%;
  background-color: white;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-right: 20rpx;

  .botton-right {
    width: 120rpx;
    height: 60rpx;
    background-color: #29b7a3;
    color: white;
    font-size: 12px;
    text-align: center;
    line-height: 60rpx;
    border-radius: 20px;
    margin-right: 20rpx;
    // position: absolute;
    // right: 20px;
    // top: 10px;
  }

  .botton-left {
    // right: 100px
  }
}

.box {
  position: relative;
  height: 100vh;
  background-color: #f7f7f7;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  padding: 40rpx;

  /deep/ .u-textarea {
    background-color: white;
    border: none;
    padding: 0;
  }

  .list-style {
    margin-top: 20rpx;

    /deep/ .u-cell {
      background-color: white;
    }

    /deep/ .u-line {
      border-bottom-style: none !important;
    }
  }

  .pat {
    margin-top: 20rpx;
    background-color: white;
    padding: 32rpx;
    border-radius: 8rpx;

    .dirsc {
      margin-top: 24rpx;
      display: flex;
      justify-content: space-between;
    }
  }

  .box-top-right-top1 {
    margin-top: 20rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .botton-style {
      width: 138rpx;
      height: 60rpx;
      background-color: #29b7a3;
      font-size: 12px;
      text-align: center;
      line-height: 60rpx;
      border-radius: 30px;
      color: white;
    }
  }

  .box-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: white;
    height: 140rpx;
    padding: 20rpx;
    border-radius: 16rpx;

    .box-top-right {
      flex: 1;
      margin-left: 20rpx;

      .box-top-right-top {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .botton-style {
          width: 138rpx;
          height: 60rpx;
          background-color: #29b7a3;
          font-size: 15px;
          text-align: center;
          line-height: 60rpx;
          border-radius: 30px;
        }
      }
    }
  }
}
</style>
