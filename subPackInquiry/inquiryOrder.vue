<template>
  <MpLoadingPage :loadingType="loadingType">
    <view class="u-page">
      <u-list @scrolltolower="scrolltolower" v-if="indexList.length > 0">
        <view style="height: 10px"></view>
        <u-list-item v-for="(item, index) in indexList" :key="item.id">
          <view class="box">
            <view class="box-top" @click="toDetal(index)">
              <u-avatar
                :src="item.DoctorInfo.HeadImg"
                size="60"
                class="avatar-syyle"
              ></u-avatar>
              <view class="box-top-right">
                <view class="box-top-right-top">
                  <p style="font-size: 18px">
                    {{ item.DoctorInfo.Name }}
                    <span
                      style="
                        font-size: 14px;
                        color: #666666;
                        margin-left: 20rpx;
                      "
                      >{{ item.DoctorInfo.WorkerTitle }}</span
                    >
                  </p>
                  <p v-if="item.State === 0" style="color: red">待支付</p>
                  <p
                    v-if="item.State === 1 && item.ConsultWay === 1"
                    style="color: black"
                  >
                    待接诊
                  </p>
                  <p
                    v-if="item.State === 1 && item.ConsultWay !== 1"
                    style="color: black"
                  >
                    待确认
                  </p>
                  <p v-if="item.State === 2" style="color: black">咨询中</p>
                  <p v-if="item.State === 3" style="color: gray">已完成</p>
                  <p v-if="item.State === 4" style="color: gray">已取消</p>
                </view>
                <view class="box-top-right-top">
                  <p style="font-size: 14px; color: #666666">
                    {{ item.DoctorInfo.OrganizationName }}
                    <span style="margin-left: 20rpx">{{
                      item.DoctorInfo.DepartmentName
                    }}</span>
                  </p>
                  <p style="color: red">￥{{ item.Amount }}</p>
                </view>
              </view>
            </view>
            <view class="box-top-right-top1">
              <p style="font-size: 14px; color: #666666">
                就诊人：{{ item.UserName }}
              </p>
              <span v-if="item.State === 0 && item.showOrderWhere">{{
                item.showOrderWhere
              }}</span>
              <view
                class="botton-style"
                v-if="item.State === 1 || item.State === 2"
                @click="toQuestion(index)"
              >
                去咨询
              </view>
              <view
                class="botton-style"
                v-if="item.State === 3 && !item.IsEva && item.Source === 1"
                @click="onServiceEvaluation(index)"
              >
                服务评价
              </view>
            </view>
          </view>
        </u-list-item>
      </u-list>
      <u-empty
        mode="data"
        icon="	https://cdn.uviewui.com/uview/empty/data.png"
        v-else
        text="当前没有咨询记录"
      >
      </u-empty>
    </view>
  </MpLoadingPage>
</template>

<script>
import { getInquiryList } from '@/api/consult.js';
import Move from '@/mixin/move.js';
import config from '@/config';
export default {
  mixins: [Move],
  data() {
    return {
      loadingType: 'loading',
      indexList: [],
      src: 'https://cdn.uviewui.com/uview/album/1.jpg',
      query: {
        pageindex: 1,
        pagesize: 10,
        source: 1,
      },
      resources: [
        `com.kangfx.wx.mp.patient-${config.resources}`,
        config.clientId,
      ],
    };
  },
  onLoad() {
    this.getList();
  },
  methods: {
    async getList(type) {
      if (type) {
        this.indexList = [];
        this.query.pageindex = 1;
      }
      let res = await getInquiryList(this.query);
      if (res.Type !== 200) {
        this.loadingType = res.Content;
        return;
      }
      res.Data.forEach((e) => {
        const isPayAliasByMp = this.resources.some((v) => v === e.PayAlias);
        // 判断是否是小程序创建的订单 小程序创建的只能小程序支付 app创建的只能在app上面支付
        e.showGiveMoneyButton = isPayAliasByMp ? 'yes' : 'no';
        if (!isPayAliasByMp) {
          if (e.PayAlias == 'com.kangfx.wx.mp.patient-kfx') {
            e.showOrderWhere = '请前往康复行小程序支付';
          } else if (e.PayAlias == 'com.kangfx.wx.mp.patient-hbszyy') {
            e.showOrderWhere = '请前往河北省小程序支付';
          } else if (e.PayAlias == 'com.kangfx.wx.mp.patient-kyx') {
            e.showOrderWhere = '请前往康易行小程序支付';
          } else if (e.PayAlias == 'com.kangfx.wx.mp.patient-internal') {
            e.showOrderWhere = '请前往康复行测试支付';
          } else if (e.PayAlias == 'com.kangfx.wx.mp.patient') {
            e.showOrderWhere = '请前往康复行小程序支付';
          }
        }
        this.indexList.push(e);
      });
      this.loadingType = 'success';
    },
    toDetal(index) {
      const isPayAliasByMp = this.resources.some(
        (v) => v === this.indexList[index].PayAlias
      );
      if (this.indexList[index].State == 0 && !isPayAliasByMp) {
        return;
      }
      uni.navigateTo({
        url: `./detail?id=${this.indexList[index].Id}&type=${this.indexList[index].State}`,
      });
    },
    toQuestion(index) {
      getApp().toChatPage(this.indexList[index].Id);
    },
    onServiceEvaluation(index) {
      uni.navigateTo({
        url:
          '/subServices/docIndex?item=' +
          encodeURIComponent(JSON.stringify(this.indexList[index].DoctorInfo)) +
          '&consultId=' +
          this.indexList[index].Id +
          '&ConsultWay=' +
          this.indexList[index].ConsultWay,
      });
    },
    scrolltolower() {
      this.$u.debounce(() => {
        this.query.pageindex++;
        this.getList();
      });
    },
    payMoeny(index) {
      const item = this.indexList[index];
      console.log('去支付', item);
    },
  },
};
</script>

<style scoped lang="scss">
.u-page {
  height: 100vh;
  background-color: #f7f7f7;
  padding-top: 32rpx;

  /deep/ .insideStyle {
    padding: 0 32rpx;
  }

  /deep/ .u-list-item {
    background-color: white;
    margin-bottom: 24rpx;
    box-shadow: 2.5px 3px 9px 0px rgba(0, 0, 0, 0.08);
    border-radius: 8rpx;
  }

  .box {
    background-color: white;
    border-radius: 12rpx;
    padding: 20rpx;

    .box-top-right-top1 {
      margin-top: 20rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .botton-style {
        width: 138rpx;
        height: 60rpx;
        background-color: #29b7a3;
        font-size: 12px;
        text-align: center;
        line-height: 60rpx;
        border-radius: 30px;
        color: white;
      }
    }

    .box-top {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .box-top-right {
        flex: 1;
        margin-left: 20rpx;

        .box-top-right-top {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .botton-style {
            width: 138rpx;
            height: 60rpx;
            background-color: #29b7a3;
            font-size: 15px;
            text-align: center;
            line-height: 60rpx;
            border-radius: 30px;
          }
        }
      }
    }
  }
}
</style>
