const path = require('path');
const CopyWebpackPlugin = require('copy-webpack-plugin'); //最新版本copy-webpack-plugin插件暂不兼容，推荐v5.0.0
const BundleAnalyzerPlugin =
  require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
module.exports = {
  configureWebpack: {
    plugins: [
      new CopyWebpackPlugin([{
        from: path.join(__dirname, 'subIM/wxcomponents'),
        to: path.join(
          __dirname,
          'unpackage',
          'dist',
          process.env.NODE_ENV === 'production' ? 'build' : 'dev',
          process.env.UNI_PLATFORM,
          'subIM',
          'wxcomponents'
        ),
      }, ]),
      // new BundleAnalyzerPlugin({
      //   analyzerPort: 7878
      // }),
    ],
  },
  // chainWebpack: config => {
  //     console.log(config)
  //   }
};
