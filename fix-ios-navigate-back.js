/**
 * 如果多次连续 wx 自带的 navigateBack，iOS 上只生效一次，Android 上生效多次。
 * 通过收集一个事件循环中调用的次数，达到和 Android 上一样的效果
 */
import Completer from './completer';

const deviceInfo = wx.getDeviceInfo();
const isIOS = deviceInfo.platform === 'ios';
// console.debug('deviceInfo.platform', deviceInfo.platform);
if (isIOS) {
  console.info('fix ios navigateBack');
  const oldNavigateBack = wx.navigateBack;
  let count = 0;
  let timer = null;
  let result = new Completer();
  /**返回，收集一个事件循环中的调用次数，然后统一处理 */
  async function navigateBack(options) {
    console.log('custom-navigateBack', count);
    if (options) {
      count += options.delta ?? 1;
    } else {
      count++;
    }

    timer && clearTimeout(timer);

    timer = setTimeout(() => {
      const oldResult = result;
      oldNavigateBack({
        ...options,
        delta: count,
      })?.then((v) => {
        oldResult.complete(v);
      });
      timer = null;
      count = 0;
      result = new Completer();
    }, 0);

    if (options?.complete || options?.success || options?.fail) {
      return;
    }

    return result.future;
  }

  Object.defineProperty(wx, 'navigateBack', {
    get() {
      return navigateBack;
    },
  });

  // console.log('wx', wx.navigateBack === navigateBack, wx.navigateBack === oldNavigateBack);
}
