<template>
  <view class="container">
    <u-navbar :title="title" @leftClick="leftClick" :placeholder="true">
    </u-navbar>

    <p>{{ data.Remark || '' }}</p>

    <view v-for="(item, index) in dataPro" :key="item.Id">
      <view style="margin-top: 20rpx">
        <p>
          {{ index + 1 }}.{{ item.Title }}
          <span style="color: red; margin-left: 10rpx" v-if="item.IsRequired"
            >*</span
          >
        </p>
        <u-radio-group
          v-model="item.value"
          placement="column"
          :disabled="disabled"
          v-if="item.ProblemType === 1"
        >
          <u-radio
            :customStyle="{ marginBottom: '8px' }"
            v-for="(k, index2) in item.GaugeProblemDetails"
            :key="k.Id"
            :label="k.ProblemOption"
            :name="k.Id"
            @change="radioGroupChange($event, index, index2)"
            @click="radioGroupClick($event, index, index2)"
          >
          </u-radio>
        </u-radio-group>
        <u-checkbox-group
          v-model="item.value"
          placement="column"
          :disabled="disabled"
          v-if="item.ProblemType === 2"
        >
          <u-checkbox
            :customStyle="{ marginBottom: '8px' }"
            v-for="(k, index2) in item.GaugeProblemDetails"
            :key="k.Id"
            :label="k.ProblemOption"
            :name="k.Id"
            @change="checkboxGroupChange($event, index, index2)"
          >
          </u-checkbox>
        </u-checkbox-group>
        <u-textarea
          :disabled="disabled"
          v-model="item.GaugeProblemDetails[0].Answer"
          placeholder="请输入内容"
          v-if="item.ProblemType === 3"
        ></u-textarea>
        <u-input
          @blur="numberInputBlur"
          :disabled="disabled"
          v-model="item.GaugeProblemDetails[0].Answer"
          v-if="item.ProblemType === 4"
          customStyle="background:white"
        >
        </u-input>
      </view>
    </view>
    <u-modal
      :show="show"
      title="温馨提示"
      content="本次评估还未完成,退出后已填写的内容不会保存,确认要退出吗"
      @cancel="cancel"
      @confirm="confirm"
      :showCancelButton="true"
    ></u-modal>
    <u-toast ref="uToast"></u-toast>

    <view class="bomm-btn-style"></view>
    <u-button
      @click="save"
      type="success"
      shape="circle"
      text="提交"
      customStyle="width:90%;bottom: 30px;position: fixed; left: 50%;transform: translateX(-50%);backgroundColor:#29B7A3;color:#fff"
      v-if="!disabled"
    >
    </u-button>
  </view>
</template>

<script>
const app = getApp();
import SessionState from '@/libs/util/session_state';
import {
  IMManager,
  IMCustomMessageBody,
  IMCustomMessageType,
  IMMessage,
} from 'kfx-im';
import { AssessClientEvent } from '../utils/eventKeys';
import { objNotEmpty } from '../utils/utils.js';
import { guid } from '../utils/validate';
import { insertGauge } from './api.js';
import { getGaugeById } from '../api/training';
export default {
  data() {
    return {
      id: '', // 量表id
      data: {},
      show: false,
      radiovalue: '',
      DctSendSign: '',
      RelatedId: '',
      goReq: false,
      next: true,
      roomType: 0, // 1:居家指导群 2:问诊群
      roomId: '', // 提交量表后，对应收到消息的会话房间号
      disabled: false,
      sendMessage: '',
      title: '自主评估',
      canNext: false,
      dataPro: [],
      Source: null,
    };
  },
  onLoad(option) {
    this.DctSendSign = option.DctSendSign;
    this.RelatedId =
      !option.RelatedId || option.RelatedId == 'null' ? null : option.RelatedId;
    this.id = option.id;
    this.roomType = parseInt(option.roomType || 0);
    this.roomId = option.roomId;
    this.sendMessage = option.sendMessage;
    this.disabled = option.disabled == 'true' ? true : false;
    this.Source = option.Source ? option.Source * 1 : null;
    if (!option.itemInfo) {
      this.getDetail();
    } else {
      this.disabled = true;
      let obj = JSON.parse(decodeURIComponent(option.itemInfo));
      obj.PatGaugeProblems.forEach((v) => {
        v.GaugeProblemDetails = v.PatGaugeProblemDetails;
      });
      obj.GaugeProblems = obj.PatGaugeProblems;
      obj.GaugeProblems.forEach((o) => {
        if (o.ProblemType === 1) {
          o.value = o.GaugeProblemDetails.filter((k) => k.Answer == 1)[0].Id;
        } else if (o.ProblemType === 2) {
          const arr = o.GaugeProblemDetails.filter((k) => k.Answer == 1);
          let arr1 = [];
          arr.forEach((m) => {
            arr1.push(m.Id);
          });
          o.value = arr1;
        }
      });
      this.data = obj;
      this.dataPro = obj.GaugeProblems;
      uni.showLoading({
        title: '正在加载数据',
      });
      this.$nextTick(() => {
        uni.hideLoading();
      });
    }
  },
  methods: {
    numberInputBlur(value) {
      const rex = /^-?\d*\.?\d{0,2}$/;
      if (!rex.test(value)) {
        this.$refs.uToast.show({
          message: '输入的数字必须是整数或者带两位小数',
          type: 'error',
        });
      }
    },
    radioGroupClick(e, index, index2) {
      console.log(e, index, index2);
      if (
        this.data.GaugeProblems[index].GaugeProblemDetails[index2].Answer === 1
      ) {
        this.data.GaugeProblems[index].GaugeProblemDetails[index2].Answer = 0;
      } else {
        this.data.GaugeProblems[index].GaugeProblemDetails[index2].Answer = 1;
      }
    },
    radioGroupChange(e, index, index2) {
      this.data.GaugeProblems[index].GaugeProblemDetails.forEach((e) => {
        e.Answer = 0;
      });
      this.data.GaugeProblems[index].GaugeProblemDetails[index2].Answer = 1;
    },
    checkboxGroupChange(e, index, index2) {
      if (e) {
        this.data.GaugeProblems[index].GaugeProblemDetails[index2].Answer = 1;
      } else {
        this.data.GaugeProblems[index].GaugeProblemDetails[index2].Answer = 0;
      }
    },
    async getDetail() {
      uni.showLoading({
        title: '正在加载数据',
      });
      let res = await getGaugeById(this.id);
      if (res.Type == 200) {
        res.Data.GaugeProblems.map((v) => {
          if (v.ProblemType === 3 || v.ProblemType === 4) {
            v.GaugeProblemDetails = [
              {
                Answer: '',
              },
            ];
          }
        });
        this.data = res.Data;
        this.data.Source = this.Source;
        this.dataPro = res.Data.GaugeProblems;
        this.title = res.Data.Name;
      }
      this.$nextTick(() => {
        uni.hideLoading();
      });
    },
    async save() {
      const checkData = this.data.GaugeProblems.filter((e) => e.IsRequired);
      let selectArrNum = 0;
      let inputArrNum = 0;
      let noSelect = 0;
      let noInput = 0;
      checkData.forEach((e) => {
        if (e.ProblemType === 3) {
          inputArrNum++;
        } else {
          selectArrNum++;
        }
      });
      console.log('总数', selectArrNum, inputArrNum);
      checkData.forEach((e, index) => {
        if (e.ProblemType != 3 && e.ProblemType != 4) {
          // 单选/多选
          if (e.GaugeProblemDetails.findIndex((o) => o.Answer === 1) == -1) {
            // 没找到
            noSelect++;
          }
        } else {
          if (
            e.GaugeProblemDetails[0].Answer === '' ||
            e.GaugeProblemDetails[0].Answer === null
          ) {
            noInput++;
          }
        }
      });
      console.log('未选择', noSelect, noInput);
      if (noSelect > 0 || noInput > 0) {
        this.next = false;
      } else {
        this.next = true;
      }
      if (!this.next) {
        console.log('还没有选择完');
        this.$refs.uToast.show({
          message: '请先选择完之后再提交',
          type: 'error',
        });
        return;
      }
      const numberInputArr = checkData.filter((p) => p.ProblemType === 4);
      const re = /^-?\d*\.?\d{0,2}$/;
      const flag = numberInputArr.some(
        (i) => !re.test(i.GaugeProblemDetails[0].Answer)
      );
      if (flag) {
        this.$refs.uToast.show({
          message: '数字类型输入有问题',
          type: 'error',
        });
        return;
      }
      this.data.DctSendSign = this.DctSendSign;
      this.data.RelatedId = this.RelatedId;
      this.data.patientId = app.globalData.userInfo.Id;
      this.data.PatGaugeProblems = JSON.parse(
        JSON.stringify(this.data.GaugeProblems)
      );
      // delete this.data.GaugeProblems
      this.data.PatGaugeProblems.forEach((e) => {
        e.PatGaugeProblemDetails = JSON.parse(
          JSON.stringify(e.GaugeProblemDetails)
        );
        // delete e.GaugeProblemDetails
        e.PatGaugeProblemDetails.forEach((k) => {
          delete k.Id;
          delete k.ProlemId;
        });
      });
      this.data.PatGaugeResults = this.data.GaugeResults;
      // delete this.data.GaugeResults
      this.data.PatGaugeDiseaseRelations = [];
      this.data.GaugeDiseaseRelations.forEach((e) => {
        delete e.Id;
        delete e.EvaluateGaugeId;
        delete e.CreatedTime;
      });
      this.data.PatGaugeProblems.map((e) => {
        if (e.ProblemType != 3 && !e.IsRequired) {
          if (
            e.PatGaugeProblemDetails.findIndex((o) => o.Answer === 1) === -1
          ) {
            console.log(e.PatGaugeProblemDetails);
            e.PatGaugeProblemDetails.map((k) => {
              k.Answer = null;
            });
          }
        }
      });
      if (this.data.Source === null) delete this.data.Source;
      // return
      this.saveGauge();
    },
    async saveGauge() {
      uni.showToast({
        title: '提交中...',
        mask: true,
      });

      let res = await insertGauge(this.data);
      if (res.Type != 200) {
        uni.hideLoading();
        uni.showToast({
          title: res.Content,
          icon: 'none',
        });
        return;
      }

      // 提交量表成功
      uni.$emit(AssessClientEvent.updateAssess);

      if (this.sendMessage) {
        uni.hideLoading();
        let data = {
          BaseEvaluateGaugeId: this.data.Id,
          DctSendSign: this.DctSendSign,
          RelatedId: this.RelatedId,
          SumPoint: res.Data.SumPoint,
          SuccessResultContent: res.Data.SuccessResultContent,
          Source: this.data.Source,
        };
        uni.redirectTo({
          url: `./result?itemInfo=${encodeURIComponent(
            JSON.stringify(data)
          )}&sendMessage=noSend&roomId=${this.roomId}`,
        });
        return;
      }

      // 给居家指导群或问诊群里发通知消息
      // 注意：此处可能没有 roomId，如果没有则表示不需要向群发送提示信息
      var session;
      try {
        session = await SessionState.instance().findSession(this.roomId);
      } catch (e) {
        console.debug('autonomy: 未找到session，roomId:', this.roomId);
      }
      if (objNotEmpty(session)) {
        let assessSubmitNotificationMessageBody = {
          assessSign: this.DctSendSign,
          assessId: this.id,
          assessName: this.data.Name,
          submitId: res.Data.Id,
          visitId: this.RelatedId,
        };
        let customMessage = new IMCustomMessageBody(
          assessSubmitNotificationMessageBody,
          IMCustomMessageType.AssessSubmitNotification
        );
        var message = IMMessage.custom(JSON.stringify(customMessage.toJson()));
        message.targetId = session.sessionId;
        try {
          await IMManager.instance.sendMessage(message);
        } catch (error) {
          console.error('autonomy: 发送消息失败', error);
        }
      }
      uni.hideLoading();

      let data = {
        BaseEvaluateGaugeId: this.data.Id,
        DctSendSign: this.DctSendSign,
        RelatedId: this.RelatedId,
        SumPoint: res.Data.SumPoint,
        SuccessResultContent: res.Data.SuccessResultContent,
      };
      uni.redirectTo({
        url: `./result?itemInfo=${encodeURIComponent(
          JSON.stringify(data)
        )}&roomType=${this.roomType}&roomId=${this.roomId}`,
      });
    },
    leftClick() {
      if (this.disabled) {
        uni.navigateBack();
      }
      this.show = true;
    },
    cancel() {
      this.show = false;
    },
    confirm() {
      this.show = false;
      uni.navigateBack();
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  padding: 10px;

  /deep/ .u-radio {
    width: 100% !important;
  }

  /deep/ .u-radio__icon-wrap {
    width: 30px !important;
    height: 30px !important;
  }

  /deep/ .u-checkbox__icon-wrap {
    width: 30px !important;
    height: 30px !important;
  }

  /deep/ .u-radio__text {
    font-size: 17px !important;
    flex: 1;
  }

  /deep/ .u-checkbox__text {
    font-size: 17px !important;
    flex: 1;
  }

  /deep/ .u-radio-group {
    margin-top: 20rpx;
    margin-left: 20rpx;
  }

  /deep/ .u-checkbox-group {
    margin-top: 20rpx;
    margin-left: 20rpx;
  }

  /deep/ .u-checkbox__text {
    line-height: normal !important;
  }

  /deep/ .u-radio__text {
    line-height: normal !important;
  }
}
</style>
