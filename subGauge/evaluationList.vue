<template>
  <view class="container">
    <u-loading-page :loading="loading"></u-loading-page>
    <view v-if="list.length > 0">
      <view v-for="(item, index) in list" :key="item.Id">
        <view class="box" @click="toDetal(item)">
          <view class="box-top">
            <!-- <u-avatar src="/static/images/lb.png" size="40" shape="square"></u-avatar> -->
            <u--image
              :showLoading="true"
              :src="
                item.IsDoctor
                  ? '/static/images/lbys.png'
                  : '/static/images/lbhz.png'
              "
              width="40px"
              height="40px"
            ></u--image>
            <view class="box-top-right">
              <view class="box-top-right-top">
                <p
                  style="
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    width: 70%;
                  "
                >
                  {{ item.Name }}
                </p>
                <p style="color: #29b7a3" v-show="item.IsEvaluate">已评估</p>
                <p v-show="!item.IsEvaluate" style="color: #ff3b30">未评估</p>
              </view>
            </view>
          </view>
          <view class="box-top-right-top1" v-if="item.IsEvaluate">
            <p style="font-size: 14px; color: #666666">
              上次评估时间：{{ item.EvaluateTime }}
            </p>
          </view>
        </view>
      </view>
    </view>
    <u-empty
      mode="data"
      icon="	https://cdn.uviewui.com/uview/empty/data.png"
      v-else
    >
    </u-empty>
    <view style="height: 100px"></view>
    <u-button
      :disabled="buttonDisable"
      type="success"
      @click="onSeeReport"
      shape="circle"
      text="查看报告"
      customStyle="width:90%;bottom: 30px;position: fixed; left: 50%;transform: translateX(-50%);fontSize:16px;z-index:99"
    >
    </u-button>
  </view>
</template>

<script>
import { getEvaList } from '@/api/training.js';
import { dateFormat } from '@/utils/validate.js';
import { ItemGroupBy } from '@/utils/utils';
export default {
  data() {
    return {
      sign: '',
      list: [],
      buttonDisable: true,
      flag: false,
      loading: false,
    };
  },
  onLoad({ sign }) {
    this.sign = sign;
  },
  onShow() {
    this.getEvaListData(this.sign);
  },
  methods: {
    async getEvaListData(sign) {
      const res = await getEvaList(sign);
      if (res.Type === 200) {
        res.Data.forEach((k) => {
          k.EvaluateTime = dateFormat(k.CreatedTime);
        });
        const sortArr = res.Data.sort((a, b) => {
          if (!a.IsDoctor && b.IsDoctor) {
            return -1;
          }
        });
        this.initListData(sortArr);
      }
    },
    initListData(listData) {
      this.loading = true;
      const ItemGroupByData = ItemGroupBy(listData, 'IsDoctor');
      ItemGroupByData.forEach((v) => {
        const data = v.data.sort((a, b) => {
          if (!a.IsEvaluate && b.IsEvaluate) {
            return -1;
          }
        });
        const sortFinData = ItemGroupBy(data, 'IsEvaluate');
        sortFinData.forEach((k) => {
          if (k.type) {
            k.data = k.data.sort((a, b) => {
              if (a.EvaluateTime < b.EvaluateTime) {
                return -1;
              }
              if (a.EvaluateTime > b.EvaluateTime) {
                return 1;
              }
            });
          }
        });
        v.sortData = sortFinData;
      });
      const listDataFin = [];
      ItemGroupByData.forEach((s) => {
        s.sortData.forEach((u) => {
          u.data.forEach((l) => {
            listDataFin.push(l);
          });
        });
      });
      this.list = listDataFin;
      this.buttonDisable = !listDataFin.some((o) => o.IsEvaluate);
      this.$nextTick(() => {
        this.loading = false;
      });
    },
    toDetal(item) {
      // 备注：评估方案的量表，填写后不需要向群中发送提交通知，所以不需要传roomId
      let url = '';
      if (!item.IsEvaluate && !item.IsDoctor) {
        url = `./autonomy?id=${item.BaseEvaluateGaugeId}&DctSendSign=${item.DctSendSign}&RelatedId=${item.RelatedId}&sendMessage=noSend`;
      } else if (item.IsEvaluate) {
        if (!item.IsDoctor) {
          url =
            `./result?itemInfo=` +
            encodeURIComponent(JSON.stringify(item)) +
            '&sendMessage=noSend';
          // url = `./autonomy?itemInfo=` + encodeURIComponent(JSON.stringify(item))
        } else {
          url =
            `./autonomy?itemInfo=` +
            encodeURIComponent(JSON.stringify(item)) +
            '&sendMessage=noSend';
        }
      }
      uni.navigateTo({
        url,
      });
    },
    onSeeReport() {
      const prescriptionId = this.list[0].RelatedId;
      uni.navigateTo({
        url: './seeReport?sign=' + this.sign,
      });
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  height: 100vh;
  background-color: #f7f7f7;
  padding: 30rpx;

  /deep/ .insideStyle {
    padding: 0 32rpx;
  }

  .box {
    padding: 20upx;
    background-color: white;
    margin-bottom: 35rpx;
    box-shadow: 2.5px 3px 9px 0px rgba(0, 0, 0, 0.08);
    border-radius: 16rpx;

    .box-top-right-top1 {
      margin-top: 20upx;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .botton-style {
        width: 138upx;
        height: 60upx;
        background-color: #29b7a3;
        font-size: 12px;
        text-align: center;
        line-height: 60upx;
        border-radius: 30px;
        color: white;
      }
    }

    .box-top {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .box-top-right {
        flex: 1;
        margin-left: 20upx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        .box-top-right-top {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .botton-style {
            width: 138upx;
            height: 60upx;
            background-color: #29b7a3;
            font-size: 15px;
            text-align: center;
            line-height: 60upx;
            border-radius: 30px;
          }
        }
      }
    }
  }
}
</style>
