import {
  setUserScanLog
} from '@/api/passport.js';
/**
 * 通过分享进入
 *
 * @param {Object} obj  传递过来的参数
 */
export function onToPathByUserShare(obj) {
  console.log('onToPathByUserShare', obj.path);
  let url = obj.path;
  // 处理特殊分享
  handleProcessingShare(obj)
  if (obj.query) {
    const decode = decodeURIComponent(obj.query);
    url += `?${decode}`;
  }
  uni.reLaunch({
    url,
  });
}

function handleProcessingShare(obj) {
  const app = getApp()
  if (obj.path === '/subReport/SpineScreening') {
    setUserScanLog({
      "UserId": app.globalData.userInfo.Id, //患者Id
      // "OpenId" : "xxxx",
      "RelationId": obj.shareUserId, //推荐者-普通用户Id
      "OrganizationId": null,
      "SingleOne": false,
      "Type": 101 //患者的推荐者-普通用户
    })
  }
}
