const app = getApp();
import {
  setOrCanDoc,
  setUserScanLog,
  getUserClaims
} from '@/api/passport.js';
import {
  initServer
} from './submit.js';
import {
  getDocInfoMation,
  setPreVisitRecord,
  insertPreApplyRecord
} from '@/api/consult.js';

import {
  getDictStd
} from '@/api/bff.js';
import logger from '@/services/logger/index.js';

// 环境
import config from '@/config';

import {
  formateDate
} from '@/utils/validate.js';
/**
 * 通过患者扫二维码进入这里，处理对应的逻辑 他的参数就是一个q：string
 *
 * @param {{UserId:string,OpenId:string,RelationId:string,Type:number}} reqData
 * @param {{docId:string} | {orgId:string,orgName:string}} obj
 */
export async function initReqData(reqData, obj) {
  if (obj.docId) {
    // 说明是医生或者治疗师的二维码
    reqData.RelationId = obj.docId;
    reqData.Type = 0;
    if (app.globalData.userInfo.Id) {
      if (obj.scene && Number(obj.scene) === 9) {
        uni.reLaunch({
          url: `/subPackIndex/user/preConsultation?doctorId=${obj.docId}`,
        });
      } else {
        getDocInfo(obj.docId);
      }
    }
  } else if (obj.deptId && obj.deptName) {
    // 说明是科室的二维码
    const {
      orgId,
      orgName,
      deptId,
      deptName
    } = obj;
    reqData.Type = 8;
    reqData.RelationId = deptId;
    reqData.OrganizationId = orgId;
    await getApp().changeOrgAndMark({
      orgId,
      orgName,
    });
    uni.setStorageSync('deptId', deptId);
    goPage('deptList', obj);
  } else if (obj.orgId && obj.groupId) {
    // 说明是通过扫描机构端的回院提醒二维码进入的
    reqData.Type = 1;
    reqData.RelationId = obj.orgId;
    await getApp().changeOrgAndMark({
      orgId: obj.orgId,
      orgName: obj.orgName,
    });
    if (!app.globalData.userInfo.Id) {
      return;
    }
    uni.redirectTo({
      url: `/subPrescription/userUpdate?Scene=returningHospital&OrgId=${obj.orgId}&GroupId=${obj.groupId}`,
    });
  } else if (obj.orgId && obj.orgName && !obj.genCode) {
    // 说明是医院的二维码
    app.globalData.via = 'org';
    const {
      orgId,
      orgName
    } = obj;
    reqData.Type = 1;
    reqData.RelationId = orgId;
    await getApp().changeOrgAndMark({
      orgId,
      orgName,
      via: app.globalData.via,
    });
    goPage(obj.toIndex || 'docList');
  } else if (obj.genCode && obj.orgId && obj.orgName) {
    // 说明是通过推广码进入的
    const {
      orgId,
      orgName,
      genCode
    } = obj;
    reqData.Type = 3;
    reqData.RelationId = genCode;
    reqData.OrganizationId = orgId;
    await getApp().changeOrgAndMark({
      orgId,
      orgName,
    });
    goPage('index');
  } else if (
    (obj.RelatedId || obj.QuickPrescriptionId) &&
    obj.Type &&
    obj.CreatorId
  ) {
    // 说明是通过快速开方生成的二维码进入
    reqData.RelationId = obj.CreatorId;
    reqData.Type = 0;
    goToPrescriptionPage(obj, 'kskf');
  } else if (obj.CreatorId && obj.RecommendId && obj.RecommendName) {
    // 扫描方案申请码
    reqData.RelationId = obj.CreatorId;
    reqData.Type = 0;
    goToPrescriptionPage(obj, 'sqm');
  } else if (obj.shareUserId) { // 如果是通过首页分享的二维码扫码进来 需要记录关联关系，并且跳转到脊柱筛查页面
    goToSpineScreeningPage(obj)
    return;
  } else if (obj.noParameterPath) {
    if (obj.noParameterPath === 'subReport/SpineScreening') {
      reqData.InviterName = '好脊星';
      reqData.Type = 6;
      delete reqData.RelationId;
    }
    if (obj.noParameterPath === 'subReport/UserLogin') {
      reqData.InviterName = '视光筛查';
      reqData.Type = 7;
      delete reqData.RelationId;
    }
    onGoToNoParameterPage(obj);
  }
  initServer(reqData);
}

function onGoToNoParameterPage(obj) {
  if (!app.globalData.userInfo.Id) {
    return;
  }
  // 无参跳转页面
  uni.redirectTo({
    url: `/${obj.noParameterPath}`,
  });
}

function goToSpineScreeningPage(obj) {
  if (!app.globalData.userInfo.Id) {
    return;
  }
  setUserScanLog({
    "UserId": app.globalData.userInfo.Id, //患者Id
    // "OpenId" : "xxxx",
    "RelationId": obj.shareUserId, //推荐者-普通用户Id
    "OrganizationId": null,
    "SingleOne": false,
    "Type": 101 //患者的推荐者-普通用户
  }).finally(() => {
    uni.reLaunch({
      url: '/subReport/SpineScreening',
    });
  })
}

async function goToPrescriptionPage(obj, type) {
  if (!app.globalData.userInfo.Id) {
    return;
  }

  const res = await getDocInfoMation({
    doctorId: obj.CreatorId,
  });

  if (res.Type == 200) {
    const orgId = res.Data.Doctor.OrganizationId,
      orgName = res.Data.Doctor.OrganizationName;
    getApp().changeOrgAndMark({
      orgId,
      orgName,
    });
    let data1 = {
      UserId: app.globalData.userInfo.Id,
      FollowUserId: res.Data.Doctor.UserId,
    };
    setOrCanDoc('setfollow', data1);
  } else {
    logger.warn('获取医生信息失败', res.Content);
  }

  if (type === 'kskf') {
    uni.redirectTo({
      url: `/subPrescription/detail?RelatedObj=${encodeURI(JSON.stringify(obj))}`,
    });
  } else if ('sqm') {
    try {
      setUserScanLog({
        "UserId": app.globalData.userInfo.Id, //患者Id
        "RelationId": obj.CreatorId,
        "OrganizationId": res.Data.Doctor.OrganizationId,
        "SingleOne": false,
        "Type": 100
      })
      insertPreApplyRecord({
        RecommendId: obj.RecommendId,
        DctId: obj.CreatorId,
        UserId: app.globalData.userInfo.Id,
        UserPhoneNumber: app.globalData.userInfo.PhoneNumber,
        RecommendName: obj.RecommendName,
      });
      uni.redirectTo({
        url: `/subPackIndex/docDetail?docId=${res.Data.Doctor.UserId}&scene=sqm`,
      });
    } catch (error) {
      this.$log.error(this.$envVersion, `App onUnhandledRejection`, JSON.stringify(error));
    }
  }
}
async function goPage(type = 'docList', objData) {
  if (!app.globalData.userInfo.Id) {
    return;
  }

  if (type === 'docList') {
    uni.reLaunch({
      url: '/subPackIndex/seeDoc?showToReport=true',
    });
  } else if (type === 'deptList') {
    const obj = {
      OrganizationName: objData.orgName,
      OrganizationId: objData.orgId,
      DepartmentId: objData.deptId,
      DepartmentName: objData.deptName,
      RoleTypes: [],
    };
    uni.redirectTo({
      url: `/subPackIndex/user/postDiagnosisReport?type=scanDept&docInfo=${encodeURIComponent(JSON.stringify(obj))}`,
    });
  } else {
    uni.reLaunch({
      url: '/pages/index/index',
    });
  }
}
async function getDocInfo(id) {
  // 获取医生信息
  let res = await getDocInfoMation({
    doctorId: id,
  });
  if (res.Type == 200) {
    const orgId = res.Data.Doctor.OrganizationId,
      orgName = res.Data.Doctor.OrganizationName;
    if (orgId && orgName) {
      await getApp().changeOrgAndMark({
        orgId,
        orgName,
      });
    }
    try {
      // 判断当前医生的机构是否开启了风险预估
      const riskRes = await getDictStd({
        orgId,
      });
      if (riskRes.Type === 200) {
        const isOpen = riskRes.Data.filter(
          (s) => s.Code === 'OldPeopleRiskWarn'
        )[0].Payload[0].Value;
        if (isOpen) {
          uni.reLaunch({
            url: '/subRiskWarning/index',
          });
          return;
        }
      }
    } catch (error) {
      console.log(error);
    }

    try {
      setPreVisitRecord({
        DoctorId: id,
        PatientId: app.globalData.userInfo.Id,
        Type: 0, //0=诊后报道 1=预问诊
        BeginTime: formateDate(new Date(), 'YYYY-MM-DD 00:00:00'), //当天起始时间
        EndTime: formateDate(new Date(), 'YYYY-MM-DD 23:59:59'), //当天结束时间
      }).then((res) => {
        if (res.Type !== 200 || !res.Data) {
          logger.error(
            config.envVersion,
            `setPreVisitRecord接口出错`,
            res.Content
          );
        }
      });
    } catch (error) {
      logger.error(config.envVersion, `setPreVisitRecord接口出错`, error);
    }

    // 判断是否报到过该医生
    const data1 = {
      UserId: app.globalData.userInfo.Id,
      ClaimTypes: ['VisitedRegister'],
      ClaimValue: id,
    };
    const repData = await getUserClaims(data1);

    if (repData.Data.length > 0) {
      uni.redirectTo({
        url: `/subPackIndex/docDetail?docId=${id}`,
      });
    } else {
      const docInfo = encodeURIComponent(JSON.stringify(res.Data.Doctor));
      uni.redirectTo({
        // url: '/subPackIndex/docDetail?docId=' + res.Data.Doctor.UserId
        url: '/subPackIndex/user/postDiagnosisReport?docInfo=' +
          docInfo +
          '&type=scanDoctor',
      });
    }
  }
}
