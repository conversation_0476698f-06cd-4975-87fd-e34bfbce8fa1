import { toUrl } from './template.js';
import { weChatOfficialAccountToMin } from './gzh.js';
import { otherToMin } from './other.js';
import { onToPathByUserShare } from './share.js';
/**
 * 通过患者点击模板或者通过公众号进入这里，将用户的访问消息发送给后台（公众号）
 *
 * @param {Object} obj = {code, handler,type}} 传递过来的参数
 */
export function jumpUrl(obj) {
  console.log('jumpUrlobj', obj);
  if (obj.handler === 'mb') {
    // 通过点击模板进来的
    toUrl(obj);
  } else if (obj.handler === 'gzh') {
    // 通过点击公众号进来的
    weChatOfficialAccountToMin(obj);
  } else if (obj.handler === 'other') {
    // 其他第三方页面跳转
    otherToMin(obj);
  } else if (obj.handler === 'share') {
    // 通过分享进入小程序
    onToPathByUserShare(obj);
  } else if (obj.handle === 'minProgram') {
    let url = `${obj.path}?id=${obj.packId}&type=1`;
    console.log('url', url);
    uni.reLaunch({
      url,
    });
  }
}
