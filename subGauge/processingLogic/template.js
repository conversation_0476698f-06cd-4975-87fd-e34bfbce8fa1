import { startTrainingProgram } from '@/api/training.js';
const app = getApp();
/**
 * 通过患者点击模板
 *
 * @param {Object} obj = {type, ohter} 传递过来的参数
 */
export async function toUrl(obj) {
  if (obj.type.toLocaleLowerCase() == 'prescription') {
    // 通过点击模板进来的(处方)
    uni.reLaunch({
      url: `/subPrescription/detail?id=${obj.prescriptionId}`,
    });
  } else if (obj.type.toLocaleLowerCase() == 'session') {
    // 都是点击模板进来的 去聊天页面
    if (obj.consultId && obj.sessionType == 3) {
      // 去往1v1聊天页面
      uni.reLaunch({
        // url: `/subPackChat/sessionChatPage?consultId=${obj.consultId}`
        url: `/pages/index/index?consultId=${obj.consultId}&sessionType=3`,
      });
    } else if (obj.sessionType == 2 && obj.trainingId) {
      // 去训练管理聊天页面
      await startTrainingProgramPlat(obj.trainingId);
      uni.reLaunch({
        url: `/pages/plan/index?trainingId=` + obj.trainingId,
      });
    } else if (obj.trainingId && !obj.sessionType) {
      // 去计划页面
      await startTrainingProgramPlat(obj.trainingId);
      uni.reLaunch({
        // url: `/subPackChat/sessionChatPage?consultId=${obj.consultId}`
        url: `/pages/plan/index`,
      });
    }
  } else if (obj.type.toLocaleLowerCase() == 'medicalcare') {
    // 点击模板 如果计划改变了 去往计划页面
    await startTrainingProgramPlat(obj.trainingId);
    uni.reLaunch({
      url: `/pages/plan/index`,
    });
  } else if (obj.type.toLocaleLowerCase() == 'subsidies') {
    // 点击模板 辅具的补贴申请
    uni.reLaunch({
      url: `/subAssistiveDevice/goodsDetail?goodsId=${obj.goodsId}&classifyId=${obj.classifyId}&prescriptionId=${obj.prescriptionId}`,
    });
  } else if (
    obj.type.toLocaleLowerCase() == 'serviceevaluation' &&
    obj.trainingId
  ) {
    uni.reLaunch({
      url: `/subServices/planIndex?Id=${obj.trainingId}`,
    });
  } else if (obj.type.toLocaleLowerCase() == 'followupmission') {
    // 随访任务
    uni.reLaunch({
      url: `/subFollowUp/followUpDetailListPage`,
    });
  } else if (obj.type.toLocaleLowerCase() == 'followuptemplatetask') {
    // 随访模板任务
    uni.reLaunch({
      url: `/subPackIndex/news`,
    });
  } else if (obj.type.toLocaleLowerCase() == 'appointment') {
    // 视光预约记录
    uni.reLaunch({
      url: `/subReport/ReservationRecord`,
    });
  } else if (obj.type.toLocaleLowerCase() == 'returninghospital') {
    // 回院提醒
    uni.reLaunch({
      url: `/subPackIndex/user/returningHospital`,
    });
  }
}
// 开始训练计划
async function startTrainingProgramPlat(trainingId) {
  let userId = app.globalData.userInfo.Id;
  if (!userId) return null;

  let r = await startTrainingProgram(userId, trainingId);
  console.log('执行处方成功', r);
  return r;
}
