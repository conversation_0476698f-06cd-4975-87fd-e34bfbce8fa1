import config from '@/config';
/**
 * 通过患者点击公众号进来
 *
 * @param {Object} obj  传递过来的参数
 */
export function otherToMin(obj) {
  uni.reLaunch({
    url: obj.path,
  });
}
export function onSaveUserFormBeforeLogin(data, option) {
  if (option.path && option.path === '/subReport/UserLogin') {
    data.InviterName = '视光筛查';
    data.Type = 7;
    delete data.RelationId;
  } else if (option.path && option.path === '/subReport/SpineScreening') {
    data.InviterName = '好脊星';
    data.Type = 6;
    delete data.RelationId;
  } else if (option.scen === 'HK') {
    data.InviterName = '慧康';
    data.Type = 10;
    delete data.RelationId;
  } else {
    // data.RelationId = config.orgId
    // data.Type = 1
    return null;
  }
  return data;
}
