const app = getApp();

import { getSecctionKey } from '@/api/other.js';

import { getrScanQRUser } from '@/api/passport.js';
/**
 * 通过患者扫二维码或者通过公众号进入这里，将用户的访问消息发送给后台
 *
 * @param {Object} reqData = {UserId,OpenId,RelationId,Type}
 */
export function initServer(reqData) {
  if (!app.globalData.userInfo.Id) {
    getWXCode(reqData);
  } else {
    if (reqData.Type === null) return;
    sendOpenIdToServer(reqData);
  }
}

function getWXCode(reqData) {
  uni.login({
    provider: 'weixin',
    success: (loginRes) => {
      console.log('loginRes', loginRes.code);
      getOpenId(loginRes.code, reqData);
    },
  });
}
async function getOpenId(code, reqData) {
  let res = await getSecctionKey(code);
  if (res.Type === 200) {
    reqData.OpenId = res.Data.OpenId;
    sendOpenIdToServer(reqData);
  }
}
async function sendOpenIdToServer(reqData) {
  await getrScanQRUser(reqData);
}
