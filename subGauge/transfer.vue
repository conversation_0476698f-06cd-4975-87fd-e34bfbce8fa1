<template>
  <view class="container">
    <view v-if="show">
      <view class="container-top">
        <u--image
          :showLoading="true"
          src="/static/images/yankang-icon.png"
          width="160px"
          height="160px"
        >
        </u--image>
      </view>
      <u-button
        v-if="!agrCheck"
        type="primary"
        :plain="true"
        shape="circle"
        :hairline="true"
        text="手机号一键登录"
        :throttleTime="1000"
        customStyle="width:80%;margin:0 auto;backgroundColor:#29B7A3"
        color="#ffffff"
        @click="handleClick"
      >
      </u-button>
      <u-button
        v-else
        type="primary"
        :plain="true"
        shape="circle"
        :hairline="true"
        text="手机号一键登录"
        :throttleTime="1000"
        customStyle="width:80%;margin:0 auto;backgroundColor:#29B7A3"
        color="#ffffff"
        openType="getPhoneNumber"
        @getphonenumber="getPhoneNumber"
      >
      </u-button>
      <view class="display-style1" style="margin-top: 20px; padding: 0 20px">
        <u-radio-group @change="groupChange1" v-model="agrCheck">
          <u-radio name="agrCheck" activeColor="#29B7A3" @change="radioChange1">
          </u-radio>
        </u-radio-group>
        <p style="font-size: 14px">
          我已阅读并同意<span style="color: #2979ff" @click="toLook('xy')"
            >《用户协议》</span
          >、<span style="color: #2979ff" @click="toLook('zc')"
            >《隐私政策》</span
          >及<span style="color: #2979ff" @click="toLook('zhzx')"
            >《账号注销协议》</span
          >
        </p>
      </view>
    </view>
    <u-modal
      :show="showToAuthentication"
      title="提示"
      content="您还没有实名认证,请先实名认证"
      confirmText="去认证"
      cancelText="取消"
      confirmColor="#29B7A3"
      @confirm="confirm"
      @cancel="confirmCancel"
      :showCancelButton="true"
    ></u-modal>
    <u-toast ref="uToast"></u-toast>
    <u-popup
      :show="showPrivacyPopup"
      :closeOnClickOverlay="false"
      :round="10"
      mode="bottom"
      @close="close"
    >
      <view style="padding: 40rpx">
        <p
          style="
            text-align: center;
            margin-bottom: 40rpx;
            font-size: 20px;
            font-weight: 700;
          "
        >
          提示
        </p>
        <text>
          在你使用服务之前，请仔细阅读<text
            @click="onClickPrivacyPopupTitle()"
            style="color: #29b7a3"
            >{{ PrivacyPopupTitle }}</text
          >。如果你同意{{ PrivacyPopupTitle }}，请点击“同意”开始使用。
        </text>
        <view class="display-style" style="margin-top: 40rpx">
          <button
            class="refuse-agree"
            @click="handleRefusePrivacyAuthorization()"
          >
            拒绝
          </button>
          <!-- <u-button type="primary" id="agree-btn" text="同意" openType="agreePrivacyAuthorization"
						@agreeprivacyauthorization="handleAgreePrivacyAuthorization()"></u-button> -->
          <button
            id="agree-btn"
            class="btn-agree"
            open-type="agreePrivacyAuthorization"
            @agreeprivacyauthorization="handleAgreePrivacyAuthorization()"
          >
            同意
          </button>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
const app = getApp();
import { initReqData } from './processingLogic/optionq.js';
import { jumpUrl } from './processingLogic/optionObj.js';
import config from '@/config';

import { initServer } from './processingLogic/submit.js';
import { onSaveUserFormBeforeLogin } from './processingLogic/other.js';
import { transformUrl } from '@/utils/utils.js';
import { getLoneShortLink } from '@/api/other.js';
export default {
  data() {
    return {
      agrCheck: '',
      reqData: {
        UserId: '', // 用户的Id
        OpenId: '', // 用户的openID
        RelationId: '', // 存储的医生(或者治疗师)ID或者是医院的ID
        Type: null, // 0：医生或者治疗师的 1:医院 2: 公众号 3:推广码
      },
      show: false, // 是否展示登录界面
      showToAuthentication: false, // 是否需要去认证
      option: {}, // 外部传过来的数据  二维码是一个string类型的 q ; 点击模板和公众号过来的是obj:{}
    };
  },
  async onLoad(option) {
    console.log('onLoad', option);
    this.option = option;
    this.$log.info(
      `${this.$envVersion}:外部跳转过来进入小程序登录页面的参数`,
      option
    );
    if (!app.globalData.token) {
      await getApp().isLaunchFinish();
    }
    this.reqData.UserId = app.globalData.userInfo.Id || '';
    this.reqData.OpenId = app.globalData.openId || '';
    if (!app.globalData.userInfo.Id) {
      this.show = true;
      if (option.q) {
        // 说明是通过二维码进来的（医院和医生） // 先就把患者登录的消息发给后端 防止用户不登录
        this.inintQrData();
      } else {
        // 说明其他方式进入小程序
        const data = onSaveUserFormBeforeLogin(this.reqData, option);
        if (!data) return;
        initServer(data);
      }
    } else {
      this.checkIsCertification();
    }
    this.saveWXCallBack();
  },
  methods: {
    close() {
      this.closePrivacyPopup();
    },
    handleClick() {
      if (!this.agrCheck) {
        uni.showModal({
          title: '温馨提示',
          content:
            '请您阅读并同意以下协议《用户协议》《隐私政策》《账号注销须知》',
          success: (res) => {
            if (res.confirm) {
              this.agrCheck = 'agrCheck';
            }
          },
        });
        return;
      }
    },
    groupChange1(n) {
      if (n == this.radioValue1 && this.num1 == 0) {
        // 第一次相等即执行以下代码
        this.num1++;
      } else {
        // 第一次后相等即执行以下代码
        // 置空 wxChcek 即取消选中的值
        this.agrCheck = '';
        // 初始化 num
        this.num1 = 0;
      }
    },
    // 当切换其他选项的时候此方法才会执行
    radioChange1(n) {
      this.radioValue1 = n;
      // 切换选项后需要初始化 num
      this.num1 = 0;
    },
    // 去实名认证
    confirm() {
      this.showModal2 = false;
      uni.navigateTo({
        url: '/subPackIndex/user/userUpdate?type=smxx',
      });
    },
    // 取消去实名认证
    confirmCancel() {
      this.showModal2 = false;
      uni.reLaunch({
        url: '/pages/index/index',
      });
    },
    // 微信一件登录
    async getPhoneNumber(e) {
      uni.showLoading({
        title: '登录中，请稍后',
      });
      const r = await getApp().loginInWithWX(e);
      if (r) {
        this.reqData.OpenId = app.globalData.openId;
        this.reqData.UserId = app.globalData.userInfo.Id;
        this.checkIsCertification();
        uni.hideLoading();
      } else {
        uni.hideLoading();
      }
    },
    // 检测是否认证 现在的流程是不要检测是否认证
    async checkIsCertification() {
      app.globalData.indexToPlan = true;
      if (this.option.q) {
        // 扫描二维码
        this.inintQrData();
      } else if (this.option.t === 'sms') {
        // 通过短信跳转
        let url = `${this.option.to}`;
        if (this.option.parmas) {
          const parmas = decodeURIComponent(this.option.parmas);
          url = url + `?${parmas}`;
        }
        uni.reLaunch({
          url,
        });
      } else {
        // 通过模板、公众号、其他方式跳转到这里的
        jumpUrl(this.option);
      }
    },
    async inintQrData() {
      const decodeUrl = decodeURIComponent(this.option.q);
      let quRL = '';
      if (decodeUrl.includes('https://s')) {
        // 说明是扫描短链接进入的
        const res = await getLoneShortLink(transformUrl(decodeUrl));
        if (res.Type !== 200 || !res.Data) {
          return;
        }
        quRL = res.Data;
      } else {
        quRL = decodeUrl;
      }
      const obj = this.getParms(quRL);
      initReqData(this.reqData, obj);
    },
    getParms(url) {
      if (url.indexOf('?') != -1) {
        let obj = {};
        let arr = url.slice(url.indexOf('?') + 1).split('&');
        arr.forEach((item) => {
          let param = item.split('=');
          obj[param[0]] = param[1];
        });
        return obj;
      } else {
        console.log('没有参数');
        return null;
      }
    },
    toLook(type) {
      uni.navigateTo({
        url: '/subPackIndex/user/aboutDetail?type=' + type,
      });
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  /deep/ .u-radio__icon-wrap {
    width: 25px !important;
    height: 25px !important;
  }

  &-top {
    text-align: center;
    padding: 100rpx;
    margin: 0 auto;

    /deep/ .u-image {
      margin: 0 auto;
    }
  }

  /deep/ .u-radio {
    margin-right: 0 !important;
  }
}
</style>
