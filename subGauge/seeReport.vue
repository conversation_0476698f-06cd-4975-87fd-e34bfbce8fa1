<template>
  <div class="seeReport">
    <div class="seeReport-back">
      <image src="./static/lb-icon.png" class="seeReport-back-img" />
    </div>

    <div class="user">
      <div class="seeReport-one">
        <div class="seeReport-one-base">基本信息</div>
        <div class="seeReport-user">
          <u-avatar :src="dataInfo.UserInfo.Img" :size="54"></u-avatar>
          <div style="margin-left: 16rpx">
            <div class="seeReport-one-top">
              <div style="width: 50%">
                <div class="seeReport-one-top-left">
                  <span class="seeReport-one-top-left-left">姓名</span>：{{
                    dataInfo.UserInfo.Name
                  }}
                </div>
                <div class="seeReport-one-top-left">
                  <span class="seeReport-one-top-left-left">年龄</span>：{{
                    dataInfo.UserInfo.Age
                  }}
                </div>
              </div>
              <div style="width: 50%">
                <div class="seeReport-one-top-left">
                  <span class="seeReport-one-top-left-left">性别</span>：
                  {{ dataInfo.UserInfo.Sex }}
                </div>
                <div class="seeReport-one-top-left">
                  <span class="seeReport-one-top-left-left">学历</span>：
                  {{ dataInfo.UserInfo.Education || '' }}
                </div>
              </div>
            </div>
            <p class="seeReport-one-bom">
              <span class="seeReport-one-top-left-left">报告生成时间</span>：{{
                dataInfo.CreatedTime
              }}
            </p>
          </div>
        </div>
      </div>

      <div class="seeReport-one">
        <div class="seeReport-one-base">评估结果</div>

        <div class="seeReport-one-tags">
          <span class="seeReport-one-tags-left">A:失能程度</span>
          <div
            style="
              display: flex;
              justify-content: flex-start;
              align-items: center;
            "
            v-if="dataInfo.SelfEvaLevel"
          >
            <u-tag
              shape="circle"
              text="轻度"
              v-if="dataInfo.SelfEvaLevel.Level === 1"
              bgColor="rgba(45, 230, 203, 0.20)"
              color="#2DE6CB"
              borderColor="#FFFFFF"
              style="margin-left: 10rpx"
            >
            </u-tag>
            <u-tag
              shape="circle"
              text="中度"
              v-if="dataInfo.SelfEvaLevel.Level === 2"
              bgColor="rgba(255, 227, 86, 0.20)"
              color="#E6C72D"
              borderColor="#FFFFFF"
              style="margin-left: 10rpx"
            >
            </u-tag>
            <u-tag
              shape="circle"
              text="重度"
              v-if="dataInfo.SelfEvaLevel.Level === 3"
              bgColor="rgba(45, 64, 230, 0.20)"
              color="#2D40E6"
              borderColor="#FFFFFF"
              style="margin-left: 10rpx"
            >
            </u-tag>
            <u-tag
              shape="circle"
              text="极重度"
              v-if="dataInfo.SelfEvaLevel.Level === 4"
              bgColor="rgba(230, 45, 45, 0.20)"
              color="#E62D2D"
              borderColor="#FFFFFF"
              style="margin-left: 10rpx"
            >
            </u-tag>
          </div>
        </div>

        <div class="seeReport-one-pross">
          <div class="seeReport-one-pross-style"></div>
          <div
            style="
              display: flex;
              align-items: center;
              justify-content: space-between;
            "
          >
            <div
              style="
                width: 25%;
                font-weight: 400;
                font-size: 24rpx;
                color: #999999;
                text-align: center;
              "
            >
              轻度
            </div>
            <div
              style="
                width: 25%;
                font-weight: 400;
                font-size: 24rpx;
                color: #999999;
                text-align: center;
              "
            >
              中度
            </div>
            <div
              style="
                width: 25%;
                font-weight: 400;
                font-size: 24rpx;
                color: #999999;
                text-align: center;
              "
            >
              重度
            </div>
            <div
              style="
                width: 25%;
                font-weight: 400;
                font-size: 24rpx;
                color: #999999;
                text-align: center;
              "
            >
              极重度
            </div>
          </div>
          <div
            class="seeReport-one-pross-circle"
            :style="{ left: topLeft }"
            v-if="dataInfo.SelfEvaLevel"
          ></div>
          <div
            class="seeReport-one-pross-circle1"
            :style="{ left: '25%' }"
          ></div>
          <div
            class="seeReport-one-pross-circle1"
            :style="{ left: '50%' }"
          ></div>
          <div
            class="seeReport-one-pross-circle1"
            :style="{ left: '75%' }"
          ></div>
        </div>

        <div>
          <div
            v-for="(item, index) in dataInfo.SelfEva"
            :key="item.Id"
            class="seeReport-one-tips"
          >
            <div class="seeReport-one-tips-titleTop">
              <div class="seeReport-one-tips-titleTop-name">
                {{ item.Name }}
              </div>
              <div style="color: #f5a031"></div>
            </div>
            <div class="seeReport-one-tips-result">
              <div class="seeReport-one-tips-result-left">
                {{ item.SumPoint
                }}<span
                  style="
                    color: rgba(102, 102, 102, 1);
                    font-weight: normal;
                    font-size: 16px;
                  "
                  >分</span
                >
              </div>
              <div class="seeReport-one-tips-result-right">
                {{ item.SuccessResultContent }}
              </div>
            </div>
          </div>
        </div>

        <div class="seeReport-one-tags">
          <span class="seeReport-one-tags-left">B:认知功能障碍</span>
          <div
            style="
              display: flex;
              justify-content: flex-start;
              align-items: center;
            "
          >
            <u-tag
              shape="circle"
              text="轻度"
              v-if="dataInfo.CognitionEvaLevel.Level === 1"
              bgColor="rgba(45, 230, 203, 0.20)"
              color="#2DE6CB"
              borderColor="#FFFFFF"
              style="margin-left: 10rpx"
            >
            </u-tag>
            <u-tag
              shape="circle"
              text="中度"
              v-if="dataInfo.CognitionEvaLevel.Level === 2"
              bgColor="rgba(255, 227, 86, 0.20)"
              color="#E6C72D"
              borderColor="#FFFFFF"
              style="margin-left: 10rpx"
            >
            </u-tag>
            <u-tag
              shape="circle"
              text="重度"
              v-if="dataInfo.CognitionEvaLevel.Level === 3"
              bgColor="rgba(45, 64, 230, 0.20)"
              color="#2D40E6"
              borderColor="#FFFFFF"
              style="margin-left: 10rpx"
            >
            </u-tag>
          </div>
        </div>
        <div class="seeReport-one-pross">
          <div class="seeReport-one-pross-style"></div>
          <div
            style="
              display: flex;
              align-items: center;
              justify-content: space-between;
            "
          >
            <div
              style="
                width: 33%;
                font-weight: 400;
                font-size: 24rpx;
                color: #999999;
                text-align: center;
              "
            >
              轻度
            </div>
            <div
              style="
                width: 33%;
                font-weight: 400;
                font-size: 24rpx;
                color: #999999;
                text-align: center;
              "
            >
              中度
            </div>
            <div
              style="
                width: 33%;
                font-weight: 400;
                font-size: 24rpx;
                color: #999999;
                text-align: center;
              "
            >
              重度
            </div>
          </div>
          <div
            class="seeReport-one-pross-circle"
            :style="{ left: bomLeft }"
            v-if="dataInfo.CognitionEvaLevel"
          ></div>
          <div
            class="seeReport-one-pross-circle1"
            :style="{ left: '33%' }"
          ></div>
          <div
            class="seeReport-one-pross-circle1"
            :style="{ left: '66%' }"
          ></div>
        </div>

        <div v-if="dataInfo.CognitionEvaLevel.Level >= 0">
          <div
            v-for="(item, index) in dataInfo.CognitionEva"
            :key="item.Id"
            class="seeReport-one-tips"
          >
            <div class="seeReport-one-tips-titleTop">
              <div class="seeReport-one-tips-titleTop-name">
                {{ item.Name }}
              </div>
              <div style="color: #f5a031"></div>
            </div>
            <div class="seeReport-one-tips-result">
              <div style="color: #ff5656; height: 100%">
                {{ item.SumPoint
                }}<span
                  style="color: rgba(102, 102, 102, 1); font-weight: normal"
                  >分</span
                >
              </div>
              <div
                style="
                  margin-left: 28rpx;
                  font-weight: normal;
                  flex: 1;
                  word-break: break-all;
                  color: #666666;
                  font-size: 24rpx;
                "
              >
                {{ item.SuccessResultContent }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { dateFormat } from '@/utils/validate.js';
import { getSchemeReportByPrescriptionId } from '@/api/consult';
import { getEvaListReq } from '@/api/training.js';
export default {
  data() {
    return {
      prescriptionId: '',
      dataInfo: {},
      topLeft: '',
      bomLeft: '',
    };
  },
  onLoad({ sign }) {
    this.sign = sign;
    this.getEvaListData(sign);
  },
  methods: {
    async getEvaListData(sign) {
      const res = await getEvaListReq(sign);
      if (res.Type === 200) {
        res.Data.CreatedTime = dateFormat(
          res.Data.CreatedTime,
          'YYYY-MM-DD HH:mm'
        );
        this.dataInfo = res.Data;
        if (
          this.dataInfo.SelfEvaLevel &&
          this.dataInfo.SelfEvaLevel.Level >= 0
        ) {
          this.topLeft =
            12.5 + 25 * (this.dataInfo.SelfEvaLevel.Level - 1) + '%';
        }
        if (
          this.dataInfo.CognitionEvaLevel &&
          this.dataInfo.CognitionEvaLevel.Level >= 0
        ) {
          this.bomLeft =
            20 + 33.3 * (this.dataInfo.CognitionEvaLevel.Level - 1) + '%';
        }
      }
    },
  },
};
</script>

<style scoped lang="scss">
.seeReport {
  width: 100%;
  height: 100vh;
  background-color: #f8f7f7;
  position: relative;

  .user {
    width: 90%;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 142rpx;
  }

  &-back {
    background-color: #3e404d;
    height: 400rpx;
    border-bottom-left-radius: 39%;
    border-bottom-right-radius: 62%;
    position: relative;
    &-img {
      position: absolute;
      width: 210rpx;
      height: 286rpx;
      left: 56rpx;
      top: 36rpx;
    }
  }

  &-user {
    display: flex;
  }

  &-one {
    background-color: white;
    border-radius: 24rpx;
    padding: 20px;
    margin-bottom: 20rpx;
    background: #ffffff;
    box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.1);

    &-base {
      font-size: 34rpx;
      font-weight: 600;
      color: #333333;
      position: relative;
      padding-left: 16rpx;
      margin-bottom: 10px;
    }

    &-tags {
      display: flex;
      justify-content: flex-start;
      align-items: center;

      &-left {
        color: #29b7a3;
        font-weight: 600;
        font-size: 28rpx;
        margin-right: 20rpx;
      }
    }

    &-pross {
      margin: 20rpx 0;
      width: 100%;
      position: relative;

      &-circle {
        width: 36rpx;
        height: 36rpx;
        background: #37eed5;
        box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.3);
        border: 4rpx solid #ffffff;
        position: absolute;
        top: -11rpx;
        z-index: 1;
        border-radius: 24rpx;
      }

      &-circle1 {
        width: 14rpx;
        height: 14rpx;
        background: white;
        position: absolute;
        z-index: 999;
        top: 0;
      }

      &-style {
        width: 100%;
        height: 14rpx;
        background: linear-gradient(90deg, #ffffff 0%, #35f0d6 100%);
        margin-bottom: 24rpx;
      }
    }

    &-base:before {
      content: '';
      position: absolute;
      left: -16rpx;
      top: 50%;
      transform: translateY(-50%);
      width: 6px;
      height: 6px;
      border-radius: 50%;
      border: 3px solid #31dbc3;
      background-color: #31dbc3;
    }

    &-top {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      &-left {
        color: #666666;
        font-size: 32rpx;

        &-left {
          font-weight: 600;
          color: #333333;
          font-size: 28rpx;
        }
      }
    }

    &-bom {
      color: gray;
      font-size: 14px;
      margin-top: 10px;
    }

    &-title {
      font-weight: 600;
      font-size: 18px;
      margin-bottom: 10px;
    }

    &-tips {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 16rpx;

      &-result {
        margin-top: 16rpx;
        display: flex;

        &-left {
          background: rgba(245, 246, 250, 1);
          color: #ff5656;
          height: 100%;
          font-size: 18px;
          width: 25%;
          border-radius: 24rpx;
          text-align: center;
          height: 82rpx;
          line-height: 82rpx;
        }

        &-right {
          background: rgba(245, 246, 250, 1);
          padding: 20rpx;
          min-height: 82rpx;
          height: auto;
          border-radius: 24rpx;
          margin-left: 28rpx;
          font-weight: normal;
          flex: 1;
          word-break: break-all;
          color: #666666;
          font-size: 24rpx;
          display: flex;
          align-items: center;
        }
      }

      &-titleTop {
        display: flex;
        justify-content: space-between;
        align-items: center;

        &-name {
          position: relative;
          padding-left: 10rpx;
          font-size: 24rpx;
        }

        &-name:before {
          content: '';
          position: absolute;
          left: -10rpx;
          top: 50%;
          transform: translateY(-50%);
          width: 4rpx;
          height: 4rpx;
          border-radius: 50%;
          border: 4rpx solid #29b7a3;
          background-color: #29b7a3;
        }
      }
    }

    &-fj {
      display: flex;
      justify-items: flex-start;
      align-items: center;
      margin-bottom: 20rpx;
    }
  }
}

/deep/ .u-tag__text {
  font-size: 24rpx;
}
</style>
