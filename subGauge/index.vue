<template>
  <view class="container">
    <u-list @scrolltolower="scrolltolower" v-if="list.length > 0">
      <u-list-item v-for="(item, index) in list" :key="item.id">
        <view class="box" @click="toDetal(index)">
          <view class="box-top">
            <!-- <u-avatar src="/static/images/lb.png" size="40" shape="square"></u-avatar> -->
            <u--image
              :showLoading="true"
              src="/static/images/lb.png"
              width="40px"
              height="40px"
            ></u--image>
            <view class="box-top-right">
              <view class="box-top-right-top">
                <p
                  style="
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    width: 70%;
                  "
                >
                  {{ item[0].Name }}
                </p>
                <p
                  style="color: #29b7a3"
                  v-show="item.length > 1 && item[0].SumPoint > 0"
                >
                  已评估{{ item.length }}次
                </p>
                <p
                  v-show="item.length == 1 && item[0].SumPoint > 0"
                  style="color: #29b7a3"
                >
                  {{ item[0].SumPoint }}分
                </p>
                <p style="color: #ff3b30" v-show="item[0].SumPoint <= 0">
                  未评估
                </p>
              </view>
            </view>
          </view>
          <!-- <view class=""> -->
          <view
            class="box-top-right-top1"
            v-if="item.length >= 1 && item[0].SumPoint > 0"
          >
            <p style="font-size: 14px; color: #666666">
              上次评估时间：{{ item[0].EvaluateTime }}
            </p>
            <span style="color: #29b7a3">重新评测></span>
          </view>
        </view>
      </u-list-item>
    </u-list>
    <u-empty
      mode="data"
      icon="	https://cdn.uviewui.com/uview/empty/data.png"
      v-else
    >
    </u-empty>
  </view>
</template>

<script>
const app = getApp();
import { getGaugeList } from './api.js';
import { dateFormat } from '@/utils/validate.js';
export default {
  data() {
    return {
      list: [],
    };
  },
  onLoad() {},
  onShow() {
    this.getList();
  },
  methods: {
    async getList() {
      let res = await getGaugeList({
        patId: app.globalData.userInfo.Id,
      });
      if (res.Type == 200) {
        const sorted = this.groupBy(res.Data, function (item) {
          return [item.BaseEvaluateGaugeId]; //按照name进行分组
        });
        sorted.forEach((e) => {
          e.forEach((k) => {
            k.EvaluateTime = dateFormat(k.EvaluateTime, 'YYYY-MM-DD');
          });
        });
        this.list = sorted;
      }
    },
    toDetal(index) {
      const item = this.list[index][0];
      console.log('查看详情', item);

      // 注意：subGauge/index 本页面暂未使用
      // 如启动本页面，通过 toDetal 方法进入量表时，需要传递 roomId
      // 由于此处页面未启用，暂不添加 roomId，如需添加，可能需要后端接口配合
      let url = '';
      if (item.SumPoint > 0) {
        url =
          `./result?itemInfo=` +
          encodeURIComponent(JSON.stringify(item)) +
          '&sendMessage=noSend';
      } else {
        url = `./autonomy?id=${item.BaseEvaluateGaugeId}&DctSendSign=${item.DctSendSign}&RelatedId=${item.RelatedId}&sendMessage=noSend`;
      }

      uni.navigateTo({
        url,
      });
    },
    scrolltolower() {
      console.log('加载更多');
    },
    // 分组
    groupBy(array, f) {
      const groups = {};
      array.forEach(function (o) {
        //注意这里必须是forEach 大写
        const group = JSON.stringify(f(o));
        groups[group] = groups[group] || [];
        groups[group].push(o);
      });
      return Object.keys(groups).map(function (group) {
        return groups[group];
      });
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  height: 100vh;
  background-color: #f7f7f7;
  padding-top: 30rpx;

  /deep/ .insideStyle {
    padding: 0 32rpx;
  }

  /deep/ .u-list-item {
    background-color: white;
    margin-bottom: 35rpx;
    box-shadow: 2.5px 3px 9px 0px rgba(0, 0, 0, 0.08);
    border-radius: 16rpx;
  }

  .box {
    padding: 20upx;

    .box-top-right-top1 {
      margin-top: 20upx;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .botton-style {
        width: 138upx;
        height: 60upx;
        background-color: #29b7a3;
        font-size: 12px;
        text-align: center;
        line-height: 60upx;
        border-radius: 30px;
        color: white;
      }
    }

    .box-top {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .box-top-right {
        flex: 1;
        margin-left: 20upx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        .box-top-right-top {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .botton-style {
            width: 138upx;
            height: 60upx;
            background-color: #29b7a3;
            font-size: 15px;
            text-align: center;
            line-height: 60upx;
            border-radius: 30px;
          }
        }
      }
    }
  }
}
</style>
