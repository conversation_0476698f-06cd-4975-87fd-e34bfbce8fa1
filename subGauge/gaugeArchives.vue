<template>
  <view>
    <view v-if="dataList && dataList.length > 0" class="list-container">
      <u-list>
        <u-list-item v-for="(item, index) in dataList" :key="index">
          <view
            class="list-item"
            hover-class="hover-class"
            @click="onTapItem"
            :data-index="index"
          >
            <image class="list-item-file" src="./static/evaluation-icon.png" />
            <text class="list-item-title">{{ item.Name }}</text>
            <!-- 未评估 -->
            <text v-if="!item.IsEvaluate" class="not-evaluated">未评估</text>
            <!-- 已评估 -->
            <view v-else class="evaluated-container">
              <text class="title">已评估</text>
              <text class="time">{{ item.ShowTime }}</text>
            </view>
          </view>
        </u-list-item>
        <u-list-item>
          <view class="footer"></view>
        </u-list-item>
      </u-list>
    </view>
    <u-empty
      v-else
      mode="data"
      icon="https://cdn.uviewui.com/uview/empty/data.png"
    >
    </u-empty>
  </view>
</template>

<script>
import { AssessClientEvent } from '@/utils/eventKeys';
import { getGaugeList } from '../subFollowUp/api';
import { dateFormat } from '../utils/validate';
export default {
  data() {
    return {
      // 量表列表
      dataList: [],
    };
  },
  onLoad() {
    this.requestAssessmentList();
    uni.$on(AssessClientEvent.updateAssess, this.requestAssessmentList);
  },
  methods: {
    /**
     * 获取量表列表
     */
    async requestAssessmentList() {
      uni.showLoading({
        title: '请求中...',
        mask: true,
      });
      const userId = getApp().globalData.userInfo.Id;
      let r = await getGaugeList({
        patId: userId,
        isLoadDoctor: true,
        isRelatedGroup: true,
      });
      uni.hideLoading();
      if (r.Type !== 200) {
        uni.showToast({
          title: r.Content,
          icon: 'none',
        });
        return;
      }

      const list =
        r.Data?.map((e) => {
          return {
            ...e,
            ShowTime: e.EvaluateTime
              ? dateFormat(e.EvaluateTime, 'YYYY.MM.DD HH:mm')
              : '',
          };
        }) ?? [];
      this.dataList = list;
    },

    /**
     * 点击评定单项
     */
    onTapItem(e) {
      const index = e.currentTarget.dataset.index;
      const item = this.dataList[index];
      console.debug('点击评定', index);

      if (item.IsEvaluate) {
        // 已评估
        uni.navigateTo({
          url:
            '/subGauge/gaugeDetailList?disabled=false&patGaugeId=' +
            item.EvaluateGaugeId +
            '&DctSendSign=' +
            item.DctSendSign +
            '&RelatedId=' +
            item.RelatedId +
            '&id=' +
            item.BaseEvaluateGaugeId +
            '&Source=' +
            item.Source,
        });
      } else {
        // 未评估
        uni.navigateTo({
          url:
            '/subGauge/autonomy?disabled=false&sendMessage=noSend&id=' +
            item.BaseEvaluateGaugeId +
            '&DctSendSign=' +
            item.DctSendSign +
            '&RelatedId=' +
            item.RelatedId +
            '&Source=' +
            item.Source,
        });
      }
    },
  },
};
</script>

<style scoped lang="scss">
.footer {
  height: env(safe-area-inset-bottom);
}

.list-container {
  margin: 0rpx 32rpx;
  display: flex;
  flex-direction: column;
  justify-content: start;
  align-items: stretch;
}

.list-item {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-top: 24rpx;

  display: flex;
  justify-content: start;
  align-items: center;
}

.list-item-file {
  width: 70rpx;
  height: 82rpx;
}

.list-item-title {
  font-weight: 400;
  font-size: 28rpx;
  color: #333333;
  flex: 1;
  padding: 0 32rpx;

  /*设置超出一行进行隐藏*/
  display: -webkit-box;
  word-break: break-all;
  text-overflow: ellipsis;
  overflow: hidden;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.list-item .not-evaluated {
  font-weight: 400;
  font-size: 28rpx;
  color: #ee0a24;
}

.evaluated-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
}

.evaluated-container .title {
  font-weight: 400;
  font-size: 28rpx;
  color: #29b7a3;
}

.evaluated-container .time {
  font-weight: 400;
  font-size: 20rpx;
  color: #999999;
  margin-top: 16rpx;
}

.hover-class {
  opacity: 0.5;
}
</style>
