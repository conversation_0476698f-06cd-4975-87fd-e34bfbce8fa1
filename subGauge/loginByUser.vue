<template>
  <view>
    <view class="base-info">
      <block v-if="!options.type || options.type !== 'onlyChart'">
        <block v-if="userInfo.WorkflowStatus !== 2">
          <u-cell-group>
            <u-cell>
              <view slot="title">
                <p>姓名<span style="color: red">*</span></p>
              </view>
              <u-input
                slot="value"
                placeholder="请输入真实姓名"
                border="none"
                v-model="smQuery.Name"
                inputAlign="right"
              ></u-input>
            </u-cell>
            <u-cell
              :border="false"
              customStyle="justify-content: space-between"
            >
              <view slot="title">
                <p>性别<span style="color: red">*</span></p>
              </view>
              <view slot="value" style="display: flex">
                <view
                  class="u-page__tag-item"
                  style="margin-right: 10rpx"
                  v-for="(item, index) in sexList"
                  :key="index"
                >
                  <MPTag
                    shape="circle"
                    iconPosition="right"
                    :icon="item.name === '男' ? 'man' : 'woman'"
                    :text="item.name"
                    :plain="!item.checked"
                    type="success"
                    :name="index"
                    @click="radioClick"
                  >
                  </MPTag>
                </view>
              </view>
            </u-cell>
            <u-cell>
              <view slot="title">
                <p>年龄<span style="color: red">*</span></p>
              </view>
              <u-input
                slot="value"
                placeholder="请输入真实年龄"
                border="none"
                v-model="smQuery.Age"
                inputAlign="right"
                type="number"
                @blur="handleUpdateUserAge"
              ></u-input>
            </u-cell>
            <u-cell>
              <view slot="title" id="targetComponent">
                <p>证件号码（选填）</p>
              </view>
              <u-input
                slot="value"
                placeholder="请输入真实证件号码"
                type="idcard"
                border="none"
                v-model="smQuery.UserCertificates[0].CertificateValue"
                inputAlign="right"
                @blur="handleUpdateUserCertificate"
              >
              </u-input>
              <ocr-navigator
                slot="right-icon"
                @onSuccess="ocrSuccess"
                certificateType="idCard"
                :opposite="false"
              >
                <u-icon name="camera" color="#24BAA3" size="28"></u-icon>
              </ocr-navigator>
            </u-cell>
          </u-cell-group>
        </block>
        <block v-else>
          <u-cell-group>
            <u-cell :border="false">
              <p slot="title">
                <span style="color: #29b7a3; margin-right: 24rpx">{{
                  userInfo.Name
                }}</span>
                ({{ userInfo.Sex }} {{ userInfo.Age + '岁' }})
              </p>
            </u-cell>
          </u-cell-group>
        </block>
        <view class="base-info-disease">
          <view class="base-info-disease-title">
            <text>请描述您的疾病或症状</text>
            <text style="color: red">*</text>
          </view>
          <MpVoiceTextarea v-model="chiefComplaint" />
        </view>
      </block>
      <view class="base-info-box">
        <view class="base-info-box-title">
          请滑动最能描述您疼痛的程度
          <span style="color: red">*</span>
        </view>
        <view
          class="base-info-box-line"
          @touchstart="(e) => touchStart(e, 'pain')"
          @touchmove="(e) => touchMove(e, 'pain')"
          @touchend="touchEnd"
          @tap="(e) => tapSelect(e, 'pain')"
        >
          <view
            class="base-info-box-line-text"
            v-for="(item, index) in 11"
            :key="index"
            :class="{
              'base-info-box-line-select': selectedPainLevel === index,
            }"
          >
            {{ item }}
          </view>
        </view>
        <view class="base-info-box-described">
          <view class="base-info-box-described-box">
            <image
              src="https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/static/frontend/mp-patient/good.png"
            ></image>
            <text class="base-info-box-described-box-text">无痛</text>
          </view>
          <view class="base-info-box-described-box">
            <image
              src="https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/static/frontend/mp-patient/bad.png"
            ></image>
            <text class="base-info-box-described-box-text">剧痛</text>
          </view>
        </view>
      </view>
      <view class="base-info-box">
        <view class="base-info-box-title">
          请滑动选择您的生活质量
          <span style="color: red">*</span>
        </view>
        <view
          class="base-info-box-line base-info-box-line1"
          @touchstart="(e) => touchStart(e, 'quality')"
          @touchmove="(e) => touchMove(e, 'quality')"
          @touchend="touchEnd"
          @tap="(e) => tapSelect(e, 'quality')"
        >
          <view
            class="base-info-box-line-text"
            v-for="(item, index) in 11"
            :key="index"
            :class="{ 'base-info-box-line-select': qualityLife === index }"
          >
            {{ item }}
          </view>
        </view>
        <view class="base-info-box-described">
          <view class="base-info-box-described-box">
            <image
              src="https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/static/frontend/mp-patient/bad.png"
            ></image>
            <text class="base-info-box-described-box-text">非常差</text>
          </view>
          <view class="base-info-box-described-box">
            <image
              src="https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/static/frontend/mp-patient/good.png"
            ></image>
            <text class="base-info-box-described-box-text">非常好</text>
          </view>
        </view>
      </view>
      <view class="base-info-box">
        <view class="base-info-box-title">
          请滑动选择您的焦虑或抑郁程度
          <span style="color: red">*</span>
        </view>
        <view
          class="base-info-box-line"
          @touchstart="(e) => touchStart(e, 'anxiety')"
          @touchmove="(e) => touchMove(e, 'anxiety')"
          @touchend="touchEnd"
          @tap="(e) => tapSelect(e, 'anxiety')"
        >
          <view
            class="base-info-box-line-text"
            v-for="(item, index) in 11"
            :key="index"
            :class="{ 'base-info-box-line-select': anxiety === index }"
          >
            {{ item }}
          </view>
        </view>
        <view class="base-info-box-described">
          <view class="base-info-box-described-box">
            <image
              src="https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/static/frontend/mp-patient/good.png"
            ></image>
            <text class="base-info-box-described-box-text">非常好</text>
          </view>
          <view class="base-info-box-described-box">
            <image
              src="https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/static/frontend/mp-patient/bad.png"
            ></image>
            <text class="base-info-box-described-box-text">非常焦虑</text>
          </view>
        </view>
      </view>
    </view>
    <view class="bomm-btn-style" style="height: 100px"></view>
    <u-button
      :loading="payMoenyDisabled"
      type="success"
      @click="submit"
      shape="circle"
      text="提交"
      customStyle="width:90%;bottom: 30px;position: fixed; left: 50%;transform: translateX(-50%);fontSize:16px;z-index:99"
    >
    </u-button>
  </view>
</template>

<script>
const app = getApp();
import {
  briGetAge,
  idCardTrue,
  idCardGetSex,
  idCardGetBri,
} from '@/utils/utils';
import { dataEntry } from '@/api/record.js';
import MPTag from '@/components/mp-tag/u-tag.vue';
import MpVoiceTextarea from '@/components/mp-voiceTextarea/mp-voice-textarea.vue';
import { saveIllnessRecord } from '@/api/record.js';
export default {
  data() {
    return {
      selectedPainLevel: undefined, // 当前选中的疼痛级别
      qualityLife: undefined,
      anxiety: undefined,
      startX: 0, // 触摸开始位置
      currentType: null, // 记录当前操作的滑动条类型
      chiefComplaint: '',
      userInfo: {},
      sexList: [
        {
          name: '男',
          checked: false,
        },
        {
          name: '女',
          checked: false,
        },
      ],
      smQuery: {
        UserId: app.globalData.userInfo.Id,
        Name: '',
        Sex: '',
        Birthday: '',
        Age: null,
        UserCertificates: [
          {
            CertificateType: 'idCard',
            CertificateValue: '',
          },
        ],
      },
      options: {},
    };
  },
  components: {
    MPTag,
    MpVoiceTextarea,
  },
  onLoad(options) {
    this.options = options;
    if (options.type && options.type === 'onlyChart') {
      uni.setNavigationBarTitle({
        title: '问卷',
      });
    }
    this.userInfo = app.globalData.userInfo;
    if (this.userInfo.Sex) {
      this.sexList.map((s) =>
        s.name === this.userInfo.Sex ? (s.checked = true) : false
      );
    }
    this.smQuery.Name = this.userInfo.Name;
    this.smQuery.Sex = this.userInfo.Sex;
    this.smQuery.Age = this.userInfo.Age;
    if (
      this.userInfo.UserCertificates &&
      this.userInfo.UserCertificates.length
    ) {
      this.smQuery.UserCertificates[0].CertificateValue =
        this.userInfo.UserCertificates[0].CertificateValue;
    }
    this.handleSetShareParams();
  },
  onReady() {},
  methods: {
    handleSetShareParams() {
      this.isdefalut = false;
      this.isUseDefaultParams = false;
      this.share.title = '请您完善个人基础健康档案';
      this.share.path =
        '/subGauge/transfer?handler=share&path=/subGauge/loginByUser';
      this.share.imageUrl =
        'https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/static/defauls/HealthCover.png';
    },
    ocrSuccess(e) {
      this.$log.info('使用了身份证识别，并识别成功');
      if (e.type === 'onSuccess') {
        const data = e.detail;
        this.smQuery.UserCertificates[0].CertificateValue = data.id.text;
        this.smQuery.Sex = data.gender.text;
        this.smQuery.Name = data.name.text;
        this.smQuery.Age = briGetAge(data.birth.text);
      }
    },
    handleUpdateUserAge(e) {
      if (Number(e) > 150 || Number(e) < 0) {
        this.smQuery.Age = null;
        uni.showToast({
          title: '年龄范围为0-150岁',
          icon: 'none',
        });
      }
    },
    handleUpdateUserCertificate(e) {
      if (e) {
        if (!idCardTrue(e)) {
          un.showToast({
            title: '请输入正确的身份证号码',
            icon: 'none',
          });
          return;
        }
        this.smQuery.Sex = idCardGetSex(e);
        this.smQuery.Birthday = idCardGetBri(e);
        this.smQuery.Age = briGetAge(this.smQuery.Birthday);
      }
    },
    radioClick(name) {
      this.sexList.map((item, index) => {
        item.checked = index === name ? true : false;
      });
      this.smQuery.Sex = this.sexList[name].name;
    },
    handleProcessingUserInfo() {
      if (!this.smQuery.Name) {
        uni.showToast({
          title: '请输入真实姓名',
          icon: 'none',
        });
        return false;
      }
      if (!this.smQuery.Sex) {
        uni.showToast({
          title: '请选择性别',
          icon: 'none',
        });
        return false;
      }
      if (!this.smQuery.Age) {
        uni.showToast({
          title: '请输入真实年龄',
          icon: 'none',
        });
        return false;
      }
      try {
        app.saveAuthen(this.smQuery);
      } catch (e) {
        this.$log.error(
          this.$envVersion,
          `修改用户信息失败`,
          JSON.stringify(e)
        );
      } finally {
        return true;
      }
    },
    touchStart(e, type) {
      this.startX = e.touches[0].clientX;
      this.currentType = type; // 记录当前操作的滑动条类型
    },
    touchMove(e, type) {
      const currentX = e.touches[0].clientX;
      // 获取滑动区域的宽度和位置信息
      const query = uni.createSelectorQuery().in(this);
      const selector =
        type === 'quality' ? '.base-info-box-line1' : '.base-info-box-line';
      query
        .select(selector)
        .boundingClientRect((data) => {
          if (data) {
            const { left, width } = data;
            // 计算触摸点相对于滑动区域的位置比例
            const offsetX = currentX - left;
            const percentage = offsetX / width;
            // 根据百分比计算对应的级别（0-10）
            let level = Math.round(percentage * 10);
            // 确保级别在0-10范围内
            level = Math.max(0, Math.min(10, level));

            // 根据类型更新对应的值
            if (type === 'pain') {
              this.selectedPainLevel = level;
            } else if (type === 'quality') {
              this.qualityLife = level;
            } else if (type === 'anxiety') {
              this.anxiety = level;
            }
          }
        })
        .exec();
    },
    touchEnd(e) {
      // 触摸结束时，保持当前选中的级别
    },
    tapSelect(e, type) {
      // 点击选择功能
      const query = uni.createSelectorQuery().in(this);
      const selector =
        type === 'quality' ? '.base-info-box-line1' : '.base-info-box-line';
      query
        .select(selector)
        .boundingClientRect((data) => {
          if (data) {
            const { left, width } = data;
            const offsetX = e.touches[0].clientX - left;
            const percentage = offsetX / width;
            let level = Math.round(percentage * 10);
            level = Math.max(0, Math.min(10, level));

            // 根据类型更新对应的值
            if (type === 'pain') {
              this.selectedPainLevel = level;
            } else if (type === 'quality') {
              this.qualityLife = level;
            } else if (type === 'anxiety') {
              this.anxiety = level;
            }
          }
        })
        .exec();
    },
    async handleSendChartData() {
      if (
        this.selectedPainLevel === undefined ||
        this.qualityLife === undefined ||
        this.anxiety === undefined
      ) {
        uni.showToast({
          title: '请完成所有选项',
          icon: 'none',
        });
        return;
      }
      uni.showLoading({
        title: '提交中',
        mask: true,
      });
      try {
        const res = await dataEntry([
          {
            UserId: app.globalData.userInfo.Id,
            Type: 4,
            Data: JSON.stringify({
              Value: this.selectedPainLevel,
            }),
            SignTime: this.options.fillDate || null,
          },
          {
            UserId: app.globalData.userInfo.Id,
            Type: 5,
            Data: JSON.stringify({
              Value: this.qualityLife,
            }),
            SignTime: this.options.fillDate || null,
          },
          {
            UserId: app.globalData.userInfo.Id,
            Type: 6,
            Data: JSON.stringify({
              Value: this.anxiety,
            }),
            SignTime: this.options.fillDate || null,
          },
        ]);
        if (res.Type === 200) {
          uni.showToast({
            title: '提交成功',
            icon: 'none',
          });
          setTimeout(() => {
            uni.reLaunch({
              url: '/pages/plan/index',
            });
          }, 1000);
        } else {
          uni.showToast({
            title: res.Content,
            icon: 'none',
          });
        }
      } catch (e) {
        uni.showToast({
          title: '提交失败',
          icon: 'none',
        });
      } finally {
        uni.hideLoading();
      }
    },
    submit() {
      if (this.options.type && this.options.type === 'onlyChart') {
        this.handleSendChartData();
        return;
      }
      // 在提交时可以使用所有三个值
      if (!this.chiefComplaint) {
        uni.showToast({
          title: '请填写疾病症状',
          icon: 'none',
        });
        return;
      }
      if (
        this.selectedPainLevel === undefined ||
        this.qualityLife === undefined ||
        this.anxiety === undefined
      ) {
        uni.showToast({
          title: '请完成所有选项',
          icon: 'none',
        });
        return;
      }
      if (app.globalData.userInfo.WorkflowStatus !== 2) {
        const isFinished = this.handleProcessingUserInfo();
        if (!isFinished) {
          return;
        }
      }
      this.handleSubmitData();
    },
    async handleSubmitData() {
      uni.showLoading({
        title: '提交中',
        mask: true,
      });
      try {
        const res = await saveIllnessRecord({
          UserId: app.globalData.userInfo.Id,
          AdditionalInfo: this.options,
          ChiefComplaint: this.chiefComplaint,
          Signs: [
            {
              Type: 4,
              Data: JSON.stringify({
                Value: this.selectedPainLevel,
              }),
              SignTime: this.options.fillDate || null,
            },
            {
              Type: 5,
              Data: JSON.stringify({
                Value: this.qualityLife,
              }),
              SignTime: this.options.fillDate || null,
            },
            {
              Type: 6,
              Data: JSON.stringify({
                Value: this.anxiety,
              }),
              SignTime: this.options.fillDate || null,
            },
          ],
        });
        if (res.Type === 200) {
          uni.showToast({
            title: '提交成功',
            icon: 'none',
          });
          setTimeout(() => {
            uni.reLaunch({
              url: '/pages/plan/index',
            });
          }, 1000);
        } else {
          uni.showToast({
            title: res.Content,
            icon: 'none',
          });
        }
      } finally {
        uni.hideLoading();
      }
    },
  },
};
</script>

<style scoped lang="scss">
.base-info {
  border-radius: 10px;
  padding: 24rpx 32rpx;

  &-box {
    background: #ffffff;
    box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);
    border-radius: 16rpx;
    margin-top: 30rpx;
    padding: 36rpx 32rpx;

    &-described {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 8rpx;

      &-box {
        display: flex;
        flex-direction: column;
        align-items: center;

        &-text {
          font-weight: 400;
          font-size: 20rpx;
          color: #333333;
        }

        image {
          width: 40rpx;
          height: 40rpx;
          margin-bottom: 6rpx;
        }
      }
    }

    &-title {
      font-weight: 500;
      font-size: 28rpx;
      color: #333333;
      line-height: 34rpx;
      margin-bottom: 30rpx;
    }

    &-line {
      height: 62rpx;
      background: linear-gradient(
        90deg,
        #3dd6cf 1%,
        #a5e1b6 28.000000000000004%,
        #f1cc66 65%,
        #f87042 100%
      );
      box-shadow: 0rpx 10rpx 20rpx 0rpx rgba(41, 183, 163, 0.08);
      border-radius: 29rpx;
      border: 2px solid #ffffff;
      display: flex;
      padding: 18rpx 20rpx;
      align-items: center;
      justify-content: space-between;

      &-text {
        width: 50rpx;
        height: 50rpx;
        font-weight: 500;
        font-size: 24rpx;
        color: #ffffff;
        line-height: 50rpx;
        text-align: center;
      }

      &-select {
        background-color: white;
        border-radius: 50%;
        color: #3dd6cf;
        font-weight: bold;
        transform: scale(1.1);
        transition: all 0.2s ease;
      }
    }

    &-line1 {
      background: linear-gradient(
        90deg,
        #f87042 1%,
        #f1cc66 28%,
        #a5e1b6 65%,
        #3dd6cf 100%
      );
      box-shadow: 0rpx 10rpx 20rpx 0rpx rgba(41, 183, 163, 0.08);
    }
  }

  &-disease {
    margin-top: 30rpx;
    padding: 36rpx 32rpx;
    background: white;
    box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);
    border-radius: 16rpx;

    &-title {
      font-weight: 400;
      font-size: 28rpx;
      color: #333333;
      line-height: 34rpx;
      margin-bottom: 22rpx;
    }
  }
}

.u-page__tag-item {
  margin-right: 20rpx;
  margin-bottom: 10rpx;
}

.u-demo-block__content {
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  display: flex;
  position: relative;
  margin-top: 32rpx;

  &-select {
    position: absolute;
    bottom: -20px;
    left: 0;
    z-index: 99;
    box-shadow: 2.5px 3px 9px 0px rgba(0, 0, 0, 0.08);
    max-height: 200rpx;
    overflow-y: auto;
    width: 100%;
  }
}

/deep/ .u-cell {
  background-color: #fff !important;
}

/deep/ .u-line {
  border: none !important;
}

// /deep/ .u-cell__right-icon-wrap {
// 	text-align: right;
// }

/deep/ .u-radio-group {
  flex: 0 !important;
}

/deep/ .u-list {
  height: auto !important;
}

/deep/ .u-radio {
  margin-right: 0 !important;
}

/deep/ .u-radio-group {
  background: '#f7f7f7' !important;
  padding: 20rpx !important;
}
</style>
