<template>
  <view class="container">
    <!-- 标签栏 -->
    <u-sticky bgColor="#fff">
      <u-tabs
        lineColor="#29B7A3"
        lineWidth="80rpx"
        :current="currentIndex"
        :activeStyle="{
          fontWeight: '500',
          fontSize: '28rpx',
          color: '#323233',
        }"
        :inactiveStyle="{
          fontWeight: '400',
          fontSize: '28rpx',
          color: '#969799',
        }"
        :list="tabList"
        @click="onChange"
      ></u-tabs>
    </u-sticky>
    <view class="warpper">
      <u-list :height="listHeight + 'px'">
        <!-- 头空白 -->
        <u-list-item>
          <view class="warpper-header"></view>
        </u-list-item>
        <!-- 当前得分细则 -->
        <u-list-item>
          <view class="container-top">
            <view class="container-top-point-warpper">
              <text class="container-top-time">{{
                currentGauge.CreatedTime
              }}</text>
              <text class="container-top-title">最终得分</text>
              <view class="container-top-point">
                <text>{{ currentGauge.SumPoint }}</text>
                <text class="container-top-point-unit">分</text>
              </view>
            </view>
            <view class="flex-start-center-wrap">
              <view
                class="container-top-box"
                style="margin-right: 32rpx"
                v-for="(item, index) in currentGauge.PatGaugeResults"
                :key="index"
              >
                <text class="container-top-box-left"
                  >{{ item.StartPoint }}-{{ item.EndPoint }}分：</text
                >
                <text class="container-top-box-right">{{ item.Content }}</text>
              </view>
            </view>
          </view>
        </u-list-item>
        <!-- 量表题目列表 -->
        <u-list-item
          v-for="(item, problemIndex) in currentGauge.PatGaugeProblems"
          :key="problemIndex"
        >
          <view class="item-title">
            <text>{{ problemIndex + 1 + '、' + item.Title }}</text>
            <text class="item-title-required" v-if="item.IsRequired">*</text>
          </view>
          <!-- 单选 -->
          <view class="item-select-box" v-if="item.ProblemType === 1">
            <view
              class="item-select-box-option"
              v-for="(o, index) in item.PatGaugeProblemDetails"
              :key="Id"
            >
              <view
                :class="[
                  'item-icon-radius',
                  o.isSelected ? 'item-icon-selected' : 'item-icon-unselected',
                ]"
              >
                <u-icon
                  v-if="o.isSelected"
                  color="#fff"
                  name="checkbox-mark"
                ></u-icon>
              </view>
              <text style="flex: 1">{{ o.ProblemOption }}</text>
            </view>
          </view>
          <!-- 多选 -->
          <view class="item-select-box" v-else-if="item.ProblemType === 2">
            <view
              class="item-select-box-option"
              v-for="(o, index) in item.PatGaugeProblemDetails"
              :key="Id"
            >
              <view
                :class="[
                  o.isSelected ? 'item-icon-selected' : 'item-icon-unselected',
                ]"
              >
                <u-icon
                  v-if="o.isSelected"
                  color="#fff"
                  name="checkbox-mark"
                ></u-icon>
              </view>
              <text style="flex: 1">{{ o.ProblemOption }}</text>
            </view>
          </view>
          <!-- 输入框/数值 -->
          <view class="item-input-box" v-else>
            <text>{{ item.PatGaugeProblemDetails[0].Answer }}</text>
          </view>
        </u-list-item>
        <!-- 尾部空白 -->
        <u-list-item>
          <view class="warpper-footer"></view>
        </u-list-item>
      </u-list>
    </view>
    <view
      class="container-bomBtn"
      v-if="option.disabled === 'false'"
      @click="handleReassessClick"
    >
      重新评估
    </view>
  </view>
</template>

<script>
import { dctGetPatGaugeById } from '../api/training';
import { dateFormat } from '../utils/validate';
import { AssessClientEvent } from '@/utils/eventKeys';
export default {
  data() {
    return {
      option: {
        patGaugeId: '',
        disabled: 'true',
      },
      // 用于显示 Tabs
      tabList: {},

      // 已评估列表
      gaugeList: [],
      // 列表可滚动高度(单位px)
      listHeight: 680,

      // 当前显示的下标
      currentIndex: 0,
      // 当前显示的评估
      currentGauge: {},
    };
  },
  onLoad(option) {
    this.option = option;
    this.loadGetGaugeDataByPatGaugeId();
    uni.$on(AssessClientEvent.updateAssess, this.loadGetGaugeDataByPatGaugeId);
  },
  onReady() {
    const window = uni.getWindowInfo();
    console.debug('window', window);
    this.listHeight = window.windowHeight - 44;
  },
  methods: {
    handleReassessClick() {
      uni.navigateTo({
        url:
          '/subGauge/autonomy?sendMessage=noSend&disabled=false&id=' +
          this.option.id +
          '&DctSendSign=' +
          this.option.DctSendSign +
          '&RelatedId=' +
          this.option.RelatedId +
          '&Source=' +
          this.option.Source,
      });
    },
    // 请求已填写的评估结果列表
    async loadGetGaugeDataByPatGaugeId() {
      uni.showLoading({
        title: '加载中...',
      });
      const res = await dctGetPatGaugeById(this.option.patGaugeId);
      if (res.Type === 200 && res.Data && res.Data.length > 0) {
        res.Data?.forEach((s) => {
          s.CreatedTime = dateFormat(s.CreatedTime, 'YYYY.MM.DD HH:mm');
          s.PatGaugeProblems.forEach((v) => {
            if (v.ProblemType === 1 || v.ProblemType === 2) {
              v.PatGaugeProblemDetails.forEach((o) => {
                if (o.Answer === '1') {
                  o.isSelected = true;
                } else {
                  o.isSelected = false;
                }
              });
            } else if (v.ProblemType === 3 || v.ProblemType === 4) {
              if (v.PatGaugeProblemDetails && v.PatGaugeProblemDetails.length) {
                v.PatGaugeProblemDetails.forEach((o) => {
                  o.Answer =
                    o.Answer === null || o.Answer === '' ? '' : o.Answer;
                });
              }
            }
          });
        });

        // 获取列表
        this.gaugeList = res.Data.reverse();
        this.tabList = this.gaugeList.map((_, index) => {
          return {
            name: `第${index + 1}次评估`,
            index: index,
          };
        });

        // 当前显示值设置
        this.currentIndex = res.Data.length - 1;
        this.currentGauge = this.gaugeList[res.Data.length - 1];
        uni.setNavigationBarTitle({
          title: this.currentGauge['Name'],
        });
        uni.hideLoading();
      } else {
        uni.hideLoading();
        uni.showToast({
          title: res.Content,
          icon: 'none',
        });
      }
    },

    onChange(event) {
      const index = event.index;
      this.currentIndex = index;
      this.currentGauge = this.gaugeList[index];
    },
  },
};
</script>

<style scoped lang="scss">
.warpper {
  padding: 0 32rpx;
  flex: 1;

  &-header {
    height: 32rpx;
  }

  &-footer {
    height: calc(env(safe-area-inset-bottom) + 24rpx);
  }
}

// 分数分析结果
.container-top {
  background: #ffffff;
  box-shadow: 0rpx 4rpx 24rpx 0rpx rgba(100, 101, 102, 0.12);
  border-radius: 32rpx;
  padding: 32rpx;
  position: relative;

  &-title {
    font-weight: 400;
    font-size: 36rpx;
    color: #323233;
  }

  &-time {
    position: absolute !important;
    top: 42rpx;
    right: 32rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #969799;
  }

  &-point-warpper {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }

  &-point {
    font-weight: bold;
    font-size: 72rpx;
    color: #323233;

    &-unit {
      margin-left: 24rpx;
      font-weight: 400;
      font-size: 36rpx;
      color: #969699;
    }
  }

  &-box {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: wrap;
    margin-top: 26rpx;
    text-align: left !important;

    &-left {
      font-weight: 400;
      font-size: 24rpx;
      color: #969799;
    }

    &-right {
      font-weight: 500;
      font-size: 24rpx;
      color: #323233;
    }
  }
}

.item {
  &-title {
    margin-top: 32rpx;
    margin-bottom: 24rpx;
    font-weight: 600;
    font-size: 28rpx;
    color: #323233;

    &-required {
      font-weight: 600;
      font-size: 28rpx;
      color: #ff5656;
      margin-left: 4rpx;
    }
  }

  // 单选、多选
  &-select-box {
    background-color: #ffffff;
    border-radius: 32rpx;

    // 选项
    &-option {
      display: flex;
      flex-direction: row;
      justify-content: start;
      align-items: center;
      margin: 0 32rpx;
      padding: 24rpx 0;
      border-bottom: 1rpx solid #ebedf0;
    }
  }

  // 选项图标
  &-icon-selected {
    width: 32rpx;
    height: 32rpx;
    background-color: #29b7a3;
    box-sizing: border-box;
    margin-right: 18rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  &-icon-unselected {
    width: 32rpx;
    height: 32rpx;
    border: 2rpx solid #969799;

    box-sizing: border-box;
    margin-right: 18rpx;
  }

  &-icon-radius {
    border-radius: 32rpx;
  }

  &-input-box {
    padding: 32rpx;
    background: #ffffff;
    border-radius: 32rpx;
    font-weight: 400;
    word-break: break-all;
    font-size: 24rpx;
    color: #646566;
  }
}

::v-deep .u-radio {
  padding: 28rpx 32rpx !important;
}

::v-deep .u-radio__icon-wrap {
  margin-right: 18rpx !important;
}
</style>
