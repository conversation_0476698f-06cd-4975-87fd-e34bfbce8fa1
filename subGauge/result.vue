<template>
  <view
    class="container"
    :style="{
      backgroundImage: 'url(' + imageBase64 + ')',
      backgroundRepeat: 'no-repeat',
      backgroundSize: 'cover',
    }"
  >
    <view class="container-image">
      <view class="container-image-bg">
        <view class="container-image-bg-score">{{ item.SumPoint || '' }}</view>
      </view>
      <p style="margin-top: 20rpx">
        {{ item.SuccessResultContent || item.SuccessEvaluateContent || '' }}
      </p>
      <p style="margin-top: 20rpx">温馨提示：此评估仅供参考</p>
      <p>如需准确的评估请到当地医院进行专业评估</p>
    </view>

    <u-button
      @click="againAssessment"
      type="success"
      shape="circle"
      text="重新评测"
      customStyle="width:90%;bottom: 80px;position: fixed; left: 50%;transform: translateX(-50%);backgroundColor:#fff;color:#29B7A3"
      v-if="showAgain"
    >
    </u-button>
    <u-button
      @click="iKnow"
      type="success"
      shape="circle"
      text="我知道了"
      customStyle="width:90%;bottom: 20px;position: fixed; left: 50%;transform: translateX(-50%);backgroundColor:#fff;color:#29B7A3"
    >
    </u-button>
  </view>
</template>

<script>
export default {
  data() {
    return {
      item: {},
      roomType: 0, // 1:居家指导群 2:问诊群
      roomId: '', // 提交量表后，对应收到消息的会话房间号
      showAgain: true,
      imageBase64:
        'data:image/jpeg;base64,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',
    };
  },
  onLoad(option) {
    this.roomType = option.roomType;
    this.roomId = option.roomId;
    this.showAgain = option.showAgain == 'false' ? false : true;
    if (option.sendMessage) {
      this.sendMessage = option.sendMessage;
    }
    this.item = JSON.parse(decodeURIComponent(option.itemInfo));
  },
  methods: {
    iKnow() {
      uni.navigateBack();
    },
    againAssessment() {
      let url = `./autonomy?id=${this.item.BaseEvaluateGaugeId}&DctSendSign=${this.item.DctSendSign}&RelatedId=${this.item.RelatedId}`;
      if (this.sendMessage) {
        url += '&sendMessage=noSend';
      }
      if (this.roomType) {
        url += `&roomType=${this.roomType}`;
      }
      if (this.roomId) {
        url += `&roomId=${this.roomId}`;
      }
      if (this.item.Source >= 0 && typeof this.item.Source === 'number') {
        url += `&Source=${this.item.Source}`;
      }
      uni.redirectTo({
        url,
      });
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  position: relative;
  background-color: #f7f7f7;

  .container-image {
    color: white;

    &-bg {
      width: 400rpx;
      height: 400rpx;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.2);
      margin: 0 auto;
      position: relative;

      &-score {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        width: 300rpx;
        height: 300rpx;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.3);
        text-align: center;
        line-height: 300rpx;
        margin: auto;
        font-weight: 700;
        font-size: 120rpx;
      }
    }
  }

  &-image {
    /deep/ .u-image {
      margin: 0 auto;
    }

    text-align: center;
    font-weight: 600;
    position: absolute;
    top: 100rpx;
    left: 0;
    right: 0;
    bottom: 0;
    margin: 40px auto;
  }

  .container-box {
    position: absolute;
    text-align: center;
    top: 40%;
    left: 50%;
    transform: translate(-50%);
    color: #1dffa1;
    font-size: 12px;
    width: 70%;
    height: 200rpx;

    .container-box-item {
      width: 40%;
      background-color: rgba(255, 255, 255, 0.2);
      height: 40px;
      line-height: 40px;
      border-radius: 20px;
      font-size: 16px;
    }
  }
}
</style>
