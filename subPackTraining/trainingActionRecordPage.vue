<template>
  <scroll-view class="scroll-content" scroll-y="true" enable-flex>
    <!-- 日历 -->
    <view class="calendar-view">
      <uni-calendar
        :showMonth="false"
        :selected="markDataList"
        @change="onSelectDate"
      ></uni-calendar>
    </view>

    <!-- 打卡内容 -->
    <view class="list-view">
      <u-list>
        <u-list-item v-for="(item, index) in selectedDateRecords" :key="index">
          <view class="list-item-view">
            <view class="list-item-title-view">
              <image src="/static/training/select.png" mode="aspectFit"></image>
              <text>{{ item.Time }}已打卡</text>
            </view>
            <!-- 视频 -->
            <view class="flex-start-center">
              <video
                id="myVideo"
                class="list-item-media-view"
                :src="item.Url"
                @tap="onPlayVideo()"
                @fullscreenchange="onVideoFullScreenChange"
                controls
                v-if="item.Type == 0"
              ></video>
              <view
                class="list-item-media-view-img"
                v-if="item.Type == 0 && item.ReportType"
                @click="onSeeJCReport(item)"
              >
                <image
                  src="./static/img1.png"
                  style="width: 100%; height: 100%"
                />
                <text class="list-item-media-view-img-text">功能训练报告</text>
              </view>
            </view>
            <!-- 照片 -->
            <u--image
              :src="item.Url"
              width="80pt"
              height="80pt"
              @click="onPreviewImage(item.Url)"
              v-if="item.Type == 1"
            ></u--image>
            <!-- 脑动极光 - PDF -->
            <view
              class="flex-start-center list-item-media-view-pdf"
              v-if="item.PDFUrl && item.Manufacturer === 3"
              @click="onPreviewPDF(item.PDFUrl)"
            >
              <u--image
                src="https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/static/frontend/mp-patient/brain-light.png"
                width="64rpx"
                height="64rpx"
              ></u--image>
              <text class="list-item-media-view-pdf-text">认知测验报告</text>
            </view>
            <!-- 易脑/赛克 - 设备对应训练数据 -->
            <view
              class="list-item-remark-view"
              v-if="
                item.RemarkDataList &&
                (item.Manufacturer === 1 || item.Manufacturer === 2)
              "
            >
              <view v-for="(data, index) in item.RemarkDataList" :key="index">
                <text>{{ data }}</text>
              </view>
            </view>
          </view>
        </u-list-item>
      </u-list>
    </view>
  </scroll-view>
</template>

<script>
import { getTrainingActionRecords } from '@/api/training.js';
import {
  dataIsValid,
  arrayNotEmpty,
  stringNotEmpty,
  stringValid,
  numberValid,
} from '@/utils/utils';
import { dateFormat } from '@/utils/validate.js';
import { getReportActionMapping, queryJCaiTechReport } from '@/api/supplier.js';

const kEnableDebug = false;
const app = getApp();
export default {
  data() {
    return {
      // 训练动作id
      actionId: '',
      // 训练方案id
      trainingProgramId: '',
      // 当前方案动作对应机构
      orgId: '',

      // 标记数据
      markDataList: [],
      // 当前日期下的打卡记录
      selectedDateRecords: [],

      // 视频是否全屏
      isFullScreen: false,
    };
  },
  async onLoad(option) {
    kEnableDebug && console.debug('动作打卡 option', option);

    this.actionId = option.actionId;
    this.trainingProgramId = option.trainingProgramId;
    this.orgId = option.orgId;
    uni.setNavigationBarTitle({
      title: option.name,
    });

    this.getMarkData();
  },
  methods: {
    // 查看睛采报告
    async onSeeJCReport(item) {
      const actionExecuteId = item.Id;
      const actionType = item.ReportType;
      const res = await queryJCaiTechReport({
        UserId: app.globalData.userInfo.Id,
        ProgramId: null, //方案ID
        ActionExecuteId: actionExecuteId, //打卡记录ID
        ActionId: null, //方案明细ID
        ContentId: null, //动作基础数据ID
        BeginDate: null,
        EndDate: null,
        ActionType: null,
        PageIndex: 1,
        PageSize: 1,
      });
      if (res.Type !== 200) {
        uni.showModal({
          content: res.Content,
          title: '温馨提示',
          icon: 'none',
          showCancel: false,
        });
        return;
      }

      if (res.Data.Data && res.Data.Data.length) {
        const endDate = this.$dateFormat(
          res.Data.Data[0].CompletedTime,
          'YYYY-MM-DD HH:mm:ss'
        );
        uni.navigateTo({
          url:
            '/subReport/AssessmentReportDetail?' +
            `actionType=${actionType}` +
            `&endDate=${endDate}` +
            `&actionExecuteId=${actionExecuteId}`,
        });
      } else {
        uni.showToast({
          title: '暂无可查报告',
          icon: 'none',
        });
      }
    },
    // 当视频进入和退出全屏时触发
    onVideoFullScreenChange(e) {
      kEnableDebug && console.debug('onVideoFullScreenChange', e);
      this.isFullScreen = e.detail.fullScreen;
    },
    // 点击播放视频
    onPlayVideo() {
      kEnableDebug && console.debug('onPlayVideo', videoContext);
      // 创建视频上下文
      const videoContext = uni.createVideoContext('myVideo');
      if (!this.isFullScreen) {
        // 请求进入全屏
        videoContext.requestFullScreen({ direction: 90 });
        videoContext.play();
      }
    },
    // 点击预览图片
    onPreviewImage(url) {
      uni.previewImage({
        urls: [url],
      });
    },
    // 查看PDF
    onPreviewPDF(url) {
      this.$log.info(`${this.$envVersion}:查看脑动极光报告：${url}`);
      const deviceInfo = uni.getDeviceInfo();
      if (
        deviceInfo.platform === 'android' &&
        url.toLowerCase().endsWith('.pdf')
      ) {
        uni.showLoading({
          title: '',
          mask: true,
        });
        uni.downloadFile({
          url: url,
          success: (res) => {
            if (res.statusCode != 200) {
              uni.showToast({
                icon: 'none',
                title: res.errMsg ?? '获取报告失败',
              });
              return;
            }

            wx.openDocument({
              filePath: res.tempFilePath,
              success: () => {
                console.log('打开文档成功');
              },
            });
          },
          complete: () => {
            uni.hideLoading();
          },
        });
      } else {
        uni.navigateTo({
          url:
            '/subReport/WebView?Url=' +
            encodeURIComponent(url) +
            '&IsShowBack=' +
            false,
        });
      }
    },
    // 选中某天
    onSelectDate(e) {
      if (Object.keys(e.extraInfo).length > 0) {
        let data = e.extraInfo.data.data;
        this.selectedDateRecords = data.ExecuteRecords;
      } else {
        this.selectedDateRecords = [];
      }
    },
    // 获取睛采动作
    async getJCReportAction() {
      const res = await getReportActionMapping({
        orgId: this.orgId,
      });
      return res;
    },
    // 获取标记的数据
    async getMarkData() {
      uni.showLoading({
        title: '正在加载...',
        mask: false,
      });
      let userId = getApp().globalData.userInfo.Id;
      let r = await getTrainingActionRecords(
        this.trainingProgramId,
        this.actionId,
        userId
      );
      if (r.Type != 200) {
        uni.hideLoading();
        uni.showToast({
          title: r.Content,
          icon: 'none',
        });
        return;
      }
      if (!arrayNotEmpty(r.Data)) {
        uni.hideLoading();
        return;
      }

      let that = this;
      // 设置厂商数据
      try {
        that.manufacturer = r.Data[0].Manufacturer;
        that.mFType = r.Data[0].MFType;
      } catch (e) {
        that.manufacturer = null;
        that.mFType = null;
      }

      // 获取需要上传到睛采的动作
      let jcActionMap = {};
      const r1 = await this.getJCReportAction();
      if (r1.Type === 200) {
        jcActionMap = new Map(r1.Data.map((e) => [e.OrgActionId, e]));
      }

      // 根据日期归整打卡数据
      let nowDate = dateFormat(Date(), 'YYYY-MM-DD');
      r.Data.forEach((e) => {
        const isJCAction = jcActionMap.has(e.ContentId) ?? false; // 是否是睛采动作
        e.ExecuteRecords.forEach((record) => {
          record.Time = dateFormat(record.CreatedTime, 'HH:mm');
          // 图片、视频
          if (stringNotEmpty(record.Media)) {
            let mediaInfo = JSON.parse(record.Media);
            record.Type = mediaInfo.type;
            record.Url = mediaInfo.url;
          }

          // 睛采相关动作打卡数据处理
          if (isJCAction) {
            record.ReportType = jcActionMap.get(e.ContentId).Type;
          }

          // 易脑、赛克打卡数据处理
          if (e.Manufacturer === 1 || e.Manufacturer === 2) {
            this.handleYNAndXeekRecordData(e, record);
          }

          // 脑动极光打卡数据处理
          if (e.Manufacturer === 3) {
            this.handleBrainLightData(e, record);
          }
        });

        let date = dateFormat(e.DateTime, 'YYYY-MM-DD');
        let data = {
          date: date, // 该日期
          data: {
            finish: e.Finish, // 是否完成打卡
            data: e, // 该日期下数据
          },
        };
        that.markDataList.push(data);

        if (nowDate == date) {
          that.selectedDateRecords = e.ExecuteRecords;
        }
      });
      uni.hideLoading();
    },
    // 赛克、易脑打卡记录数据解析处理
    handleYNAndXeekRecordData(data, record) {
      // 厂商（1：易脑 2：赛克）
      let manufacturer = data.Manufacturer;
      // 赛克呼吸动作，则对应的动作类型（1：呼气训练 2：吸气训练 3：气道廓清）
      let mFType = data.MFType;
      record.Manufacturer = manufacturer;
      record.MFType = mFType;

      // 对应设备返回打卡训练数据
      if (dataIsValid(record.Remark)) {
        let remarkInfo = JSON.parse(record.Remark);
        kEnableDebug && console.debug('remarkInfo', remarkInfo);
        var remarkDataList = [];

        // 易脑-训练基本信息
        if (remarkInfo.WorkoutPrescriptionRecordVO && manufacturer == 1) {
          let recordVO = remarkInfo.WorkoutPrescriptionRecordVO;
          remarkDataList.push(recordVO.PrescriptionName || '-');
          remarkDataList.push(`肢体：${recordVO.Limb == 4 ? '上肢' : '下肢'}`);
          let prescriptionDuration = (
            numberValid(recordVO.PrescriptionDuration) / 60
          )
            .toFixed(1)
            .toString();
          remarkDataList.push(`时长：${prescriptionDuration}min`);
          remarkDataList.push(
            `难度：${recordVO.PrescriptionDifficulty || '-'}星`
          );
          remarkDataList.push(`设备：${recordVO.Device || '-'}`);
          let actualDuration = numberValid(recordVO.ActualDuration / 60)
            .toFixed(1)
            .toString();
          remarkDataList.push(`实际训练时长：${actualDuration}min`);
          remarkDataList.push(
            `训练时间：${
              recordVO.CreateTime != null
                ? dateFormat(new Date(recordVO.CreateTime), 'YYYY-MM-DD HH:mm')
                : '-'
            }`
          );
        }

        // 易脑-训练数据
        if (
          remarkInfo.WorkoutPrescriptionEval &&
          remarkInfo.WorkoutPrescriptionEval.Radar &&
          manufacturer == 1
        ) {
          let radar = remarkInfo.WorkoutPrescriptionEval.Radar;
          remarkDataList.push(
            `brunnstrom得分：${radar.BrunnstromScore.toString() || ''}`
          );
          remarkDataList.push(`平移得分：${radar.Horizontal.toString() || ''}`);
          remarkDataList.push(`抬举得分：${radar.Vertical.toString() || ''}`);
          remarkDataList.push(`爆发力得分：${radar.Power.toString() || ''}`);
          remarkDataList.push(
            `稳定性得分：${radar.Stability.toString() || ''}`
          );
          remarkDataList.push(`总分：${radar.Total.toString() || ''}`);
        }

        // 赛克呼吸
        var xeekResult;
        try {
          xeekResult = remarkInfo.Result[0].Result;
        } catch (e) {
          xeekResult = null;
        }
        // 呼气训练
        if (xeekResult && manufacturer == 2 && mFType == 1) {
          remarkDataList.push(`训练次数：${xeekResult.count || '-'}`);
          remarkDataList.push(
            `训练方式：${xeekResult.train_type == 1 ? '自动' : '手动'}`
          );
          if (xeekResult.train_type == 1) {
            remarkDataList.push(
              `训练难度：${xeekResult.difficulty_level || '-'}星`
            );
          }
          if (xeekResult.train_type == 2) {
            remarkDataList.push(`阻抗：${xeekResult.target_load || '-'}`);
          }
          remarkDataList.push(`训练的时间：${xeekResult.date || '-'}`);
          remarkDataList.push(`最大呼气压：${xeekResult.MEP_max || '-'}cmH2O`);
          remarkDataList.push(`平均呼气量：${xeekResult.FVC_avg || '-'}ml`);
          remarkDataList.push(`总呼气量：${xeekResult.FVC_all || '-'}mL`);
          remarkDataList.push(
            `平均呼气流量：${xeekResult.PEF_avg || '-'}L/min`
          );
        }

        // 吸气训练
        if (xeekResult && manufacturer == 2 && mFType == 2) {
          remarkDataList.push(`训练次数：${xeekResult.count || '-'}`);
          remarkDataList.push(
            `训练方式：${xeekResult.train_type == 1 ? '自动' : '手动'}`
          );
          if (xeekResult.train_type == 1) {
            remarkDataList.push(
              `训练难度：${xeekResult.difficulty_level || '-'}星`
            );
          }
          if (xeekResult.train_type == 2) {
            remarkDataList.push(`阻抗：${xeekResult.target_load || '-'}`);
          }
          remarkDataList.push(`训练的时间：${xeekResult.date || '-'}`);
          remarkDataList.push(`最大吸气压：${xeekResult.MIP_max || '-'}cmH2O`);
          remarkDataList.push(`平均吸气量：${xeekResult.FIVC_avg || '-'}ml`);
          remarkDataList.push(`总吸气量：${xeekResult.FIVC_all || '-'}mL`);
          remarkDataList.push(
            `平均吸气流量：${xeekResult.PIF_avg || '-'}L/min`
          );
        }

        // 气道廓清
        if (xeekResult && manufacturer == 2 && mFType == 3) {
          remarkDataList.push(
            `阻力级别：${xeekResult.difficulty_level || '-'}档`
          );
          remarkDataList.push(
            `训练方式：${xeekResult.train_type == 1 ? '自动' : '手动'}`
          );
          remarkDataList.push(`呼吸次数：${xeekResult.count || '-'}`);
          remarkDataList.push(
            `振动频率: ${xeekResult.train_frequence || '-'}Hz`
          );
          remarkDataList.push(`振动总时间：${xeekResult.duration || '-'}秒`);
          remarkDataList.push(`训练的时间：${xeekResult.date || ''}`);
          remarkDataList.push(
            `最小振动频率：${xeekResult.frequence_min || '-'}Hz`
          );
          remarkDataList.push(
            `平均振动频率：${xeekResult.frequence_avg || '-'}Hz`
          );
          remarkDataList.push(
            `最大振动频率：${xeekResult.frequence_max || '-'}Hz`
          );
          remarkDataList.push(
            `最小正向压力：${xeekResult.pressure_min || '-'}cmH2O`
          );
          remarkDataList.push(
            `平均正向压力：${xeekResult.pressure_avg || '-'}cmH2O`
          );
          remarkDataList.push(
            `最大正向压力：${xeekResult.pressure_max || '-'}cmH2O`
          );
          remarkDataList.push(
            `最小振动幅度：${xeekResult.amplitude_min || '-'}cmH2O`
          );
          remarkDataList.push(
            `平均振动幅度：${xeekResult.amplitude_avg || '-'}cmH2O`
          );
          remarkDataList.push(
            `最大振动幅度：${xeekResult.amplitude_max || '-'}cmH2O`
          );
        }

        record.RemarkDataList = remarkDataList;
      }
    },
    // 脑动极光打卡记录数据解析处理
    handleBrainLightData(data, record) {
      if (!stringNotEmpty(record.Remark)) return;

      // 厂商（3：脑动极光）
      let manufacturer = data.Manufacturer;
      record.Manufacturer = manufacturer;

      try {
        const remarkInfo = JSON.parse(record.Remark);
        record.PDFUrl = remarkInfo.Result;
      } catch (e) {
        kEnableDebug && console.debug('解析脑动极光数据失败', e);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.scroll-content {
  height: 100%;
  width: 100%;
}

.tips-view {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  height: 100rpx;
  background-color: white;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333333;
  margin: 20rpx 0;
}

.list-view {
  margin-bottom: env(safe-area-inset-bottom);
  margin-bottom: constant(safe-area-inset-bottom);
}

.list-view /deep/ .u-list {
  height: auto !important;
}

.list-item-view {
  padding: 24rpx 32rpx;
  background-color: white;
  margin: 24rpx 32rpx 0 32rpx;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}

.list-item-title-view {
  display: flex;
  flex-direction: row;
  justify-content: start;
  align-items: center;
  font-size: 30rpx;
  color: #333333;
  margin-bottom: 20rpx;
}

.list-item-title-view image {
  width: 40rpx;
  height: 40rpx;
  margin-right: 24rpx;
}

.list-item-media-view {
  width: 160rpx;
  height: 160rpx;

  // 睛采
  &-img {
    width: 160rpx;
    height: 160rpx;
    margin-left: 16rpx;
    position: relative;

    &-text {
      font-weight: 600;
      font-size: 20rpx;
      color: #333333;
      line-height: 28rpx;
      position: absolute;
      top: 16rpx;
      left: 16rpx;
    }
  }

  // 脑动极光
  &-pdf {
    background-color: #e3f9f7;
    border-radius: 20rpx;
    padding: 24rpx 29rpx 24rpx 18rpx;

    &-image {
      height: 64rpx;
      width: 64rpx;
    }

    &-text {
      font-weight: 500;
      font-size: 30rpx;
      color: #333333;
      margin-left: 11rpx;
    }
  }
}

.list-item-remark-view {
  display: flex;
  flex-direction: column;
  justify-content: start;
}

.list-item-remark-view text {
  font-size: 30rpx;
  color: #333333;
}
</style>
