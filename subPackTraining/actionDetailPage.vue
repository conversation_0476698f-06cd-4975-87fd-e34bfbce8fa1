<template>
  <view class="container">
    <!-- tabs标签 -->
    <view class="tab-bar">
      <u-tabs
        :list="tabTitles"
        :current="currentIndex"
        @click="onClickTab"
        lineWidth="0"
        :activeStyle="{
          color: '#ffffff',
          backgroundColor: '#29B7A3',
          fontSize: '17px',
          padding: '4px 10px',
          borderRadius: '35px',
        }"
        :inactiveStyle="{
          color: '#29B7A3',
          backgroundColor: '#ffffff',
          fontSize: '17px',
          padding: '4px 10px',
          borderRadius: '35px',
        }"
        :itemStyle="{
          'border-radius': '35px',
          'height': '35px',
          'display': 'flex',
          'justify-content': 'start',
          'align-items': 'center',
          'padding': '0px 6px',
        }"
      ></u-tabs>
    </view>

    <!-- 内容 -->
    <view class="list-view-wrapper" :style="'height:' + contentHeight + 'px'">
      <!-- 图片/视频 -->
      <swiper
        :circular="true"
        :style="{
          height: imageHeight + 'px',
          padding: '0 16px',
        }"
      >
        <swiper-item
          v-for="(item, index) in currentAction.MediaList"
          :key="index"
        >
          <video
            class="media-video"
            :src="item.Url"
            @play="onPlayVideo(item)"
            controls
            v-if="item.Type === 0"
          ></video>
          <u--image
            :src="item.Url"
            :width="imageWidth"
            :height="imageHeight"
            @click="onPreviewImage(currentAction.MediaList, item.Url)"
            :data-item="item"
            mode="aspectFit"
            v-else
          ></u--image>
        </swiper-item>
      </swiper>
      <!-- 展示内容 -->
      <view
        class="list-view"
        @touchstart="handleTouchStart"
        @touchend="handleTouchEnd"
      >
        <u-list
          :height="
            contentHeight -
            imageHeight -
            48 -
            (!disabled ? safeBottom + (isFollowAction ? 40 : 92) : 0) +
            'px'
          "
        >
          <u-list-item
            v-for="(item, index) in currentAction.ContentList"
            :key="index"
          >
            <view class="list-item" v-if="item.Content !== '暂无'">
              <view class="flex-start-center" style="margin-bottom: 16rpx">
                <image
                  style="width: 32rpx; height: 32rpx"
                  :src="
                    item.Title === '注意事项'
                      ? './static/img3.png'
                      : './static/img2.png'
                  "
                  mode="aspectFill"
                ></image>
                <text class="list-item-title">{{ item.Title }}</text>
              </view>
              <text
                class="list-item-content"
                v-if="item.Title !== '注意事项'"
                >{{ item.Content }}</text
              >
              <text
                class="list-item-content"
                v-else
                :style="'color:' + (item.Content === '暂无' ? '' : '#FF4273')"
                >{{ item.Content }}</text
              >
            </view>
          </u-list-item>
        </u-list>
      </view>

      <!-- 底部按钮 -->
      <view
        class="button-column-view"
        :style="'margin-bottom:' + safeBottom + 'px'"
        v-if="!disabled"
      >
        <view
          v-if="isFollowAction"
          :class="checkInAvailable ? 'button-view' : 'button-view-disable'"
          :hover-class="checkInAvailable ? 'hover-class' : null"
          hover-stay-time="70"
          @click="onStartFollow"
          >开始跟练</view
        >
        <block v-else>
          <view
            :class="
              checkInAvailable ? 'button-outline-view' : 'button-view-disable'
            "
            :hover-class="checkInAvailable ? 'hover-class' : null"
            hover-stay-time="70"
            @click="onNormalRecord(true)"
            >普通打卡</view
          >
          <view
            :class="checkInAvailable ? 'button-view' : 'button-view-disable'"
            :style="{ 'margin-top': 24 + 'rpx' }"
            :hover-class="checkInAvailable ? 'hover-class' : null"
            hover-stay-time="70"
            @click="onSpecialRecord"
            >视频打卡</view
          >
        </block>
      </view>
    </view>

    <!-- 悬浮标记 -->
    <view class="float-view" v-if="!disabled">
      <text>{{ finishCount }}</text>
      <text style="color: #999999">/{{ totalCount }}</text>
    </view>

    <!-- 右侧标签 -->
    <view class="container-action" @click="onOpenActionRecords">
      <image src="static/icon-jl.png" class="container-action-image"></image>
      <text>打卡记录</text>
    </view>

    <!-- 弹窗 -->
    <u-popup
      :show="showPrivacyPopup"
      :closeOnClickOverlay="false"
      :round="10"
      mode="bottom"
      @close="close"
    >
      <view style="padding: 40rpx">
        <p
          style="
            text-align: center;
            margin-bottom: 40rpx;
            font-size: 20px;
            font-weight: 700;
          "
        >
          提示
        </p>
        <text>
          在你使用服务之前，请仔细阅读<text
            @click="onClickPrivacyPopupTitle()"
            style="color: #29b7a3"
            >{{ PrivacyPopupTitle }}</text
          >。如果你同意{{ PrivacyPopupTitle }}，请点击"同意"开始使用。
        </text>
        <view class="display-style" style="margin-top: 40rpx">
          <u-button
            text="拒绝"
            @click="handleRefusePrivacyAuthorization()"
          ></u-button>
          <button
            id="agree-btn"
            class="btn-agree"
            open-type="agreePrivacyAuthorization"
            @agreeprivacyauthorization="handleAgreePrivacyAuthorization()"
          >
            同意
          </button>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import {
  homeTrainingActionPunchCard,
  getTrainingProgramInfoById,
} from '../api/training.js';
import {
  dataIsValid,
  arrayNotEmpty,
  stringNotEmpty,
  stringValid,
} from '../utils/utils';
import { TrainingClientEvent } from '../utils/eventKeys.js';
import { getReportActionMapping } from '../api/supplier.js';
import { videoCheckIn } from './video-check-in';
import { objNotEmpty } from '../utils/utils';

const kEnableDebug = false;
export default {
  data() {
    return {
      safeBottom: 16,
      imageWidth: 430,
      imageHeight: 233,

      // 内容高度
      contentHeight: 767,

      // 标签栏
      tabTitles: [],

      // 是否可以点击，历史或未开始进来时
      disabled: true,
      // 当前显示tab下标
      currentIndex: 0,
      // 训练动作
      actions: [],

      // 当前方案对应机构
      orgId: '',
      // 居家方案id
      trainingPlanId: '',

      // 动作中，是否已通过播放视频来自动打卡
      // 元素格式：actionId_actionIndex
      autoPunchActions: [],

      // 需要上传到睛采的动作列表
      jcActions: [],

      // 触摸相关
      touchStartX: 0,
      touchEndX: 0,
      minSwipeDistance: 80, // 最小滑动距离
    };
  },

  computed: {
    /** 判断是否需要上传睛彩或者播放视频打卡 如果返回 true 则不需要*/
    isJCAction() {
      if (this.jcActions.length === 0) return false;

      const action = this.actions[this.currentIndex];
      return this.jcActions.some((s) => s.OrgActionId === action?.ContentId);
    },

    // 当前显示的动作
    currentAction() {
      if (
        !arrayNotEmpty(this.actions) ||
        this.currentIndex >= this.actions.length
      ) {
        return null;
      }

      return this.actions[this.currentIndex];
    },

    // 当前动作下 - 已完成打卡次数
    finishCount() {
      return objNotEmpty(this.currentAction) &&
        dataIsValid(this.currentAction.TodayFinishCount)
        ? this.currentAction.TodayFinishCount
        : 0;
    },

    // 当前动作下 - 需要打卡次数
    totalCount() {
      return objNotEmpty(this.currentAction) &&
        dataIsValid(this.currentAction.TodayShouldCount)
        ? this.currentAction.TodayShouldCount
        : 0;
    },

    /** 是否还能打卡 */
    checkInAvailable() {
      return this.finishCount < this.totalCount;
    },

    /** 判断是否需要跟练 */
    isFollowAction() {
      return (
        objNotEmpty(this.currentAction) &&
        arrayNotEmpty(this.currentAction.FollowVideo) &&
        this.currentAction.FollowVideo.length > 0
      );
    },
  },

  onLoad: async function (option) {
    kEnableDebug && console.debug('训练详情页', option);

    this.saveWXCallBack();
    this.currentIndex = parseInt(option.index);
    this.disabled = Boolean(option.disabled == 'true');
    this.trainingPlanId = option.trainingPlanId;
    this.loadData();
    this.listenEvent();
  },

  onReady() {
    const res = uni.getWindowInfo();
    kEnableDebug && console.debug('窗口信息', res);

    this.safeBottom = Math.max(16, res.safeAreaInsets.bottom);
    this.imageWidth = res.screenWidth - 32;
    this.imageHeight = res.screenHeight * 0.25;
    this.contentHeight = res.screenHeight - res.safeAreaInsets.top - 44 - 67;
  },

  onUnload() {
    uni.$off(TrainingClientEvent.punchSuccess, this.punchSuccessFunction);
  },

  methods: {
    // 点击标签
    onClickTab(e) {
      let index = e.index;
      if (this.currentIndex != index) {
        this.currentIndex = index;
      }
    },

    // 触摸开始
    handleTouchStart(e) {
      this.touchStartX = e.touches[0].clientX;
    },

    // 触摸结束
    handleTouchEnd(e) {
      this.touchEndX = e.changedTouches[0].clientX;
      this.handleSwipe();
    },

    // 处理滑动
    handleSwipe() {
      const swipeDistance = this.touchEndX - this.touchStartX;

      // 判断是否达到最小滑动距离
      if (Math.abs(swipeDistance) < this.minSwipeDistance) {
        return;
      }

      // 左滑，currentIndex + 1
      if (swipeDistance < 0 && this.currentIndex < this.actions.length - 1) {
        // 延迟一个动画时间，避免滑动太快，导致内容没有加载出来
        uni.showLoading({
          title: '加载中...',
          mask: true,
        });
        setTimeout(() => {
          this.currentIndex++;
          uni.hideLoading();
        }, 300);
      }
      // 右滑，currentIndex - 1
      else if (swipeDistance > 0 && this.currentIndex > 0) {
        // 延迟一个动画时间，避免滑动太快，导致内容没有加载出来
        uni.showLoading({
          title: '加载中...',
          mask: true,
        });
        setTimeout(() => {
          this.currentIndex--;
          uni.hideLoading();
        }, 300);
      }
    },

    // 点击开始播放
    async onPlayVideo(e) {
      if (!this.checkInAvailable) return;
      if (this.isJCAction) return; // 睛彩动作，不需要自动打卡
      if (this.isFollowAction) return; // 跟练动作，不需要自动打卡

      const value = this.currentAction.Id + '_' + this.currentIndex;
      if (this.autoPunchActions.includes(value)) return; // 该动作已通过播放视频来自动打卡
      // 先标记，如果打卡失败再取消标记，避免重复触发
      this.autoPunchActions.push(value);

      this.onNormalRecord(false);
    },

    // 预览图片
    onPreviewImage(urls, url) {
      console.log(url);
      uni.previewImage({
        current: url,
        urls: urls,
      });
    },

    // 点击开始跟练
    onStartFollow() {
      const videoUrls = [];
      let currentVideoIndex = -1;

      if (this.finishCount >= this.totalCount) {
        // 无打卡次数: 只看该动作视频，无自动打卡
        videoUrls.push(
          ...this.currentAction.FollowVideo.map((url) => {
            return {
              actionId: this.currentAction.Id,
              valid: false,
              url: url,
            };
          })
        );
        currentVideoIndex = 0;
      } else {
        // 有打卡次数: 则遍历当前分组动作，获取可打卡的视频，并找到当前视频，在跟练轮播列表中的索引
        // 遍历当前分组动作
        const group = this.actions[this.currentIndex].Group;
        this.actions
          .filter(
            (e) =>
              e.Group === group &&
              (e.TodayFinishCount || 0) < (e.TodayShouldCount || 0)
          )
          .forEach((action, index) => {
            // 检查是否有剩余打卡次数且存在跟练视频
            if (arrayNotEmpty(action.FollowVideo)) {
              videoUrls.push(
                ...action.FollowVideo.map((url) => {
                  return {
                    actionId: action.Id,
                    valid: true,
                    url: url,
                  };
                })
              );
            }
          });

        // 当前准备跟练的视频，在轮播列表中的索引
        currentVideoIndex = videoUrls.findIndex(
          (e) => e.actionId === this.currentAction.Id
        );
      }

      if (videoUrls.length === 0) {
        uni.showToast({
          title: '暂无跟练视频',
          icon: 'none',
        });
        return;
      }

      uni.navigateTo({
        url:
          '/subPackTraining/followVideoPage' +
          `?videoUrls=${encodeURIComponent(JSON.stringify(videoUrls))}` +
          `&index=${currentVideoIndex}` +
          `&trainingPlanId=${this.trainingPlanId}`,
      });
    },

    // 普通打卡
    async onNormalRecord(showToast) {
      if (!this.checkInAvailable) {
        return;
      }

      uni.showLoading({
        title: '正在加载...',
        mask: true,
      });

      const action = this.currentAction;
      let trainingId = action.TrainingProgramId;
      let actionId = action.Id;
      const value = actionId + '_' + this.currentIndex;
      if (!dataIsValid(trainingId) || !dataIsValid(actionId)) {
        uni.hideLoading();
        uni.showToast({
          title: '方案或动作id为空',
          icon: 'none',
        });
        this.autoPunchActions = this.autoPunchActions.filter(
          (s) => s !== value
        );
        return;
      }

      let punchData = [
        {
          TrainingActionid: actionId,
          TrainingProgramId: trainingId,
        },
      ];
      let r = await homeTrainingActionPunchCard(punchData);
      if (r.Type != 200 || !arrayNotEmpty(r.Data)) {
        uni.hideLoading();
        uni.showToast({
          title: r.Content,
          icon: 'none',
        });
        this.autoPunchActions = this.autoPunchActions.filter(
          (s) => s !== value
        );
        return;
      }

      // 判断打卡是否成功
      let data = r.Data[0];
      if (!data.Success) {
        uni.hideLoading();
        uni.showToast({
          title: data.Message,
          icon: 'none',
        });
        this.autoPunchActions = this.autoPunchActions.filter(
          (s) => s !== value
        );
        return;
      }

      uni.hideLoading();
      uni.$emit(TrainingClientEvent.punchSuccess, [actionId]);
      if (showToast) {
        uni.hideLoading();
        uni.showToast({
          title: '打卡成功',
          icon: 'none',
        });
      }
      uni.hideLoading();
    },

    // 非普通打卡
    onSpecialRecord() {
      if (!this.checkInAvailable) return;

      if (this.isJCAction) {
        // 睛彩打卡
        this.handleJCSpecialRecord();
        return;
      }

      // 非睛彩，视频/图片打卡
      if (!uni.canIUse('chooseMedia.success.tempFiles')) {
        uni.showToast({
          title: '当前客户端版本过低，无法使用该功能，请升级到最新版本后重试。',
          icon: 'none',
        });
        return;
      }
      uni.chooseMedia({
        count: 1,
        mediaType: ['image', 'video'],
        sourceType: ['camera', 'album'],
        maxDuration: 60,
        camera: 'back',
        sizeType: ['original'],
        success: (res) => {
          let tempFile = res.tempFiles[0];
          if (res.type == 'image') {
            this.onOpenPunchCardPage(1, tempFile.tempFilePath);
          } else {
            this.onOpenPunchCardPage(0, tempFile.tempFilePath);
          }
        },
        fail: (err) => {
          kEnableDebug && console.debug(e);
        },
      });
    },

    /**
     * 睛彩视频打卡
     */
    handleJCSpecialRecord() {
      uni.showActionSheet({
        itemList: ['拍摄', '从手机相册选择'],
        success: (item) => {
          kEnableDebug && console.debug(item);

          if (item.tapIndex === 0) {
            // 直接拍摄打卡
            const config = this.jcActions.find(
              (s) => s.OrgActionId === this.currentAction.ContentId
            );
            const { Tip, Direction, Image } = config;
            // const test = '家属手持手机站在人体<span style="color:#ED9E42">侧方</span>，镜头正对人体正侧面，保持横屏状态，稳定进行拍摄，确保整个人像在手机屏幕中间。';
            videoCheckIn({
              actionId: this.currentAction.Id,
              trainingProgramId: this.trainingPlanId,
              direction: Direction === 0 ? 'landscape' : 'vertical',
              tip: Tip,
              image: Image,
            });
          } else {
            // 选择相册视频打卡
            if (!uni.canIUse('chooseMedia.success.tempFiles')) {
              uni.showToast({
                title:
                  '当前客户端版本过低，无法使用该功能，请升级到最新版本后重试。',
                icon: 'none',
              });
              return;
            }

            uni.chooseMedia({
              count: 1,
              mediaType: ['video'],
              sourceType: ['album'],
              sizeType: ['original'],
              success: (res) => {
                let tempFile = res.tempFiles[0];
                this.onOpenPunchCardPage(0, tempFile.tempFilePath);
              },
              fail: (err) => {
                kEnableDebug && console.log(e);
              },
            });
          }
        },
        fail: (e) => {
          kEnableDebug && console.log(e);
        },
      });
    },

    // 打开特殊打卡页面
    // type: 0视频 1图片
    onOpenPunchCardPage(type, filePath) {
      uni.navigateTo({
        url:
          '/subPackTraining/actionSpecialPunchCardPage?' +
          `type=${type}` +
          `&actionId=${this.currentAction.Id}` +
          `&trainingProgramId=${this.trainingPlanId}` +
          `&filePath=${filePath}`,
      });
    },

    // 打开打卡记录页面
    onOpenActionRecords() {
      uni.navigateTo({
        url:
          '/subPackTraining/trainingActionRecordPage?' +
          `name=${this.currentAction.Name}` +
          `&trainingProgramId=${this.trainingPlanId}` +
          `&actionId=${this.currentAction.Id}` +
          `&orgId=${this.orgId}`,
      });
    },

    // 监听事件
    listenEvent() {
      const that = this;

      // 打卡成功，刷新状态
      this.punchSuccessFunction = async function (actionIds) {
        kEnableDebug && console.debug('打卡成功事件', actionIds);
        let r = await that.requestTrainingPlanDetail();
        if (r.Type !== 200) {
          uni.showToast({
            title: r.Content,
            icon: 'none',
          });
        }
      };
      uni.$on(TrainingClientEvent.punchSuccess, this.punchSuccessFunction);
    },

    // 处理数据，加载数据
    async loadData() {
      if (!stringNotEmpty(this.trainingPlanId)) return;

      uni.showLoading({
        title: '正在加载...',
        mask: true,
      });
      let r0 = await this.requestTrainingPlanDetail();
      uni.hideLoading();
      if (r0.Type !== 200) {
        uni.showToast({
          title: r0.Content,
          icon: 'none',
        });
        return;
      }

      // 设置标签标题
      this.tabTitles = this.actions.map((e) => {
        return {
          name: e.Name,
        };
      });

      // 获取睛采数据
      const r1 = await this.requestJCData();
      if (r1.Type !== 200) {
        uni.showToast({
          title: r1.Content,
          icon: 'none',
        });
      }
    },

    // --------------------- 网络请求 ---------------------

    // 获取训练方案下的动作
    async requestTrainingPlanDetail() {
      let r = await getTrainingProgramInfoById(this.trainingPlanId);
      if (r.Type != 200) {
        uni.showToast({
          title: r.Content,
          icon: 'none',
        });
        return r;
      }

      // 处理动作数据
      var actions = r.Data.TrainingActions || [];
      actions.forEach((action, index) => {
        // 处理图片或者视频
        let medias = action.Media || [];
        if (medias && !Array.isArray(medias)) {
          medias = JSON.parse(action.Media);
        }
        let mediaList = medias.map((e) => {
          return {
            Url: e.Url,
            Type: action.MediaType,
          };
        });
        action.MediaList = mediaList;

        // 处理内容显示
        let contentList = [
          {
            Title: '运动名称',
            Content: stringValid(action.Name),
          },
        ];
        if (action.Type === 0) {
          contentList.push({
            Title: '频率',
            Content: `一天${action.Freq}次`,
          });
        }
        contentList.push({
          Title: '注意事项',
          Content: stringValid(action.Notes),
        });
        let iotParameters = action.InstrumentParameter
          ? JSON.parse(action.InstrumentParameter)
          : null;
        if (iotParameters && iotParameters.length) {
          // 动作关联物联设备，显示参数设置
          let content = iotParameters
            .map(
              (e) =>
                e.Name +
                (e.Type == 1 ? '(' : '') +
                (e.ValueLabel || '') +
                (e.Unit || '') +
                (e.Type == 1 ? ')' : '')
            )
            .join(',');
          contentList.push({
            Title: '参数',
            Content: stringValid(content),
          });
        } else {
          // 动作未关联物联设备，显示运动时间、运动强度
          contentList.push({
            Title: '运动时间',
            Content: stringValid(action.ActionTime),
          });
          contentList.push({
            Title: '运动强度',
            Content: stringValid(action.ActionStrength),
          });
        }
        action.ContentList = contentList;
      });

      this.orgId = r.Data.OrganizationId;
      this.actions = actions;

      return r;
    },

    // 获取睛采动作数据
    async requestJCData() {
      if (!this.orgId) {
        this.$log.warn(
          '当前计划里面的动作没有机构 Id, 无法判断是否需要上传到睛采'
        );
        return {
          Type: 500,
          Content: '机构id为空',
        };
      }

      const res = await getReportActionMapping({ orgId: this.orgId });
      if (res.Type === 200) {
        this.jcActions = res.Data;
      }

      return res;
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: start;
  align-items: center;
  background-color: #f8f7f7;
  position: relative;

  &-action {
    position: fixed;
    right: 0;
    top: 45%;
    width: 160rpx;
    height: 280rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    background-image: url('./static/icon-dk.png');
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center center;

    &-image {
      width: 52rpx;
      height: 52rpx;
    }

    text {
      font-weight: 400;
      font-size: 24rpx;
      color: #ffffff;
      margin-top: 2rpx;
    }
  }
}

.tab-bar {
  width: 100%;
  height: 67px;
  padding: 16px;

  /deep/ .u-tabs__wrapper__nav__item {
    padding: 0;
  }
}

.media {
  &-video {
    width: 100%;
    height: 100%;
    border-radius: 16rpx;
  }
}

.list-view-wrapper {
  width: 100%;
}

.list-view {
  padding: 12px;
  margin: 12px 32rpx;
  background-color: white;
  border-radius: 12px;
  box-sizing: content-box;
  display: flex;
  flex-direction: column;
  justify-content: start;
  align-items: center;
  overflow: hidden;
}

.list-item {
  padding-bottom: 40rpx;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}

.list-item-title {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
  margin-left: 4rpx;
}

.list-item-content {
  font-size: 16px;
  color: #666666;
}

.button-column-view {
  display: flex;
  flex-direction: column;
  justify-content: start;
  align-items: stretch;

  padding: 0 32rpx;
  font-size: 28rpx;
  font-weight: 600;
}

.button-outline-view {
  height: 40px;
  color: #29b7a3;
  border: 1rpx solid #29b7a3;
  background-color: white;
  border-radius: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.button-view {
  height: 40px;
  color: white;
  background-color: #29b7a3;
  border-radius: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.button-view-disable {
  height: 40px;
  color: gray;
  border: 1rpx solid gray;
  background-color: white;
  border-radius: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.float-view {
  position: absolute;
  right: 60rpx;
  bottom: 400rpx;
  width: 120rpx;
  height: 120rpx;
  background-color: white;
  border: 1rpx solid #29b7a3;
  border-radius: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 30rpx;
  color: #29b7a3;
  font-weight: bold;
}
</style>
