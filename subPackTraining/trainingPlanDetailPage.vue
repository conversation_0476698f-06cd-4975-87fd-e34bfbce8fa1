<template>
  <view class="content">
    <!-- 温馨提示 -->
    <u-modal
      :show="alertShow"
      title="方案说明"
      :content="explainContent"
      showConfirmButton="true"
      @confirm="alertShow = false"
    >
    </u-modal>
    <!-- 主滚动界面 -->
    <scroll-view
      enable-flex
      class="scroll-content"
      scroll-y="true"
      enable-back-to-top="true"
      v-if="Object.keys(trainingPlan).length > 0"
    >
      <!-- 卡片 -->
      <view class="card-section">
        <!-- 渐变头 -->
        <view class="card-header">
          <image
            class="card-header-icon"
            src="/static/training/training-card.png"
          ></image>
          <text class="card-header-text">{{ trainingPlan.Name }}</text>
          <view
            class="card-header-button"
            hover-class="hover-class"
            @click="onContactDoctor"
          >
            <text>咨询指导</text>
            <u-badge
              numberType="overflow"
              :absolute="true"
              :offset="[-5, -10]"
              max="99"
              :value="unReadCount"
            >
            </u-badge>
          </view>
        </view>
        <!-- 显示数据 -->
        <view class="card-data-content">
          <view class="card-data-view">
            <text class="card-data-content"
              >{{ trainingPlan.RemainingLife }}天</text
            >
            <text class="card-data-title">剩余天数</text>
          </view>
          <view class="card-data-view">
            <text class="card-data-content"
              >{{ Math.ceil(trainingPlan.FinishRate * 100) }}%</text
            >
            <text class="card-data-title">计划完成度</text>
          </view>
        </view>
      </view>
      <!-- 方案说明 -->
      <view class="explain-section" @click="alertShow = true">
        <text>方案说明：{{ trainingPlan.Remark }}</text>
        <uni-icons type="right" size="15" color="#999999"></uni-icons>
      </view>
      <!-- 标签栏 -->
      <view class="tabs-view">
        <u-subsection
          :list="tabTitles"
          mode="subsection"
          :current="currentIndex"
          activeColor="#29B7A3"
          inactiveColor="#333333"
          bgColor="#ffffff"
          fontSize="16"
          @change="onClickTab"
        >
        </u-subsection>
      </view>
      <!-- 康复治疗 -->
      <view class="list-view" v-show="currentIndex == 0">
        <view
          v-if="
            communityMoItems.length > 0 ||
            trainingMoItems.length > 0 ||
            treatmentActions.length > 0
          "
        >
          <!-- 社区医嘱 -->
          <u-list>
            <u-list-item v-for="(item, index) in communityMoItems" :key="index">
              <view
                class="list-community-item"
                @click="onOpenCommunityDetailPage"
                :data-item="item"
              >
                <view class="list-community-item-row">
                  <text class="list-community-item-title">{{
                    item.BaseMoItemName
                  }}</text>
                  <text class="list-community-item-count"
                    >{{ item.ExecuteCount || 0
                    }}<text class="list-community-item-total"
                      >/{{ item.TotalCount || 0 }}</text
                    ></text
                  >
                </view>
                <view
                  class="list-community-item-row"
                  style="margin-top: 8rpx; align-items: start"
                >
                  <text class="list-community-item-content">
                    {{
                      `${item.FreqDay}天${item.Freq}次 共${item.TotalCount}次`
                    }}
                  </text>
                  <view
                    class="list-community-item-button"
                    style="margin-top: 18rpx"
                    >寻找治疗点</view
                  >
                </view>
              </view>
            </u-list-item>
          </u-list>
          <!-- 康复治疗 - 穴位医嘱 -->
          <u-list>
            <u-list-item v-for="(item, index) in trainingMoItems" :key="index">
              <view
                class="list-treat-item"
                @click="onOpenMedicalAdviceDetailPage"
                :data-item="item"
              >
                <text class="list-item-title" style="margin-bottom: 24rpx">{{
                  item.MoItemName
                }}</text>
                <text class="list-item-content">
                  {{ `${item.FreqDay}天${item.Freq}次 共${item.TotalCount}次` }}
                </text>
              </view>
            </u-list-item>
          </u-list>
          <!-- 康复治疗 - 运动医嘱 -->
          <u-list>
            <u-list-item v-for="(o, index) in treatmentActions" :key="index">
              <view
                class="list-warpper-box"
                :style="
                  index === treatmentActions.length - 1
                    ? 'margin-bottom: 0'
                    : ''
                "
              >
                <view
                  class="list-warpper-box-top flex-start-center"
                  :style="o.isOpen ? '' : 'margin-bottom: 0'"
                  @click="onClickGroupActionHeader(o.type, index)"
                >
                  <image
                    src="https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/static/frontend/mp-patient/action-icon.png"
                    style="width: 40rpx; height: 40rpx"
                  ></image>
                  <text class="list-warpper-box-top-title">{{
                    o.type + (o.data.length ? '(' + o.data.length + ')' : '')
                  }}</text>
                  <u-icon
                    :name="o.isOpen ? 'arrow-down' : 'arrow-right'"
                    :size="20"
                  ></u-icon>
                </view>
                <block v-for="item in o.data" :key="item.Id">
                  <view
                    class="list-punch-item"
                    @click="onOpenActionDetailPage"
                    :data-item="item"
                    v-if="o.isOpen"
                  >
                    <u--image
                      :src="item.ActionUnitImgURL"
                      width="120rpx"
                      height="120rpx"
                      radius="8rpx"
                    >
                    </u--image>
                    <view class="list-punch-item-right-view">
                      <view class="list-punch-item-title-view">
                        <text class="list-item-title">{{ item.Name }}</text>
                        <text class="list-item-content" style="color: #29b7a3">
                          {{ item.TodayFinishCount }}/{{
                            item.TodayShouldCount
                          }}
                        </text>
                      </view>
                      <view class="list-punch-item-chart-inactive">
                        <view
                          class="list-punch-item-chart-active"
                          :style="{
                            width:
                              (item.TodayFinishCount * 100.0) /
                                item.TodayShouldCount +
                              '%',
                          }"
                        >
                        </view>
                      </view>
                    </view>
                  </view>
                </block>
              </view>
            </u-list-item>
          </u-list>
        </view>
        <!-- 康复治疗 - 无数据 -->
        <view style="padding-bottom: 24upx" v-else>
          <u-empty
            mode="data"
            icon="http://cdn.uviewui.com/uview/empty/data.png"
            text="当前方案没有康复治疗"
          >
          </u-empty>
        </view>
      </view>
      <!-- 康复评定 -->
      <view class="list-view" v-show="currentIndex == 1">
        <!-- 康复评定 - 列表 -->
        <u-list v-if="assessList.length > 0">
          <u-list-item
            v-for="(item, index) in assessList"
            :key="item.BaseEvaluateGaugeId"
          >
            <view class="list-assess-item" @click="onOpenAssessPage(item)">
              <view class="list-assess-item-top">
                <u--image
                  :showLoading="true"
                  src="/static/images/lb.png"
                  width="34px"
                  height="41.5px"
                >
                </u--image>
                <p class="list-assess-item-top name">{{ item.Name }}</p>
                <p
                  class="list-assess-item-top evaluated"
                  v-if="item.TotalCount > 1"
                >
                  已评估{{ item.TotalCount }}次
                </p>
                <p
                  class="list-assess-item-top evaluated"
                  v-else-if="item.TotalCount == 1"
                  style="color: #29b7a3"
                >
                  {{ item.SumPoint }}分
                </p>
                <p class="list-assess-item-top unevaluated" v-else>未评估</p>
              </view>
              <view class="list-assess-item-bottom">
                <p v-if="item.IsEvaluate" class="list-assess-item-bottom time">
                  上次评估时间：{{ item.EvaluateTime }}
                </p>
              </view>
            </view>
          </u-list-item>
        </u-list>
        <!-- 康复评定 - 无数据 -->
        <view style="padding-bottom: 24upx" v-else>
          <u-empty
            mode="data"
            icon="http://cdn.uviewui.com/uview/empty/data.png"
            text="当前方案没有康复评定"
          >
          </u-empty>
        </view>
      </view>
    </scroll-view>
    <!-- 没有已经进行的训练方案 ，也没有有效未执行方案 -->
    <scroll-view
      enable-flex
      class="scroll-content-no-data"
      scroll-y="true"
      enable-back-to-top="true"
      v-else
    >
      <u-empty
        mode="data"
        icon="http://cdn.uviewui.com/uview/empty/data.png"
        text="当前暂无进行中的康复计划"
      >
      </u-empty>
    </scroll-view>
  </view>
</template>

<script>
import { getTrainingProgramInfoById } from '@/api/training.js';
import {
  dataIsValid,
  numberValid,
  objNotEmpty,
  stringValid,
  ItemGroupBy,
} from '@/utils/utils';
import { dateFormat } from '@/utils/validate.js';
import { getGaugeList } from '@/subGauge/api';
import SessionState from '../libs/util/session_state';

const app = getApp();
export default {
  data() {
    return {
      // 训练方案的id
      trainingPlanId: '',

      alertShow: false,

      // 当前选中的tab标签下标
      currentIndex: 0,
      tabTitles: ['康复治疗', '康复评定'],

      // 训练方案
      trainingPlan: {},
      // 方案说明
      explainContent: '',

      // 社区医嘱 - 普通、穴位
      communityMoItems: [],
      // 康复治疗 - 动作
      treatmentActions: [],
      // 康复治疗 - 穴位
      trainingMoItems: [],
      // 康复评估
      assessList: [],
    };
  },
  onLoad: async function (option) {
    this.trainingPlanId = option.trainingPlanId;
    this.loadData();
  },
  methods: {
    // 点击选择标签栏
    onClickTab: function (index) {
      this.currentIndex = index;
    },
    // 打开社区医嘱详情
    onOpenCommunityDetailPage(e) {
      let item = e.currentTarget.dataset.item;
      if (!item.Id) {
        uni.showToast({
          title: '医嘱id为空',
          icon: 'none',
        });
        return;
      }

      uni.navigateTo({
        url: '/subPackIndex/community/detail?id=' + item.Id,
      });
    },
    // 点击分组动作头部进行展开/收起
    onClickGroupActionHeader(type, index) {
      this.treatmentActions[index].isOpen =
        !this.treatmentActions[index].isOpen;
    },
    // 打开动作详情
    onOpenActionDetailPage: function (e) {
      let item = e.currentTarget.dataset.item;
      let actions = this.treatmentActions.map((s) => s.data).flat();
      let index = actions.findIndex(
        (element) => element.Id == item.Id && dataIsValid(element.Id)
      );
      uni.navigateTo({
        url: `/subPackTraining/actionDetailPage?index=${index}&disabled=true&trainingPlanId=${this.trainingPlanId}`,
      });
    },
    // 打开居家医嘱详情
    onOpenMedicalAdviceDetailPage: function (e) {
      let item = e.currentTarget.dataset.item;
      let data = JSON.stringify(item);
      uni.navigateTo({
        url: '/subPackTraining/trainingMedicalAdviceDetailPage',
        success: (res) => {
          res.eventChannel.emit(
            'trainingPlanDetailSendDataToTrainingMedicalAdviceDetail',
            data
          );
        },
      });
    },
    // 打开自主评估页面
    onOpenAssessPage(item) {
      let roomId = this.trainingPlan.RoomId;
      let url = '';
      if (item.IsEvaluate == true) {
        url = `/subGauge/result?disabled=true&roomType=1&showAgain=false&itemInfo=${encodeURIComponent(JSON.stringify(item))}&roomId=${roomId}`;
      } else {
        url = `/subGauge/autonomy?id=${item.BaseEvaluateGaugeId}&DctSendSign=${item.DctSendSign}&roomType=1&disabled=true&RelatedId=${item.RelatedId}&roomId=${roomId}`;
      }

      uni.navigateTo({
        url: url,
      });
    },
    // 点击咨询指导
    async onContactDoctor(e) {
      // 获取居家训练会话
      uni.showLoading({
        title: '正在打开...',
        mask: true,
      });
      let roomId = this.trainingPlan.RoomId;
      var session;
      try {
        session = await SessionState.instance().findSession(roomId);
        uni.hideLoading();
      } catch (e) {
        uni.showToast({
          title: e,
          icon: 'none',
        });
        return;
      }
      if (objNotEmpty(session)) {
        uni.navigateTo({
          url: `/subPackChat/sessionChatPage?roomId=${roomId}&sendable=false`,
        });
      } else {
        uni.showToast({
          title: '未获取到会话',
          icon: 'none',
        });
      }
    },
    // 初始化数据
    async loadData() {
      uni.showLoading({
        title: '正在加载...',
      });
      const rs = await Promise.all([
        this.loadTrainingPlanDetail(),
        this.loadAssessList(),
      ]);
      uni.hideLoading();
    },
    // 获取训练方案
    async loadTrainingPlanDetail() {
      let r = await getTrainingProgramInfoById(this.trainingPlanId);
      if (r.Type != 200) {
        uni.showToast({
          title: r.Content,
          icon: 'none',
        });
        return null;
      }

      var trainingPlan = r.Data;
      this.explainContent = stringValid(trainingPlan.Remark);

      // 处理完成百分比
      trainingPlan.FinishRate = numberValid(trainingPlan.FinishRate);

      // 处理剩余时间
      trainingPlan.RemainingLife = numberValid(trainingPlan.RemainingLife);

      this.trainingPlan = trainingPlan;
      if (trainingPlan.CommunityMoItems) {
        this.communityMoItems =
          trainingPlan.CommunityMoItems.filter(
            (e) =>
              e.MoItemMethod == 0 || e.MoItemMethod == 1 || e.MoItemMethod == 5
          ) || [];
      } else {
        this.communityMoItems = [];
      }
      if (trainingPlan.TrainingActions && trainingPlan.TrainingActions.length) {
        const actionsList = ItemGroupBy(trainingPlan.TrainingActions, 'Group');
        actionsList.forEach((s) => {
          (s.isOpen = true), (s.type = s.type || '指导资料');
        });
        this.treatmentActions = actionsList;
      } else {
        this.treatmentActions = [];
      }
      this.trainingMoItems =
        trainingPlan.TrainingMoItems.filter(
          (e) =>
            e.MoItemMethod == 1 || e.MoItemMethod == 5 || e.MoItemMethod == 6
        ) || [];
    },
    // 获取自主评估列表
    async loadAssessList() {
      let params = {
        patId: app.globalData.userInfo.Id,
        programId: this.trainingPlanId,
        isLoadDoctor: false,
      };
      this.assessList = [];
      let r = await getGaugeList(params);
      if (r.Type != 200) {
        uni.showToast({
          title: r.Content,
          icon: 'none',
        });
        return;
      }

      var assessMap = {};
      // 将同一量表放入一个数组
      for (var element of r.Data) {
        var list = assessMap[element.BaseEvaluateGaugeId] || [];
        list.push(element);
        assessMap[element.BaseEvaluateGaugeId] = list;
      }

      let that = this;
      Object.values(assessMap).forEach(function (list) {
        // 筛选已评估量表
        var evaluatedList = list.filter(function (e) {
          return e.IsEvaluate == true;
        });
        // 量表按提交时间降序排列
        evaluatedList.sort(function (a, b) {
          return b.EvaluateTime < a.EvaluateTime ? -1 : 1;
        });

        // 如果有提交记录，则取最新一次提交数据用作展示
        // 如果没有提交过，则取返回的第一条数据
        var assess = evaluatedList[0] || list[0];
        assess.TotalCount = evaluatedList.length;
        assess.EvaluateTime = dateFormat(assess.EvaluateTime, 'YYYY-MM-DD');
        that.assessList.push(assess);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  height: 100%;
}

.scroll-content {
  background-color: #f7f7f7;
}

.scroll-content-no-data {
  background-color: #f7f7f7;
  display: flex;
  justify-content: center;
  align-items: center;
}

.card-section {
  margin: 24rpx 38rpx;
  background-color: white;
  display: flex;
  flex-direction: column;
  justify-content: start;
  border-radius: 8rpx;
  box-shadow: 0px 1px 8px rgba(255, 255, 255, 0.6);
}

.card-header {
  background-color: #29b7a3;
  border-top-left-radius: 8rpx;
  border-top-right-radius: 8rpx;
  padding: 0 24rpx;
  height: 128rpx;
  display: flex;
  flex-direction: row;
  justify-content: start;
  align-items: center;

  &-icon {
    width: 48rpx;
    height: 46rpx;
  }

  &-text {
    font-size: 32rpx;
    color: white;
    margin-left: 20rpx;
    flex: 1;
  }

  &-button {
    width: 162rpx;
    height: 56rpx;
    border-radius: 28rpx;
    border: 4rpx solid white;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;

    text {
      color: white;
      font-size: 26rpx;
    }
  }
}

.card-bottom-view {
  flex: 1;
  padding: 24rpx 0rpx;
  display: flex;
  flex-direction: row;
  align-items: stretch;
  justify-content: center;
}

.card-data-content {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  padding: 24rpx;
}

.card-data-view {
  display: flex;
  flex-direction: column-reverse;
  justify-content: start;
  align-items: center;
}

.card-data-title {
  color: #999999;
  font-size: 26rpx;
}

.card-data-content {
  font-size: 32rpx;
  color: #29b7a3;
  font-weight: bold;
}

.explain-section {
  margin: 0 32rpx 24rpx 32rpx;
  padding: 16rpx 24rpx;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  background-color: white;
  border-radius: 8rpx;
  box-shadow: 0px 1px 8px rgba(255, 255, 255, 0.6);
}

.explain-section text {
  flex: 1;
  font-size: 26rpx;
  color: #29b7a3;
}

.tabs-view {
  margin: 0 32rpx 24rpx 32rpx;
  background-color: white;
  border-radius: 8rpx;

  /deep/ .u-subsection__bar {
    height: 100%;
  }

  /deep/ .u-subsection {
    height: 90rpx;
  }
}
// 列表外层
.list-warpper {
  margin: 0 32rpx 32rpx 32rpx;
  // background-color: white;
  border-radius: 8rpx;
  box-shadow: 0px 1px 8px rgba(255, 255, 255, 0.6);

  /deep/ .u-list {
    height: auto !important;
  }
  &-box {
    background: #ffffff;
    border-radius: 8rpx 8rpx 8rpx 8rpx;
    padding: 16rpx;
    margin-bottom: 20rpx;
    &-top {
      margin-bottom: 30rpx;
      &-title {
        flex: 1;
        margin-left: 10rpx;
        font-weight: bold;
        font-size: 32rpx;
        color: #323233;
      }
    }
  }
}

.list-view {
  margin: 0rpx 32rpx 24rpx 32rpx;
  background-color: white;
  border-radius: 8rpx;
  box-shadow: 0px 1px 8px rgba(255, 255, 255, 0.6);
}

.list-view /deep/ .u-list {
  height: auto !important;
}

// 社区医嘱
.list-community-item {
  margin: 0 24rpx;
  padding: 24rpx 0;
  border-radius: 8rpx;
  border-bottom: 2rpx solid #e5e5e5;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: stretch;

  &-row {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  // 医嘱名
  &-title {
    flex: 1;
    color: #23b4a3;
    font-size: 28rpx;
    font-weight: 600;
    display: -webkit-box;
    overflow: hidden;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  &-count {
    color: #29b7a3;
    font-size: 28rpx;
    font-weight: 600;
  }

  &-total {
    color: #666666;
    font-size: 24rpx;
    font-weight: 600;
  }

  &-content {
    color: #666666;
    font-size: 24rpx;
  }

  &-button {
    color: #29b7a3;
    font-size: 24rpx;
    padding: 10rpx 20rpx;
    background-color: #f5f5f5;
    border-radius: 56rpx;
  }
}

.list-punch-item {
  margin-bottom: 20rpx;
  padding: 24rpx 0;
  border-radius: 8rpx;
  border-bottom: 2rpx solid #e5e5e5;
  display: flex;
  flex-direction: row;
  justify-content: start;
  align-items: center;
}

.list-punch-item-right-view {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: stretch;
  margin-left: 24rpx;
}

.list-punch-item-title-view {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.list-punch-item-chart-inactive {
  height: 24rpx;
  width: 100%;
  border-radius: 12rpx;
  margin-top: 24rpx;
  background-color: #f6f6f6;
  position: relative;
}

.list-punch-item-chart-active {
  height: 24rpx;
  width: 20%;
  border-radius: 12rpx;
  background-color: #29b7a3;
  position: absolute;
  top: 0;
  left: 0;
}

.list-treat-item {
  margin: 0 24rpx;
  padding: 24rpx 0;
  border-radius: 8rpx;
  border-bottom: 2rpx solid #e5e5e5;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}

.list-item-title {
  color: #333333;
  font-size: 32rpx;
  font-weight: bold;
}

.list-item-content {
  color: #666666;
  font-size: 30rpx;
}

.list-assess-item {
  margin: 0 24rpx;
  padding: 24rpx 0;
  border-radius: 8rpx;
  border-bottom: 2rpx solid #e5e5e5;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: stretch;
}

.list-assess-item-top {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;

  .name {
    margin: 0 32rpx;
    flex: 1;
    font-size: 30rpx;
    color: #333333;
  }

  .evaluated {
    font-size: 30rpx;
    font-weight: bold;
    color: #23b4a3;
  }

  .unevaluated {
    font-size: 30rpx;
    font-weight: bold;
    color: #ff3b30;
  }
}

.list-assess-item-bottom {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;

  .time {
    margin-top: 24rpx;
    font-size: 30rpx;
    color: #999999;
  }

  .button {
    margin-top: 24rpx;
    font-size: 30rpx;
    color: #23b4a3;
  }
}
</style>
