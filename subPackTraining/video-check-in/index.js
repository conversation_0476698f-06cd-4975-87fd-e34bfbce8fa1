import { toQueryString } from '../../utils/utils';

/**
 * 视频打卡
 *
 * @param {Object} options 选项
 * @param {'landscape'|'vertical'} options.direction 方向
 * @param {string} options.tip 提示
 * @param {string} options.image 提示图片
 * @param {string} options.actionId 动作Id
 * @param {string} options.trainingProgramId 项目Id
 */
export function videoCheckIn(options) {
  if (!options) throw new Error('缺少参数');
  const query = toQueryString(options);
  if (options.direction === 'landscape') {
    uni.navigateTo({
      url: `/subPackTraining/video-check-in/VideoCheckInLandscape?${query}`,
    });
  } else {
    uni.navigateTo({
      url: `/subPackTraining/video-check-in/VideoCheckInVertical?${query}`,
    });
  }
}

export const VideoCheckInPageVM = {
  data() {
    return {
      actionId: '',
      trainingProgramId: '',
      image: '',
      tip: '',
    };
  },
  onLoad(option) {
    console.log(option);
    this.actionId = option.actionId;
    this.trainingProgramId = option.trainingProgramId;
    this.tip = decodeURIComponent(option.tip);
    this.image = decodeURIComponent(option.image);
  },
  methods: {
    goToCheckIn(filePath) {
      const query = toQueryString({
        type: 0,
        actionId: this.actionId,
        trainingProgramId: this.trainingProgramId,
        filePath,
      });
      uni.redirectTo({
        url: `/subPackTraining/actionSpecialPunchCardPage?${query}`,
      });
    },
  },
};
