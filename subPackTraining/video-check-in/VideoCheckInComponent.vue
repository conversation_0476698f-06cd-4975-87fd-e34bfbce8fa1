<template>
  <view class="root-container" :class="direction">
    <camera
      class="camera"
      :device-position="devicePosition"
      flash="off"
      @error="error"
    ></camera>
    <view class="auth-tip" v-if="isAuthed === false">
      <text>无摄像头权限</text>
      <br />
      <text>请在小程序设置中授予摄像头权限，然后重新进入此页面</text>
    </view>
    <view class="top" :class="{ 'tip-expended': expended }">
      <view class="tip-container">
        <view class="image-container">
          <image class="image" :src="image"></image>
        </view>
        <rich-text :nodes="tip"></rich-text>
      </view>
      <view class="button" @click="toggleTip">
        <uni-icons :type="expendedIcon" color="white" size="20"></uni-icons>
      </view>
    </view>

    <view class="bottom">
      <view class="timer" :class="{ hidden: !recording }">{{
        duration | formateClock
      }}</view>
      <view class="operate-container">
        <view
          class="button back"
          :class="{ hidden: recording }"
          @click="handleBack"
        >
          <uni-icons type="down" color="white" size="20"></uni-icons>
        </view>
        <view class="button capture" :class="{ recording }" @click="takeVideo">
          <view class="circle"></view>
        </view>
        <view
          class="button switch"
          :class="{ hidden: recording }"
          @click="togglePosition"
        >
          <uni-icons type="loop" color="white" size="20"></uni-icons>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    image: String,
    tip: String,
    direction: String,
  },
  filters: {
    formateClock: function (seconds) {
      // 计算小时、分钟和秒
      const hrs = Math.floor(seconds / 3600);
      const mins = Math.floor((seconds % 3600) / 60);
      const secs = seconds % 60;

      // 使用 `padStart` 保证两位数显示
      const formatted =
        String(hrs).padStart(2, '0') +
        ':' +
        String(mins).padStart(2, '0') +
        ':' +
        String(secs).padStart(2, '0');

      return formatted;
      return value + ':';
    },
  },
  data() {
    return {
      /**是否正在拍摄 */
      recording: false,
      /**摄像头 */
      devicePosition: 'back',
      /**是否展开提示 */
      expended: true,
      /**计时（秒） */
      duration: 0,
      /**是否已授予摄像头权限 */
      isAuthed: null,
    };
  },
  computed: {
    expendedIcon() {
      if (this.direction === 'landscape') {
        return this.expended ? 'left' : 'right';
      } else {
        return this.expended ? 'up' : 'down';
      }
    },
  },
  destroyed() {
    this.stopClock();
  },
  methods: {
    takeVideo() {
      if (this.isAuthed === false) {
        uni.showToast({
          title: '请授予摄像头权限后重新进入此页面',
          icon: 'none',
        });
        return;
      }
      console.log('录像');
      if (this.operation) {
        return;
      }
      this.operation = true;
      const ctx = uni.createCameraContext();
      if (!this.recording) {
        this.recording = true;
        ctx.startRecord({
          timeoutCallback: (res) => {
            console.log('录像超时结束', res);
            this.recording = false;
            this.stopClock();
          },
          success: () => {
            console.log('开始录像');
            this.startClock();
          },
          fail: (e) => {
            this.recording = false;
            console.error('开始录像失败', e);
            if (e.errMsg.endsWith('not allowed to use microphone.')) {
              uni.showToast({
                title: `请到小程序设置中打开麦克风权限`,
                icon: 'none',
              });
            } else {
              uni.showToast({
                title: `发生错误\n${e.errMsg}`,
                icon: 'fail',
              });
            }
          },
          complete: () => {
            console.debug('调用开始命令完成');
            this.operation = false;
          },
        });
        return;
      }

      uni.showLoading({
        title: '处理中',
        mask: true,
      });

      this.stopClock();
      ctx.stopRecord({
        compressed: false,
        success: (res) => {
          console.log('停止录像成功', res);
          uni.hideLoading();
          this.recording = false;
          this.operation = false;
          this.$emit('finish', res.tempVideoPath);
        },
        fail: (e) => {
          // 录制时间太短会进入这里
          console.error('停止录像失败', e);
          uni.hideLoading();
          this.recording = false;
          this.operation = false;
          if (e.errMsg.endsWith('record time too short')) {
            uni.showToast({
              title: `录制时间太短`,
              icon: 'none',
            });
          } else {
            uni.showToast({
              title: `发生错误\n${e.errMsg}`,
              icon: 'fail',
            });
          }
        },
      });
    },
    togglePosition() {
      const position = this.devicePosition === 'front' ? 'back' : 'front';
      console.log('切换摄像头', position);
      this.devicePosition = position;
    },
    error(e) {
      console.error(e);
      // insertCamera:fail auth deny
      // if (e.detail.errMsg)
      this.$log.error(e);
      this.isAuthed = false;
      this.recording = false;
      this.stopClock();
    },
    handleBack() {
      uni.navigateBack();
    },
    toggleTip() {
      this.expended = !this.expended;
    },
    startClock() {
      this.stopClock();
      this.timer = setInterval(() => {
        this.duration++;
      }, 1000);
    },
    stopClock() {
      if (this.timer) {
        clearInterval(this.timer);
        this.duration = 0;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.root-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.camera {
  width: 100%;
  height: 100%;
}

.button {
  background-color: rgba(0, 0, 0, 0.6);
  padding: 8px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  aspect-ratio: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.hidden {
  display: none;
}

.auth-tip {
  position: absolute;
  inset: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.top {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: 0.5s;

  .tip-container {
    align-self: stretch;
    display: flex;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.6);
    padding: 34px;
    color: white;

    .image-container {
      background-color: lightblue;
      align-self: stretch;
      flex-shrink: 0;
      overflow: hidden;
    }

    .image {
      width: 100%;
      height: 100%;
    }
  }
}

.bottom {
  position: absolute;
  bottom: 0;
  right: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-items: stretch;

  .timer {
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    padding: 8rpx;
    border-radius: 4rpx;
    transition: 0.5s;
  }

  .operate-container {
    align-self: stretch;
    display: flex;
    align-items: center;
    justify-content: space-evenly;

    .back {
    }

    .capture {
      width: 75px;
      height: 75px;

      padding: 5px;
      background-color: rgba(0, 0, 0, 0.51);
      border: 0px solid transparent;
      transition: 0.5s ease-out;

      .circle {
        background-color: white;
        border-radius: 50%;
        width: 100%;
        height: 100%;
        transition: 0.5s ease-out;
      }
    }

    .recording {
      border: 5px solid white;
      // transition: 0.5s;

      .circle {
        background-color: #fd493b;
      }
    }

    .switch {
    }
  }
}

$tip-size: 300px;

.vertical {
  .top {
    top: -$tip-size;
    right: 0;
    left: 0;

    .tip-container {
      height: $tip-size;

      .image-container {
        // aspect-ratio: 9 / 16;
        margin-right: 16rpx;
        // width: calc(($tip-size - 68px) * 9 / 16);
        // height: calc($tip-size - 68px);
        width: 130.5px;
        height: 232px;
      }
    }

    .button {
      margin-top: 50px;
    }
  }

  .bottom {
    left: 0;

    .timer {
      margin-bottom: 40rpx;
    }

    .operate-container {
      padding-bottom: 110rpx;
    }
  }

  .tip-expended {
    top: 0px;
  }
}

.landscape {
  .top {
    left: -$tip-size;
    top: 0;
    bottom: 0;
    flex-direction: row;

    .tip-container {
      width: $tip-size;
      flex-direction: column;
      justify-content: space-evenly;

      .image-container {
        aspect-ratio: 16 / 9;
        // height: calc(($tip-size - 68px) * 9 / 16);
        // width: calc($tip-size - 68px);
        // height: 130.5px;
        // width: 232px;
      }
    }

    .button {
      margin-left: 50px;
    }
  }

  .bottom {
    top: 0;
    flex-direction: row;

    .timer {
      margin-right: 40rpx;
    }

    .operate-container {
      flex-direction: column-reverse;
      padding-right: 60rpx;
    }
  }

  .tip-expended {
    left: 0px;
  }
}
</style>
