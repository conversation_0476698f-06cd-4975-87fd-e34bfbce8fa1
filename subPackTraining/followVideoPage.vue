<template>
  <view class="video-page">
    <!-- 返回按钮 -->
    <view class="back-btn" @click="onBack">
      <u-icon name="arrow-left" color="#fff"></u-icon>
    </view>
    <!-- 错误提示 -->
    <view v-if="hasError" class="error" @click="onRetryVideo">
      视频加载失败，请重试
      <u-icon name="reload" color="#fff"></u-icon>
    </view>
    <block v-else>
      <!-- 视频播放器 -->
      <video
        id="activeVideo"
        :src="currentVideo.url"
        :autoplay="true"
        :controls="false"
        :show-fullscreen-btn="false"
        :show-play-btn="true"
        :show-center-play-btn="true"
        :enable-progress-gesture="true"
        :enable-play-gesture="true"
        :page-gesture="true"
        :show-progress="true"
        :show-loading="true"
        @click="onVideoClick"
        @error="handleVideoError"
        @play="handleVideoPlay"
        @pause="handleVideoPause"
        @timeupdate="handleVideoTimeUpdate"
        @ended="handleNextVideo"
        style="width: 100%; height: 100%"
      ></video>
      <!-- 自定义控制器 -->
      <view
        class="video-mask"
        :style="{
          paddingBottom: safeBottom + 'px',
          display: showControls ? 'flex' : 'none',
        }"
      >
        <!-- 自定义播放按钮 -->
        <view class="video-mask-play" @click="onPlayOrPause">
          <u-icon
            v-if="!isPlaying"
            name="play-right-fill"
            size="30"
            color="#fff"
          ></u-icon>
          <u-icon v-else name="pause" size="30" color="#fff"></u-icon>
        </view>
        <!-- 视频当前播放时长 -->
        <view class="video-mask-current-time">
          {{ formattedCurrentTime }}
        </view>
        <!-- 自定义进度条 -->
        <view class="video-mask-progress-bar">
          <view
            class="video-mask-progress-bar-inner"
            :style="{
              width: videoProgress + '%',
            }"
          >
            <view
              class="video-mask-progress-dot-wrapper"
              @touchstart="onProgressTouchStart"
              @touchmove="onProgressTouchMove"
              @touchend="onProgressTouchEnd"
            >
              <view class="video-mask-progress-dot"></view>
            </view>
          </view>
        </view>
        <!-- 视频总时长 -->
        <view class="video-mask-duration">
          {{ formattedDuration }}
        </view>
      </view>
    </block>
  </view>
</template>

<script>
import { TrainingClientEvent } from '../utils/eventKeys.js';
import { homeTrainingActionPunchCard } from '../api/training.js';
import { arrayNotEmpty } from '../utils/utils';

const kEnableDebug = false;
export default {
  data() {
    return {
      safeBottom: 16,

      // 视频列表
      videoList: [],
      // 当前视频索引
      currentIndex: 0,
      // 是否加载失败
      hasError: false,

      // 是否显示控制器
      showControls: true,
      // 进度条长度
      progressBarWidth: 0,
      // 是否正在播放
      isPlaying: false,
      // 视频当前播放时长（用于显示当前播放多少秒）
      videoCurrentTime: 0,
      // 当前视频进度条位置（实际控制进度条位置，拖动中进度需要显示不变）
      videoProgress: 0,

      // 进度条拖动相关
      isTouching: false,
      touchStartX: 0,
      touchStartProgress: 0,
    };
  },

  computed: {
    currentVideo() {
      if (
        this.videoList.length === 0 ||
        this.currentIndex >= this.videoList.length
      ) {
        return null;
      }

      return this.videoList[this.currentIndex];
    },

    // 当前播放视频的时长（秒）
    videoDuration: {
      get() {
        return this.currentVideo?.duration || 0;
      },
      set(val) {
        // 验证输入值
        if (!val) {
          console.warn('无效视频时长:', val);
          return;
        }
        // 确保 currentVideo 存在
        if (this.currentVideo) {
          this.videoList[this.currentIndex].duration = val;
        }
      },
    },

    // 格式化后的视频时长显示
    formattedDuration() {
      return this.formatTime(this.videoDuration);
    },

    // 格式化后的当前播放时长显示
    formattedCurrentTime() {
      return this.formatTime(this.videoCurrentTime);
    },
  },

  created() {
    this.trainingPlanId = '';
  },

  onLoad(options) {
    kEnableDebug && console.debug('onLoad', options);

    this.trainingPlanId = options.trainingPlanId;
    if (options.videoUrls) {
      this.videoList = JSON.parse(decodeURIComponent(options.videoUrls)).map(
        (item) => {
          return {
            ...item,
            duration: 0, // 记录视频时长，用于进度条拖动
            hasNotified: false, // 确保每个视频只会发送一次打卡通知
          };
        }
      );
    }
    if (options.index !== undefined) {
      this.currentIndex = parseInt(options.index);
    }
  },

  onReady() {
    this.videoContext = uni.createVideoContext('activeVideo');
    this.safeBottom = Math.min(uni.getWindowInfo().safeAreaInsets.bottom, 16);

    // 获取进度条宽度
    this.getProgressBarWidth();
  },

  onUnload() {
    kEnableDebug && console.debug('页面卸载，清理资源');

    // 打过卡的动作，发送事件给打开当前页面的页面，通知其刷新
    const actionIds = this.videoList
      .filter((v) => v.hasNotified)
      .map((v) => v.actionId);
    if (actionIds.length > 0) {
      uni.$emit(TrainingClientEvent.punchSuccess, actionIds);
    }
  },

  methods: {
    onBack() {
      uni.navigateBack();
    },

    handleVideoError(e) {
      kEnableDebug && console.error('视频播放出错', e.detail.errMsg);
      this.$log.error(
        `${this.$envVersion}:跟练视频播放出错`,
        e.detail.errMsg,
        JSON.stringify(this.currentVideo)
      );
      this.hasError = true;
    },

    handleVideoPlay() {
      kEnableDebug &&
        console.debug('视频播放', JSON.stringify(this.currentVideo));
      this.$log.info('播放跟练视频', JSON.stringify(this.currentVideo));
      this.isPlaying = true;

      // 发送事件给打开当前页面的页面，通知其自动打卡
      if (
        !this.videoList[this.currentIndex].hasNotified &&
        this.videoList[this.currentIndex].valid
      ) {
        this.videoList[this.currentIndex].hasNotified = true;
        this.handlePunch();
      }
    },

    handleVideoTimeUpdate(e) {
      kEnableDebug && console.debug('视频播放进度', e.detail);
      const { currentTime, duration } = e.detail;
      this.videoCurrentTime = currentTime;

      if (this.videoDuration !== duration) {
        this.videoDuration = duration; // 使用 setter 更新时长
      }

      if (!this.isTouching) {
        this.videoProgress = (currentTime / duration) * 100;
      }
    },

    handleVideoPause() {
      kEnableDebug && console.debug('视频暂停');
      this.isPlaying = false;
    },

    handlePrevVideo() {
      kEnableDebug && console.debug('上一个视频');
      if (this.currentIndex > 0) {
        this.currentIndex--;
      }
    },

    handleNextVideo() {
      kEnableDebug && console.debug('下一个视频');
      if (this.currentIndex < this.videoList.length - 1) {
        this.currentIndex++;
      } else {
        uni.navigateBack();
      }
    },

    // 单击视频，隐藏/显示控制器
    onVideoClick() {
      kEnableDebug && console.debug('点击视频');
      this.showControls = !this.showControls;
    },

    // 点击播放或暂停
    onPlayOrPause() {
      kEnableDebug && console.debug('点击播放或暂停');
      this.isPlaying ? this.videoContext.pause() : this.videoContext.play();
    },

    onProgressTouchStart(e) {
      kEnableDebug && console.debug('开始拖动进度条', e.detail);

      this.isTouching = true;

      // 记录开始触摸位置和当前进度
      this.touchStartX = e.touches[0].clientX;
      this.touchStartProgress = this.videoProgress;
    },

    onProgressTouchMove(e) {
      kEnableDebug && console.debug('拖动进度条', e.touches[0].clientX);
      if (!this.progressBarWidth) return;

      const moveX = e.touches[0].clientX - this.touchStartX;
      const movedProgress = (moveX / this.progressBarWidth) * 100;
      let newProgress = this.touchStartProgress + movedProgress;
      // 更新进度显示
      this.videoProgress = Math.max(0, Math.min(100, newProgress));
    },

    onProgressTouchEnd(e) {
      kEnableDebug && console.debug('结束拖动进度条', e.detail);
      if (!this.videoContext) return;

      // 设置视频播放位置
      const newTime = (this.videoProgress / 100) * this.videoDuration;
      this.videoContext.seek(newTime);

      this.isTouching = false;
    },

    // 点击重试视频
    onRetryVideo() {
      kEnableDebug && console.debug('点击重试视频');
      this.hasError = false;
      this.videoContext.play();
    },

    // 打卡
    async handlePunch() {
      let punchData = [
        {
          TrainingActionid: this.currentVideo.actionId,
          TrainingProgramId: this.trainingPlanId,
        },
      ];
      let r = await homeTrainingActionPunchCard(punchData);
      if (r.Type != 200 || !arrayNotEmpty(r.Data)) {
        uni.showToast({
          title: r.Content,
          icon: 'none',
        });
        this.videoList[this.currentIndex].hasNotified = false;
        return;
      }

      // 判断打卡是否成功
      let data = r.Data[0];
      if (!data.Success) {
        uni.showToast({
          title: data.Message,
          icon: 'none',
        });
        this.videoList[this.currentIndex].hasNotified = false;
        return;
      }
    },

    // 获取进度条宽度
    getProgressBarWidth() {
      const query = uni.createSelectorQuery().in(this);
      query
        .select('.video-mask-progress-bar')
        .boundingClientRect((data) => {
          if (data) {
            this.progressBarWidth = data.width;
          }
        })
        .exec();
    },

    // 格式化时间显示（秒 -> MM:SS）
    formatTime(seconds) {
      if (typeof seconds !== 'number' || seconds < 0) {
        return '00:00';
      }
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = Math.floor(seconds % 60);
      return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    },
  },
};
</script>

<style lang="scss" scoped>
.video-page {
  position: relative;
  width: 100vw;
  height: 100vh;
  background-color: #000;

  display: flex;
  align-items: center;
  justify-content: center;
}

.back-btn {
  position: absolute;
  top: 0;
  left: 0;
  padding: 20rpx 64rpx 64rpx 32rpx;
  z-index: 1;
}

.video-mask {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 50rpx;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: content-box;
  z-index: 1;

  .video-mask-play {
    width: 50rpx;
    height: 50rpx;
    display: flex;
    margin-left: 15rpx;
    align-items: center;
    justify-content: center;
  }

  .video-mask-progress-bar {
    flex: 1;
    height: 8rpx;
    border-radius: 8rpx;
    background-color: rgba(255, 255, 255, 0.5);

    .video-mask-progress-bar-inner {
      position: relative;
      width: 0;
      height: 100%;
      border-radius: 8rpx;
      background-color: #fff;

      .video-mask-progress-dot-wrapper {
        width: 50rpx;
        height: 50rpx;
        position: absolute;
        top: 50%;
        right: 0;
        transform: translate(50%, -50%);
        display: flex;
        align-items: center;
        justify-content: center;

        .video-mask-progress-dot {
          width: 22rpx;
          height: 22rpx;
          background-color: #fff;
          border-radius: 50%;
        }
      }
    }
  }

  .video-mask-current-time {
    color: #fff;
    font-size: 16rpx;
    padding-right: 25rpx;
    width: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .video-mask-duration {
    color: #fff;
    font-size: 16rpx;
    width: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #fff;
  text-align: center;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
