<template>
  <scroll-view
    class="scroll-content"
    scroll-y="true"
    enable-flex="true"
    enable-back-to-top="true"
  >
    <!-- 没有已经进行的训练方案 -->
    <u-empty
      mode="data"
      icon="http://cdn.uviewui.com/uview/empty/data.png"
      v-if="
        availableTrainingPlanList.length == 0 &&
        unavailableTrainingPlanList.length == 0
      "
    >
    </u-empty>
    <!-- 可执行方案列表 -->
    <view
      class="list-view"
      style="margin-bottom: 32upx"
      v-if="availableTrainingPlanList.length > 0"
    >
      <u-list>
        <u-list-item
          v-for="(item, index) in availableTrainingPlanList"
          :key="index"
        >
          <view
            class="list-item"
            hover-class="hover-class"
            @click="onSwitchPlan(item.Id)"
          >
            <view class="list-item-top">
              <u--image
                :src="item.ActionUnitImgURL"
                width="60pt"
                height="60pt"
                radius="4pt"
              ></u--image>
              <view class="list-item-top-middle-view" style="flex: 1">
                <text class="list-item-top-title">{{ item.Name }}</text>
                <text class="list-item-top-content">{{
                  item.CreatorName + '  ' + item.StartTime
                }}</text>
              </view>
              <view class="button-submit">切换计划 </view>
            </view>
            <view class="list-chart-view">
              <view class="list-chart-title-view">
                <text>剩余{{ item.RemainingLife }}天</text>
                <text>{{ (item.FinishRate * 100).toFixed(2) }}%</text>
              </view>
              <view class="list-item-chart-inactive" style="margin-top: 10upx">
                <view
                  class="list-item-chart-active"
                  :style="{ width: item.FinishRate * 100 + '%' }"
                />
              </view>
            </view>
          </view>
        </u-list-item>
      </u-list>
    </view>
    <!-- 已失效计划列表 -->
    <view class="list-view" v-if="unavailableTrainingPlanList.length > 0">
      <view class="list-title-view">已失效计划</view>
      <u-list>
        <u-list-item
          v-for="(item, index) in unavailableTrainingPlanList"
          :key="index"
        >
          <view
            class="list-item"
            hover-class="hover-class"
            @click="onCheckPlanDetail(item.Id)"
          >
            <view class="list-item-top">
              <u--image
                :src="item.ActionUnitImgURL"
                width="60pt"
                height="60pt"
                radius="4pt"
              ></u--image>
              <view class="list-item-top-middle-view" style="flex: 1">
                <text class="list-item-top-title" style="color: #cccccc">{{
                  item.Name
                }}</text>
                <text class="list-item-top-content">{{
                  item.CreatorName + '  ' + item.StartTime
                }}</text>
              </view>
              <view
                class="button-outside"
                @click.stop="onToEvaluation(item.Id)"
                v-if="!item.IsEva"
              >
                <view class="button-submit">评价</view>
              </view>
              <view
                class="button-submit"
                style="background-color: #cccccc"
                v-else
                >切换计划</view
              >
            </view>
            <view class="list-chart-view">
              <view class="list-chart-title-view" style="color: #cccccc">
                <text>剩余{{ item.RemainingLife }}天</text>
                <text>{{ (item.FinishRate * 100).toFixed(2) }}%</text>
              </view>
              <view class="list-item-chart-inactive" style="margin-top: 10upx">
                <view
                  class="list-item-chart-active"
                  :style="{ width: item.FinishRate * 100 + '%' }"
                >
                </view>
              </view>
            </view>
          </view>
        </u-list-item>
      </u-list>
    </view>
    <!-- 上拉提示尾部 -->
    <view
      class="pull-foot-view"
      :style="{
        display:
          !haveMore &&
          (availableTrainingPlanList.length > 0 ||
            unavailableTrainingPlanList.length > 0)
            ? ''
            : 'none',
      }"
    >
      没有更多了
    </view>
  </scroll-view>
</template>

<script>
import {
  getAllTrainingPlanList,
  startTrainingProgram,
} from '@/api/training.js';
import { arrayNotEmpty, dataIsValid, stringNotEmpty } from '@/utils/utils';
import { dateFormat } from '@/utils/validate.js';
import { TrainingClientEvent } from '@/utils/eventKeys.js';

const app = getApp();

export default {
  data() {
    return {
      pageIndex: 1,
      haveMore: true,

      // 可执行方案
      availableTrainingPlanList: [],
      // 已失效方案
      unavailableTrainingPlanList: [],
    };
  },
  onLoad() {
    uni.startPullDownRefresh();
  },
  onPullDownRefresh: async function () {
    uni.showLoading({
      title: '正在加载...',
      mask: true,
    });
    await this.loadNewData();
    uni.hideLoading();
    uni.stopPullDownRefresh();
  },
  onReachBottom: async function () {
    uni.showLoading({
      title: '正在加载...',
      mask: true,
    });
    await this.loadMoreData();
    uni.hideLoading();
  },
  methods: {
    // 已失效的计划可以去评价
    onToEvaluation(Id) {
      console.log('计划的Id', Id);
      uni.navigateTo({
        url: '/subServices/planIndex?Id=' + Id,
      });
    },
    // 获取userId
    getUserId() {
      let userId = app.globalData.userInfo.Id;
      if (!stringNotEmpty(userId)) {
        return null;
      }
      return userId;
    },
    // 点击切换计划
    async onSwitchPlan(trainingId) {
      uni.showLoading({
        title: '正在加载...',
        mask: true,
      });
      let r = await this.startTrainingProgram(trainingId);
      uni.hideLoading();

      if (r.Type == 200) {
        uni.$emit(TrainingClientEvent.switchTrainingPlan, trainingId);
        uni.navigateBack();
      }
      uni.showToast({
        title: r.Content,
        icon: 'none',
      });
    },
    // 点击查看计划详情
    onCheckPlanDetail(trainingId) {
      uni.navigateTo({
        url: `/subPackTraining/trainingPlanDetailPage?trainingPlanId=${trainingId}`,
      });
    },
    // 重新加载数据
    async loadNewData() {
      this.pageIndex = 1;
      this.haveMore = true;
      this.availableTrainingPlanList = [];
      this.unavailableTrainingPlanList = [];
      await this.loadData();
    },
    // 加载更多数据
    async loadMoreData() {
      if (!this.haveMore) return;
      this.pageIndex++;
      this.loadData();
    },
    // 获取全部训练方案
    async loadData() {
      let userId = this.getUserId();
      if (userId == null) return;

      let r = await getAllTrainingPlanList(userId, this.pageIndex);
      if (r.Type == 200 && r.Data) {
        // 过滤当前执行中方案
        let allTrainingPlanList = r.Data.Data.filter((e) => e.State != 1);
        if (!arrayNotEmpty(allTrainingPlanList)) {
          this.haveMore = false;
          return;
        }

        allTrainingPlanList.forEach((item) => {
          item.StartTime = dateFormat(item.StartTime, 'YYYY-MM-DD');
        });

        let availableTrainingPlanList = allTrainingPlanList.filter(
          (e) => e.Group == 0
        );
        let unavailableTrainingPlanList = allTrainingPlanList.filter(
          (e) => e.Group == 1
        );

        if (this.pageIndex == 1) {
          this.availableTrainingPlanList = availableTrainingPlanList;
          this.unavailableTrainingPlanList = unavailableTrainingPlanList;
        } else {
          this.availableTrainingPlanList.push(...availableTrainingPlanList);
          this.unavailableTrainingPlanList.push(...unavailableTrainingPlanList);
        }
      } else {
        uni.showToast({
          title: r.Content,
          icon: 'none',
        });
      }

      return r;
    },
    // 开始训练计划
    async startTrainingProgram(trainingId) {
      let userId = this.getUserId();
      if (!stringNotEmpty(userId) || !stringNotEmpty(trainingId)) return null;

      let r = await startTrainingProgram(userId, trainingId);
      if (r.Type != 200) {
        uni.showToast({
          title: r.msg,
          icon: 'none',
        });
      }

      return r;
    },
  },
};
</script>

<style lang="scss" scoped>
@import url('../common/common.css');

.scroll-content-no-data {
  background-color: #f7f7f7;
  display: flex;
  justify-content: center;
  align-items: center;
}

.scroll-content {
  padding: 24upx 32upx;
}

.list-view {
  padding-bottom: 32upx;
  background-color: white;
  border-radius: 8upx;
  box-shadow: 0px 1px 8px rgba(255, 255, 255, 0.6);
}

.list-view /deep/ .u-list {
  height: auto !important;
}

.list-item {
  margin: 32upx 24upx 0 24upx;
  border-radius: 8upx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: stretch;
}

.list-item-top {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.list-item-top-middle-view {
  margin: 0 24upx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}

.list-item-top-title {
  color: #29b7a3;
  font-size: 30upx;
}

.list-item-top-content {
  margin-top: 20upx;
  color: #999999;
  font-size: 26upx;
}

.list-chart-view {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: stretch;
}

.list-chart-title-view {
  margin-top: 10upx;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  color: #29b7a3;
  font-size: 26upx;
}

.list-item-chart-inactive {
  height: 16upx;
  width: 100%;
  border-radius: 8upx;
  margin: 24upx 0upx 0upx 0upx;
  background-color: #f6f6f6;
  position: relative;
}

.list-item-chart-active {
  height: 16upx;
  width: 20%;
  border-radius: 8upx;
  background-color: #29b7a3;
  position: absolute;
  top: 0;
  left: 0;
}

.list-title-view {
  display: flex;
  justify-content: center;
  align-items: center;
  color: red;
  padding-top: 32upx;
}

.button-outside {
  width: 144upx;
  height: 80px;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.button-submit {
  width: 144upx;
  height: 42upx;
  background-color: #29b7a3;
  color: white;
  border-radius: 21upx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 26upx;
}
</style>
