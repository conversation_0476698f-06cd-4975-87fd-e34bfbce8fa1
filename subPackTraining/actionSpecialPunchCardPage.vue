<template>
  <view class="content">
    <!-- 有数据 -->
    <view v-if="!noData">
      <view class="media-view">
        <video
          v-if="type == 0"
          class="media-item"
          :src="filePath"
          controls
        ></video>
        <image
          v-else
          class="media-item"
          :src="filePath"
          mode="aspectFit"
        ></image>
      </view>
      <view
        class="button-view"
        hover-class="hover-class"
        hover-start-time="70"
        @click="onPunchCard"
        >打卡</view
      >
    </view>
    <!-- 无数据 -->
    <u-empty
      mode="data"
      icon="http://cdn.uviewui.com/uview/empty/data.png"
      v-else
    >
    </u-empty>
  </view>
</template>

<script>
import { homeTrainingActionPunchCard } from '@/api/training.js';
import { stringNotEmpty } from '@/utils/utils';

import { TrainingClientEvent } from '@/utils/eventKeys.js';
import { uploadFileToServer } from '../services/UploadService';

const kEnableDebug = false;
export default {
  data() {
    return {
      actionId: '',
      trainingProgramId: '',

      /**0:视频 1:图片*/
      type: 0,
      noData: true,
      filePath: '',
    };
  },
  onLoad: function (option) {
    kEnableDebug && console.debug('option', option);

    this.actionId = option.actionId;
    this.trainingProgramId = option.trainingProgramId;
    this.type = option.type;
    this.noData = !stringNotEmpty(option.filePath);
    if (option.filePath) {
      this.filePath = decodeURIComponent(option.filePath);
    }
  },
  methods: {
    // 点击打卡
    async onPunchCard() {
      uni.showLoading({
        title: '正在请求...',
        mask: true,
      });

      let r = await uploadFileToServer(this.filePath, {
        isCompressed: false,
      });
      if (r.statusCode != 200) {
        uni.hideLoading();

        let errMsg = r.errMsg;
        if (r.statusCode === 413) {
          errMsg = '文件大小超过限制';
        }
        uni.showToast({
          title: errMsg,
          icon: 'none',
        });
        return;
      }

      let data = r.data;
      if (data.Type != 200) {
        uni.hideLoading();
        uni.showToast({
          title: data.Content,
          icon: 'none',
        });
        return;
      }

      var punchData = {
        TrainingActionid: this.actionId,
        TrainingProgramId: this.trainingProgramId,
        Media: JSON.stringify({
          type: parseInt(this.type),
          url: data.Data,
        }),
      };

      let rr = await homeTrainingActionPunchCard([punchData]);
      uni.hideLoading();

      if (rr.Type != 200) {
        uni.showToast({
          title: rr.Content,
          icon: 'none',
        });
        return;
      }

      // 判断打卡是否成功
      let statusData = rr.Data[0];
      if (!statusData.Success) {
        uni.showToast({
          title: statusData.Message,
          icon: 'none',
        });
        return;
      }

      uni.showToast({
        title: '打卡成功',
        icon: 'none',
      });

      uni.$emit(TrainingClientEvent.punchSuccess, [this.actionId]);
      uni.navigateBack();
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: start;
}

.media-view {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: stretch;
  aspect-ratio: 16/9;
  margin-bottom: 100rpx;
}

.media-item {
  width: 100%;
  height: 100%;
}

.button-view {
  margin: 0 50rpx;
  height: 100rpx;
  font-size: 36rpx;
  color: white;
  background-color: #29b7a3;
  border-radius: 50rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
