<template>
  <scroll-view class="scroll-content" scroll-y="true" enable-flex>
    <!-- 如果不是宣教类型的医嘱 -->
    <block v-if="medicalAdvice.MoItemMethod !== 6">
      <!-- 医嘱 -->
      <view class="item-view">
        <text class="title">医嘱</text>
        <text class="content">
          <text>{{ medicalAdvice.MoItemName }}</text>
          <text style="margin-left: 10px">{{
            medicalAdvice.MoItemMethod !== 5
              ? medicalAdvice.FreqDay +
                '天' +
                medicalAdvice.Freq +
                '次,共' +
                medicalAdvice.TotalCount +
                '次'
              : 'x' + medicalAdvice.TotalCount
          }}</text>
        </text>
      </view>
      <!-- 视频指导 -->
      <view class="item-view" v-if="showVideo">
        <text class="title">视频指导</text>
        <video
          class="item-video"
          :src="medicalAdvice.VideoUrl"
          controls
        ></video>
      </view>
      <!-- 穴位 -->
      <view class="item-view" v-if="medicalAdvice.MoItemMethod == 1">
        <text class="title">穴位</text>
        <view class="item-button-view">
          <view
            :class="
              index == selectedIndex ? 'item-button' : 'item-button-disable'
            "
            v-for="(item, index) in medicalAdvice.TrainingMoItemDetails"
            :key="item.Id"
            @click="onSelectAcupoint"
            :data-index="index"
            >{{ item.Name }} x {{ item.Count }}</view
          >
        </view>
        <text
          class="content"
          style="margin: 24rpx 0"
          v-if="showAcupointRemark"
          >{{ acupointInfo.Remark }}</text
        >
        <u-grid :border="false" @click="onPreviewImage">
          <u-grid-item
            v-for="(url, index) in acupointInfo.AcuPointImgs"
            :key="url"
          >
            <u--image
              :src="url"
              :width="imageWidth"
              :height="imageWidth"
            ></u--image>
          </u-grid-item>
        </u-grid>
      </view>
      <!-- 治疗须知 -->
      <view class="item-view" v-if="showTreatmentInstructions">
        <text class="title">治疗须知</text>
        <rich-text
          style="margin-top: 20rpx"
          :nodes="medicalAdvice.Remark"
        ></rich-text>
      </view>
    </block>
    <block v-else>
      <view class="container">
        <block
          v-for="(item, index) in recoveryMissionRelationsList"
          :key="item.Id"
        >
          <view
            class="container-box flex-between-center"
            @click="handleSeeMissionDetail(item, index)"
          >
            <image
              :src="item.Img"
              style="width: 124rpx; height: 124rpx"
            ></image>
            <text class="text-max1 container-box-title">{{ item.Title }}</text>
            <text
              class="container-box-state"
              :class="!item.IsRead ? '' : 'container-box-statefin'"
              >{{ !item.IsRead ? '未读' : '已读' }}</text
            >
          </view>
        </block>
      </view>
    </block>
  </scroll-view>
</template>

<script>
import { arrayNotEmpty, stringNotEmpty } from '@/utils/utils.js';
import { setRead } from '@/api/consult.js';
import { getMissionImageUrl } from '../utils/mission';

export default {
  data() {
    return {
      // 上一个界面传过来的医嘱详情数据
      medicalAdvice: {},
      // 穴位图片宽高
      imageWidth: 0,

      // 治疗须知是否显示
      showTreatmentInstructions: false,
      // 视频指导是否显示
      showVideo: false,

      // 选择的穴位下标
      selectedIndex: -1,
      // 穴位显示内容
      acupointInfo: {},
      // 穴位说明是否显示
      showAcupointRemark: false,
      // 穴位图片是否显示
      showAcupointImages: false,
      // 宣教显示列表
      recoveryMissionRelationsList: [],
    };
  },
  onLoad: function (option) {
    let that = this;
    const eventChannel = this.getOpenerEventChannel();
    eventChannel.on('indexSendDataToTrainingMedicalAdviceDetail', function (e) {
      let data = JSON.parse(e);
      that.loadData(data);
    });
    eventChannel.on(
      'trainingPlanDetailSendDataToTrainingMedicalAdviceDetail',
      function (e) {
        let data = JSON.parse(e);
        that.loadData(data);
      }
    );
  },
  onReady() {
    let that = this;
    uni.getSystemInfo({
      success: (res) => {
        let factor = res.screenWidth / 375.0;
        that.imageWidth = (res.screenWidth - 52.0 * factor) / 3.0;
      },
    });
  },
  methods: {
    handleSeeMissionDetail(item, index) {
      const id = item.RecoveryMissionId;
      const recoveryId = item.Id;
      // 数据库标记为已读
      setRead({
        Id: recoveryId,
      });
      // 本地修改为已读
      const list = this.recoveryMissionRelationsList;
      list[index].IsRead = true;
      this.recoveryMissionRelationsList = [];
      this.recoveryMissionRelationsList = list;
      uni.navigateTo({
        url: `/subPropaganda/detail?id=${id}`,
      });
    },
    // 处理加载数据
    loadData(medicalAdvice) {
      if (stringNotEmpty(medicalAdvice.BaseInfo)) {
        let baseInfo = JSON.parse(medicalAdvice.BaseInfo);
        medicalAdvice.Remark = baseInfo.Remark;
        medicalAdvice.VideoUrl = baseInfo.MoItemVideoUrl;

        this.showTreatmentInstructions = stringNotEmpty(medicalAdvice.Remark);
        this.showVideo = stringNotEmpty(medicalAdvice.VideoUrl);
      }

      this.medicalAdvice = medicalAdvice;
      if (arrayNotEmpty(medicalAdvice.TrainingMoItemDetails)) {
        this.selectedIndex = 0;
        this.refreshAcupointInfo();
      }
      if (
        medicalAdvice.RecoveryMissionRelations &&
        medicalAdvice.RecoveryMissionRelations.length
      ) {
        let list = medicalAdvice.RecoveryMissionRelations;
        list.forEach((e, index) => {
          if (!e.Img) {
            e.Img = getMissionImageUrl(index);
          }
        });
        this.recoveryMissionRelationsList = list;
      }
    },
    // 点击选择穴位
    onSelectAcupoint(e) {
      let index = e.currentTarget.dataset.index;
      this.selectedIndex = index;
      this.refreshAcupointInfo();
    },
    // 预览穴位图片
    onPreviewImage(index) {
      let url = this.acupointInfo.AcuPointImgs[index];
      uni.previewImage({
        current: url,
        urls: this.acupointInfo.AcuPointImgs,
      });
    },
    // 刷新显示穴位相关信息
    refreshAcupointInfo() {
      if (this.selectedIndex < 0) return;

      var acupointInfo =
        this.medicalAdvice.TrainingMoItemDetails[this.selectedIndex];
      if (stringNotEmpty(acupointInfo.BaseInfo)) {
        let baseInfo = JSON.parse(acupointInfo.BaseInfo);
        acupointInfo.Remark = baseInfo.Remark;
        acupointInfo.AcuPointImgs = baseInfo.AcuPointImg;

        this.showAcupointRemark = stringNotEmpty(baseInfo.Remark);
        this.showAcupointImages = arrayNotEmpty(acupointInfo.AcuPointImgs);
      }
      this.acupointInfo = acupointInfo;
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding: 32rpx;

  &-box {
    background: #ffffff;
    border-radius: 24rpx;
    padding: 24rpx 32rpx;
    margin-bottom: 24rpx;

    &-title {
      flex: 1;
      font-weight: 600;
      font-size: 28rpx;
      color: #333333;
      line-height: 40rpx;
      margin-left: 32rpx;
      margin-right: 24rpx;
    }

    &-state {
      font-weight: 400;
      font-size: 28rpx;
      line-height: 40rpx;
      color: #ed9e42;
    }

    &-statefin {
      color: #29b7a3 !important;
    }
  }
}

.scroll-content {
  width: 100%;
  height: 100%;
  padding-bottom: env(safe-area-inset-bottom);
  padding-bottom: contant(safe-area-inset-bottom);
}

.item-view {
  margin-top: 20rpx;
  width: 100%;
  padding: 24rpx 32rpx;
  background-color: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}

.title {
  font-size: 30rpx;
  color: #333333;
  font-weight: bold;
}

.content {
  font-size: 30rpx;
  color: #666666;
  margin-top: 16rpx;
}

.item-video {
  width: 100%;
  margin-top: 24rpx;
}

.item-button-view {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: start;
  align-items: center;
  align-content: flex-start;
  margin-top: 4rpx;
}

.item-button {
  font-size: 26rpx;
  color: white;
  background-color: #29b7a3;
  padding: 8rpx 32rpx;
  border-radius: 50rpx;
  margin: 20rpx 20rpx 0 0;
}

.item-button-disable {
  font-size: 26rpx;
  color: #29b7a3;
  background-color: white;
  padding: 8rpx 32rpx;
  border-radius: 50rpx;
  border: 1rpx solid #29b7a3;
  margin: 20rpx 20rpx 0 0;
}
</style>
