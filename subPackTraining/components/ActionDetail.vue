<template>
  <view :style="'height:' + height + 'px'">
    <!-- 图片/视频 -->
    <swiper
      class="swiper-view"
      :circular="true"
      :style="{
        height: imageHeight + 'px',
        padding: '0 16px',
      }"
    >
      <swiper-item v-for="(item, index) in action.MediaList" :key="index">
        <video
          :id="action.Id"
          class="media-video"
          :src="item.Url"
          @play="onPlayVideo(item)"
          @ended="onVideoEnded(item)"
          controls
          v-if="item.Type == 0"
        ></video>
        <u--image
          :src="item.Url"
          :width="imageWidth"
          :height="imageHeight"
          @click="onPreviewImage(action.MediaList, item.Url)"
          :data-item="item"
          mode="aspectFit"
          v-else
        ></u--image>
      </swiper-item>
    </swiper>
    <!-- 展示内容 -->
    <view
      class="list-view"
      :style="{
        height: contentHeight - imageHeight - safeBottom - 16 + 'px',
      }"
    >
      <u-list :height="contentHeight - imageHeight - safeBottom - 32 + 'px'">
        <u-list-item v-for="(item, index) in action.ContentList" :key="index">
          <view class="list-item" v-if="item.Content !== '暂无'">
            <view class="flex-start-center" style="margin-bottom: 16rpx">
              <image
                style="width: 32rpx; height: 32rpx"
                :src="
                  item.Title === '注意事项'
                    ? './static/img3.png'
                    : './static/img2.png'
                "
                mode="aspectFill"
              ></image>
              <text class="list-item-title">{{ item.Title }}</text>
            </view>
            <text class="list-item-content" v-if="item.Title !== '注意事项'">{{
              item.Content
            }}</text>
            <text
              class="list-item-content"
              v-else
              :style="'color:' + (item.Content === '暂无' ? '' : '#FF4273')"
              >{{ item.Content }}</text
            >
          </view>
        </u-list-item>
        <!-- 底部留白，避免按钮遮挡内容 -->
        <u-list-item v-if="clickEnable">
          <view style="height: 93px"></view>
        </u-list-item>
      </u-list>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    height: {
      type: Number,
      value: 767,
      required: true,
    },
    action: {
      type: Object,
      value: {},
      required: true,
    },
  },

  watch: {
    action: {
      handler(newVal) {
        this.isFollowAction =
          newVal.FollowVideo && newVal.FollowVideo.length > 0;
      },
    },
  },

  data() {
    return {
      safeBottom: 16,
      imageWidth: 430,
      imageHeight: 233,

      // 打卡按钮是否有效
      buttonEnable: false,
      // 是否有跟练视频
      isFollowAction: false,

      // 当前动作下 - 已完成打卡次数
      finishCount: 0,
      // 当前动作下 - 需要打卡次数
      totalCount: 0,
    };
  },
};
</script>

<style lang="scss" scoped></style>
