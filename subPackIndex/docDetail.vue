<template>
  <view class="container">
    <!-- 最上方防止塌陷 -->
    <!-- <view :style="[style]" style="width: 100%;"></view> -->
    <!-- 标题 -->
    <u-navbar
      :placeholder="true"
      :title="title"
      @leftClick="onLeftClick"
      :safeAreaInsetTop="true"
      :fixed="true"
      bgColor="rgba(0, 0, 0, 0)"
    >
    </u-navbar>
    <!-- 中间区域 -->
    <view class="container-box">
      <!-- 上方的医生 -->
      <view class="container-box-doctor">
        <view class="container-box-doctor-top">
          <view class="container-box-doctor-top-left">
            <image
              class="container-box-doctor-top-left-img"
              src="static/bz.png"
              alt=""
              srcset=""
            />
            <!-- 名称 -->
            <view class="container-box-doctor-top-left-box">
              <text class="container-box-doctor-top-left-box-name">{{
                docInfo.Doctor.Name
              }}</text>
              <view
                class="container-box-doctor-top-left-box-recommend"
                v-if="docInfo.Recommend"
              >
                <span>推荐</span>
                <u-icon name="thumb-up-fill" color="#B76F29"></u-icon>
              </view>
            </view>
            <!-- 职位 -->
            <view class="container-box-doctor-top-left-workbox">
              <span class="container-box-doctor-top-left-workbox-worktitle">{{
                docInfo.Doctor.WorkerTitle
              }}</span>
              <!-- <view class="container-box-doctor-top-left-workbox-recommend" v-if="docInfo.Recommend">
								<span>推荐</span> <u-icon name="thumb-up-fill" color="#B76F29"></u-icon>
							</view> -->
              <view
                @click="onLookAuthentication"
                class="container-box-doctor-top-left-workbox-recommend"
                v-if="onIsShowPracticeCertification()"
              >
                <span>执业认证</span>
                <image
                  src="/subPackIndex/static/guarantee.png"
                  style="width: 24rpx; height: 24rpx"
                ></image>
              </view>
            </view>
            <!-- 科室和机构 -->
            <view class="container-box-doctor-top-left-workbox1">
              <span class="container-box-doctor-top-left-workbox1-text">{{
                docInfo.Doctor.PracticeOrganizationName ||
                docInfo.Doctor.OrganizationName
              }}</span>
              <span class="container-box-doctor-top-left-workbox1-text1">{{
                docInfo.Doctor.DepartmentName
              }}</span>
            </view>
            <!-- 评价 -->
            <view class="container-box-doctor-top-left-workbox1">
              <span>
                <span class="container-box-doctor-top-left-workbox1-text2"
                  >评分</span
                >
                <span
                  class="container-box-doctor-top-left-workbox1-count"
                  v-if="docAvgStart && docAvgStart > 0"
                  >{{ docAvgStart }}</span
                >
                <span
                  class="container-box-doctor-top-left-workbox1-text2"
                  style="margin-left: 20rpx"
                  v-else
                  >暂无评分</span
                >
              </span>
              <span style="margin-left: 20rpx">
                <span class="container-box-doctor-top-left-workbox1-text2"
                  >服务患者</span
                >
                <span class="container-box-doctor-top-left-workbox1-count">{{
                  docInfo.ConsultCount
                }}</span>
              </span>
            </view>
            <!-- 经验 -->
            <!-- <view class="container-box-doctor-top-left-experience">
							<view class="container-box-doctor-top-left-experience-each" v-for="i in 4">
								经验丰富
							</view>
						</view> -->
          </view>
          <view class="container-box-doctor-top-right">
            <u-avatar :src="docInfo.Doctor.HeadImg" size="110"></u-avatar>
            <!-- <u-button @click="onchangeFollowed" :customStyle="{ width: '50%', marginTop: '20rpx', height: '60rpx' }"
							type="primary" shape="circle" :text="isFollowed ? '取消关注' : '关注'" :plain="true"></u-button> -->
            <view
              class="container-box-doctor-top-right-text"
              @click="onchangeFollowed"
            >
              {{ isFollowed ? '取消关注' : '关注' }}
            </view>
          </view>
        </view>
        <view
          v-for="(item, index) in docInfo.Doctor.AbstractTagsList"
          :key="item"
        >
          <view
            class="container-box-doctor-abstractTags"
            v-if="isShowMoreInfo ? true : index > 2 ? false : true"
          >
            <image
              v-if="index <= 2"
              class="container-box-doctor-abstractTags-img"
              :src="medalList[index]"
            ></image>
            <u-text
              class="container-box-doctor-abstractTags-text"
              :lines="1"
              :text="item"
            ></u-text>
          </view>
        </view>
        <u-divider customStyle="margin: 24rpx;"></u-divider>
        <view class="container-box-doctor-skilledTags">
          <view v-for="(item, index) in docInfo.Doctor.SkilledTagsList">
            <view
              class="container-box-doctor-skilledTags-item"
              v-if="isShowMoreInfo ? true : index > 3 ? false : true"
            >
              {{ item }}
            </view>
          </view>
        </view>
        <image
          v-if="
            docInfo.Doctor &&
            (docInfo.Doctor.AbstractTagsList.length > 3 ||
              docInfo.Doctor.SkilledTagsList.length > 4)
          "
          @click="isShowMoreInfo = !isShowMoreInfo"
          src="/subPackIndex/static/lookMore.png"
          class="container-box-doctor-lookMore"
          :class="isShowMoreInfo ? 'container-box-doctor-noLookMore' : ''"
        >
        </image>
      </view>
      <!-- 中间的问诊 -->
      <block v-if="option.scenType !== 'fxyg'">
        <view
          class="container-box-midbox"
          :class="
            !docInfo.IsEnable || !openTherapistConsult || !openNurseConsult
              ? 'container-box-disable'
              : ''
          "
          @click="onToConsultation"
        >
          <view class="container-box-midbox-left">
            <view class="container-box-midbox-left-top">
              <image
                src="static/user.png"
                style="width: 36rpx; height: 36rpx"
              ></image>
              <!-- {{ docInfo.countText }} -->
              <span class="container-box-midbox-left-top-text">向TA咨询</span>
            </view>
            <view class="container-box-midbox-left-top">
              <view style="width: 36rpx; height: 36rpx"></view>
              <span class="container-box-midbox-left-top-count"
                >已接诊{{ docInfo.TreatedCount }}位患者</span
              >
            </view>
          </view>
          <view class="container-box-midbox-right">
            <view style="display: flex; align-items: center">
              <view
                v-if="
                  docInfo.IsEnable && (openTherapistConsult || openNurseConsult)
                "
              >
                <span
                  class="container-box-midbox-right-befprice"
                  v-if="docInfo.ShowCost"
                  >原价¥{{ docInfo.ShowCost }}</span
                >
                <span
                  class="container-box-midbox-right-nowprice"
                  v-if="docInfo.RichTextCost > 0"
                  >限时¥{{ docInfo.RichTextCost }}</span
                >
                <span class="container-box-midbox-right-nowprice" v-else
                  >免费</span
                >
              </view>
              <view v-else>
                <span class="container-box-midbox-right-nowprice"
                  >暂未开启服务</span
                >
              </view>
              <u-icon name="arrow-right" color="#29B7A3"></u-icon>
            </view>
          </view>
        </view>
        <!-- 中间的报到 -->
        <view
          class="container-box-midbox"
          style="background: white"
          @click="onToDiseaseReport"
          v-if="option.scenType !== 'fxyg'"
        >
          <view class="container-box-midbox-left">
            <view class="container-box-midbox-left-top">
              <image
                src="static/user.png"
                style="width: 36rpx; height: 36rpx"
              ></image>
              <span class="container-box-midbox-left-top-text">向TA报到</span>
            </view>
            <view class="container-box-midbox-left-top">
              <view style="width: 36rpx; height: 36rpx"></view>
              <span class="container-box-midbox-left-top-count"
                >已报到{{ docInfo.Doctor.VisitedRegisterCount }}位患者</span
              >
            </view>
          </view>
          <view class="container-box-midbox-right">
            <u-icon name="arrow-right" color="#29B7A3"></u-icon>
          </view>
        </view>
      </block>
      <!-- 医生简介 -->
      <view class="container-box-introduction">
        <MpTitle title="简介" />
        <u-read-more
          textIndent="0"
          color="#29B7A3"
          :shadowStyle="shadowStyle"
          closeText="展开"
          ref="uReadMore1"
          showHeight="100"
        >
          <rich-text :nodes="abstract"></rich-text>
        </u-read-more>
      </view>
      <!-- 医生擅长 -->
      <view class="container-box-introduction">
        <MpTitle title="擅长" />
        <u-read-more
          textIndent="0"
          color="#29B7A3"
          :shadowStyle="shadowStyle"
          closeText="展开"
          ref="uReadMore2"
          showHeight="100"
        >
          <rich-text :nodes="skilled"></rich-text>
        </u-read-more>
      </view>
      <!-- 门诊评价 -->
      <view class="container-box-introduction">
        <MpTitle :title="'患者评价(' + evaluationInfo.totle + ')'">
          <span
            style="font-size: 32rpx; color: #29b7a3"
            @click="onToLookMoreIntroduction"
            v-if="evaluationInfo.totle > 5"
            >查看全部</span
          >
        </MpTitle>
        <!-- 标签 -->
        <view
          class="container-box-introduction-tags"
          v-if="evaluationInfo.tages.length > 0"
        >
          <view
            class="container-box-introduction-tags-each"
            v-for="(item, index) in evaluationInfo.tages"
            :key="index"
          >
            {{ item.Tag }}
          </view>
        </view>
        <!-- 评价 -->
        <view
          class="container-box-introduction-appraise"
          v-for="(item, index) in evaluationInfo.list"
          :key="index"
        >
          <!-- 患者 -->
          <view class="container-box-introduction-appraise-top">
            <span class="container-box-introduction-appraise-top-name">{{
              item.UserName || '匿名'
            }}</span>
            <span class="container-box-introduction-appraise-top-time">{{
              item.evaluationTime
            }}</span>
          </view>
          <!-- 评分 -->
          <u-rate
            :count="5"
            v-model="item.AverageScore"
            readonly
            activeColor="#FCA653"
            inactiveColor="#DCDEE0"
          ></u-rate>
          <!-- 评论 -->
          <view class="container-box-introduction-appraise-comment">{{
            item.OtherAdvice
          }}</view>
          <!-- 分割线 -->
          <u-divider></u-divider>
        </view>
      </view>
    </view>
    <!-- 底部留高 -->
    <u-gap height="30" bgColor="#f6f6f6"></u-gap>
    <view
      class="btnButtomStyle"
      @click="handleToConsultation"
      v-if="option.scenType === 'fxyg'"
    >
      咨询
    </view>
    <u-modal
      :show="showModal"
      :closeOnClickOverlay="true"
      @close="showModal = false"
      title="温馨提示"
      content="您还没有登录,请登录之后再查看"
    >
      <button type="primary" slot="confirmButton" @click="onUserLogin">
        点击登录
      </button>
    </u-modal>
    <u-modal
      :show="showModal2"
      title="提示"
      content="您还没有实名认证,请先实名认证"
      confirmText="去认证"
      cancelText="取消"
      confirmColor="#29B7A3"
      @confirm="onToAuthentication"
      @cancel="showModal2 = false"
      :showCancelButton="true"
    ></u-modal>
    <u-toast ref="uToast"></u-toast>
    <u-modal
      :show="show"
      title="提示"
      :buttonReverse="true"
      :content="showContent"
      confirmText="是"
      cancelText="否"
      @confirm="confirm"
      cancelColor="#29B7A3"
      confirmColor="#606266"
      @cancel="cancel"
      :showCancelButton="true"
    ></u-modal>
    <u-modal
      :show="show1"
      :confirmText="confirmText"
      cancelText="不同意"
      @confirm="confirm1"
      @cancel="cancel1"
      :showCancelButton="true"
      :confirmColor="timeDel == 0 ? '#29B7A3' : '#606266'"
    >
      <view
        v-html="contents"
        slot="default"
        :style="'height:' + windowHeight + 'px'"
      ></view>
    </u-modal>
    <!-- 这是现实执业认证的入口 -->
    <u-overlay :show="showOverlay" @click="showOverlay = false">
      <view class="overlaywarp">
        <!-- <image :src="showAuthenticationImage"></image> -->
        <u-swiper :list="showAuthenticationImage"></u-swiper>
      </view>
    </u-overlay>
    <view v-if="showUserDiaLog" class="overlay">
      <view class="out-wrapper">
        <view class="wrapper">
          <text class="wrapper-title">
            感谢关注！可以添加我的医生助手微信，离院后有任何问题随时联系
          </text>
          <block v-if="!userInfo.Name">
            <u--input
              shape="circle"
              placeholder="请输入就诊人姓名"
              v-model="inputUserName"
              :customStyle="{ 'margin-top': '38rpx', 'widht': '100%' }"
            ></u--input>
          </block>
          <block v-if="docInfo.Doctor.AssistantWeChatQrCode">
            <view class="wrapper-banner">
              <u--image
                :src="docInfo.Doctor.AssistantWeChatQrCode"
                width="130px"
                height="130px"
              ></u--image>
            </view>
            <text class="wrapper-qrcode"
              >长按识别图中二维码，添加医生助手微信</text
            >
          </block>
          <block v-if="!userInfo.Name">
            <view class="wrapper-btn" @click="handleSaveUserName"> 确定 </view>
          </block>
        </view>
        <img
          src="/subPackIndex/static/icon-close.png"
          style="width: 40rpx; height: 40rpx; margin-top: 62rpx"
          @click="showUserDiaLog = false"
        />
      </view>
    </view>
    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script>
const app = getApp();
import MpTitle from '@/components/mp-title/mp-title.vue';
import config from '@/config';
import { getNotice } from '@/api/other.js';
import { getDocEvaluation, getDocAvgStart } from '@/api/satisfactory.js';
import {
  isOnData,
  getDocInfoMation,
  checkUserFirstConsult,
} from '@/api/consult.js';
import { getUserCaseInfo } from '@/api/record.js';
import {
  openTherapistConsult,
  agreementTime,
  openNurseConsult,
} from '@/api/bff.js';
import { getPayOrderInfo } from '@/api/consult.js';
import {
  getDoctorOrganizationAuthentications,
  setOrCanDoc,
} from '@/api/passport.js';
import { updatePartDetail } from '@/services/userAuthen/index.js';
export default {
  components: {
    MpTitle,
  },
  data() {
    return {
      inputUserName: '',
      showUserDiaLog: false,
      shadowStyle: {
        backgroundImage:
          'linear-gradient(-180deg, rgba(255, 255, 255, 0) 0%, #F6F6F6 80%)',
        paddingTop: '100px',
        marginTop: '-100px',
      },
      option: {},
      showOverlay: false,
      showAuthenticationImage: '',
      isShowMoreAbstract: false,
      isShowMoreSkilled: false,
      medalList: [
        '/subPackIndex/static/goldMedal.png',
        '/subPackIndex/static/silverMedal.png',
        '/subPackIndex/static/bronzeMedal.png',
      ],
      isShowMoreInfo: false,
      title: '医生主页',
      share: {
        title: '',
      },
      docInfo: {},
      openTherapistConsult: true, // 治疗是是否开启问诊
      openNurseConsult: true, // 护士是否开启问诊
      evaluationInfo: {
        list: [],
        totle: 0,
        tages: [],
      }, // 评价的数据
      docAvgStart: 0, // 医生服务的平均分
      isFollowed: false,
      showModal: false, // 是否弹出登录提示
      showModal2: false, // 是否弹出认证提示
      onceInquiry: false, // 是否第一次问诊
      show: false, // 是否弹出还有在问诊的弹框
      showContent: '', // 弹出还有在问诊的弹框的提示的文案
      show1: false,
      timeDel: 0,
      TimeDel: 0,
      userHavePat: false,
      windowHeight: 0, // 设备参数
      contents: '', // 协议内容
      doctorId: '',
      confirmText: '',
      userInfo: {},
    };
  },
  computed: {
    style() {
      const style = {};
      // 状态栏高度，由于某些安卓和微信开发工具无法识别css的顶部状态栏变量，所以使用js获取的方式
      style.height = uni.$u.addUnit(uni.$u.sys().statusBarHeight, 'px');
      style.backgroundColor = this.bgColor;
      return uni.$u.deepMerge(style, uni.$u.addStyle(this.customStyle));
    },
    abstract() {
      if (this.docInfo?.Doctor?.Abstract) {
        return this.docInfo.Doctor.Abstract.replace(/\n/g, '<br/>');
      }
      return '暂无简介';
    },
    skilled() {
      if (this.docInfo?.Doctor?.Skilled) {
        return this.docInfo.Doctor.Skilled.replace(/\n/g, '<br/>');
      }
      return '暂无擅长';
    },
  },
  onPullDownRefresh() {
    this.onGetDocInfo(this.doctorId);
  },
  async onLoad(option) {
    console.log('option', option);
    this.share.title = '康复有疑问，点击来咨询';
    this.isdefalut = true;
    uni.$on('changFollowedDoc', () => {
      this.onGetDocInfo(option.docId);
    });
    // 获取医生详情、评价数据、平均分
    this.initData({
      docId: option.docId,
      share: option.share,
    });
    // 获取患者是否有病例信息
    this.onGetPatInfo();
    // 获取协议的展示时间
    this.onGetAgreementTimeInfo();
    // 获取设备信息的参数
    this.onGetSysInfo();
    // 获取协议内容
    this.onGetNot();
    const doctorId = option.docId ? option.docId : this.docInfo.UserId;
    this.doctorId = doctorId;
    this.option = option;
    this.userInfo = app.globalData.userInfo;
  },
  methods: {
    handleSaveUserName() {
      if (!this.inputUserName) {
        uni.showToast({
          title: '请输入您的名字',
          icon: 'none',
        });
        return;
      }
      uni.showLoading({
        title: '提交中...',
        mask: true,
      });
      updatePartDetail({
        UserId: app.globalData.userInfo.Id,
        Name: this.inputUserName,
      }).finally(() => {
        this.showUserDiaLog = false;
        uni.hideLoading();
      });
    },
    onCheckIsShowDialog() {
      if (!this.docInfo.Doctor.AssistantId) {
        this.showUserDiaLog = false;
      } else {
        this.showUserDiaLog = true;
      }
    },
    // 是否显示执业认证
    onIsShowPracticeCertification() {
      if (this.docInfo && this.docInfo.Doctor) {
        return this.docInfo.Doctor.RoleTypes.some((v) => v === 'doctor');
      }
    },
    // 查看执业认证数据
    async onLookAuthentication() {
      const userId = this.docInfo.Doctor.UserId;
      const res = await getDoctorOrganizationAuthentications({
        userId,
      });
      if (res.Type !== 200) return;
      const showImg = res.Data[0].UserCertificates.filter(
        (s) => s.CertificateType === 'electronicPractice_Img'
      );
      if (!showImg || !showImg.length || !showImg[0].CertificateValue) return;
      this.showAuthenticationImage = JSON.parse(showImg[0].CertificateValue);
      this.showOverlay = true;
    },
    async initData(option) {
      if (!option) {
        option = this.option;
      }
      // 获取医生详情、评价数据、平均分
      uni.showLoading({
        title: this.$loadingMsg,
      });
      await this.onGetDocInfo(option.docId, option.share);
      const res = await Promise.all([
        this.onGetDocEvaluationList(option.docId),
        this.onGetAverageStart(option.docId),
      ]);
      console.log('res', res);
      const errorList = res.filter((s) => !s.Type || s.Type !== 200);
      if (errorList.length > 0) {
        uni.showToast({
          title: errorList[0].Content,
          icon: 'none',
        });
        uni.hideLoading();
      }
      // 对评价数据和平均分进行数据处理
      this.handleDoctorData(res[0].Data, res[1].Data);
      uni.hideLoading();
    },
    handleDoctorData(r1, r2) {
      this.docAvgStart = r2.Average;
      r1.Data.forEach((e) => {
        e.evaluationTime = this.$dateFormat(e.Created, 'YYYY-MM-DD');
        e.UserName = e.UserName
          ? e.UserName[0] + '*'.replace(e.UserName.length - 1)
          : e.UserName;
      });
      this.evaluationInfo = {
        list: r1.Data,
        totle: r1.Total,
        tages: r1.TagAndScore.TagInfos.filter((v) => v.Count > 0),
      };
    },
    onLeftClick() {
      let pages = getCurrentPages();
      if (pages.length > 1) {
        uni.navigateBack();
      } else {
        uni.reLaunch({
          url: '/pages/index/index',
        });
      }
    },
    async onGetNot() {
      let res = await getNotice();
      this.contents = res;
    },
    onGetSysInfo() {
      uni.getSystemInfo({
        success: (res) => {
          this.windowHeight = res.windowHeight * 0.75;
        },
      });
    },
    async onGetAgreementTimeInfo() {
      let res = await agreementTime();
      if (res.Type == 200) {
        const itemList = res.Data.filter(
          (v) => v.Code === 'PatientReadingTime'
        );
        if (itemList.length > 0) {
          this.timeDel = itemList[0].Payload[0].Value;
          this.TimeDel = itemList[0].Payload[0].Value;
        }
      }
    },
    onToLookMoreIntroduction() {
      uni.navigateTo({
        url:
          './docEvaluation?docInfo=' +
          encodeURIComponent(JSON.stringify(this.docInfo.Doctor)),
      });
    },
    // 检查是否登录或者是否认证
    checkUserStatus() {
      if (!app.globalData.userInfo.Id) {
        // 如果没有登录
        this.showModal = true;
        return false;
      }
      if (app.globalData.userInfo.WorkflowStatus != 2) {
        //如果没有实名
        this.showModal2 = true;
        return false;
      }
      return true;
    },
    // 获取患者信息
    async onGetPatInfo() {
      let data = {
        userId: app.globalData.userInfo.Id,
        dateSerachType: 0,
        type: 0,
        pageindex: 1,
        pagesize: 10,
        IsSelfBuild: true,
      };
      let res = await getUserCaseInfo(data);
      if (res.Type == 200 && res.Data.length > 0) {
        this.userHavePat = true;
      }
    },
    async confirm1() {
      if (this.timeDel > 0) {
        return;
      }
      this.show = false;
      this.show1 = false;
      this.timeDel = this.TimeDel;
      const { isHaveDoctor, isHaveTherapist, isHaveNurse } =
        this.getDoctorRole();
      uni.showLoading({
        title: this.$loadingMsg,
        mask: true,
      });
      if (isHaveDoctor) {
        //医生
        if (this.onceInquiry) {
          uni.navigateTo({
            url:
              './healthInformation?doctorId=' +
              this.docInfo.Doctor.UserId +
              '&show1=' +
              this.userHavePat +
              '&orgId=' +
              this.docInfo.Doctor.OrganizationId,
            complete: () => {
              uni.hideLoading();
            },
          });
          return;
        }
        uni.navigateTo({
          url:
            '/subPrescription/InterviewDescription?doctorId=' +
            this.docInfo.Doctor.UserId +
            '&show1=' +
            this.userHavePat,
          complete: () => {
            uni.hideLoading();
          },
        });
      } else {
        getApp().subscribeMessage(async () => {
          this.ConsultWayList;
          let query = {
            UserId: app.globalData.userInfo.Id,
            UserName: app.globalData.userInfo.Name,
            Sex: app.globalData.userInfo.Sex,
            DocUserId: this.docInfo.Doctor.UserId,
            Organization: this.docInfo.Doctor.OrganizationId,
            OrganizationName: this.docInfo.Doctor.OrganizationName,
            DepartmentId: this.docInfo.Doctor.DepartmentId,
            DepartmentName: this.docInfo.Doctor.DepartmentName,
            CostState: 1,
            PayAlias: 'com.kangfx.wx.mp.patient' + `-${config.resources}`,
            ConsultWay: this.getDocConsultWay(), // 1 问诊  2 咨询
            Source: 1,
            CreatorId: app.globalData.userInfo.Id,
            AutoCreateMedical: false, // 医生就是true 治疗师就是false
          };
          let res = await getPayOrderInfo(query);
          if (res.Type == 200 && res.Data.Amount == 0) {
            // 不需要支付费用
            let consultId = res.Data.ConsultId;
            let backPath = '/pages/interview/index';
            uni.navigateTo({
              url:
                '/subPackChat/sessionChatPage?consultId=' +
                consultId +
                '&backPath=' +
                backPath +
                '&fromAddInquiry=true',
              complete: () => {
                uni.hideLoading();
              },
            });
          } else if (res.Type == 200 && res.Data.Amount > 0) {
            // 需要支付费用
            uni.navigateTo({
              url: `/subPrescription/cashier?orderId=${res.Data.OrderNo}&docName=${this.docInfo.Doctor.Name}&needMoeny=${res.Data.Amount}&consultId=${res.Data.ConsultId}&PaymentId=${res.Data.Payment.Id}`,
              complete: () => {
                uni.hideLoading();
              },
            });
          } else {
            this.$refs.uToast.show({
              message: res.Content,
              type: 'error',
            });
            uni.hideLoading();
          }
        });
      }
      let data1 = {
        UserId: app.globalData.userInfo.Id,
        FollowUserId: this.docInfo.Doctor.UserId,
      };
      await setOrCanDoc('setfollow', data1);
      this.isFollowed = true;
      const orgId = this.docInfo.Doctor.OrganizationId,
        orgName = this.docInfo.Doctor.OrganizationName;
      await getApp().changeOrgAndMark({
        orgId,
        orgName,
      });
    },
    cancel1() {
      this.show1 = false;
      this.timeDel = this.TimeDel;
    },
    cancel() {
      this.show = false;
      this.showModal = false;
      uni.reLaunch({
        url: `/pages/interview/index`,
        success: (res) => {},
      });
    },
    getDocConsultWay() {
      const { isHaveDoctor, isHaveTherapist, isHaveNurse } =
        this.getDoctorRole();
      let ConsultWay = null;
      if (isHaveDoctor) {
        ConsultWay = 1;
      } else if (isHaveTherapist) {
        ConsultWay = 2;
      } else if (isHaveNurse) {
        ConsultWay = 3;
      }
      return ConsultWay;
    },
    // 判断当前是什么角色
    getDoctorRole() {
      const RoleTypes = this.docInfo.Doctor.RoleTypes;
      const isHaveDoctor = RoleTypes.some((v) => v === 'doctor');
      const isHaveTherapist = RoleTypes.some((v) => v === 'therapist');
      const isHaveNurse = RoleTypes.some((v) => v === 'nurse');
      return {
        isHaveDoctor,
        isHaveTherapist,
        isHaveNurse,
      };
    },
    confirm() {
      const { isHaveDoctor, isHaveTherapist, isHaveNurse } =
        this.getDoctorRole();
      if (!isHaveDoctor) {
        this.timeDel = 0;
        this.confirm1();
        return;
      }
      this.show = false;
      this.show1 = true;
      if (!this.timeDel || this.timeDel == 0) {
        this.confirmText = '同意';
      } else {
        this.confirmText = '同意' + `(${this.timeDel}s)`;
        let setIntervalTime = setInterval(() => {
          this.timeDel--;
          this.confirmText = '同意' + `(${this.timeDel}s)`;
          if (this.timeDel == 0) {
            this.confirmText = '同意';
            clearInterval(setIntervalTime);
          }
        }, 1000);
      }
    },
    // 风险预估的咨询
    async handleToConsultation() {
      uni.showLoading({
        title: '加载中',
        mask: true,
      });
      getApp().subscribeMessage(async () => {
        let query = {
          UserId: app.globalData.userInfo.Id,
          UserName: app.globalData.userInfo.Name,
          Sex: app.globalData.userInfo.Sex,
          DocUserId: this.docInfo.Doctor.UserId,
          Organization: this.docInfo.Doctor.OrganizationId,
          OrganizationName: this.docInfo.Doctor.OrganizationName,
          DepartmentId: this.docInfo.Doctor.DepartmentId,
          DepartmentName: this.docInfo.Doctor.DepartmentName,
          CostState: 1,
          PayAlias: 'com.kangfx.wx.mp.patient' + `-${config.resources}`,
          ConsultWay: this.getDocConsultWay(), // 1 问诊  2 咨询
          Source: 1,
          CreatorId: app.globalData.userInfo.Id,
          AutoCreateMedical: true, // 医生就是true 治疗师就是false
          Describing: '',
          OfflineDate: this.$dateFormat(
            new Date(),
            'YYYY-MM-DD HH:mm:ss',
            false
          ),
          HospitName: this.docInfo.Doctor.OrganizationName,
          ConsultReportInputs: [],
          Extend: {
            Extend: JSON.stringify({
              Type: 'HxUnion',
              Data: this.option.reportId,
            }),
          },
          IsValidateDct: false,
        };
        let res = await getPayOrderInfo(query);
        if (res.Type == 200 && res.Data.Amount == 0) {
          // 不需要支付费用
          let consultId = res.Data.ConsultId;
          let backPath = '/pages/interview/index';
          uni.navigateTo({
            url:
              '/subPackChat/sessionChatPage?consultId=' +
              consultId +
              '&backPath=' +
              backPath +
              '&fromAddInquiry=true',
            complete: () => {
              uni.hideLoading();
            },
          });
        } else if (res.Type == 200 && res.Data.Amount > 0) {
          // 需要支付费用
          uni.navigateTo({
            url: `/subPrescription/cashier?orderId=${res.Data.OrderNo}&docName=${this.docInfo.Doctor.Name}&needMoeny=${res.Data.Amount}&consultId=${res.Data.ConsultId}&PaymentId=${res.Data.Payment.Id}`,
            complete: () => {
              uni.hideLoading();
            },
          });
        } else {
          this.$refs.uToast.show({
            message: res.Content,
            type: 'error',
          });
          uni.hideLoading();
        }
        let data1 = {
          UserId: app.globalData.userInfo.Id,
          FollowUserId: this.docInfo.Doctor.UserId,
        };
        await setOrCanDoc('setfollow', data1);
        const orgId = this.docInfo.Doctor.OrganizationId,
          orgName = this.docInfo.Doctor.OrganizationName;
        await getApp().changeOrgAndMark({
          orgId,
          orgName,
        });
      });
    },
    // 点击问诊
    async onToConsultation() {
      if (!app.globalData.userInfo.Id) {
        // 如果没有登录
        this.showModal = true;
        return;
      }
      if (!this.openNurseConsult || !this.openTherapistConsult) {
        // 如果院区没有开启问诊
        this.$refs.uToast.show({
          message: '未开启咨询服务',
          type: 'error',
        });
        return;
      }
      if (!this.docInfo.IsEnable) {
        // 如果医生没有开问诊
        const { isHaveDoctor, isHaveTherapist, isHaveNurse } =
          this.getDoctorRole();
        let message = '';
        if (isHaveDoctor) {
          message = '医生未开启咨询服务';
        } else if (isHaveTherapist) {
          message = '治疗师未开启咨询服务';
        } else if (isHaveNurse) {
          message = '护士未开启咨询服务';
        }
        this.$refs.uToast.show({
          message,
          type: 'error',
        });
        return;
      }
      if (this.option.scenType !== 'fxyg') {
        if (app.globalData.userInfo.WorkflowStatus != 2) {
          //如果没有实名
          this.showModal2 = true;
          return;
        }
        uni.showLoading({
          title: '加载中',
          mask: true,
        });
        // 判断是否第一次进行问诊
        let resData = await checkUserFirstConsult({
          userId: app.globalData.userInfo.Id,
        });
        if (resData.Type === 200) {
          this.onceInquiry = resData.Data;
        }
        // 判断是否有问诊中的数据
        let res = await isOnData();
        uni.hideLoading();
        if (res.Type == 200) {
          if (res.Data.Consult) {
            this.showContent = '当前已有进行中的咨询,' + this.onGetHaveText();
            this.show = true;
          } else if (res.Data.Advisory) {
            this.showContent = '当前已有进行中的咨询,' + this.onGetHaveText();
            this.show = true;
          } else if (res.Data.Nurse) {
            this.showContent = '当前已有进行中的咨询,' + this.onGetHaveText();
            this.show = true;
          } else {
            this.confirm();
          }
        } else {
          this.confirm();
        }
      } else {
        uni.showLoading({
          title: '加载中',
          mask: true,
        });
        getApp().subscribeMessage(async () => {
          this.ConsultWayList;
          let query = {
            UserId: app.globalData.userInfo.Id,
            UserName: app.globalData.userInfo.Name,
            Sex: app.globalData.userInfo.Sex,
            DocUserId: this.docInfo.Doctor.UserId,
            Organization: this.docInfo.Doctor.OrganizationId,
            OrganizationName: this.docInfo.Doctor.OrganizationName,
            DepartmentId: this.docInfo.Doctor.DepartmentId,
            DepartmentName: this.docInfo.Doctor.DepartmentName,
            CostState: 1,
            PayAlias: 'com.kangfx.wx.mp.patient' + `-${config.resources}`,
            ConsultWay: this.getDocConsultWay(), // 1 问诊  2 咨询
            Source: 1,
            CreatorId: app.globalData.userInfo.Id,
            AutoCreateMedical: true, // 医生就是true 治疗师就是false
            Describing: '',
            OfflineDate: this.$dateFormat(
              new Date(),
              'YYYY-MM-DD HH:mm:ss',
              false
            ),
            HospitName: this.docInfo.Doctor.OrganizationName,
            ConsultReportInputs: [],
            Extend: {
              Extend: JSON.stringify({
                Type: 'HxUnion',
                Data: this.option.reportId,
              }),
            },
          };
          let res = await getPayOrderInfo(query);
          if (res.Type == 200 && res.Data.Amount == 0) {
            // 不需要支付费用
            let consultId = res.Data.ConsultId;
            let backPath = '/pages/interview/index';
            uni.navigateTo({
              url:
                '/subPackChat/sessionChatPage?consultId=' +
                consultId +
                '&backPath=' +
                backPath +
                '&fromAddInquiry=true',
              complete: () => {
                uni.hideLoading();
              },
            });
          } else if (res.Type == 200 && res.Data.Amount > 0) {
            // 需要支付费用
            uni.navigateTo({
              url: `/subPrescription/cashier?orderId=${res.Data.OrderNo}&docName=${this.docInfo.Doctor.Name}&needMoeny=${res.Data.Amount}&consultId=${res.Data.ConsultId}&PaymentId=${res.Data.Payment.Id}`,
              complete: () => {
                uni.hideLoading();
              },
            });
          } else {
            this.$refs.uToast.show({
              message: res.Content,
              type: 'error',
            });
            uni.hideLoading();
          }
          let data1 = {
            UserId: app.globalData.userInfo.Id,
            FollowUserId: this.docInfo.Doctor.UserId,
          };
          await setOrCanDoc('setfollow', data1);
          const orgId = this.docInfo.Doctor.OrganizationId,
            orgName = this.docInfo.Doctor.OrganizationName;
          await getApp().changeOrgAndMark({
            orgId,
            orgName,
          });
        });
      }
    },
    onGetHaveText() {
      const { isHaveDoctor, isHaveTherapist, isHaveNurse } =
        this.getDoctorRole();
      if (isHaveDoctor) {
        return '是否确认发起新的咨询？';
      } else if (isHaveTherapist) {
        return '是否确认发起新的咨询？';
      } else if (isHaveNurse) {
        return '是否确认发起新的咨询？';
      }
      return '';
    },
    // 前往认证
    onToAuthentication() {
      this.showModal2 = false;
      uni.navigateTo({
        url: '/subPackIndex/user/userUpdate?type=smxx',
      });
    },
    // 前往登录
    onUserLogin() {
      getApp().openLoginPage();
    },
    // 登录之后重新获取数据
    rest() {
      this.showModal = false;
      this.onGetDocInfo(this.doctorId);
    },
    // 前往症候报到
    onToDiseaseReport() {
      if (!app.globalData.userInfo.Id) {
        // 如果没有登录
        this.showModal = true;
        return;
      }
      if (app.globalData.userInfo.WorkflowStatus != 2) {
        //如果没有实名
        this.showModal2 = true;
        return;
      }
      const docInfo = encodeURIComponent(JSON.stringify(this.docInfo.Doctor));
      uni.navigateTo({
        url: '/subPackIndex/user/postDiagnosisReport?docInfo=' + docInfo,
      });
    },
    // 取消或者关注医生
    async onchangeFollowed() {
      if (!app.globalData.userInfo.Id) {
        // 如果没有登录
        this.showModal = true;
        return;
      }
      // if (app.globalData.userInfo.WorkflowStatus != 2) { //如果没有实名
      // 	this.showModal2 = true
      // 	return
      // }
      let type = this.isFollowed ? 'cancelfollow' : 'setfollow';
      let data = {
        UserId: app.globalData.userInfo.Id,
        FollowUserId: this.docInfo.Doctor.UserId,
      };
      let res = await setOrCanDoc(type, data);
      if (res.Type == 200) {
        if (type == 'cancelfollow') {
          this.$refs.uToast.show({
            message: '取消关注成功',
            type: 'success',
          });
        } else {
          this.$refs.uToast.show({
            message: '关注成功',
            type: 'success',
          });
        }
        this.isFollowed = type == 'cancelfollow' ? false : true;
        let pages = getCurrentPages();
        const prePage = pages[pages.length - 2]; //上一个页面
        if (prePage.$vm.rest) {
          prePage.$vm.rest();
        }
      } else {
        this.$refs.uToast.show({
          message: res.Content,
          type: 'error',
        });
      }
    },
    onGetAverageStart(doctorId) {
      return getDocAvgStart({
        dctId: doctorId,
      });
    },
    onGetDocEvaluationList(doctorId) {
      return getDocEvaluation({
        DoctorId: doctorId,
        PageIndex: 1,
        PageSize: 5,
        Score: [],
        Type: 2,
        Tags: [],
        OrgId: this.docInfo.Doctor.OrganizationId,
      });
    },
    async setTitleAndConsult(
      res,
      title,
      consultFunction,
      consultCode,
      consultProperty
    ) {
      this.title = title;
      const res1 = await consultFunction(res.Data.Doctor.OrganizationId);
      if (res1.Type === 200 && res1.Data.length > 0) {
        const consult = res1.Data.find((v) => v.Code === consultCode);
        if (consult) {
          this[consultProperty] = consult.Payload[0]?.Value;
        }
      }
    },
    async onGetDocInfo(doctorId, share) {
      let res = await getDocInfoMation({
        doctorId,
      });
      if (res.Type !== 200) {
        uni.showModal({
          cancelText: '返回',
          showCancel: false,
          title: res.Content,
          success: () => {
            uni.navigateBack();
          },
        });
        return;
      }
      uni.stopPullDownRefresh();
      res.Data.Doctor.AbstractTagsList = res.Data.Doctor.AbstractTags
        ? JSON.parse(res.Data.Doctor.AbstractTags)
        : [];
      res.Data.Doctor.SkilledTagsList = res.Data.Doctor.SkilledTags
        ? JSON.parse(res.Data.Doctor.SkilledTags)
        : [];
      this.docInfo = res.Data;
      let countText = '咨询';
      if (res.Data.Doctor.RoleTypes.includes('therapist')) {
        countText = '咨询';
        this.setTitleAndConsult(
          res,
          '治疗师主页',
          openTherapistConsult,
          'TherapistConsult',
          'openTherapistConsult'
        );
      } else if (res.Data.Doctor.RoleTypes.includes('nurse')) {
        countText = '咨询';
        this.setTitleAndConsult(
          res,
          '护士主页',
          openNurseConsult,
          'NurseConsult',
          'openNurseConsult'
        );
      }
      this.docInfo.countText = countText;
      // 该患者是否关注了医生
      this.isFollowed = res.Data.Doctor.Followed;
      if (share) {
        uni.setStorageSync('shareObj', {
          RelationId: doctorId,
          OrganizationId: res.Data.Doctor.OrganizationId,
          Type: 4,
        });
      }
      this.$nextTick(() => {
        this.$refs.uReadMore1.init();
        this.$refs.uReadMore2.init();
      });
      // 判断是否可以显示弹框
      if (this.option.scene && this.option.scene === 'sqm') {
        this.onCheckIsShowDialog();
      }
    },
  },
};
</script>

<style scoped lang="scss">
@font-face {
  font-family: 'HuXiaoBo';
  src: url('@/fonts/HuXiaoBo.ttf');
}

.overlaywarp {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;

  /deep/ .u-swiper {
    width: 100% !important;
    height: 80% !important;
  }

  /deep/ .u-swiper__wrapper {
    height: 100% !important;
  }

  /deep/ .u-swiper__wrapper__item__wrapper__image {
    height: 100% !important;
  }
}
/deep/ .u-input {
  flex: 0 !important;
}
.overlay {
  position: fixed;
  top: 0;
  width: 100vw;
  height: 100vh;
  border: 0;
  right: 0;
  left: 0;
  z-index: 9999;
  background-color: rgba($color: #000000, $alpha: 0.7);
  display: flex;
}
.out-wrapper {
  margin: auto;
  width: 580rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.wrapper {
  position: relative;
  margin: auto;
  background: linear-gradient(180deg, #e2fffc 0%, #f8fffe 24%, #ffffff 66%);
  box-shadow: 0rpx -2rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);
  border-radius: 24rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 60rpx 54rpx;
  &-title {
    font-weight: 500;
    font-size: 30rpx;
    color: #181818;
  }
  &-banner {
    margin-top: 48rpx;
    width: 322rpx;
    height: 314rpx;
    background-image: url('https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/static/frontend/mp-patient/index-overlay-banner.png');
    background-size: cover;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-top: 30rpx;
  }
  &-qrcode {
    font-size: 26rpx;
    color: #999999;
  }
  &-btn {
    width: 300rpx;
    height: 64rpx;
    background: #29b7a3;
    border-radius: 32rpx;
    line-height: 64rpx;
    text-align: center;
    color: white;
    margin-top: 40rpx;
  }
}

.container {
  background-image: url('static/bg.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
  position: relative;
  width: 100%;

  /deep/ .u-modal__content {
    overflow-y: auto;
    text-align: left !important;
  }

  &-box {
    width: 90%;
    margin: 40rpx auto;

    &-disable {
      filter: grayscale(100%);
      -webkit-filter: grayscale(100%);
      -moz-filter: grayscale(100%);
      -ms-filter: grayscale(100%);
      -o-filter: grayscale(100%);
      filter: progid:DXImageTransform.Microsoft.BasicImage(grayscale=1);
      -webkit-filter: grayscale(1);
    }

    &-doctor {
      width: 100%;
      box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);
      border-radius: 24rpx;
      background: white;
      padding: 36rpx;
      display: flex;
      flex-direction: column;

      &-top {
        margin-bottom: 16rpx;
        display: flex;
        justify-content: space-between;

        &-right {
          width: 256rpx;
          display: flex;
          flex-direction: column;
          align-items: center;
          margin-top: 4%;

          &-text {
            padding: 6rpx 24rpx;
            text-align: center;
            color: #29b7a3;
            font-weight: 400;
            font-size: 24rpx;
            box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);
            border-radius: 40rpx;
            border: 2rpx solid #29b7a3;
            margin-top: 20rpx;
          }
        }

        &-left {
          padding-top: 4%;
          flex: 1;
          position: relative;

          &-experience {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            margin-top: 20rpx;

            &-each {
              text-align: center;
              height: 42rpx;
              line-height: 42rpx;
              background: rgba(27, 188, 156, 0.1);
              border-radius: 8rpx;
              font-size: 24rpx;
              font-weight: 400;
              color: #29b7a3;
              margin-right: 16rpx;
              margin-bottom: 16rpx;
              padding: 0 10rpx;
            }
          }

          &-workbox1 {
            margin-top: 16rpx;

            &-text {
              font-size: 28rpx;
              font-weight: 600;
              color: #333333;
            }

            &-text1 {
              font-size: 28rpx;
              font-weight: 400;
              color: #999999;
              margin-left: 20rpx;
            }

            &-text2 {
              font-size: 24rpx;
              font-weight: 600;
              color: #999999;
            }

            &-count {
              font-size: 36rpx;
              font-family: DINAlternate, DINAlternate;
              font-weight: bold;
              color: #b76f29;
              margin-left: 8rpx;
            }
          }

          &-workbox {
            margin-top: 24rpx;
            display: flex;
            align-items: center;

            &-worktitle {
              font-size: 32rpx;
              font-weight: 600;
              color: #000000;
            }

            &-recommend {
              background: rgba(183, 111, 41, 0.1);
              border-radius: 8rpx;
              text-align: center;
              padding: 4rpx;
              margin-left: 20rpx;
              font-size: 24rpx;
              color: #b76f29;
              display: flex;
              align-items: center;
            }
          }

          &-img {
            width: 114rpx;
            height: 138rpx;
            position: absolute;
            z-index: 0;
            top: 0;
            left: 0;
          }

          &-box {
            z-index: 1;
            margin-top: 2%;
            display: flex;
            align-items: center;

            &-name {
              font-size: 56rpx;
              font-weight: 600;
              color: #000000;
            }

            &-recommend {
              background: rgba(183, 111, 41, 0.1);
              border-radius: 8rpx;
              text-align: center;
              padding: 4rpx;
              margin-left: 20rpx;
              font-size: 24rpx;
              color: #b76f29;
              display: flex;
              align-items: center;
            }
          }
        }
      }

      &-abstractTags {
        // padding: 0 18rpx;
        width: 100%;
        height: 54rpx;
        // background: rgba(189,166,102,0.16);
        border-radius: 8rpx;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        font-weight: 600;
        font-size: 24rpx;
        margin-bottom: 16rpx;

        &-img {
          width: 50rpx;
          height: 54rpx;
          background: rgba(189, 166, 102, 0.16);
        }

        /deep/ .u-text__value {
          font-weight: 600 !important;
          font-size: 24rpx !important;
          color: #9e7e49 !important;
          height: 54rpx;
          line-height: 54rpx;
          padding: 0 18rpx 0 10rpx;
          background: rgba(189, 166, 102, 0.16);
        }
      }

      &-skilledTags {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        flex-wrap: wrap;

        &-item {
          margin-right: 16rpx;
          margin-bottom: 8rpx;
          padding: 4rpx 16rpx;
          background: rgba(27, 188, 156, 0.1);
          border-radius: 8rpx;
          font-weight: 600;
          font-size: 24rpx;
          color: #29b7a3;
          text-align: center;
        }
      }

      &-lookMore {
        width: 40rpx;
        height: 40rpx;
        margin: auto;
      }

      &-noLookMore {
        transform: rotate(180deg) !important;
      }

      &-bom {
        position: relative;

        // &-img {
        // 	position: absolute;
        // 	top: 0;
        // 	left: 0;
        // 	width: 72rpx;
        // 	height: 36rpx;
        // }
        &-goodAt {
          font-size: 36rpx;
          font-family: HuXiaoBo;
          font-weight: normal;
          color: #29b7a3;
          margin-right: 20rpx;
        }

        &-text {
          font-size: 28rpx;
          font-weight: 400;
          color: #676767;
          // margin-left: 92rpx;
        }
      }
    }

    &-midbox {
      width: 100%;
      background: linear-gradient(
        270deg,
        #1bbc9c -100%,
        #ffffff 30%,
        #ffffff 0%
      );
      box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);
      border-radius: 24rpx;
      padding: 32rpx;
      margin: 32rpx 0;
      display: flex;
      justify-content: space-between;
      align-items: center;

      &-left {
        &-top {
          display: flex;
          align-items: center;

          &-text {
            margin-left: 20rpx;
            font-weight: 600;
            font-size: 28rpx;
            color: #29b7a3;
            font-style: normal;
          }

          &-count {
            font-weight: 400;
            font-size: 24rpx;
            color: #666666;
            font-style: normal;
            margin-left: 20rpx;
          }
        }
      }

      &-right {
        text-align: right;

        &-befprice {
          font-weight: 400;
          font-size: 24rpx;
          color: #cdcdcd;
          font-style: normal;
          text-decoration-line: line-through;
        }

        &-nowprice {
          font-weight: 600;
          font-size: 32rpx;
          color: #29b7a3;
          text-align: center;
          font-style: normal;
          margin: 0 8rpx 0 10rpx;
        }
      }
    }

    &-introduction {
      width: 100%;
      margin-bottom: 32rpx;

      /deep/ .u-text__value {
        // font-weight: 600 !important;
        // font-size: 28rpx !important;
        // color: #333333 !important;
        // line-height: 48rpx !important;
        // font-style: normal !important;
        // margin-top: 24rpx !important;
      }

      &-tags {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        margin-top: 24rpx;

        &-each {
          padding: 16rpx;
          text-align: center;
          background: rgba(27, 188, 156, 0.1);
          border-radius: 8rpx;
          margin: 0 24rpx 24rpx 0;
          font-weight: 400;
          font-size: 24rpx;
          color: #29b7a3;
          font-style: normal;
        }
      }

      &-appraise {
        width: 100%;
        margin: 20rpx 0;

        &-comment {
          margin-top: 20rpx;
          margin-bottom: 34rpx;
          font-weight: 600;
          font-size: 28rpx;
          color: #333333;
          font-style: normal;
        }

        &-top {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 16rpx;

          &-name {
            font-weight: 600;
            font-size: 28rpx;
            color: #989898;
            font-style: normal;
          }

          &-time {
            font-weight: 600;
            font-size: 24rpx;
            color: #989898;
            font-style: normal;
          }
        }
      }
    }
  }
}
</style>
