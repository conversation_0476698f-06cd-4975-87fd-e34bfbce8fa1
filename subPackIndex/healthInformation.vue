<template>
  <div class="container">
    <u-cell :border="false" customStyle="borderRadius:16rpx">
      <u-avatar :src="userInfo.HeadImg" slot="icon"></u-avatar>
      <span slot="title"
        ><span style="font-size: 32rpx; margin-left: 24rpx">{{
          userInfo.Name
        }}</span>
        <span style="margin-left: 16rpx; color: #333333; font-size: 24rpx"
          >{{ userInfo.Age || '' }}岁</span
        >
        <span style="margin-left: 16rpx; font-size: 24rpx; color: #333333">{{
          userInfo.Sex
        }}</span></span
      >
    </u-cell>
    <view style="height: 10upx"></view>

    <view class="container-textarea">
      <p
        style="
          font-size: 15px;
          color: #303133;
          margin-bottom: 4px;
          padding: 0 9px;
        "
      >
        既往史
      </p>
      <view class="u-page__tag-item" style="padding: 0 9px">
        <u-tag
          :text="item.Key"
          :plain="!item.checked"
          type="primary"
          :name="item.Id"
          @click="JWScheckboxClick(item)"
          v-for="(item, index) in JWSList"
          :key="item.Id"
        >
        </u-tag>
      </view>
      <u-textarea
        v-model="PreviousHistoryText"
        count
        :maxlength="200"
        :height="140"
        placeholder="请填写您过往所患疾病名称"
      >
      </u-textarea>
    </view>

    <view style="height: 10upx"></view>

    <view class="container-textarea">
      <p
        style="
          font-size: 15px;
          color: #303133;
          margin-bottom: 4px;
          padding: 0 9px;
        "
      >
        过敏史
      </p>
      <view class="u-page__tag-item" style="padding: 0 9px">
        <u-tag
          :text="item.Key"
          :plain="!item.checked"
          type="primary"
          :name="item.Id"
          @click="GMScheckboxClick(item)"
          v-for="(item, index) in GMSList"
          :key="item.Id"
        >
        </u-tag>
      </view>
      <u-textarea
        v-model="AllergicHistoryText"
        count
        :maxlength="200"
        :height="140"
        placeholder="请填写过敏药物"
      >
      </u-textarea>
    </view>
    <u-button
      type="primary"
      text="下一步"
      @click="onNext"
      shape="circle"
      customStyle="width:90%;bottom: 30px;position: fixed; left: 50%;transform: translateX(-50%);fontSize:16px;z-index:99"
    >
    </u-button>
  </div>
</template>

<script>
const app = getApp();
import CiTiao from '@/mixin/CiTiao.js';
import { getUserArchivesInfo, updateArchives } from '@/api/record.js';
export default {
  mixins: [CiTiao],
  data() {
    return {
      doctorId: null,
      userHavePat: null,
      userInfo: {},
      JWSList: [],
      GMSList: [],
      orgId: '',
      PreviousHistoryText: '',
      AllergicHistoryText: '',
      dataInfo: {},
    };
  },
  onLoad({ doctorId, show1, orgId }) {
    this.doctorId = doctorId;
    this.userHavePat = show1;
    this.userInfo = app.globalData.userInfo;
    this.orgId = orgId;
    this.getTages(this.getInfo);
  },
  methods: {
    JWScheckboxClick({ Id }) {
      this.JWSList.map((v) => {
        if (v.Id === Id) {
          v.checked = !v.checked;
        }
      });
    },
    GMScheckboxClick({ Id }) {
      this.GMSList.map((v) => {
        if (v.Id === Id) {
          v.checked = !v.checked;
        }
      });
    },
    async getInfo() {
      let data = {
        PageCondition: {
          PageIndex: 1,
          PageSize: 10,
        },
        FilterGroup: {
          Rules: [
            {
              Field: 'UserId',
              Value: app.globalData.userInfo.Id,
              Operate: 3,
            },
          ],
        },
      };
      let res = await getUserArchivesInfo(data);
      if (res.Type === 200) {
        if (!res.Data.Rows.length) {
          res.Data.Rows[0] = {};
        }
        if (!res.Data.Rows[0].PreviousHistory) {
          // 既往使
          res.Data.Rows[0].PreviousHistory = '';
        } else {
          res.Data.Rows[0].PreviousHistory = JSON.parse(
            res.Data.Rows[0].PreviousHistory
          );
          this.PreviousHistoryText = res.Data.Rows[0].PreviousHistory.Text;
          const findData = res.Data.Rows[0].PreviousHistory.Tags;
          findData.forEach((s) => {
            this.JWSList.forEach((k) => {
              if (k.Key === s) {
                k.checked = true;
              }
            });
          });
        }
        if (!res.Data.Rows.length || !res.Data.Rows[0].AllergicHistory) {
          // 过敏史
          res.Data.Rows[0].AllergicHistory = '';
        } else {
          res.Data.Rows[0].AllergicHistory = JSON.parse(
            res.Data.Rows[0].AllergicHistory
          );
          this.AllergicHistoryText = res.Data.Rows[0].AllergicHistory.Text;
          const findData = res.Data.Rows[0].AllergicHistory.Tags;
          findData.forEach((s) => {
            this.GMSList.forEach((k) => {
              if (k.Key === s) {
                k.checked = true;
              }
            });
          });
        }

        this.dataInfo = res.Data.Rows[0];
      }
    },
    async onNext() {
      const dataCopy = JSON.parse(JSON.stringify(this.dataInfo));
      const jwsTags = {
        Tags: [],
        Text: '',
      };
      const jwsChoose = this.JWSList.filter((o) => o.checked);
      if (jwsChoose.length > 0) {
        jwsChoose.forEach((m) => {
          jwsTags.Tags.push(m.Key);
        });
      }
      jwsTags.Text = this.PreviousHistoryText;
      const gmsTags = {
        Tags: [],
        Text: '',
      };
      const gmsChoose = this.GMSList.filter((o) => o.checked);
      if (gmsChoose.length > 0) {
        gmsChoose.forEach((m) => {
          gmsTags.Tags.push(m.Key);
        });
      }
      gmsTags.Text = this.AllergicHistoryText;

      dataCopy.AllergicHistory = JSON.stringify(gmsTags);
      dataCopy.PreviousHistory = JSON.stringify(jwsTags);
      if (!dataCopy.Id) {
        dataCopy.UserId = app.globalData.userInfo.Id;
      }
      const reqData = [dataCopy];
      console.log('reqData', reqData);
      updateArchives(dataCopy.Id ? 'Update' : 'Create', reqData);
      uni.redirectTo({
        url:
          '/subPrescription/InterviewDescription?doctorId=' +
          this.doctorId +
          '&show1=' +
          this.userHavePat,
      });
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  height: 100vh;
  background-color: #f7f7f7;

  /deep/ .hideHhowPlace {
    color: red;
  }

  /deep/ .u-cell {
    background-color: #fff !important;
  }

  /deep/ .u-upload__button {
    background-color: #fafafa !important;
  }

  /deep/ .u-upload {
    border-radius: 0 0 16rpx 16rpx !important;
  }

  /deep/ .u-cell__title {
    font-size: 16px;
  }

  /deep/ .u-line {
    border: none !important;
  }

  /deep/ .u-border {
    border-style: none !important;
    border-color: #fafafa !important;
  }

  /deep/ .u-textarea__field {
    background-color: #fafafa !important;
    padding: 10px;
  }

  /deep/ .u-cell__body__content {
    flex: none !important;
  }

  /deep/ ._span {
    flex: 1 !important;
    text-align: right;
  }

  .u-page__tag-item {
    display: flex;
    flex-wrap: wrap;

    .u-transition {
      margin-right: 20rpx;
      margin-bottom: 10rpx;
    }

    /deep/ .u-fade-enter-active {
      margin-right: 10rpx;
      margin-bottom: 10rpx;
    }
  }
}
</style>
