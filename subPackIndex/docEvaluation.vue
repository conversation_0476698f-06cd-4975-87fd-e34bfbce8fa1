<template>
  <view class="container">
    <!-- 顶部的评价 -->
    <u-sticky bgColor="#fff">
      <u-tabs :list="list1" :scrollable="false" @click="tabsClick"></u-tabs>
    </u-sticky>

    <!-- 下方的评价类型 -->
    <scroll-view
      scroll-x="true"
      class="display-style1 scroll-view_H"
      v-if="showItemList"
    >
      <view
        id="demo1"
        class="scroll-view-item_H uni-bg-red"
        v-for="(item, index) in list"
        :key="index"
        @click="chooseItem(item, index)"
        :class="chooseIndex === index ? 'scroll-view-item_H-first' : ''"
        :style="index === 0 ? 'display:none' : ''"
      >
        <p
          style="overflow: hidden; white-space: nowrap; text-overflow: ellipsis"
        >
          {{ item.Tag }}
        </p>
        <p>({{ item.Count }})</p>
      </view>
    </scroll-view>

    <!-- 列表 -->
    <template v-if="Evaluation.length > 0">
      <view
        v-for="(item, index) in Evaluation"
        :key="index"
        class="container-evaluation-each"
      >
        <view class="display-style1" style="align-items: flex-start">
          <!-- <u--image :showLoading="true" :src="item.HeadImg" width="60px" height="60px" shape="circle">
					</u--image> -->
          <view class="container-evaluation-each-right">
            <view class="container-evaluation-each-right-top display-style">
              <p>
                <span style="font-weight: 600; font-size: 16px"
                  >{{ item.UserName || '匿名' }}
                </span>
              </p>
              <u-rate
                :count="5"
                activeColor="#29B7A3"
                v-model="item.AverageScore"
                :readonly="true"
              >
              </u-rate>
            </view>
            <span style="font-size: 16px">{{ item.evaluationTime }}</span>
            <view
              class="container-evaluation-each-right-bom"
              :class="
                lookMoreIndex == index
                  ? 'container-evaluation-each-right-lookMoreInfo'
                  : ''
              "
              style="margin-top: 10rpx"
              @click="lookMore(index)"
            >
              {{ item.OtherAdvice }}
            </view>
          </view>
        </view>
        <view class="container-evaluation-list">
          <view
            class="container-evaluation-list-each"
            v-for="(o, index2) in item.SatisfactionTags"
            :key="index2"
          >
            {{ o.Tag }}
          </view>
        </view>
      </view>
    </template>
    <u-empty
      mode="data"
      icon="http://cdn.uviewui.com/uview/empty/data.png"
      text="没有相关评价"
      v-else
    >
    </u-empty>
  </view>
</template>

<script>
const app = getApp();
import { getDocEvaluation } from '@/api/satisfactory.js';
import { dateFormat } from '../utils/validate';
const debounce = uni.$u.debounce;
export default {
  data() {
    return {
      lookMoreIndex: null,
      docInfo: {},
      list1: [],
      list: [],
      chooseIndex: 0,
      Evaluation: [],
      query: {
        PageIndex: 1,
        PageSize: 10,
        Score: [],
        Tags: [],
        DoctorId: '',
        OrgId: '',
      },
      showItemList: true,
    };
  },
  onLoad({ docInfo }) {
    this.docInfo = JSON.parse(decodeURIComponent(docInfo));
    this.getEvaluationList();
  },
  methods: {
    lookMore(index) {
      if (this.lookMoreIndex == index) {
        this.lookMoreIndex = null;
        return;
      }
      this.lookMoreIndex = index;
      console.log('点击的index', index);
    },
    tabsClick({ name }) {
      this.chooseIndex = 0;
      this.query.Tags = [];
      console.log('name', name);
      if (name.includes('好评')) {
        this.showItemList = true;
        this.query.Score = [5, 4];
      } else if (name.includes('中评')) {
        this.showItemList = true;
        this.query.Score = [3];
      } else if (name.includes('差评')) {
        this.query.Score = [2, 1];
        this.showItemList = false;
      } else if (name === '全部') {
        this.showItemList = true;
        this.query.Score = [];
      }
      this.restList(true);
    },
    async getEvaluationList(isChange = false) {
      this.query.OrgId = this.docInfo.OrganizationId;
      this.query.DoctorId = this.docInfo.UserId;
      const res = await getDocEvaluation({
        DoctorId: this.docInfo.UserId,
        ...this.query,
        Type: 2,
      });
      if (res.Type === 200) {
        res.Data.Data.forEach((e) => {
          e.evaluationTime = dateFormat(e.Created, 'YYYY-MM-DD');
          e.UserName = e.UserName
            ? e.UserName[0] + '*'.replace(e.UserName.length - 1)
            : e.UserName;
          this.Evaluation.push(e);
        });
        res.Data.TagAndScore.TagInfos.unshift({
          Tag: '全部',
          Count: 0,
        });
        this.list = res.Data.TagAndScore.TagInfos;
        if (!isChange) {
          this.renderBom(res);
        }
      }
    },
    renderBom(res) {
      const goodCount = res.Data.TagAndScore.ScoreInfo.filter(
        (s) => s.Score === 5 || s.Score === 4
      ).reduce((sum, item) => sum + item.Count, 0);
      const mediumCount = res.Data.TagAndScore.ScoreInfo.filter(
        (s) => s.Score === 3
      ).reduce((sum, item) => sum + item.Count, 0);
      const poorCount = res.Data.TagAndScore.ScoreInfo.filter(
        (s) => s.Score === 1 || s.Score === 2
      ).reduce((sum, item) => sum + item.Count, 0);
      let ScoreInfoArr = [
        {
          name: '全部',
        },
        {
          name: '好评' + goodCount,
        },
        {
          name: '中评' + mediumCount,
        },
        {
          name: '差评' + poorCount,
        },
      ];
      this.list1 = ScoreInfoArr;
    },
    chooseItem(item, index) {
      this.chooseIndex = index;
      this.query.Tags = item.Tag === '全部' ? [] : [item.Tag];
      this.restList();
    },
    restList(isChange = false) {
      this.query.PageIndex = 1;
      this.Evaluation = [];
      this.getEvaluationList(isChange);
    },
  },
  onReachBottom() {
    console.log('触底啦');
    debounce(() => {
      this.query.PageIndex++;
      this.getEvaluationList();
    });
  },
};
</script>

<style scoped lang="scss">
.container {
  padding: 10px;

  &-evaluation {
    &-list {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      flex-flow: row wrap;

      &-each {
        margin: 10rpx 0;
        margin-right: 37rpx;
        height: 50rpx;
        text-align: center;
        line-height: 50rpx;
        background-color: #29b7a3;
        color: white;
        font-size: 24rpx;
        border-radius: 50rpx;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        padding: 0 20rpx;
      }
    }

    &-each {
      margin-top: 20rpx;
      padding: 20rpx 20rpx;
      margin-bottom: 20rpx;
      background-color: white;

      &-right {
        margin-left: 20rpx;
        flex: 1;

        &-bom {
          overflow: hidden;
          -webkit-line-clamp: 2;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          line-break: anywhere;
        }

        &-lookMoreInfo {
          -webkit-line-clamp: 9999 !important;
          -webkit-box-orient: vertical;
        }
      }
    }
  }

  .scroll-view-item_H {
    display: inline-block;
    background-color: white;
    width: 120px;
    height: 60px;
    text-align: center;
    font-size: 36rpx;
    margin-right: 20rpx;
    border-radius: 16rpx;
  }

  .scroll-view-item_H-first {
    background-color: #29b7a3;
    color: white;
  }

  .scroll-view_H {
    white-space: nowrap;
    width: 100%;
    margin-top: 20rpx;
  }

  &-type {
    width: 200px;
    background-color: red;
    margin-right: 20rpx;
  }
}
</style>
