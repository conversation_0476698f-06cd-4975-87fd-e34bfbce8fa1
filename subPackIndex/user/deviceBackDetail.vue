<template>
  <view class="container">
    <MpLoadingPage :loadingType="loadingType">
      <view v-if="OrderDetailId">
        <view class="container-top">
          <p
            class="container-top-bz"
            style="margin-bottom: 24rpx; font-weight: 600"
          >
            请按照以下地址把设备退回
          </p>
          <p
            class="container-top-user"
            style="margin-bottom: 24rpx; color: #333333"
          >
            收货人：{{ backUserInfo.Payload[0].Value }}
            {{ backUserInfo.Payload[1].Value }}
          </p>
          <p class="container-top-user" style="color: #333333">
            收货地址：{{ backUserInfo.Payload[2].Value }}
          </p>
        </view>
        <view
          class="display-style1"
          style="margin-top: 20rpx; background-color: white; padding: 20rpx"
        >
          <p>快递单号<span style="color: red">*</span>：</p>
          <u-input
            placeholder="请输入快递单号"
            border="none"
            v-model="backDiviceQuery.ExpressNumber"
          ></u-input>
        </view>
        <view
          class="display-style3"
          style="margin-top: 20rpx; background-color: white; padding: 20rpx"
        >
          <p>备注：</p>
          <u-textarea
            v-model="backDiviceQuery.ApplyReason"
            placeholder="请输入内容"
          ></u-textarea>
        </view>
        <view class="container-bom">
          <p>退还说明：</p>
          <p style="margin-top: 24rpx">1.{{ backUserInfo.Payload[3].Value }}</p>
        </view>
        <u-button
          type="success"
          shape="circle"
          text="退还"
          customStyle="width:90%;bottom: 30px;position: fixed; left: 50%;transform: translateX(-50%);backgroundColor:#29B7A3;color:#fff"
          @click="deviceBack"
        ></u-button>
      </view>
      <view v-if="!OrderDetailId">
        <u-cell>
          <p
            style="margin-left: 32rpx; color: #333333; font-weight: 600"
            slot="title"
          >
            {{ item.Title }}
          </p>
          <u-avatar
            src="/static/images/defaultDevice.png"
            size="60"
            shape="square"
            slot="icon"
          ></u-avatar>
        </u-cell>
        <view style="height: 32rpx"></view>
        <u-cell title="设备保证使用金">
          <p slot="value" style="color: #ff3b30; font-weight: 600">
            ￥{{ item.Price }}
          </p>
        </u-cell>
        <u-cell title="支付方式" :value="payInfo.PaymentName" />
        <u-cell title="支付时间" :value="payTime" />
        <view v-if="item.OrderAftersales && item.OrderAftersales.length > 0">
          <u-cell title="退还时间" :value="payInfo.ReturnTime" />
          <u-cell
            title="回寄快递单号"
            :value="item.OrderAftersales[0].ExpressNumber"
          />
          <u-cell
            title="退款时间"
            :value="item.OrderAftersales[0].RefundTime"
            v-if="item.status_each == '已退还'"
          />
          <u-cell
            title="退款方式"
            :value="item.payBackType"
            v-if="item.status_each == '已退还'"
          />
          <u-cell
            title="退款金额"
            :value="'全部退款(￥' + item.OrderAftersales[0].Price + ')'"
            v-if="
              item.status_each == '已退还' && !item.OrderAftersales[0].Reason
            "
          />
          <u-cell
            title="退款金额"
            :value="'部分退款(￥' + item.OrderAftersales[0].Price + ')'"
            v-if="
              item.status_each == '已退还' && item.OrderAftersales[0].Reason
            "
          />
          <u-cell
            title="部分退款原因"
            :value="item.OrderAftersales[0].Reason"
            v-if="
              item.status_each == '已退还' && item.OrderAftersales[0].Reason
            "
          />
        </view>
      </view>
      <view class="container-footer" v-if="!OrderDetailId">
        <!-- <view class="right-button1 look" @click="devBack" v-if="item.OrderAftersales && item.OrderAftersales.length == 0">
						归还设备</view> -->
        <view class="right-button look" @click="lookOrder">查看原始订单</view>
      </view>
    </MpLoadingPage>
    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script>
import { deviceBackInfo } from '@/api/consult.js';
import { dateFormat } from '@/utils/validate.js';
import { GetExpressCompany } from '@/api/order.js';
import { getDictStd } from '@/api/bff.js';
export default {
  data() {
    return {
      loadingType: 'loading',
      PrescriptOrgId: '',
      backDiviceQuery: {
        ExpressName: '',
        ExpressNumber: '',
        ExpressCode: '',
        Phone: '',
        ApplyReason: '',
        OrderDetailId: '',
      },
      value: '',
      value1: '',
      backUserInfo: {},
      OrderDetailId: '',
      item: {},
      payInfo: {
        PaymentName: '',
      },
      payTime: '',
      PrescriptionId: '',
      ProviderData: [
        {
          Key: '微信',
          Value: 0,
        },
        {
          Key: '支付宝',
          Value: 1,
        },
        {
          Key: '免费',
          Value: 2,
        },
        {
          Key: '其他支付',
          Value: 3,
        },
      ],
    };
  },
  onLoad(option) {
    this.PrescriptOrgId = option.PrescriptOrgId;
    if (option.OrderDetailId) {
      this.getBackUserInfo();
      this.OrderDetailId = option.OrderDetailId;
    } else {
      const { item, payInfo, payTime } = option;
      this.item = JSON.parse(item);
      if (this.item.OrderAftersales && this.item.OrderAftersales.length > 0) {
        this.item.OrderAftersales[0].CreatedTime =
          this.item.OrderAftersales[0].CreatedTime &&
          dateFormat(this.item.OrderAftersales[0].CreatedTime);
        this.item.OrderAftersales[0].RefundTime =
          this.item.OrderAftersales[0].RefundTime &&
          dateFormat(this.item.OrderAftersales[0].RefundTime);
      }
      this.payInfo = JSON.parse(payInfo);
      this.payInfo.PaymentName = this.ProviderData.filter(
        (o) => o.Value === this.payInfo.PayType
      )[0].Key;
      this.payTime = payTime == 'null' ? '' : dateFormat(payTime);
      this.PrescriptionId = option.PrescriptionId;
      uni.setNavigationBarTitle({
        title: this.item.status_each,
      });
      this.loadingType = 'success';
    }
  },
  methods: {
    devBack() {
      uni.redirectTo({
        url:
          './deviceBackDetail?OrderDetailId=' +
          this.item.Id +
          '&PrescriptOrgId=' +
          this.PrescriptOrgId,
      });
    },
    lookOrder() {
      uni.navigateTo({
        url: '/subPackIndex/user/treatmentDetail?id=' + this.PrescriptionId,
      });
    },
    async checkExpressNum() {
      const res = await GetExpressCompany(this.backDiviceQuery.ExpressNumber);
      if (res.Type === 200) {
        if (!res.Data[this.backDiviceQuery.ExpressNumber]) {
          uni.showModal({
            content: `未存在${this.backDiviceQuery.ExpressNumber}的快递单号`,
          });
          return;
        }
        const obj = res.Data[this.backDiviceQuery.ExpressNumber];
        this.backDiviceQuery.ExpressCode = obj.ComCode;
        this.backDiviceQuery.ExpressName = obj.Name;
        this.backDiviceQuery.Phone = this.backUserInfo.Payload[1].Value;
        this.backDiviceQuery.OrderDetailId = this.OrderDetailId;
        this.sendData();
      }
    },
    async sendData() {
      let res = await deviceBackInfo(this.backDiviceQuery);
      if (res.Type == 200) {
        this.$refs.uToast.show({
          message: '操作成功',
          type: 'success',
        });
        setTimeout(() => {
          let pages = getCurrentPages();
          const prePage = pages[pages.length - 2]; //上一个页面
          if (prePage.$vm.rest) {
            prePage.$vm.rest();
          }
          uni.navigateBack();
        }, 1500);
      } else {
        this.$refs.uToast.show({
          message: res.Content,
          type: 'error',
        });
      }
    },
    async deviceBack() {
      if (!this.backDiviceQuery.ExpressNumber) {
        this.$refs.uToast.show({
          message: '请输入快递单号',
          type: 'error',
        });
        return;
      }
      this.checkExpressNum();
    },
    async getBackUserInfo() {
      let res = await getDictStd({
        code: 'DeviceReturningInfo',
        orgId: this.PrescriptOrgId,
      });
      if (res.Type == 200) {
        const obj = res.Data.filter((v) => v.Code === 'DeviceReturningInfo')[0];
        this.backUserInfo = obj;
        this.loadingType = 'success';
      } else {
        this.loadingType = res.Content;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  padding: 32rpx;

  /deep/ .u-cell {
    background-color: white;
  }

  /deep/ .u-line {
    border-bottom-style: none !important;
  }

  .container-top {
    padding: 32rpx;
    background-color: white;
    border-radius: 8rpx;

    .container-top-bz {
      color: #29b7a3;
      font-size: 18px;
    }

    .container-top-user {
      font-size: 14px;
      color: #333333;
      margin-top: 18rpx;
    }
  }

  .container-bom {
    font-size: 14px;
    margin-top: 32rpx;
    color: #999999;
  }

  .container-footer {
    position: absolute;
    bottom: 10px;
    width: 100%;
    height: 60px;
    background-color: white;

    .right-button {
      width: 100px;
      height: 35px;
      border-radius: 20px;
      text-align: center;
      line-height: 35px;
      background-color: #00aeb7;
      color: white;
      font-size: 14px;
      position: absolute;
      right: 20px;
      // bottom: 17px;
      top: 0;
      bottom: 0;
      margin: auto;
    }

    .right-button1 {
      width: 100px;
      height: 35px;
      border-radius: 20px;
      text-align: center;
      line-height: 35px;
      background-color: #00aeb7;
      color: white;
      font-size: 14px;
      position: absolute;
      right: 130px;
      top: 0;
      bottom: 0;
      margin: auto;
    }
  }
}
</style>
