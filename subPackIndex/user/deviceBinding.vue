<template>
  <view class="container">
    <u-list @scrolltolower="scrolltolower">
      <u-list-item v-for="(item, index) in list" :key="index">
        <u-cell :title="item.Name" @click="checkItem(item)"> </u-cell>
      </u-list-item>
    </u-list>
    <view style="height: 80px"></view>
    <u-toast ref="uToast"></u-toast>
    <u-modal
      :show="show"
      title="温馨提示"
      content="已绑定相同类型的设备,如需绑定新的设备,请先解绑？"
      showConfirmButton="true"
      showCancelButton="true"
      confirmText="去解绑"
      @confirm="onSure"
      @cancel="show = false"
    ></u-modal>
  </view>
</template>

<script>
const app = getApp();
import { checkBindingUserDevices, bindingUserDevices } from '@/api/training.js';
export default {
  data() {
    return {
      show: false,
      devItem: {},
      query: {
        familyId: '',
        pageindex: 1,
        pagesize: 10,
      },
      list: [],
    };
  },
  onLoad({ item }) {
    this.devItem = JSON.parse(item);
    this.checkItem({
      UserId: app.globalData.userInfo.Id,
      Sex: app.globalData.userInfo.Sex,
      Birthday: app.globalData.userInfo.Birthday,
      UserName: app.globalData.userInfo.Name,
    });
  },
  methods: {
    checkItem(item) {
      this.checkIsBind(item);
    },
    async checkIsBind(item) {
      let data = [
        {
          DeviceCode: this.devItem.Data.DeviceCode,
          DeviceTypeCode: this.devItem.Data.DeviceTypeCode,
          DeviceFactory: this.devItem.Data.DeviceFactory,
          MenberId: item.UserId,
        },
      ];
      let res = await checkBindingUserDevices(data);
      if (res.Type == 200) {
        if (res.Data) {
          this.bindThisDev(item);
        } else {
          console.log('不能添加');
          this.show = true;
        }
      }
    },
    async bindThisDev(item) {
      let data = [
        {
          DeviceCode: this.devItem.Data.DeviceCode,
          DeviceTypeCode: this.devItem.Data.DeviceTypeCode,
          DeviceFactory: this.devItem.Data.DeviceFactory,
          UserId: app.globalData.userInfo.Id,
          MenberId: item.UserId,
          Sex: item.Sex,
          UserName: item.Name,
        },
      ];
      if (item.Birthday) {
        data[0].Birthday = item.Birthday;
      }
      let res = await bindingUserDevices(data);
      if (res.Type == 200) {
        this.$refs.uToast.show({
          message: '设备绑定成功',
          type: 'success',
        });
        setTimeout(() => {
          let pages = getCurrentPages();
          const prePage = pages[pages.length - 2]; //上一个页面
          if (prePage.$vm.rest) {
            prePage.$vm.rest();
          }
          uni.navigateBack();
        }, 1000);
      } else {
        this.$refs.uToast.show({
          message: res.Message || '绑定失败',
          type: 'error',
        });
      }
    },
    onSure() {
      uni.navigateBack();
      this.show = false;
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  /deep/ .u-cell {
    background-color: white;
  }
}
</style>
