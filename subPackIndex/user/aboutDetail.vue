<template>
  <view class="container">
    <view class="container-top" v-if="type == 'yl'">
      <u--image
        :showLoading="true"
        src="/static/images/yankang-icon.png"
        width="100px"
        height="100px"
      ></u--image>
      <p style="text-align: left; margin-top: 20rpx">
        “康复行医疗”是基于互联网的康复医疗服务平台，平台利用互联网技术手段和线上线相融合的方式，为康复医疗领域提供各种“线上+线下”的互联网医疗服务，主要应用场景面向居家康复和社区康复。通过平台的服务，可以节约患者就医成本，足不出户就可以享受专业康复医疗服务，降低慢病致残率，促进患者尽快回归社会、提高生活质量。
        平台与院内康复业务系统相结合，构建医生、治疗师、患者和设备无缝衔接的医疗服务体系，形成“院内+院外”全生命周期的康复闭环管理，打通康复医疗服务“最后一公里”
      </p>
    </view>

    <view class="container-top" v-if="type == 'zhzx'">
      <p style="text-align: left; margin-top: 20rpx">
        甲方：成都双流康易行互联网医院有限公司
        <br />
        乙方：您
        <br />
        亲爱的用户，在您正式开始注销您的用户帐号之前，请认真阅读《帐号注销协议》（以下简称“本协议”），充分理解并接受本协议的全部内容。在阅读本协议的过程中，如您有任何问题或者不同意相关任何条款，请您立即停止帐号注销程序，疑问可通过客服电话联系我们。
        <br />
        一、 【特别提示】
        <br />1.注销康复行用户帐号为不可恢复的操作，一旦注销完成，将无法恢复，注销用户帐号后您将再无法使用本用户帐号，也无法找回您帐号中及帐号相关的任何内容或信息，也无法找回您的浏览、添加、绑定等的任何内容或信息，即使您使用相同的手机号码再次注册并使用康复行。
        <br />2.请您确保您所申请注销的康复行用户帐号是您本人依照康复行相关协议以及规则注册并由康复行提供给您使用的帐号。

        <br />3.建议您在注销用户帐号前，自行备份帐号相关的信息，并确认与本帐号相关的所有服务及事宜均已进行妥善处理。

        <br />4.注销完成后，康复行将删除该帐号下您的个人信息，且该帐号此前已关联的相关产品和服务将不再关联。
        <br />二、经慎重考虑后，如果您仍决定注销帐号，为保障您的帐号安全和相关权益，您需先行检查并确保和承诺您申请和注销的账户已经同时满足以下条件，这些条件包括但不限于：
        <br />1.该帐号为您本人合法拥有并登陆使用，且处于安全状态。帐号能正常使用，没有处于违规处理期间，且没有被盗、被封等风险；
        <br />2.账户无未完成的订单、服务；
        <br />3.帐号权限解除：帐号已经解除与其他康复行账号授权或绑定关系;
        <br />4.帐号无任何未了结争议、纠纷，包括投诉举报或者被投诉举报；
        <br />5、本帐号不侵犯任何第三方的合法权益，如因此引发争议，将由您自行承担；
        <br />6、本帐号或者甲方官方公布的其他条件。
        <br />上述问题您可首先尝试自行处理，如您无法处理的或在处理过程中有任何疑惑的，可联系甲方客服协助处理。
        <br />三、如您按照注销流程开始注销帐号的，或者您勾选“下一步”操作的，均视为您已经同意和遵守本协议全部内容。如您在注销后需要再次使用我们服务的，欢迎您重新注册登陆，此时该帐号将作为新的用户帐号。
        <br />四、如何注销帐号/注销流程
        点击【我的】-【设置】-【注销账号】-【我要注销】-【仍要注销】
        <br />五、用户帐号注销后，您将无法登录、使用本帐号，也无法找回本用户帐号及帐号相关的任
        <br />何内容或信息，包括但不限于：
        <br />1.个人已提交的身份信息、帐号信息等。
        <br />2.已经发布的视频、点赞、评论、互动等内容数据。
        <br />3.帐号内的实名认证信息将被删除。
        <br />4.您通过康复行购买唯高商城的商品的交易记录将被删除或对其进行匿名化处理。
        <br />5.您与医生/治疗师的交流记录、上传的病历资料等数据。
        <br />6、您在该帐号下的相关收益也将被删除，故特别提醒您，在注销本账户前请妥善处理您账号下的相关收益。一旦您注销本账号，如该账号下还存在相关收益或权益的（包括在帐号使用期间已产生的及未来可能产生的收益），视为您自愿放弃该等收益或权益，我们有权对该账号下的全部收益或权益做清除处理，因此产生的后果由您自行承担，您同意不会要求我们恢复或赔偿该等收益或权益。
        <br />六、本帐号注销，将导致康复行终止为您提供服务，注销本帐号并不代表本帐号注销前的帐号行为或相关责任得到豁免或减免，您仍需要对该帐号注销前使用的行为承担相应责任，包括但不限于：违约责任、损害赔偿责任以及履约义务等。
        <br />七、在您的帐号注销期间，如果您的帐号涉及争议、纠纷，包括但不限于：投诉、举报、诉讼、仲裁等，您理解并同意，康复行有权自行决定是否终止本帐号的注销而无需另行得到您的同意。
        <br />八、其他：
        <br />1、您知悉并同意：即使您该产品（或服务）的账号被注销，也并不减轻或免除您应根据相关法律法规、相关协议、规则等（可能）需要承担的相关责任。
        <br />2、本协议签订地为中华人民共和国四川省成都市双流区。
        <br />3、本协议的成立、生效、履行、解释及纠纷解决，适用中华人民共和国大陆地区法律（不包括冲突法）。
        <br />4、您和康易行之间因本协议发生的（和/或与本协议有关的）任何纠纷或争议，首先友好协商解决；协商不成的，您同意将纠纷或争议提交至本协议签订地有管辖权的人民法院管辖。
        <br />5、如在账号注销过程中有任何问题您无法处理的或在处理过程中有任何疑惑的，可联系康易行客服协助处理。
        <br />6、本协议未尽事宜，参考《用户注册协议》执行。
      </p>
    </view>

    <u-parse :content="Text" v-if="type != 'yl' && type != 'zhzx'"></u-parse>
  </view>
</template>

<script>
import { getMinAppInfo } from '@/api/content.js';
export default {
  data() {
    return {
      type: '',
      Text: '',
    };
  },
  onLoad({ type }) {
    let title = '';
    switch (type) {
      case 'yl':
        title = '关于康复行医疗';
        break;
      case 'xy':
        title = '用户协议';
        break;
      case 'zc':
        title = '隐私政策';
        break;
      case 'zhzx':
        title = '账号注销';
    }
    this.type = type;
    uni.setNavigationBarTitle({
      title,
    });
    this.getInfo(type);
  },
  methods: {
    async getInfo(type) {
      if (type == 'yl') return;
      let num = type == 'xy' ? 1 : 0;
      let res = await getMinAppInfo(num);
      if (res.Type == 200) {
        this.Text = res.Data.Text;
      }
    },
    changeone() {
      uni.requestSubscribeMessage({
        tmplIds: ['Sy7nYla5djot64pH2QODh2qs5UCcUFRj3T1MgB52TjY'],
        success(res) {
          console.log('res订阅消息', res);
        },
        complete() {},
      });
    },
    changetwo() {
      uni.requestSubscribeMessage({
        tmplIds: [
          'kS3_Wj60Whb_ffi_DMNZ8H1G87-YbkY1F_5I_vJGwPU',
          'oX_lAC-2Ez-yJ8168wN_Qvr6kEu2aftvq1ioVE0LxSE',
          'yUl6NK4v8ntLoC2BvxFMfgAnRH25xzpTosqffbw0aCk',
        ],
        success(res) {
          console.log('res订阅消息', res);
        },
        complete() {},
      });
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  padding: 20rpx;

  &-top {
    text-align: center;
    padding: 40rpx;
    margin: 0 auto;

    /deep/ .u-image {
      margin: 0 auto;
    }
  }
}
</style>
