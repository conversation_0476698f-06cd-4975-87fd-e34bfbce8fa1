<template>
  <view class="container">
    <!-- <u--input placeholder="请输入订单编号" border="surround" shape="circle" v-model="query.PrescriptionId"
			@confirm="confirm" suffixIcon="search" prefixIconStyle="font-size: 22px;color: #909399"></u--input> -->
    <!-- <u-search placeholder="请输入订单编号" v-model="query.PrescriptionId" actionText="搜索" @custom="rest" :actionStyle="{color:'#29B7A3'}"></u-search> -->
    <!-- 搜索 -->
    <view class="container-search">
      <u-input
        placeholder="请输入订单编号"
        customStyle="{backgroundColor:#FAFAFA}"
        type="number"
        v-model="query.PrescriptionId"
      >
        <u-button
          slot="suffix"
          @tap="rest"
          type="success"
          icon="search"
        ></u-button>
      </u-input>
    </view>
    <u-list @scrolltolower="scrolltolower" v-if="indexList.length > 0">
      <u-list-item v-for="(item, index) in indexList" :key="index">
        <view class="each-box">
          <u-cell :border="false">
            <p
              style="color: #333333; margin-top: 24rpx; font-weight: 600"
              slot="title"
            >
              订单编号:{{ item.Order.OrderNo }}
            </p>
          </u-cell>
          <template v-for="(o, index2) in item.Order.OrderDetails">
            <u-cell
              :border="false"
              @click="toBackDev(o, item)"
              customStyle="marginTop: 12rpx;"
            >
              <p
                style="color: #333333; font-weight: 600; margin-left: 32rpx"
                slot="title"
              >
                {{ o.Title }}
              </p>
              <u-image
                slot="icon"
                :radius="10"
                src="/static/images/defaultDevice.png"
                width="50px"
                height="50px"
              />
              <span
                slot="label"
                :style="o.status_color"
                style="margin-left: 32rpx"
                >{{ o.status_each }}</span
              >
              <view class="" slot="value" style="text-align: right">
                <p style="color: red">￥{{ o.TotalPrice }}</p>
              </view>
            </u-cell>
          </template>
        </view>
      </u-list-item>
    </u-list>
    <u-toast ref="uToast"></u-toast>
    <u-empty
      mode="data"
      icon="http://cdn.uviewui.com/uview/empty/data.png"
      text="暂无缴纳纪录"
      v-if="indexList.length == 0"
    >
    </u-empty>
  </view>
</template>

<script>
import { deviceBack } from '@/api/consult.js';
const debounce = uni.$u.debounce;
export default {
  data() {
    return {
      indexList: [],
      query: {
        PrescriptionId: '',
        pageindex: 1,
        pagesize: 10,
      },
    };
  },
  onLoad({ PrescriptionId }) {
    this.query.PrescriptionId = PrescriptionId || '';
    this.getList();
  },
  methods: {
    async getList() {
      uni.showLoading({
        title: '正在加载数据',
      });
      let res = await deviceBack(this.query);
      if (res.Type == 200 && res.Data != null && res.Data.length > 0) {
        res.Data.forEach((e) => {
          e.Order.OrderDetails.forEach((o) => {
            if (o.OrderAftersales.length > 0) {
              o.payBackType = e.Order.PayType === 3 ? '线下退回' : '原路退回';
              o.status_each =
                o.OrderAftersales[0].Status == 3 ? '已退还' : '退还中';
              o.status_color =
                o.OrderAftersales[0].Status == 3
                  ? 'color:#999999'
                  : 'color:#FF3B30';
            } else {
              o.status_each = '已支付';
              o.status_color = 'color:#29B7A3';
            }
          });
          this.indexList.push(e);
        });
        this.$nextTick(() => {
          uni.hideLoading();
        });
      } else {
        this.$nextTick(() => {
          uni.hideLoading();
        });
      }
    },
    scrolltolower() {
      debounce(() => {
        this.query.pageindex++;
        this.getList();
      });
    },
    confirm() {
      this.rest();
    },
    rest() {
      if (isNaN(Number(this.query.PrescriptionId))) {
        //当输入不是数字的时候，Number后返回的值是NaN;然后用isNaN判断。
        this.$refs.uToast.show({
          message: '订单编号只能是数字',
          type: 'error',
        });
        return;
      }
      this.query.pageindex = 1;
      this.indexList = [];
      this.getList();
    },
    toBackDev(o, item) {
      if (
        o.OrderAftersales &&
        o.OrderAftersales.length > 0 &&
        item.Order.OrderExpresses &&
        item.Order.OrderExpresses.length > 0
      ) {
        o.OrderAftersales[0].ExpressNumber = item.Order.OrderExpresses.filter(
          (v) => v.Type === 1
        )
          .map((s) => s.ExpressNum)
          .join('、');
      }
      item.Order.ReturnTime = item.Prescription.ReturnTime
        ? this.$dateFormat(item.Prescription.ReturnTime)
        : '';
      uni.navigateTo({
        url: `./deviceBackDetail?item=${JSON.stringify(o)}&payInfo=${JSON.stringify(item.Order)}&payTime=${item.Order.PayTime}&PrescriptionId=${item.Prescription.PrescriptionId}&PrescriptOrgId=${item.Prescription.OrganizationId}`,
      });
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  /deep/ .u-list {
    max-height: calc(100vh - 200rpx) !important;
    height: auto;
  }

  /deep/ .insideStyle {
    padding: 0 32rpx;
  }

  overflow: hidden;

  .each-box {
    background-color: white;
    margin-bottom: 24rpx;
    // box-shadow: 2.5px 3px 9px 0px rgba(0, 0, 0, 0.08);
    border-radius: 8rpx;
  }

  .container-search {
    background-color: white;
    width: 100%;
    padding: 32rpx;
    margin-bottom: 32rpx;
    border-radius: 0 0 24rpx 24rpx;

    /deep/ .u-input {
      padding: 0 0 0 6px !important;
    }
  }
}
</style>
