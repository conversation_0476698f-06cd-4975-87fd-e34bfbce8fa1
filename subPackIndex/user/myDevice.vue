<template>
  <view class="container">
    <!-- 列表信息 -->
    <view
      class="container-list"
      v-for="(item, index) in list"
      v-if="list.length > 0"
    >
      <u-cell>
        <view class="container-left" slot="title">
          <p style="margin-bottom: 8px">{{ item.UserName }}</p>
          <p style="margin-bottom: 8px">设备类型:{{ item.DeviceTypeName }}</p>
          <p style="margin-bottom: 8px">设备编号:{{ item.DeviceCode }}</p>
          <p style="margin-bottom: 8px; color: #999999">
            绑定时间：{{ item.CreatedTimes }}
          </p>
        </view>
        <u-button
          shape="circle"
          text="解绑"
          slot="value"
          @click="unbinding(item)"
          customStyle="width:70px;backgroundColor:#29B7A3;color:#fff"
        ></u-button>
      </u-cell>
    </view>

    <u-empty
      mode="data"
      icon="http://cdn.uviewui.com/uview/empty/data.png"
      text="当前没有设备信息"
      v-if="list.length == 0"
    >
    </u-empty>

    <view style="height: 80px" v-if="list.length > 0"></view>

    <u-button
      shape="circle"
      text="绑定设备"
      customStyle="width:90%;bottom: 30px;position: fixed; left: 50%;transform: translateX(-50%);backgroundColor:#29B7A3;color:#fff"
      @click="addDevice"
    ></u-button>

    <u-modal
      :show="show"
      title="操作警告"
      content="需要解绑此设备吗？"
      showConfirmButton="true"
      showCancelButton="true"
      @confirm="onSure"
      @cancel="show = false"
    ></u-modal>
    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script>
const app = getApp();
import { getUserDevicesList, unbindDevice } from '@/api/training.js';
import { dateFormat } from '../../utils/validate';
export default {
  data() {
    return {
      list: [],
      show: false,
      unbindingItem: {},
      query: {
        userId: app.globalData.userInfo.Id,
        pageindex: 1,
        pagesize: 10,
      },
    };
  },
  onLoad() {
    this.getData();
  },
  methods: {
    async getData() {
      let res = await getUserDevicesList(this.query);
      if (res.Type == 200 && res.Data.length > 0) {
        res.Data.forEach((e) => {
          e.CreatedTimes = dateFormat(e.CreatedTime);
          this.list.push(e);
        });
      }
    },
    addDevice() {
      uni.scanCode({
        scanType: ['barCode', 'qrCode', 'datamatrix', 'pdf417'],
        success: function (res) {
          console.log('条码类型：' + res.scanType);
          console.log('条码内容：' + res.result);
          uni.navigateTo({
            url: './deviceBinding?item=' + res.result,
          });
        },
      });
    },
    // 解绑设备
    unbinding(item) {
      console.log('item', item);
      this.unbindingItem = item;
      this.show = true;
    },
    rest() {
      this.list = [];
      this.query.pageindex = 1;
      this.getData();
    },
    async onSure() {
      let res = await unbindDevice({
        id: this.unbindingItem.Id,
      });
      if (res.Type == 200) {
        this.$refs.uToast.show({
          message: res.Content,
          type: 'success',
        });
        this.show = false;
        this.list = [];
        this.query.pageindex = 1;
        this.getData();
      } else {
        this.$refs.uToast.show({
          message: res.Content,
          type: 'error',
        });
        this.show = false;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  padding: 0 10px 10px 10px;

  .container-list {
    margin-bottom: 10rpx;

    /deep/ .u-cell {
      background-color: white;
      box-shadow: -1px -1px 4px -2px rgba(0, 0, 0, 0.3) inset;
      border-radius: 12rpx;
      transform: translateY(10px);
    }
  }
}
</style>
