<template>
  <view class="container">
    <u-cell>
      <view slot="title"> 收货人<span style="color: red">*</span> </view>
      <u-input
        clearable
        placeholder="请输入收货人姓名"
        border="none"
        v-model="query.Name"
        slot="value"
        inputAlign="right"
      >
      </u-input>
    </u-cell>
    <u-cell>
      <view slot="title"> 手机号<span style="color: red">*</span> </view>
      <u-input
        clearable
        placeholder="请输入手机号码"
        border="none"
        v-model="query.Tel"
        slot="value"
        inputAlign="right"
      >
      </u-input>
    </u-cell>
    <u-cell is-link>
      <view slot="title"> 所在地区<span style="color: red">*</span> </view>
      <view slot="value" @click="changeArea">
        {{ addressTogeter ? addressTogeter : '请选择' }}
      </view>
    </u-cell>
    <u-cell :border="false">
      <view slot="title"> 详细地址<span style="color: red">*</span> </view>
    </u-cell>

    <u-textarea
      v-model="query.Address"
      @input="textareaInput($event)"
      :maxlength="80"
      placeholder="请输入详细地址"
      count
      :height="120"
    ></u-textarea>
    <u-cell title="是否为默认地址" customStyle="marginTop:24rpx">
      <u-switch
        v-model="query.IsDefault"
        slot="value"
        activeColor="#29B7A3"
      ></u-switch>
    </u-cell>

    <view style="height: 60px"></view>
    <u-button
      shape="circle"
      text="保存"
      :custom-style="btnSaveStyle"
      @click="save"
      :loading="btnLoading"
    ></u-button>
    <u-picker
      :immediateChange="true"
      :show="show"
      ref="uPicker"
      keyName="Value"
      :columns="columns"
      @change="changeHandler"
      @cancel="close"
      @confirm="confirm"
      :defaultIndex="defaultPickerIndex"
    ></u-picker>
    <u-modal
      :show="showRich"
      title="提示"
      @confirm="confirmContent"
      @cancel="sendData"
      :showCancelButton="true"
      confirmText="修改并保存"
      cancelText="忽略"
    >
      <view class="slot-content">
        <rich-text :nodes="content"></rich-text>
      </view>
    </u-modal>
    <u-popup
      :show="showPrivacyPopup"
      :closeOnClickOverlay="false"
      :round="10"
      mode="bottom"
      @close="closePrivacyPopup()"
    >
      <view style="padding: 40rpx">
        <p
          style="
            text-align: center;
            margin-bottom: 40rpx;
            font-size: 20px;
            font-weight: 700;
          "
        >
          提示
        </p>
        <text>
          在你使用服务之前，请仔细阅读<text
            @click="onClickPrivacyPopupTitle()"
            style="color: #29b7a3"
            >{{ PrivacyPopupTitle }}</text
          >。如果你同意{{ PrivacyPopupTitle }}，请点击“同意”开始使用。
        </text>
        <view class="display-style" style="margin-top: 40rpx">
          <u-button
            text="拒绝"
            @click="handleRefusePrivacyAuthorization()"
          ></u-button>
          <!-- <u-button type="primary" id="agree-btn" text="同意" openType="agreePrivacyAuthorization"
						@agreeprivacyauthorization="handleAgreePrivacyAuthorization()"></u-button> -->
          <button
            id="agree-btn"
            class="btn-agree"
            open-type="agreePrivacyAuthorization"
            @agreeprivacyauthorization="handleAgreePrivacyAuthorization()"
          >
            同意
          </button>
        </view>
      </view>
    </u-popup>
    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script>
const app = getApp();
import { addAddress } from '@/api/order.js';
import { getLocaDetail, getSSQByAddress } from '@/api/other.js';
import { getRegionCode, getAddressList } from '@/api/dictionary.js';
export default {
  data() {
    return {
      defaultPickerIndex: [0, 0, 0],
      btnSaveStyle: {
        width: '90%',
        position: 'fixed',
        left: '50%',
        transform: 'translateX(-50%)',
        backgroundColor: '#29B7A3',
        color: '#fff',
        bottom: '20px',
      },
      btnLoading: false,
      getAddressObj: {},
      showRich: false,
      content: '',
      query: {
        Name: '',
        Tel: '',
        Province: '',
        City: '',
        County: '',
        ProvinceName: '',
        CityName: '',
        CountyName: '',
        Address: '',
        UserId: app.globalData.userInfo.Id,
        IsDefault: false,
      },
      show: false,
      columns: [[], [], []],
      addressQuery: {
        FilterGroup: {
          Rules: [
            {
              Field: 'DictId', //写死
              Value: '28', //写死
              Operate: 3, //写死
            },
            {
              Field: 'ParentId', //写死
              Value: '86', //父级ID,最顶级是86(中国)
              Operate: 3, //写死
            },
          ],
          Operate: 1,
        },
        PageCondition: {
          PageSize: 1000,
          SortConditions: [
            {
              SortField: 'OrderNumber',
            },
          ],
        },
      },
      addressTogeter: '',
      option: {},
    };
  },
  async onLoad({ item1, needShowUser }) {
    uni.onKeyboardHeightChange(this.listener);
    await this.getAddress();
    if (item1) {
      const item = JSON.parse(item1);
      this.query = {
        Name: item.Name,
        Tel: item.Tel,
        Province: item.Province,
        City: item.City,
        County: item.County,
        ProvinceName: item.ProvinceName,
        CityName: item.CityName,
        CountyName: item.CountyName,
        Address: item.Address,
        UserId: app.globalData.userInfo.Id,
        IsDefault: item.IsDefault,
        Id: item.Id,
      };
      this.addressTogeter = item.ProvinceName + item.CityName + item.CountyName;
      uni.setNavigationBarTitle({
        title: '编辑地址',
      });
      // 对地区数据进行处理
      this.onSetAddressInfo();
    } else {
      this.saveWXCallBack();
      this.getLocalTions();
    }
    if (needShowUser) {
      const user = app.globalData.userInfo;
      this.query.Name = user.Name;
      this.query.Tel = user.PhoneNumber;
    }
  },
  onUnload() {
    uni.offKeyboardHeightChange(this.listener);
  },
  methods: {
    async onSetAddressInfo() {
      const provinceIdIndex = this.columns[0].findIndex(
        (v) => v.Value === this.query.ProvinceName
      );
      if (provinceIdIndex > -1) {
        this.defaultPickerIndex[0] = provinceIdIndex;
        await this.changeHandler(
          {
            columnIndex: 0,
            value: [this.columns[0][provinceIdIndex]],
          },
          false
        );
      }

      const cityIdIndex = this.columns[1].findIndex(
        (v) => v.Value === this.query.CityName
      );
      if (cityIdIndex > -1) {
        this.defaultPickerIndex[1] = cityIdIndex;
        await this.changeHandler(
          {
            columnIndex: 1,
            value: ['', this.columns[1][cityIdIndex]],
          },
          false
        );
      }
      const countyIdIndex = this.columns[2].findIndex(
        (v) => v.Value === this.query.CountyName
      );
      if (countyIdIndex > -1) {
        this.defaultPickerIndex[2] = countyIdIndex;
      }
    },
    listener(res) {
      console.log('res', res);
      if (res.height > 0) {
        this.btnSaveStyle.bottom = res.height + 'px';
      } else {
        this.btnSaveStyle.bottom = '20px';
      }
    },
    textareaInput(e) {
      if (e.length > 80) {
        this.query.Address = e.slice(0, 80);
      }
    },
    // 获取城市信息
    async getAddress() {
      // 获取省级
      let res = await getAddressList(this.addressQuery);
      if (res.Type === 200) {
        this.columns[0] = res.Data.Rows;
        // 获取市级
        this.addressQuery.FilterGroup.Rules[1].Value = res.Data.Rows[0].Key;
        let res2 = await getAddressList(this.addressQuery);
        if (res2.Type === 200) {
          this.columns[1] = res2.Data.Rows;
          //获取区级
          this.addressQuery.FilterGroup.Rules[1].Value = res2.Data.Rows[0].Key;
          let res3 = await getAddressList(this.addressQuery);
          if (res3.Type === 200) {
            this.columns[2] = res3.Data.Rows;
          }
        }
      }
    },
    async save() {
      if (!this.query.Name) {
        this.$refs.uToast.show({
          message: '请填写收货人',
          type: 'error',
        });
        return;
      }
      if (!this.query.Tel) {
        this.$refs.uToast.show({
          message: '请填写手机号',
          type: 'error',
        });
        return;
      }
      if (!this.query.ProvinceName) {
        this.$refs.uToast.show({
          message: '请选择区域',
          type: 'error',
        });
        return;
      }
      if (!this.query.Address) {
        this.$refs.uToast.show({
          message: '请填写详细地址',
          type: 'error',
        });
        return;
      }
      const reg = /^1[3-9]\d{9}$/;
      if (!reg.test(this.query.Tel)) {
        this.$refs.uToast.show({
          message: '手机号格式不正确',
          type: 'error',
        });
        return;
      }
      this.onGetSSQByAddress();
    },
    async onGetSSQByAddress() {
      const res = await getSSQByAddress({
        address: this.query.Address,
        city: this.query.CityName,
      });
      console.log('res', res);
      if (res.status === '1' && res.geocodes.length > 0) {
        let flag = false;
        let data;
        const matchingData = res.geocodes.filter(
          (v) =>
            v.city === this.query.CityName &&
            v.district === this.query.CountyName
        );
        if (matchingData.length === 0) {
          flag = true;
          data = res.geocodes[0];
        }
        if (flag) {
          this.content = `识别到您填写地址所在地区为<span style="color: red">${data.province}${data.city}${data.district}</span>，是否修改?`;
          this.getAddressObj = {
            province: data.province,
            city: data.city,
            district: data.district,
          };
          this.showRich = true;
        } else {
          this.sendData();
        }
      } else {
        this.sendData();
      }
    },
    confirmContent() {
      const data = this.getAddressObj;
      this.query.ProvinceName = data.province;
      this.query.CityName = data.city;
      this.query.CountyName = data.district;
      const obj = {
        province: data.province,
        city: data.city,
        county: data.district,
      };
      if (data.province === data.city) {
        obj.county = data.district;
        delete obj.city;
      }
      this.getPCCCode({
        province: data.province,
        city: data.city,
        county: data.district,
      });
    },
    async getPCCCode(data) {
      const res = await getRegionCode(data);
      if (res.Type === 200) {
        this.query.Province = res.Data.ProvinceCode;
        this.query.City = res.Data.CityCode;
        this.query.County = res.Data.CountyCode;
        this.sendData();
      }
    },
    async sendData() {
      console.log('query', this.query);
      let type = this.query.Id ? 'update' : 'create';
      this.btnLoading = true;
      let res = await addAddress(type, this.query);
      if (res.Type == 200) {
        this.$refs.uToast.show({
          message: '地址保存成功',
          type: 'success',
        });
        setTimeout(() => {
          let pages = getCurrentPages();
          const prePage = pages[pages.length - 2]; //上一个页面
          if (prePage.$vm.getData) {
            prePage.$vm.getData();
          }
          if (prePage.$vm.getBeforData) {
            prePage.$vm.getBeforData(this.query);
          }
          uni.navigateBack();
        }, 1500);
      } else {
        this.$refs.uToast.show({
          message: res.Content,
          type: 'error',
        });
      }

      this.btnLoading = false;
      this.showRich = false;
    },
    changeArea() {
      this.show = true;
    },
    async changeHandler(e, isShow = true) {
      // 省级变了
      if (e.columnIndex == 0) {
        // 获取省级的key
        const Sid = e.value[0].Key;
        this.columns[1] = [];
        this.columns[2] = [];
        this.addressQuery.FilterGroup.Rules[1].Value = Sid;
        let res1 = await getAddressList(this.addressQuery);
        if (res1.Type != 200) {
          return;
        }
        if (res1.Data.Rows.length > 0) {
          if (isShow) {
            this.show = false;
            this.show = true;
          }
          this.columns[1] = res1.Data.Rows;
          this.addressQuery.FilterGroup.Rules[1].Value = res1.Data.Rows[0].Key;
          let res2 = await getAddressList(this.addressQuery);
          if (res2.Data.Rows.length > 0) {
            if (isShow) {
              this.show = false;
              this.show = true;
            }
            this.columns[2] = res2.Data.Rows;
          }
        }
      } else if (e.columnIndex == 1) {
        // 市级改变了
        const Sid = e.value[1].Key;
        this.addressQuery.FilterGroup.Rules[1].Value = Sid;
        let res1 = await getAddressList(this.addressQuery);
        if (res1.Data.Rows.length) {
          if (isShow) {
            this.show = false;
            this.show = true;
          }
          this.columns[2] = res1.Data.Rows;
        }
      }
    },
    close() {
      this.show = false;
    },
    confirm(e) {
      this.query.Province = e.value[0].Key;
      this.query.ProvinceName = e.value[0].Value;
      if (e.value[2]) {
        this.query.City = e.value[1].Key;
        this.query.County = e.value[2].Key;
        this.query.CityName = e.value[1].Value;
        this.query.CountyName = e.value[2].Value;
        this.addressTogeter =
          e.value[0].Value + e.value[1].Value + e.value[2].Value;
      } else {
        this.query.City = e.value[0].Key;
        this.query.County = e.value[1].Key;
        this.query.CityName = e.value[0].Value;
        this.query.CountyName = e.value[1].Value;
        this.addressTogeter =
          e.value[0].Value + e.value[0].Value + e.value[1].Value;
      }
      this.show = false;
    },
    getLocalTions() {
      uni.getLocation({
        type: 'wgs84',
        success: (res) => {
          if (res.errMsg == 'getLocation:ok') {
            let prams = res.longitude + ',' + res.latitude;
            this.getLocaInfoDetail(prams);
          }
        },
        fail: (err) => {},
      });
    },
    async getLocaInfoDetail(prams) {
      let res = await getLocaDetail(prams);
      this.query.ProvinceName = res.province;
      this.query.CityName = res.city;
      this.query.CountyName = res.district;
      this.addressTogeter =
        res.province + res.city + (res.district ? res.district : '');
      if (res.province) {
        const provinceId = this.columns[0].filter(
          (v) => v.Value === res.province
        )[0].Key;
        const provinceIdIndex = this.columns[0].findIndex(
          (v) => v.Value === res.province
        );
        if (provinceIdIndex > -1) {
          this.defaultPickerIndex[0] = provinceIdIndex;
          await this.changeHandler(
            {
              columnIndex: 0,
              value: [this.columns[0][provinceIdIndex]],
            },
            false
          );
        }
        this.query.Province = provinceId;
        if (provinceId) {
          this.addressQuery.FilterGroup.Rules[1].Value = provinceId;
          let res2 = await getAddressList(this.addressQuery);
          if (res2.Data.Total > 0) {
            const cityId = res2.Data.Rows.filter((v) => v.Value === res.city)[0]
              .Key;
            const cityIdIndex = this.columns[1].findIndex(
              (v) => v.Value === res.city
            );
            if (cityIdIndex > -1) {
              this.defaultPickerIndex[1] = cityIdIndex;
              await this.changeHandler(
                {
                  columnIndex: 1,
                  value: ['', this.columns[1][cityIdIndex]],
                },
                false
              );
            }
            this.query.City = cityId;
            if (cityId) {
              this.addressQuery.FilterGroup.Rules[1].Value = cityId;
              let res3 = await getAddressList(this.addressQuery);
              if (res3.Data.Total > 0) {
                const countyId = res3.Data.Rows.filter(
                  (v) => v.Value === res.district
                )[0].Key;
                const countyIdIndex = this.columns[2].findIndex(
                  (v) => v.Value === res.district
                );
                if (countyIdIndex > -1) {
                  this.defaultPickerIndex[2] = countyIdIndex;
                  await this.changeHandler(
                    {
                      columnIndex: 2,
                      value: ['', '', this.columns[2][countyIdIndex]],
                    },
                    false
                  );
                }
                this.query.County = countyId;
              }
            }
          }
        }
      }
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  padding-top: 32rpx;

  /deep/ .u-cell {
    background-color: white;
  }

  /deep/ .u-textarea__field {
    background-color: #fafafa !important;
    padding: 10px;
  }

  /deep/ .u-border {
    border-style: none !important;
    border-color: #fafafa !important;
  }

  /deep/ .u-textarea {
    padding: 0 20rpx;
  }
}
</style>
