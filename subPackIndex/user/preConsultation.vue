<template>
  <view class="container">
    <view class="container-top">
      患者您好，请填写您的基本信息，就诊时医生将为您提供更好的诊疗服务
    </view>
    <view class="container-chat">
      <u-list
        :height="scrollHeight"
        :showScrollbar="false"
        :scrollTop="listScrollTop"
        :scrollWithAnimation="true"
        :customStyle="{
          padding: '0 32rpx',
          backgroundColor: '#F7F7F7',
          flex: 1,
        }"
        :enableFlex="true"
      >
        <!-- 消息体 -->
        <u-list-item v-for="(message, index) in messageList" :key="index">
          <!-- 患者消息 -->
          <view class="session-custom-cell" v-if="message.role === 'patient'">
            <view class="time-view" v-if="message.showTime">
              <text>{{ message.sendTime }}</text>
            </view>
            <view class="session-body-view session-body-view--right">
              <MpAvatar
                :src="patientAvatar"
                width="40px"
                height="40px"
              ></MpAvatar>
              <view class="session-content-view session-content-view--right">
                <view
                  class="session-content-body-view session-content-body-view--right"
                >
                  <view class="session-custom-text-sender-cell">
                    <text class="custom-text">{{ message.text }}</text>
                  </view>
                </view>
                <!-- 重新描述按钮 - 仅对最新患者消息显示，放在文字下面 -->
                <view
                  v-if="index === latestPatientMessageIndex"
                  class="redescribe-button"
                  @click="handleRedescribe(message, index)"
                >
                  <image
                    class="redescribe-icon"
                    src="/subPackIndex/static/icon-back.png"
                  />
                  <text class="redescribe-text">重新描述</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 医生消息 -->
          <view
            class="session-custom-cell"
            v-else-if="message.role === 'doctor'"
          >
            <view class="time-view" v-if="message.showTime">
              <text>{{ message.sendTime }}</text>
            </view>
            <view class="session-body-view">
              <MpAvatar
                :src="doctorAvatar"
                width="40px"
                height="40px"
              ></MpAvatar>
              <view class="session-content-view">
                <view class="session-content-body-view">
                  <view class="session-custom-text-receiver-cell">
                    <text class="custom-text" v-if="message.type === 'text'">{{
                      message.text
                    }}</text>
                    <!-- 语音播放按钮 - 仅对医生的文本消息显示 -->
                    <view
                      v-if="message.type === 'text'"
                      class="voice-play-button"
                      :class="{
                        'voice-play-button--playing':
                          currentPlayingIndex === index,
                      }"
                      @click="handleVoicePlay(message, index)"
                    >
                      <image
                        class="voice-play-icon"
                        :src="
                          currentPlayingIndex === index
                            ? '/subPackIndex/static/voice.gif'
                            : '/subPackIndex/static/voice.png'
                        "
                      />
                      <text class="voice-play-text">
                        {{
                          currentPlayingIndex === index
                            ? '停止播放'
                            : '语音播放'
                        }}
                      </text>
                    </view>
                    <view
                      class="options-container"
                      v-if="message.type === 'objective-options'"
                    >
                      <view
                        class="option-item"
                        v-for="(option, optionIndex) in message.options"
                        :key="optionIndex"
                        @click="handleOptionClick(option, message, index)"
                        :class="{ 'option-item--selected': option.selected }"
                      >
                        <text class="option-text">{{ option.text }}</text>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </u-list-item>
      </u-list>
    </view>
    <!-- 键盘输入区域 -->
    <view
      v-if="!dialogueEnded"
      class="container-keyboard"
      :style="{
        transform: isKeyboardShow
          ? `translateY(-${actualKeyboardHeight}px)`
          : 'translateY(0)',
        transition: 'transform 0.25s ease-out',
      }"
    >
      <!-- 语音输入模式 -->
      <view class="voice-mode" v-if="isVoiceMode">
        <view class="voice-toggle-left" @click="toggleInputMode">
          <image
            class="voice-icon"
            src="/subPackIndex/static/icon-keyboard.png"
          />
        </view>
        <view
          class="voice-button"
          :class="{ 'voice-button--recording': isRecording }"
          @touchstart="startVoiceRecord"
          @touchend="endVoiceRecord"
          @touchcancel="cancelVoiceRecord"
        >
          <text class="voice-text">按住 说话</text>
        </view>
      </view>

      <!-- 文本输入模式 -->
      <view class="text-mode" v-else>
        <view class="text-toggle-left" @click="toggleInputMode">
          <image class="voice-icon" src="/subPackIndex/static/icon-voice.png" />
        </view>
        <view class="input-wrapper">
          <textarea
            v-model="inputText"
            class="text-input"
            placeholder="请输入..."
            :auto-height="true"
            :show-confirm-bar="false"
            :adjust-position="false"
            :focus="textareaFocus"
            @focus="onInputFocus"
            @blur="onInputBlur"
            @input="onTextInput"
            @keyboardheightchange="onKeyboardHeightChange"
          />
        </view>
        <view
          class="confirm-button"
          :class="{ 'confirm-button--active': inputText.trim() }"
          @click="sendTextMessage"
        >
          <text class="confirm-text">确认</text>
        </view>
      </view>
    </view>

    <!-- 录音弹窗 -->
    <view
      class="record-modal"
      v-if="showRecordModal"
      @click="cancelVoiceRecord"
    >
      <view class="record-content" @click.stop>
        <view class="record-animation">
          <view class="record-circle">
            <view class="record-inner-circle">
              <image
                class="record-mic-icon"
                src="/static/icons/mic-white.png"
              />
            </view>
          </view>
          <view class="record-waves">
            <view class="wave" v-for="n in 3" :key="n"></view>
          </view>
        </view>
        <view class="record-text">{{ recordText }}</view>
        <view class="record-tip">松手发送，点击空白区域取消</view>
      </view>
    </view>
    <!-- 身体部位选择遮罩层 -->
    <view class="container-bodyDialog" v-if="openBodyPartDialog">
      <!-- 遮罩背景 -->
      <view class="mask-overlay" @click="closeMask">
        <!-- 遮罩内容区域 -->
        <view class="mask-content" @click.stop>
          <view class="mask-header">
            <text class="mask-title"
              >这个不舒服具体在身体哪个地方呢?请在下方图片中点击您最不舒服的部位。(多选）</text
            >
          </view>
          <view class="mask-body" @click.stop>
            <image
              class="mask-body-img"
              src="/subPackIndex/static/icon-body.png"
            ></image>
            <block v-for="(item, index) in bodyPoints" :key="index">
              <image
                @click.stop="handlePointClick(item.Ext, $event)"
                :style="{ top: item.Top, left: item.Left }"
                :src="
                  item.IsSelect
                    ? '/subPackIndex/static/icon-select.png'
                    : '/subPackIndex/static/icon-default.png'
                "
                class="mask-body-point"
              ></image>
            </block>
            <!-- 这里可以放置身体图片或其他内容 -->
          </view>
          <view class="mask-footer">
            <view class="confirm-btn" @click.stop="confirmSelection">
              <text class="btn-text">确认</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
const app = getApp();
import request from '@/libs/request';
import { guid } from '@/utils/validate';
import bodyPoints from './data/bodySelect.js';
import { getDocInfoMation } from '@/api/consult.js';
import dayjs from 'dayjs';
import MpAvatar from '@/components/mp-avatar/mp-avatar.vue';

// 引入微信同声传译插件
const plugin = requirePlugin('WechatSI');
const manager = plugin.getRecordRecognitionManager();
const WechatSI = plugin;

export default {
  components: {
    MpAvatar,
  },
  data() {
    return {
      // 预问诊医生Id
      doctorId: '',
      // 消息列表， 其中role === 'patient' 为患者消息（在页面的右侧） role === 'doctor' 为医生发送的消息（在页面的左侧）
      // type 表示消息类型（text、objective-options等）
      messageList: [],
      // 滚动区域高度
      scrollHeight: 0,
      // 列表滚动位置
      listScrollTop: 0,
      // 患者头像
      patientAvatar: '',
      // 患者姓名
      patientName: '',
      // 医生头像
      doctorAvatar: '',
      // 医生姓名
      doctorName: '',
      // 键盘相关
      keyboardHeight: 120, // 键盘区域高度
      actualKeyboardHeight: 0, // 实际键盘弹出高度
      isKeyboardShow: false, // 键盘是否弹出
      inputText: '', // 输入文本
      isVoiceMode: true, // 是否为语音模式，默认为语音模式
      isRecording: false, // 是否正在录音
      // 录音弹窗相关
      showRecordModal: false, // 是否显示录音弹窗
      recordText: '正在聆听...', // 录音提示文字
      recognizedText: '', // 识别到的文字
      // 语音播放相关
      innerAudioContext: null, // 音频播放器
      currentPlayingIndex: -1, // 当前播放的消息索引
      dialogueEnded: false, // 整个流程是否结束
      textareaFocus: false, // 输入框是否聚焦
      openBodyPartDialog: false, // 是否打开弹窗 选择部位 只有当questionId === 3 的时候 才会有机会打开它
      bodyPoints: bodyPoints, // 选择部位
    };
  },
  computed: {
    // 获取最新患者消息的索引
    latestPatientMessageIndex() {
      for (let i = this.messageList.length - 1; i >= 0; i--) {
        if (this.messageList[i].role === 'patient') {
          return i;
        }
      }
      return -1;
    },
  },
  watch: {
    // 监听消息列表变化，自动滚动到底部
    messageList: {
      handler() {
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      },
      deep: true,
    },
  },
  async onLoad(option) {
    await getApp().isLaunchFinish();
    this.doctorId = option.doctorId;
    // 获取医生基本信息
    this.onGetDoctorInfo(this.doctorId);
    this.initPageData();
    // 为messageList中填充消息
    this.onSetMessageList();
  },
  onReady() {
    this.calculateScrollHeight();
    this.initRecord();
    this.initAudioPlayer();
    // 页面准备完成后滚动到底部
    this.$nextTick(() => {
      this.scrollToBottom();
    });
  },
  onUnload() {
    // 页面卸载时销毁音频播放器
    if (this.innerAudioContext) {
      this.innerAudioContext.destroy();
      this.innerAudioContext = null;
    }
  },
  methods: {
    async handleSendMessageToLocalServeTest(question, answer, context = null) {
      const token = uni.getStorageInfoSync('token');
      const params = {
        SessionId: guid(),
        Question: question,
        Answer: answer,
        Context: context,
      };
      const res = await request.post(
        'https://suited-living-quail.ngrok-free.app/api/bailian/preconsult',
        params,
        {
          timeout: 600000,
        }
      );
      console.log('res', res);
    },
    async onGetDoctorInfo() {
      const res = await getDocInfoMation({
        doctorId: this.doctorId,
      });
      if (res.Type === 200) {
        this.doctorAvatar =
          res.Data.Doctor.HeadImg || '/static/common/default-avatar.png';
        this.doctorName = res.Data.Doctor.Name;
      }
    },
    // 初始化页面数据
    initPageData() {
      const userInfo = app.globalData.userInfo;
      this.patientAvatar =
        userInfo.HeadImg || '/static/common/default-avatar.png';
      this.patientName = userInfo.Name || '患者';
    },

    // 计算滚动区域高度
    calculateScrollHeight() {
      const systemInfo = uni.getSystemInfoSync();
      const windowHeight = systemInfo.windowHeight;
      // 减去顶部提示区域高度(108rpx转px) 和底部键盘区域高度
      const topHeight = uni.upx2px(108);
      const keyboardAreaHeight = this.isKeyboardShow
        ? uni.upx2px(120) + this.actualKeyboardHeight
        : uni.upx2px(120);

      this.scrollHeight = windowHeight - topHeight - keyboardAreaHeight;
    },

    // 滚动到聊天底部
    scrollToBottom() {
      // 先重置滚动位置，然后设置一个大值确保滚动到底部
      this.listScrollTop = 0;
      this.$nextTick(() => {
        // 使用较大的数值确保滚动到底部
        this.listScrollTop = 999999;
      });
    },

    // 添加消息
    onSetMessageList() {
      const userInfo = app.globalData.userInfo;
      const currentTime = dayjs(new Date()).format('YYYY-MM-DD HH:mm');

      // 添加患者基本信息消息
      this.messageList.push({
        role: 'patient',
        type: 'text',
        text: userInfo.Name + '，' + userInfo.Sex + '，' + userInfo.Age + '岁',
        sendTime: currentTime,
        showTime: true,
      });

      // 添加医生回复消息
      this.messageList.push({
        role: 'doctor',
        type: 'text',
        text: '您好！我是医生小助手，帮您提前将病情传送给医生。请问您这次来看病的主要目的是什么？',
        sendTime: currentTime,
        showTime: false,
        questionId: 1,
      });

      // 添加选择项消息
      this.messageList.push({
        role: 'doctor',
        type: 'objective-options',
        text: '',
        sendTime: currentTime,
        showTime: false,
        options: [
          { text: '初诊(第一次来看这个问题)', selected: false },
          { text: '复诊(回来复查/检查报告解读/做治疗开药等)', selected: false },
          { text: '预约/开具检查', selected: false },
          { text: '开具医疗证明(病假条/诊断证明/健康证明等)', selected: false },
          { text: '办理手续(转诊/医保审批等)', selected: false },
          { text: '其他', selected: false },
        ],
        questionId: 1,
      });
    },

    // 处理选择项点击
    handleOptionClick(option, message, messageIndex) {
      // 重置所有选项的选中状态
      message.options.forEach((opt) => {
        opt.selected = false;
      });

      // 设置当前选项为选中状态
      option.selected = true;

      // 这里可以添加后续逻辑，比如发送选择结果到服务器
      console.log('用户选择了:', option.text);
      const currentTime = dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss');
      switch (option.text) {
        case '初诊(第一次来看这个问题)':
          this.messageList.push({
            role: 'doctor',
            type: 'text',
            text: '现在，请描述一下您哪里不舒服，多长时间了？（您本次就诊的主要想解决的问题）比如，左边膝盖痛了两周了。请长按下方按钮语音描述。',
            sendTime: currentTime,
            showTime: false,
            questionId: 2,
          });
          // 删除包含选项的医生消息
          this.messageList.splice(messageIndex, 1);
          break;
        case '复诊(回来复查/检查报告解读/做治疗开药等)':
          this.messageList.push({
            role: 'doctor',
            type: 'text',
            text: '好的，明白您是来复查，已反馈给医生，请耐心等待。',
            sendTime: currentTime,
            showTime: false,
          });

          // 删除包含选项的医生消息
          this.messageList.splice(messageIndex, 1);
          this.dialogueEnded = true;
          break;
        case '预约/开具检查':
          this.messageList.push({
            role: 'doctor',
            type: 'text', // 语音消息
            text: '好的明白，您的需求已反馈给医生，请耐心等待。',
            sendTime: currentTime,
            showTime: false,
          });

          // 删除包含选项的医生消息
          this.messageList.splice(messageIndex, 1);
          this.dialogueEnded = true;
          break;
        case '开具医疗证明(病假条/诊断证明/健康证明等)':
          this.messageList.push({
            role: 'doctor',
            type: 'text',
            text: '好的明白，您的需求已反馈给医生，请耐心等待。',
            sendTime: currentTime,
            showTime: false,
          });

          // 删除包含选项的医生消息
          this.messageList.splice(messageIndex, 1);
          this.dialogueEnded = true;
          break;
        case '办理手续(转诊/医保审批等)':
          this.messageList.push({
            role: 'doctor',
            type: 'text',
            text: '好的明白，您的需求已反馈给医生，请耐心等待。',
            sendTime: currentTime,
            showTime: false,
          });

          // 删除包含选项的医生消息
          this.messageList.splice(messageIndex, 1);
          this.dialogueEnded = true;
          break;
        case '其他':
          this.isVoiceMode = false;
          this.$nextTick(() => {
            this.textareaFocus = true;
          });
          break;
        default:
          break;
      }
    },

    // 处理语音播放功能
    handleVoicePlay(message, messageIndex) {
      console.log('语音播放消息:', message);

      // 如果当前正在播放同一条消息，则停止播放
      if (this.currentPlayingIndex === messageIndex) {
        this.stopVoicePlay();
        return;
      }

      // 停止当前播放的音频
      this.stopVoicePlay();

      // 检查消息文本是否存在
      if (!message.text || !message.text.trim()) {
        uni.showToast({
          title: '消息内容为空',
          icon: 'none',
          duration: 2000,
        });
        return;
      }

      // 设置当前播放状态
      this.currentPlayingIndex = messageIndex;

      // 调用微信同声传译插件进行文字转语音
      WechatSI.textToSpeech({
        lang: 'zh_CN',
        content: message.text,
        success: (res) => {
          console.log('TTS成功:', res);
          if (res.retcode !== 0) {
            console.error('TTS转换失败:', res);
            this.currentPlayingIndex = -1;
            uni.showToast({
              title: '语音转换失败',
              icon: 'none',
              duration: 2000,
            });
            return;
          }

          // 播放转换后的音频
          this.innerAudioContext.src = res.filename;
          this.innerAudioContext.play();
        },
        fail: (error) => {
          console.error('TTS调用失败:', error);
          this.currentPlayingIndex = -1;
          uni.showToast({
            title: '语音播放失败',
            icon: 'none',
            duration: 2000,
          });
        },
      });
    },

    // 停止语音播放
    stopVoicePlay() {
      if (this.innerAudioContext) {
        this.innerAudioContext.stop();
      }
      this.currentPlayingIndex = -1;
    },

    // 初始化音频播放器
    initAudioPlayer() {
      this.innerAudioContext = uni.createInnerAudioContext();

      // 播放开始事件
      this.innerAudioContext.onPlay(() => {
        console.log('开始播放语音');
      });

      // 播放结束事件
      this.innerAudioContext.onEnded(() => {
        console.log('语音播放完成');
        this.currentPlayingIndex = -1;
      });

      // 播放停止事件
      this.innerAudioContext.onStop(() => {
        console.log('语音播放停止');
        this.currentPlayingIndex = -1;
      });

      // 播放错误事件
      this.innerAudioContext.onError((error) => {
        console.error('音频播放错误:', error);
        this.currentPlayingIndex = -1;
        uni.showToast({
          title: '音频播放失败',
          icon: 'none',
          duration: 2000,
        });
      });
    },

    // 处理重新描述功能
    handleRedescribe(message, messageIndex) {
      console.log('重新描述消息:', message);

      // 1. 将消息文案放入textarea
      this.inputText = message.text;

      // 2. 切换到文本输入模式
      this.isVoiceMode = false;

      // 3. 删除该消息之后的所有医生消息，以及该患者消息本身
      // 找到该消息之后的所有消息，删除医生类型的消息
      const messagesToRemove = [];

      // 添加该患者消息本身到删除列表
      messagesToRemove.push(messageIndex);

      // 添加该消息之后的所有医生消息到删除列表
      for (let i = messageIndex + 1; i < this.messageList.length; i++) {
        if (this.messageList[i].role === 'doctor') {
          messagesToRemove.push(i);
        }
      }

      // 从后往前删除，避免索引变化问题
      for (let i = messagesToRemove.length - 1; i >= 0; i--) {
        this.messageList.splice(messagesToRemove[i], 1);
      }

      this.dialogueEnded = false;
      // 4. 聚焦textarea并弹出键盘
      this.$nextTick(() => {
        this.textareaFocus = true;
      });

      console.log('重新描述处理完成');
    },

    // 键盘相关方法
    // 切换输入模式
    toggleInputMode() {
      this.isVoiceMode = !this.isVoiceMode;
    },

    // 输入框获得焦点
    onInputFocus() {
      console.log('输入框获得焦点');
      this.isKeyboardShow = true;
      this.textareaFocus = true;
    },

    // 输入框失去焦点
    onInputBlur() {
      console.log('输入框失去焦点');
      this.isKeyboardShow = false;
      this.actualKeyboardHeight = 0;
      this.textareaFocus = false;
    },

    // 文本输入
    onTextInput(e) {
      this.inputText = e.detail.value;
    },

    // 键盘高度变化
    onKeyboardHeightChange(e) {
      const height = e.detail.height;
      this.actualKeyboardHeight = height;
      this.isKeyboardShow = height > 0;

      // 更新键盘区域高度
      this.keyboardHeight = Math.max(100, height + 20);
      this.calculateScrollHeight();

      // 键盘弹出时滚动到底部
      if (height > 0) {
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      }
    },

    // 发送文本消息
    sendTextMessage() {
      if (!this.inputText.trim()) return;

      const currentTime = dayjs(new Date()).format('YYYY-MM-DD HH:mm');

      // 添加患者消息
      this.messageList.push({
        role: 'patient',
        type: 'text',
        text: this.inputText.trim(),
        sendTime: currentTime,
        showTime: false,
      });

      // 清空输入框
      this.inputText = '';

      // 滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom();

        // 重新聚焦textarea，保持键盘不关闭
        // setTimeout(() => {
        //   this.textareaFocus = true;
        // }, 100);
      });

      this.handleSendMessageToLocalServeTest(
        '请问您这次来看病的主要目的是什么？',
        '我第一次来',
        null
      );

      // // 判断患者回答的是哪一问题 questionId
      // let questionId = -1;
      // const doctorMessage = this.messageList.filter(
      //   (item) => item.role === 'doctor'
      // );
      // if (doctorMessage.length) {
      //   questionId = doctorMessage[doctorMessage.length - 1].questionId;
      // }
      // switch (questionId) {
      //   case 2:
      //     this.messageList.push({
      //       role: 'doctor',
      //       type: 'text',
      //       text: '这个不舒服具体在身体哪个地方呢？请在下方图片中点击您最不舒服的部位。',
      //       sendTime: currentTime,
      //       showTime: false,
      //       questionId: questionId + 1,
      //     });
      //     // 需要全局弹出选择部位的图片和确定按钮
      //     this.openBodyPartDialog = true;
      //     break;
      //   case 4:
      //     this.messageList.push(
      //       {
      //         role: 'doctor',
      //         type: 'text',
      //         text: '谢谢，走路会加重，休息能缓解。',
      //         sendTime: currentTime,
      //         showTime: false,
      //         questionId: questionId + 1,
      //       },
      //       {
      //         role: 'doctor',
      //         type: 'text',
      //         text: '另外，您当前或以前还有其他健康问题吗？比如，高血压、糖尿病、心脏问题等？请长按下方按钮语音描述。',
      //         sendTime: currentTime,
      //         showTime: false,
      //         questionId: questionId + 1,
      //       }
      //     );
      //     break;
      //   case 5:
      //     this.messageList.push({
      //       role: 'doctor',
      //       type: 'text',
      //       text: '感谢分享，这些信息很重要。问诊结束，信息已全部转达给医生，请耐心等待。',
      //       sendTime: currentTime,
      //       showTime: false,
      //       questionId: questionId + 1,
      //     });
      //     this.dialogueEnded = true;
      //     break;

      //   default:
      //     break;
      // }

      // // 这里可以添加发送到服务器的逻辑
      // console.log('发送文本消息');
    },

    // 发送语音识别消息
    sendVoiceMessage(text) {
      if (!text || !text.trim()) return;

      const currentTime = dayjs(new Date()).format('YYYY-MM-DD HH:mm');

      // 添加患者语音消息
      this.messageList.push({
        type: 'patient',
        text: text.trim(),
        sendTime: currentTime,
        showTime: false,
        isVoiceMessage: true, // 标记为语音消息
      });

      // 滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom();
      });

      // 显示发送成功提示
      uni.showToast({
        title: '语音消息已发送',
        icon: 'success',
        duration: 1500,
      });

      // 这里可以添加发送到服务器的逻辑
      console.log('发送语音识别消息:', text);
    },

    // 初始化录音服务
    initRecord() {
      // 有新的识别内容返回，则会调用此事件
      manager.onRecognize = (res) => {
        console.log('onRecognize', res);
        let text = res.result;
        if (text) {
          this.recognizedText = text;
          this.recordText = text || '正在聆听...';
        }
      };

      // 识别结束事件
      manager.onStop = (res) => {
        console.log('onStop', res);
        let text = res.result;
        if (text && text.trim()) {
          // 直接发送识别到的文字消息
          const recognizedText = text.trim();
          console.log('识别结果:', recognizedText);

          // 调用发送消息方法，直接发送语音识别的文字
          this.sendVoiceMessage(recognizedText);
        } else {
          console.log('没有识别到有效内容');
          uni.showToast({
            title: '没有识别到内容，请重试',
            icon: 'none',
            duration: 2000,
          });
        }

        // 重置录音状态
        this.showRecordModal = false;
        this.isRecording = false;
        this.recordText = '正在聆听...';
        this.recognizedText = '';
      };

      // 录音开始事件
      manager.onStart = () => {
        console.log('录音开始');
        this.recordText = '正在聆听...';
      };

      // 录音错误事件
      manager.onError = (res) => {
        console.error('录音错误:', res);
        this.showRecordModal = false;
        this.isRecording = false;
        uni.showToast({
          title: '录音失败，请重试',
          icon: 'none',
        });
      };
    },

    // 检查录音权限
    async checkRecordAuth() {
      return new Promise((resolve) => {
        uni.getSetting({
          success: (res) => {
            if (res.authSetting['scope.record'] === false) {
              // 用户之前拒绝了权限，需要引导用户去设置页面开启
              uni.showModal({
                title: '提示',
                content: '检测到您没打开录音功能权限，是否去设置打开？',
                confirmText: '确认',
                cancelText: '取消',
                success: (modalRes) => {
                  if (modalRes.confirm) {
                    uni.openSetting({
                      success: (settingRes) => {
                        if (settingRes.authSetting['scope.record']) {
                          resolve(true);
                        } else {
                          resolve(false);
                        }
                      },
                      fail: () => resolve(false),
                    });
                  } else {
                    resolve(false);
                  }
                },
              });
            } else if (res.authSetting['scope.record'] === undefined) {
              // 用户还没有授权过，可以直接调用授权
              uni.authorize({
                scope: 'scope.record',
                success: () => resolve(true),
                fail: () => resolve(false),
              });
            } else {
              // 用户已经授权
              resolve(true);
            }
          },
          fail: () => resolve(false),
        });
      });
    },

    // 开始语音录制
    async startVoiceRecord() {
      console.log('开始语音录制');

      // 检查录音权限
      const hasAuth = await this.checkRecordAuth();
      if (!hasAuth) {
        uni.showToast({
          title: '需要录音权限才能使用语音功能',
          icon: 'none',
        });
        return;
      }

      this.isRecording = true;
      this.showRecordModal = true;
      this.recordText = '正在聆听...';
      this.recognizedText = '';

      // 开始录音识别
      manager.start({
        lang: 'zh_CN',
      });
    },

    // 结束语音录制
    endVoiceRecord() {
      console.log('结束语音录制');
      if (this.isRecording) {
        manager.stop();
      }
    },

    // 取消语音录制
    cancelVoiceRecord() {
      console.log('取消语音录制');
      this.showRecordModal = false;
      this.isRecording = false;
      this.recordText = '正在聆听...';
      this.recognizedText = '';
      if (this.isRecording) {
        manager.stop();
      }
    },

    // 关闭遮罩层
    closeMask() {
      // 只有点击遮罩背景时才关闭，身体部位点击不应该关闭
      this.openBodyPartDialog = false;
    },

    // 处理身体部位点击
    handlePointClick(extId, event) {
      console.log('点击身体部位:', extId);

      // 只阻止事件冒泡，不阻止默认行为，确保不会触发遮罩关闭
      if (event) {
        event.stopPropagation();
      }

      // 找到对应的身体部位并切换选中状态
      const pointIndex = this.bodyPoints.findIndex(
        (point) => point.Ext === extId
      );
      if (pointIndex !== -1) {
        // 使用 $set 确保响应式更新
        this.$set(
          this.bodyPoints[pointIndex],
          'IsSelect',
          !this.bodyPoints[pointIndex].IsSelect
        );

        console.log('身体部位选中状态已更新:', this.bodyPoints[pointIndex]);
      }
    },

    // 确认选择
    confirmSelection() {
      // 获取所有选中的身体部位
      const selectedPoints = this.bodyPoints.filter((point) => point.IsSelect);
      if (!selectedPoints.length) {
        uni.showToast({
          title: '请选择至少一个身体部位',
          icon: 'none',
        });
        return;
      }

      // 可以在这里添加后续的消息处理逻辑
      const currentTime = dayjs(new Date()).format('YYYY-MM-DD HH:mm');
      // 构建选中部位的描述文本
      const selectedNames = selectedPoints
        .map((point) => {
          // 优先使用 PositiveName，如果没有则使用 BackName，都没有则使用 Ext 编号
          return point.PositiveName || point.BackName || `部位${point.Ext}`;
        })
        .join('、');
      let questionId = -1;
      const doctorMessage = this.messageList.filter(
        (item) => item.role === 'doctor'
      );
      if (doctorMessage.length) {
        questionId = doctorMessage[doctorMessage.length - 1].questionId;
      }
      this.messageList.push(
        {
          role: 'doctor',
          type: 'text',
          text: `好的，我已经了解您选择的不舒服部位是：${selectedNames}。`,
          sendTime: currentTime,
          showTime: false,
          questionId: questionId + 1,
        },
        {
          role: 'doctor',
          type: 'text',
          text: `日常生活有什么活动会让这个不舒服加重，什么情况下会有所减轻呢？请长按下方按钮语音描述。`,
          sendTime: currentTime,
          showTime: false,
          questionId: questionId + 1,
        }
      );
      this.openBodyPartDialog = false;
    },
  },
};
</script>
<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;

  &-top {
    height: 108rpx;
    background: #f0fffc;
    font-weight: 500;
    font-size: 26rpx;
    color: #39ccaf;
    padding: 20rpx 32rpx;
    display: flex;
    align-items: center;
  }

  &-chat {
    flex: 1;
    background: #f7f7f7;
    overflow: hidden;
  }

  &-keyboard {
    min-height: 120rpx;
    background: #ffffff;
    border-top: 1rpx solid #e5e5e5;
    padding: 20rpx 26rpx 40rpx 26rpx;
    display: flex;
    align-items: flex-end;
    position: relative;
    z-index: 100;
  }
}

// 聊天消息样式
.session-custom-cell {
  margin-bottom: 32rpx;
}

.time-view {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 16rpx 0;

  text {
    font-size: 28rpx;
    color: #999999;
  }
}

.session-body-view {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  margin-bottom: 16rpx;

  &--right {
    flex-direction: row-reverse;
  }
}

.session-content-view {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-left: 16rpx;
  flex: 1;

  &--right {
    align-items: flex-end;
    margin-left: 0;
    margin-right: 16rpx;
  }
}

.name {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 8rpx;
}

.session-content-body-view {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  width: 100%;

  &--right {
    flex-direction: row-reverse;
  }
}

.session-custom-text-sender-cell {
  background: #29b7a3;
  border-radius: 16rpx;
  padding: 24rpx;
  max-width: 500rpx;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    right: -12rpx;
    top: 20rpx;
    width: 0;
    height: 0;
    border-left: 12rpx solid #29b7a3;
    border-top: 8rpx solid transparent;
    border-bottom: 8rpx solid transparent;
  }
}

.session-custom-text-receiver-cell {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  max-width: 500rpx;
  position: relative;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

  &::before {
    content: '';
    position: absolute;
    left: -12rpx;
    top: 20rpx;
    width: 0;
    height: 0;
    border-right: 12rpx solid #ffffff;
    border-top: 8rpx solid transparent;
    border-bottom: 8rpx solid transparent;
  }
}

.custom-text {
  font-size: 32rpx;
  color: #333333;
  line-height: 1.5;
  word-wrap: break-word;
  word-break: break-all;
}

.session-custom-text-sender-cell .custom-text {
  color: #ffffff;
}

// 重新描述按钮样式
.redescribe-button {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 8rpx;
  padding: 8rpx 0;
  transition: all 0.3s ease;

  &:active {
    opacity: 0.7;
  }
}

.redescribe-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}

.redescribe-text {
  font-size: 24rpx;
  color: #666666;
  line-height: 1;
}

// 语音播放按钮样式
.voice-play-button {
  width: 178rpx;
  height: 60rpx;
  background: #ffffff;
  border-radius: 8rpx;
  border: 1px solid #29b7a3;
  padding: 16rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-top: 8rpx;

  &--playing {
    .voice-play-text {
      color: #ff6b6b;
      font-weight: 500;
    }

    .voice-play-icon {
      // animation: voice-playing 1s ease-in-out infinite;
      width: 28rpx;
      height: 28rpx;
    }
  }
}

.voice-play-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
}

.voice-play-text {
  font-size: 24rpx;
  color: #29b7a3;
  line-height: 1;
}

// 选择项样式
.options-container {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.option-item {
  border: 2rpx solid #e9ecef;
  padding: 16rpx 20rpx;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f5f6fa;
  border-radius: 8rpx;
  color: #333333;

  &--selected {
    background: #dff4f1;
    border: 1px solid #29b7a3;

    .option-text {
      color: #29b7a3;
      font-weight: 500;
    }
  }
}

.option-text {
  font-size: 30rpx;
  color: #333333;
  line-height: 1.4;
}

// 键盘输入区域样式 - 严格按照UI设计
.voice-mode {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 20rpx;
}

.voice-toggle-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.voice-icon {
  width: 42rpx;
  height: 42rpx;
}

.voice-button {
  flex: 1;
  height: 80rpx;
  background: #f8f7f7;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #e0e0e0;

  &--recording {
    background: #29b7a3;
    border-color: #29b7a3;

    .voice-text {
      color: #ffffff;
    }
  }
}

.voice-text {
  font-size: 32rpx;
  color: #333333;
  font-weight: 400;
}

.text-mode {
  display: flex;
  align-items: flex-end;
  width: 100%;
  gap: 20rpx;
}

.text-toggle-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.input-wrapper {
  flex: 1;
  min-height: 80rpx;
  max-height: 200rpx;
  background: #f5f5f5;
  border-radius: 8rpx;
  border: 2rpx solid #e0e0e0;
  display: flex;
  align-items: flex-start;
  padding: 16rpx 12rpx;
}

.text-input {
  width: 100%;
  min-height: 40rpx;
  border: none;
  outline: none;
  background: transparent;
  font-size: 32rpx;
  color: #333333;
  line-height: 1.4;
  resize: none;
}

.confirm-button {
  width: 120rpx;
  height: 80rpx;
  background: #cccccc;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  &--active {
    background: #29b7a3;

    .confirm-text {
      color: #ffffff;
    }
  }
}

.confirm-text {
  font-size: 32rpx;
  color: #999999;
  font-weight: 400;
}

// 录音弹窗样式 - 参考微信设计
.record-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.record-content {
  width: 400rpx;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 24rpx;
  padding: 60rpx 40rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.record-animation {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}

.record-circle {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: #29b7a3;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  animation: record-pulse 2s ease-in-out infinite;
}

.record-inner-circle {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.record-mic-icon {
  width: 40rpx;
  height: 40rpx;
}

.record-waves {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.record-waves .wave {
  position: absolute;
  width: 120rpx;
  height: 120rpx;
  border: 4rpx solid rgba(41, 183, 163, 0.3);
  border-radius: 50%;
  animation: wave-expand 2s ease-out infinite;
}

.record-waves .wave:nth-child(1) {
  animation-delay: 0s;
}

.record-waves .wave:nth-child(2) {
  animation-delay: 0.7s;
}

.record-waves .wave:nth-child(3) {
  animation-delay: 1.4s;
}

.record-text {
  font-size: 32rpx;
  color: #ffffff;
  text-align: center;
  margin-bottom: 20rpx;
  min-height: 44rpx;
  line-height: 44rpx;
}

.record-tip {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
}

@keyframes record-pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(41, 183, 163, 0.7);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 20rpx rgba(41, 183, 163, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(41, 183, 163, 0);
  }
}

@keyframes wave-expand {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(2.5);
    opacity: 0;
  }
}

@keyframes voice-playing {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

// 遮罩层样式
.container-bodyDialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10000;
}

.mask-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.mask-content {
  width: 100%;
  height: 1100rpx;
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  animation: mask-slide-up 0.3s ease-out;
  display: flex;
  flex-direction: column;
}

.mask-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 0 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  padding-bottom: 24rpx;
  margin-bottom: 32rpx;
}

.mask-title {
  font-size: 30rpx;
  font-weight: 400;
  color: #222222;
}

.mask-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f5f5f5;
}

.close-text {
  font-size: 40rpx;
  color: #666666;
  line-height: 1;
}

.mask-body {
  position: relative;
  padding: 0 32rpx 32rpx;
  flex: 1;
  &-img {
    width: 100%;
    height: 100%;
  }
  &-point {
    position: absolute;
    width: 56rpx;
    height: 56rpx;
    z-index: 9;
  }
}

.mask-description {
  font-size: 28rpx;
  color: #666666;
  text-align: center;
  margin-bottom: 40rpx;
  display: block;
}

.body-diagram {
  width: 100%;
  height: 400rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx dashed #ddd;
}

.placeholder-text {
  font-size: 28rpx;
  color: #999999;
}

.mask-footer {
  padding: 0 32rpx 32rpx;
}

.confirm-btn {
  width: 100%;
  height: 88rpx;
  background: #29b7a3;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &:active {
    background: #248a7a;
    transform: scale(0.98);
  }
}

.btn-text {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: 500;
}

@keyframes mask-slide-up {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}
</style>
