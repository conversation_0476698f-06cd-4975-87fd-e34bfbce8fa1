<template>
  <page-meta :root-font-size="newFonSize + 'px'"></page-meta>
  <view class="">
    <view class="item-button1"> 字体大小测试 </view>
    <view class="item-button2"> 字体大小测试 </view>
    <view class="item-button3"> 字体大小测试 </view>
    <view class="item-button4"> 字体大小测试 </view>
    <view class="item-button5"> 字体大小测试 </view>
    <button type="default" @click="onFontSize(12)">小</button>
    <button type="default" @click="onFontSize(14)">默认</button>
    <button type="default" @click="onFontSize(16)">大</button>
    <button type="default" @click="onFontSize(18)">特大</button>
    <!-- <input type="text" v-model="newFonSize"/> -->
    <button type="default" @click="onFontSizeSure">确定</button>

    <view style="padding: 20rpx">
      <slideChoose
        ref="refswiper"
        :max-value="maxValue"
        ruleType="cm"
        :current="current"
        @finishRuler="finishRuler"
        :decimal="true"
      />
      <!-- <view style="font-size: 50px;text-align: center;">{{value}} cm</view> -->
      <u-input
        placeholder="后置插槽"
        v-model="value"
        @change="onInputChange"
        customStyle="width:30%;margin:0 auto;height:50px"
      >
        <template slot="suffix"> kg </template>
      </u-input>
    </view>

    <view style="padding: 20rpx">
      <slideChoose
        ref="refswiper1"
        :max-value="maxValue1"
        ruleType="kg"
        :current="current1"
        @finishRuler="finishRuler1"
        :decimal="true"
      />
      <view style="font-size: 50px; text-align: center">{{ value1 }} kg</view>
    </view>
  </view>
</template>

<script>
const app = getApp();
import slideChoose from '@/components/mp-dlm-slide-choose/mp-dlm-slide-choose.vue';
export default {
  components: {
    slideChoose,
  },
  data() {
    const currentValue = 155;
    const currentValue1 = 65;
    return {
      newFonSize: uni.getStorageSync('root_font_size') || 14,
      current: currentValue,
      maxValue: 230,
      value: currentValue - 15,

      current1: currentValue1,
      maxValue1: 150,
      value1: (currentValue1 - 15) / 10,
    };
  },
  methods: {
    onInputChange(e) {
      console.log('e', e);
      // this.current = (e * 1 + 15) * 10
      this.current = e * 10 + 15;
    },
    setRootFontSize(fontSize) {
      uni.setStorageSync('root_font_size', fontSize);
      app.globalData.rootFontSize = fontSize + 'px';
    },
    onFontSize(number) {
      this.newFonSize = number;
    },
    onFontSizeSure() {
      this.setRootFontSize(this.newFonSize);
    },
    finishRuler(e) {
      this.value = e;
    },
    finishRuler1(e) {
      this.value1 = e;
    },
  },
};
</script>

<style scoped lang="scss">
.item-button1 {
  font-size: 1rem;
}

.item-button2 {
  font-size: 1.162rem;
}

.item-button3 {
  font-size: 1.245rem;
}

.item-button4 {
  font-size: 1.494rem;
}

.item-button5 {
  font-size: 1.6rem;
}

/deep/ .u-input__content__field-wrapper__field {
  font-size: 30px !important;
}
</style>
