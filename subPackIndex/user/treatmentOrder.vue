<template>
  <MpLoadingPage :loadingType="loadingType">
    <view class="container">
      <u-list @scrolltolower="scrolltolower" v-if="list.length > 0">
        <u-list-item v-for="(item, index) in list" :key="item.Id">
          <view
            style="margin-bottom: 32rpx;background-color: white;padding-bottom: 10px;border-radius: 8rpx;}"
            @click="toseeDetail(item)"
          >
            <u-cell :border="false" customStyle="marginTop: 24rpx;">
              <view style="font-weight: 600; font-size: 16px" slot="title">
                订单编号:{{ item.TreatOrderNo }}
              </view>
              <p
                slot="value"
                style="color: #29b7a3; font-size: 16px"
                v-show="item.OrderState == 3"
              >
                {{ item.OrderStateName }}
              </p>
              <p
                slot="value"
                style="font-size: 16px; color: #333333"
                v-show="item.OrderState == 2 && item.AutoReceiving > 0"
              >
                待收货
              </p>
              <p
                slot="value"
                style="font-size: 16px; color: #333333"
                v-show="item.OrderState == 2 && item.AutoReceiving <= 0"
              >
                已完成
              </p>
              <p
                slot="value"
                style="font-size: 16px; color: #ff3b30"
                v-show="item.OrderState == 1"
              >
                待发货
              </p>
              <!-- <p slot="value" style="font-size: 16px;text-align: right;"
							v-show="!resources.includes(item.Payment.PayAlias) && item.OrderState == 0">{{item.showOrderWhere}}
						</p> -->
              <p
                slot="value"
                style="font-size: 16px; color: #ff3b30"
                v-show="item.OrderState == 0"
              >
                {{ item.OrderStateName }}
              </p>
              <p
                slot="value"
                style="font-size: 16px; color: #333333"
                v-show="
                  (item.OrderState == 0 && item.RxExpireMinutes <= 0) ||
                  item.OrderState == 4
                "
              >
                已取消
              </p>
            </u-cell>

            <view
              v-for="(o, index2) in item.TreatOrderMoOutputDtos"
              :key="o.PrescriptionId"
            >
              <u-cell :border="false">
                <view
                  slot="title"
                  style="font-weight: 600; font-size: 16px"
                  v-show="o.MoItemChargeMode == 1"
                >
                  {{ o.MoName }} {{ '(部位' + o.Part + ')' }}
                </view>
                <view
                  slot="title"
                  style="font-weight: 600; font-size: 16px"
                  v-show="o.MoItemChargeMode != 1"
                >
                  {{ o.MoName }}
                </view>
                <view
                  slot="label"
                  style="font-size: 16px; color: #999999; margin-top: 24rpx"
                  v-show="o.MoItemChargeMode === 3"
                >
                  {{ o.FreqDay }}天{{ o.Freq }}次,共{{ o.MoDay }}天
                </view>
                <view
                  slot="label"
                  style="font-size: 16px; color: #999999; margin-top: 24rpx"
                  v-show="o.MoItemChargeMode === 5"
                >
                  {{ o.MoMonth }}个月
                </view>
                <view
                  slot="label"
                  style="font-size: 16px; color: #999999; margin-top: 24rpx"
                  v-show="o.MoItemChargeMode === 1 || o.MoItemChargeMode === 2"
                >
                  {{ o.FreqDay }}天{{ o.Freq }}次,共{{ o.TotalCount }}次
                </view>
                <view slot="value" style="text-align: right; font-size: 16px">
                  <!-- <p style="font-size: 16px;" v-show="o.MoItemChargeMode === 1">
										<span>￥{{o.Price * o.Part > 0 ? (o.Price * o.Part).toFixed(2) : o.Price * o.Part}}</span>
										<span style="text-decoration: line-through;color:#b5b5b5"
											v-if="item.ShowPrice > item.Price">￥{{o.ShowPrice * o.Part > 0 ? (o.ShowPrice * o.Part).toFixed(2) : o.ShowPrice * o.Part}}</span>
									</p> -->
                  <!-- <p style="font-size: 16px;" v-show="o.MoItemChargeMode != 1">
										<span>￥{{o.Price > 0 ? (o.Price).toFixed(2) : o.Price}}</span>
										<span style="text-decoration: line-through;color:#b5b5b5"
											v-if="item.ShowPrice > item.Price">￥{{o.ShowPrice > 0 ? (o.ShowPrice).toFixed(2) : o.ShowPrice}}</span>
									</p> -->
                  <p
                    style="color: #999999; font-size: 16px"
                    v-show="o.MoItemChargeMode === 4"
                  >
                    x1
                  </p>
                  <p
                    style="color: #999999; font-size: 16px"
                    v-show="o.MoItemChargeMode === 3"
                  >
                    x{{ o.MoDay }}
                  </p>
                  <p
                    style="color: #999999; font-size: 16px"
                    v-show="
                      o.MoItemChargeMode === 1 || o.MoItemChargeMode === 2
                    "
                  >
                    x{{ o.TotalCount }}
                  </p>
                  <p
                    style="color: #999999; font-size: 16px"
                    v-show="o.MoItemChargeMode === 5"
                  >
                    x{{ o.MoMonth }}
                  </p>
                </view>
              </u-cell>
            </view>
            <p
              style="
                font-size: 16px;
                color: #999999;
                margin-right: 14px;
                text-align: right;
              "
            >
              <span
                style="
                  font-size: 16px;
                  color: #999999;
                  margin-top: 4px;
                  float: left;
                  margin-left: 15px;
                "
                v-if="item.RentDataMoney > 0"
              >
                (含设备使用保证金￥{{ item.RentDataMoney }})
              </span>
              <span style="font-weight: 600; font-size: 16px">合计</span>：<span
                style="color: red; font-size: 16px"
                >￥{{ item.TotlePriceIn }}</span
              >
            </p>
            <view
              class="right-button"
              v-if="item.OrderState == 2"
              @click.stop="toseeDetail(item)"
              >确认收货</view
            >
          </view>
        </u-list-item>
      </u-list>
      <u-empty
        mode="data"
        icon="http://cdn.uviewui.com/uview/empty/data.png"
        text="当前没有治疗订单"
        v-else
      ></u-empty>
    </view>
  </MpLoadingPage>
</template>

<script>
import { getTreatOrders } from '@/api/consult.js';
import config from '@/config';
export default {
  data() {
    return {
      loadingType: 'loading',
      list: [],
      query: {
        pageindex: 1,
        pagesize: 20,
      },
      resources: [
        `com.kangfx.wx.mp.patient-${config.resources}`,
        config.clientId,
      ],
    };
  },
  onLoad() {},
  onShow() {
    this.reset();
  },
  methods: {
    async getTreatmentList() {
      let res = await getTreatOrders(this.query);
      if (res.Type !== 200) {
        this.loadingType = res.Content;
        return;
      }
      res.Data.forEach((e) => {
        if (e.OrderState == 0) {
          e.OrderStateName = '待支付';
        } else if (e.OrderState == 1) {
          e.OrderStateName = '待发货';
        } else if (e.OrderState == 2) {
          e.OrderStateName = '待收货';
        } else if (e.OrderState == 3) {
          e.OrderStateName = '已完成';
        } else if (e.OrderState == 4) {
          e.OrderStateName = '已取消';
        }
        e.TotlePriceIn = this.accAdd(e.RentDataMoney, e.TreatOrder.Price);
        this.list.push(e);
      });
      this.loadingType = 'success';
    },
    accAdd(arg1, arg2) {
      var r1, r2, m;
      try {
        r1 = arg1.toString().split('.')[1].length;
      } catch (e) {
        r1 = 0;
      }
      try {
        r2 = arg2.toString().split('.')[1].length;
      } catch (e) {
        r2 = 0;
      }
      m = Math.pow(10, Math.max(r1, r2));
      return (arg1 * m + arg2 * m) / m;
    },
    scrolltolower() {
      this.$u.debounce(() => {
        this.query.pageindex++;
        this.getTreatmentList();
      });
    },
    toseeDetail(item) {
      // if (item.OrderState == 0) { //  && !this.resources.includes(item.Payment.PayAlias)
      // 	return
      // }
      uni.navigateTo({
        url: './treatmentDetail?id=' + item.Id,
      });
    },
    reset() {
      this.query.pageindex = 1;
      this.list = [];
      this.getTreatmentList();
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  padding: 32rpx;

  .right-button {
    width: 80px;
    height: 25px;
    border-radius: 20px;
    text-align: center;
    line-height: 25px;
    background-color: #00aeb7;
    color: white;
    font-size: 14px;
    float: right;
    margin: 10px 10px 0 10px;
  }

  /deep/ .u-list-item {
    transform: translateY(10px);
  }

  /deep/ .u-cell {
    background-color: white;
  }

  /deep/ .u-cell__body {
    font-size: 14px;
  }
}
</style>
