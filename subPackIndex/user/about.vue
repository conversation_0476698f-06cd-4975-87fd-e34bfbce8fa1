<template>
  <view class="container">
    <view class="container-top">
      <u--image
        :showLoading="true"
        src="/static/images/yankang-icon.png"
        width="100px"
        height="100px"
      ></u--image>
      <p style="margin-top: 20rpx; font-size: 16px">康复行</p>
    </view>

    <u-cell
      title="关于康复行医疗"
      :isLink="true"
      url="/subPackIndex/user/aboutDetail?type=yl"
    ></u-cell>
    <u-cell
      title="互联网医院资质"
      :isLink="true"
      @click="imagePreviewImage"
    ></u-cell>
    <u-cell title="版本信息" :value="appVersion"></u-cell>
    <u-cell
      title="用户协议"
      :isLink="true"
      url="/subPackIndex/user/aboutDetail?type=xy"
    ></u-cell>
    <u-cell
      title="隐私政策"
      :isLink="true"
      url="/subPackIndex/user/aboutDetail?type=zc"
    ></u-cell>
    <u-cell
      title="账号注销协议"
      :isLink="true"
      url="/subPackIndex/user/aboutDetail?type=zhzx"
    ></u-cell>
  </view>
</template>

<script>
export default {
  data() {
    return {
      show: false,
      appVersion: '',
      srcImage: '/subPackIndex/static/zz.jpg',
    };
  },
  onLoad() {
    this.getInfo();
  },
  methods: {
    imagePreviewImage() {
      uni.previewImage({
        urls: [this.srcImage],
        current: this.srcImage,
      });
    },
    getInfo() {
      uni.getSystemInfo({
        success: (res) => {
          // console.log(res)
          this.appVersion = res.appVersion;
        },
      });
    },
  },
};
</script>

<style scoped lang="scss">
/deep/ .u-cell {
  background-color: white;
}

.container {
  &-top {
    text-align: center;
    padding: 100rpx;
    margin: 0 auto;

    /deep/ .u-image {
      margin: 0 auto;
    }
  }
}
</style>
