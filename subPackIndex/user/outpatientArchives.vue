<template>
  <MpLoadingPage :loadingType="loadingType">
    <view class="container">
      <view class="container-box">
        <p
          style="
            text-align: right;
            color: #29b7a3;
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 24rpx;
          "
        >
          ID:{{ consultId || vistData.Vist.ConsultId }}
        </p>
        <view
          class="display-style"
          style="margin-bottom: 24rpx; color: #333333"
        >
          <p>姓名：{{ userInfo.Name || '' }}</p>
          <p>性别：{{ userInfo.Sex || '' }}</p>
          <p>年龄：{{ data.Consult.Age || vistData.Vist.Age || '' }}</p>
        </view>
        <view
          class="display-style"
          style="margin-bottom: 24rpx; color: #333333"
        >
          <p>出生日期：{{ userInfo.Birthday || '' }}</p>
          <p>民族：{{ userData.Nation || '' }}</p>
        </view>
        <view
          class="display-style"
          style="margin-bottom: 24rpx; color: #333333"
        >
          <p>婚姻状况：{{ userData.Marital || '' }}</p>
          <p>职业：{{ userData.Professional || '' }}</p>
        </view>
        <p style="margin-bottom: 24rpx; color: #333333">
          住址：{{ userData.Address || '' }}
        </p>
        <p style="color: #333333">工作单位：{{ userData.WorkUnit || '' }}</p>
      </view>
      <view class="container-box">
        <view class="container-box-each">
          <p style="font-weight: 600; font-size: 18px; margin-bottom: 16rpx">
            就诊时间
          </p>
          <p style="color: #333333; margin-bottom: 32rpx">
            {{ data.Consult.VistDate || vistData.Vist.InDate || '' }}
          </p>
        </view>
        <view class="container-box-each">
          <p style="font-weight: 600; font-size: 18px; margin-bottom: 16rpx">
            科别
          </p>
          <p style="color: #333333; margin-bottom: 32rpx">
            {{ data.Medical.Department || vistData.Vist.DepartmentName || '' }}
          </p>
        </view>
        <view class="container-box-each">
          <p style="font-weight: 600; font-size: 18px; margin-bottom: 16rpx">
            主诉
          </p>
          <p style="color: #333333; margin-bottom: 32rpx">
            {{ data.Medical.Complain || vistData.Vist.ChiefComplaint || '' }}
          </p>
        </view>
        <view class="container-box-each">
          <p style="font-weight: 600; font-size: 18px; margin-bottom: 16rpx">
            现病史
          </p>
          <p style="color: #333333; margin-bottom: 32rpx">
            {{
              data.Medical.PresentIllness || vistData.Vist.PresentHistory || ''
            }}
          </p>
        </view>
        <view class="container-box-each">
          <p style="font-weight: 600; font-size: 18px; margin-bottom: 16rpx">
            既往史
          </p>
          <p style="color: #333333; margin-bottom: 32rpx">
            {{ data.Medical.HistoryIllness || vistData.Vist.PastHistory || '' }}
          </p>
        </view>
        <view class="container-box-each">
          <p style="font-weight: 600; font-size: 18px; margin-bottom: 16rpx">
            辅助检查结果
          </p>
          <p style="color: #333333">
            {{
              data.Medical.AuxiliaryDiagnosis ||
              vistData.Vist.AuxiliaryDiagnosis ||
              ''
            }}
          </p>
          <view
            class="display-style1"
            style="flex-flow: row wrap; margin-bottom: 32rpx"
          >
            <u--image
              :showLoading="true"
              :src="i.Url"
              width="80px"
              height="80px"
              v-for="(i, index) in data.Urls"
              :key="i.Url"
              @click="preImage(index)"
              v-if="data.Urls && data.Urls.length > 0"
            ></u--image>
            <u--image
              :showLoading="true"
              :src="i.ReportUrl"
              width="80px"
              height="80px"
              v-for="(i, index) in vistData.VisitReportDetails"
              :key="i.ReportUrl"
              @click="preImage(index)"
              v-if="
                vistData.VisitReportDetails &&
                vistData.VisitReportDetails.length > 0
              "
            ></u--image>
          </view>
        </view>
        <view class="container-box-each">
          <p style="font-weight: 600; font-size: 18px; margin-bottom: 16rpx">
            诊断
          </p>
          <p
            style="color: #333333"
            v-for="(i, index) in data.Diagnoses"
            v-if="data.Diagnoses"
          >
            {{ index + 1 + ':' + i.DiagnoseName }}
          </p>
          <p
            style="color: #333333"
            v-for="(i, index) in vistData.VisitDiagnoses"
            v-if="vistData.VisitDiagnoses"
          >
            {{ index + 1 + ':' + i.DiagnoseName }}
          </p>
        </view>
        <view class="container-box-each">
          <p style="font-weight: 600; font-size: 18px; margin-bottom: 16rpx">
            处置
          </p>
          <p
            style="color: #333333"
            v-for="(item, index2) in data.Disposal"
            :key="index2"
            v-if="data.Disposal"
          >
            {{ item }}
          </p>
          <p
            style="color: #333333"
            v-for="(item, index2) in vistData.Vist.Disposal"
            :key="index2"
            v-if="vistData.Vist.Disposal"
          >
            {{ item }}
          </p>
        </view>
        <view
          class="container-box-each"
          v-if="
            data.Medical.Complain ||
            data.Medical.HistoryIllness ||
            data.Medical.PresentIllness
          "
        >
          <p style="font-weight: 600; font-size: 18px; margin-bottom: 16rpx">
            医生签名：
          </p>
          <u--image
            v-if="data.Doctor.CASignature"
            :showLoading="true"
            :src="data.Doctor.CASignature"
            width="80px"
            height="80px"
            @click="lookDocPic"
          ></u--image>
        </view>
      </view>
    </view>
  </MpLoadingPage>
</template>

<script>
const app = getApp();
import { getConsultRecordInfo } from '@/api/consult.js';
import { dateFormat } from '@/utils/validate.js';
import { getVisit, getUserInfoArch } from '@/api/record.js';
export default {
  data() {
    return {
      loadingType: 'loading',
      consultId: '',
      data: {},
      userData: {},
      userInfo: {},
      vistData: {},
    };
  },
  methods: {
    async getDetail(consultId) {
      let res = await getConsultRecordInfo(consultId);
      if (res.Type == 200) {
        res.Data.Consult.VistDate = dateFormat(
          res.Data.Consult.VistDate,
          'YYYY-MM-DD'
        );
        // console.log()
        res.Data.Disposal = res.Data.Disposal && res.Data.Disposal.split(`\n`);
        this.data = res.Data;
      }
    },
    async getDetail2(visitId) {
      let res = await getVisit({
        visitId,
      });
      if (res.Type == 200) {
        res.Data.Vist.InDate = dateFormat(res.Data.Vist.InDate, 'YYYY-MM-DD');
        res.Data.Vist.Disposal =
          res.Data.Vist.Disposal && res.Data.Vist.Disposal.split(`\n`);
        this.vistData = res.Data;
      }
    },
    async getUserDetail() {
      let res = await getUserInfoArch({
        userId: app.globalData.userInfo.Id,
      });
      if (res.Type !== 200) {
        this.loadingType = res.Content;
        return;
      }
      this.userData = res.Data;
      this.loadingType = 'success';
    },
    preImage(index) {
      let urls = [];
      if (this.data.Urls) {
        this.data.Urls.forEach((e) => {
          urls.push(e.Url);
        });
      } else if (this.vistData.VisitReportDetails) {
        this.vistData.VisitReportDetails.forEach((e) => {
          urls.push(e.ReportUrl);
        });
      }
      uni.previewImage({
        current: index,
        urls,
      });
    },
    lookDocPic() {
      uni.previewImage({
        current: 0,
        urls: [this.data.Doctor.CASignature],
      });
    },
  },
  onLoad({ consultId, visitId }) {
    this.consultId = consultId;
    this.visitId = visitId;
    if (consultId) {
      this.getDetail(consultId);
    } else {
      this.getDetail2(visitId);
    }

    this.getUserDetail();
    app.globalData.userInfo.Birthday = dateFormat(
      app.globalData.userInfo.Birthday,
      'YYYY-MM-DD'
    );
    this.userInfo = app.globalData.userInfo;
  },
};
</script>

<style scoped lang="scss">
.container {
  padding: 32rpx;

  .container-box {
    background-color: white;
    padding: 32rpx;
    border-radius: 16rpx;
    margin-bottom: 32rpx;

    .container-box-each {
      margin-bottom: 16rpx;

      /deep/ .u-fade-enter-active {
        margin-right: 7rpx;
        margin-bottom: 10rpx;
      }
    }
  }
}
</style>
