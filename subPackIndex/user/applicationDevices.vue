<template>
  <view class="application-container">
    <u-form
      :model="formData"
      ref="uForm"
      label-position="left"
      label-width="160rpx"
    >
      <!-- 姓名 -->
      <u-form-item label="姓名" prop="name" required>
        <u-input
          v-model="formData.name"
          placeholder="请输入真实姓名"
          border="bottom"
          clearable
        ></u-input>
      </u-form-item>

      <!-- 身份证号 -->
      <u-form-item label="身份证号" prop="idCard" required>
        <u-input
          v-model="formData.idCard"
          placeholder="请输入身份证号码"
          border="bottom"
          type="idcard"
          clearable
        >
          <template slot="suffix">
            <u-icon name="camera" color="#24BAA3" size="28"></u-icon>
          </template>
        </u-input>
      </u-form-item>

      <!-- 性别 -->
      <u-form-item label="性别" prop="gender" required>
        <view class="radio-group">
          <view
            class="radio-item"
            :class="{ active: formData.gender === '男' }"
            @click="formData.gender = '男'"
          >
            <u-icon
              name="man"
              :color="formData.gender === '男' ? '#24BAA3' : '#909399'"
            ></u-icon>
            <text>男</text>
          </view>
          <view
            class="radio-item"
            :class="{ active: formData.gender === '女' }"
            @click="formData.gender = '女'"
          >
            <u-icon
              name="woman"
              :color="formData.gender === '女' ? '#24BAA3' : '#909399'"
            ></u-icon>
            <text>女</text>
          </view>
        </view>
      </u-form-item>

      <!-- 年龄 -->
      <u-form-item label="年龄" prop="age" required>
        <u-input
          v-model="formData.age"
          placeholder="请输入年龄"
          border="bottom"
          type="number"
          clearable
        ></u-input>
      </u-form-item>

      <!-- 人员类型 -->
      <u-form-item label="人员类型" prop="personType" required>
        <view class="person-type-group">
          <view
            class="person-type-item"
            :class="{ active: formData.personType === '常规补贴' }"
            @click="formData.personType = '常规补贴'"
          >
            <text>常规补贴</text>
          </view>
          <view
            class="person-type-item"
            :class="{ active: formData.personType === '老年人或低保户' }"
            @click="formData.personType = '老年人或低保户'"
          >
            <text>老年人或低保户</text>
          </view>
          <view
            class="person-type-item"
            :class="{ active: formData.personType === '特困户' }"
            @click="formData.personType = '特困户'"
          >
            <text>特困户</text>
          </view>
        </view>
      </u-form-item>

      <!-- 证明材料 -->
      <u-form-item label="证明材料" prop="proofMaterials" required>
        <u-upload
          :file-list="fileList"
          @afterRead="afterRead"
          @delete="deletePic"
          name="proofMaterials"
          multiple
          :max-count="5"
          width="140rpx"
          height="140rpx"
        ></u-upload>
        <view class="upload-tips">身份证明/残疾证/低保证明</view>
      </u-form-item>

      <!-- 提交按钮 -->
      <view class="btnButtomStyle" @click="handleSubmitApplication">
        提交申请
      </view>
    </u-form>
  </view>
</template>

<script>
const app = getApp();
import config from '@/config';
import { getPayOrderInfo } from '@/api/consult.js';
export default {
  data() {
    return {
      formData: {
        name: '',
        idCard: '',
        gender: '男', // 默认选择男性
        age: '',
        personType: '常规补贴', // 默认选择常规补贴
        proofMaterials: [],
      },
      fileList: [],
    };
  },
  onLoad() {
    // 页面加载时的初始化操作
    uni.setNavigationBarTitle({
      title: '辅具申请',
    });
  },
  methods: {
    handleSubmitApplication() {
      uni.showLoading({
        title: '提交中...',
      });
      uni.showLoading({
        title: '加载中',
        mask: true,
      });
      getApp().subscribeMessage(async () => {
        this.ConsultWayList;
        let query = {
          UserId: app.globalData.userInfo.Id,
          UserName: app.globalData.userInfo.Name,
          Sex: app.globalData.userInfo.Sex,
          DocUserId: '269ff455-e24d-4000-bd23-df3de83b85ec', // dev: 08478d81-9582-4151-9288-fca71beb43fb
          Organization: 'a3b25ba2-5a52-4b9f-bb5f-cc9e7a5d4216', // dev: bbdcbc14-290f-43f6-91d9-fd31529dbec3
          OrganizationName: '自贡市第四人民医院', // dev: 双流医院
          DepartmentId: '3a19359e-1d92-ab4f-fcf8-b4e04872fcb8', // dev: 3a073eb8-225c-208f-b724-752886062b3f
          DepartmentName: '骨科', // dev: 外科007
          CostState: 1,
          PayAlias: 'com.kangfx.wx.mp.patient' + `-${config.resources}`,
          ConsultWay: 1, // 1 问诊  2 咨询
          Source: 1,
          CreatorId: app.globalData.userInfo.Id,
          AutoCreateMedical: true, // 医生就是true 治疗师就是false
          Describing: '膝关节术后，需要辅具',
          OfflineDate: this.$dateFormat(
            new Date(),
            'YYYY-MM-DD HH:mm:ss',
            false
          ),
          HospitName: '双流医院',
        };
        let res = await getPayOrderInfo(query);
        if (res.Type == 200 && res.Data.Amount == 0) {
          // 不需要支付费用
          let consultId = res.Data.ConsultId;
          let backPath = '/pages/interview/index';
          uni.navigateTo({
            url:
              '/subPackChat/sessionChatPage?consultId=' +
              consultId +
              '&backPath=' +
              backPath +
              '&fromAddInquiry=true',
            complete: () => {
              uni.hideLoading();
            },
          });
        } else if (res.Type == 200 && res.Data.Amount > 0) {
          // 需要支付费用
          uni.navigateTo({
            url: `/subPrescription/cashier?orderId=${res.Data.OrderNo}&docName=周富&needMoeny=${res.Data.Amount}&consultId=${res.Data.ConsultId}&PaymentId=${res.Data.Payment.Id}`,
            complete: () => {
              uni.hideLoading();
            },
          });
        } else {
          uni.hideLoading();
          uni.showToast({
            title: res.Message,
            icon: 'none',
          });
        }
      });
    },
    // 上传文件后的回调
    afterRead(event) {
      const { file } = event;
      // 此处可以进行文件上传操作
      // 模拟上传
      setTimeout(() => {
        // 上传成功后，将文件信息添加到fileList
        if (Array.isArray(file)) {
          file.forEach((item) => {
            this.fileList.push({
              ...item,
              status: 'success',
              message: '上传成功',
            });
            this.formData.proofMaterials.push(item.url);
          });
        } else {
          this.fileList.push({
            ...file,
            status: 'success',
            message: '上传成功',
          });
          this.formData.proofMaterials.push(file.url);
        }
      }, 1000);
    },
    // 删除图片
    deletePic(event) {
      this.fileList.splice(event.index, 1);
      this.formData.proofMaterials.splice(event.index, 1);
    },
    // 提交表单
    submitForm() {
      this.$refs.uForm.validate((valid) => {
        if (valid) {
          uni.showLoading({
            title: '提交中...',
          });

          // 这里可以进行表单提交操作
          setTimeout(() => {
            uni.hideLoading();
            uni.showToast({
              title: '提交成功',
              icon: 'success',
            });
            // 提交成功后可以进行页面跳转或其他操作
          }, 1500);
        } else {
          uni.showToast({
            title: '请完善表单信息',
            icon: 'none',
          });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.application-container {
  padding: 24rpx 48rpx;
  min-height: 100vh;
  position: relative;
}

.u-form {
  background-color: #ffffff;
  padding: 25rpx 20rpx;
  border-radius: 12rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid rgba(232, 232, 232, 0.8);
}

.radio-group,
.person-type-group {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10rpx;
}

.radio-item {
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  margin-right: 15rpx;
  background-color: #f7f9fc;
  border-radius: 30rpx;
  transition: all 0.3s;
  border: 1rpx solid #e8e8e8;
  font-size: 26rpx;

  &.active {
    background-color: rgba(36, 186, 163, 0.1);
    color: #24baa3;
    border: 1rpx solid rgba(36, 186, 163, 0.3);
    box-shadow: 0 2rpx 6rpx rgba(36, 186, 163, 0.1);
  }

  &:hover {
    transform: translateY(-1rpx);
    box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  }

  text {
    margin-left: 6rpx;
  }
}

.person-type-item {
  padding: 10rpx 16rpx;
  margin-right: 12rpx;
  margin-bottom: 12rpx;
  background-color: #f7f9fc;
  border-radius: 8rpx;
  transition: all 0.3s;
  border: 1rpx solid #e8e8e8;
  font-size: 26rpx;

  &.active {
    background-color: rgba(36, 186, 163, 0.1);
    color: #24baa3;
    border: 1rpx solid rgba(36, 186, 163, 0.5);
    box-shadow: 0 2rpx 6rpx rgba(36, 186, 163, 0.1);
  }

  &:hover {
    transform: translateY(-1rpx);
    box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  }
}

.upload-tips {
  font-size: 22rpx;
  color: #909399;
  margin-top: 6rpx;
}

.submit-btn-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 40rpx;
  margin-bottom: 30rpx;
}

/* 覆盖上传组件样式 */
/deep/ .u-upload__button {
  background-color: #f7f9fc !important;
  border: 1rpx dashed #d0d7de !important;
  border-radius: 8rpx !important;
  transition: all 0.3s;
}

/deep/ .u-upload__button:hover {
  border-color: #24baa3 !important;
  background-color: rgba(36, 186, 163, 0.05) !important;
}

/* 表单项样式优化 */
/deep/ .u-form-item {
  margin-bottom: 16rpx;
  padding-bottom: 10rpx;
}

/deep/ .u-form-item__body__left__content {
  color: #333;
  font-weight: 500;
  font-size: 26rpx;
}

/deep/ .u-input {
  margin: 6rpx 0;
}

/deep/ .u-form-item__body__right__content {
  padding-right: 0;
}

/deep/ .u-form-item__body__left {
  margin-bottom: 4rpx;
}
</style>
