<template>
  <view class="container">
    <u-list @scrolltolower="scrolltolower" v-if="list.length > 0">
      <u-list-item v-for="(item, index) in list" :key="item.Id">
        <view class="container-box">
          <view class="container-box-def" v-if="item.IsDefault"> 默认 </view>
          <u-cell :border="false" @click.stop="changeAddress(item)">
            <view class="container-box-title" slot="title">
              <view
                style="
                  display: flex;
                  align-items: center;
                  justify-content: flex-start;
                "
              >
                <p style="margin-right: 10px">
                  收货人:{{ item.Name }} {{ item.Tel }}
                </p>
              </view>
              <p style="margin-top: 32rpx">
                收货地址:{{ item.ProvinceName }}{{ item.CityName
                }}{{ item.CountyName }}{{ item.Address }}
              </p>
            </view>
            <view
              slot="value"
              style="transform: translateX(10px); text-align: center"
            >
              <view class="display-style2" @click.stop="">
                <u-icon
                  name="edit-pen-fill"
                  color="#29B7A3"
                  size="28"
                  @click.stop="edit(item)"
                ></u-icon>
                <u-icon
                  name="trash-fill"
                  color="red"
                  size="30"
                  @click.stop="delAddress(item)"
                  v-if="isEditAddr === 'yes'"
                ></u-icon>
              </view>
            </view>
          </u-cell>
        </view>
      </u-list-item>
    </u-list>
    <view style="height: 90px" v-if="list.length > 0"></view>
    <u-button
      shape="circle"
      text="添加地址"
      customStyle="width:90%;bottom: 30px;position: fixed; left: 50%;transform: translateX(-50%);backgroundColor:#29B7A3;color:#fff"
      @click="addAddress"
    ></u-button>
    <u-empty
      mode="data"
      icon="http://cdn.uviewui.com/uview/empty/data.png"
      text="没有收货地址"
      v-if="list.length == 0"
    >
    </u-empty>
    <u-modal
      :show="show1"
      title="温馨提示"
      :showCancelButton="true"
      content="确定要删除吗?"
      @confirm="confirm"
      @cancel="show1 = false"
    ></u-modal>
    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script>
const app = getApp();
import { getUserAddrInfo, delUserAddress } from '@/api/order.js';
export default {
  data() {
    return {
      show1: false,
      delItem: {},
      query: {
        PageCondition: {
          PageIndex: 1,
          PageSize: 10,
        },
        FilterGroup: {
          Rules: [
            {
              Field: 'UserId',
              Value: app.globalData.userInfo.Id,
              Operate: 3,
            },
          ],
          Operate: 1,
        },
      },
      list: [],
      isEditAddr: 'yes', //编辑地址还是选择地址 true编辑 fasle选择
    };
  },
  onLoad({ isChange }) {
    if (isChange && isChange == 'no') {
      this.isEditAddr = 'no';
    }
    this.getData(1);
  },
  methods: {
    // 重新选择收货地址
    changeAddress(item) {
      if (this.isEditAddr !== 'no') return;
      let pages = getCurrentPages();
      const prePage = pages[pages.length - 2]; //上一个页面
      uni.navigateBack();
      setTimeout(() => {
        prePage.$vm.getBeforData(item);
      }, 100);
    },
    async getData(type) {
      if (!type) {
        this.query.PageCondition.PageIndex = 1;
        this.list = [];
      }
      uni.showLoading({
        title: this.$loadingMsg,
      });
      let res = await getUserAddrInfo(this.query);
      if (res.Type === 200) {
        res.Data.Rows.forEach((e) => {
          e.isEditAddr = this.isEditAddr;
          this.list.push(e);
        });
      }
      this.$nextTick(() => {
        uni.hideLoading();
      });
    },
    scrolltolower() {
      this.query.PageCondition.PageIndex++;
      this.getData(1);
    },
    addAddress() {
      const needShowUser = this.list.length > 0 ? '' : 'yes';
      uni.navigateTo({
        url: `./addOrEditAddress?needShowUser=${needShowUser}`,
      });
    },
    edit(item) {
      uni.navigateTo({
        url: './addOrEditAddress?item1=' + JSON.stringify(item),
      });
    },
    delAddress(item) {
      this.show1 = true;
      this.delItem = item;
    },
    async confirm() {
      let res = await delUserAddress({
        id: this.delItem.Id,
      });
      if (res.Type == 200) {
        this.$refs.uToast.show({
          message: '删除成功',
          type: 'success',
        });
        this.show1 = false;
        this.rest();
      } else {
        this.$refs.uToast.show({
          message: res.Content,
          type: 'error',
        });
      }
    },
    rest() {
      this.query.PageCondition.PageIndex = 1;
      this.list = [];
      this.getData();
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  overflow: hidden;
  padding: 0 10px 10px 10px;

  /deep/ .u-icon {
    margin-left: 10rpx;
  }

  /deep/ .u-list {
    height: calc(100% - 90px) !important;
    // height: auto !important;
  }

  /deep/ .u-list-item {
    position: relative;
  }

  /deep/ .u-cell__body {
    padding: 0;
  }

  .container-box {
    position: relative;
    background-color: white;
    margin-bottom: 32rpx;
    padding: 32rpx;
    // box-shadow: -1px -1px 4px -2px rgba(0, 0, 0, 0.3) inset;
    border-radius: 8rpx;
    transform: translateY(10px);

    &-def {
      width: 100rpx;
      height: 20px;
      text-align: center;
      line-height: 20px;
      font-size: 14px;
      color: white;
      background-color: #29b7a3;
      position: absolute;
      top: 0;
      right: 0;
      z-index: 99;
    }

    /deep/ .u-cell {
      background-color: white;
    }

    .container-box-title {
      color: '#333333';
    }
  }
}
</style>
