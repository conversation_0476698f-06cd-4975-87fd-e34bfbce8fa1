<template>
  <div class="seeReport">
    <div class="seeReport-one" v-for="item in list" :key="item.CreatedTime">
      <div class="seeReport-one-base">
        <span>患者: {{ item.PatName }}</span>
        <span style="font-size: 28rpx; font-weight: 600; color: #666666">{{
          item.CreatedTime
        }}</span>
      </div>
      <div v-for="(p, index) in item.Schemes" :key="index">
        <div v-if="p.Type">
          <div class="seeReport-one-tips-titleTop">
            <div class="seeReport-one-tips-titleTop-name">
              {{ p.Type === 1 ? '初次评估' : '末次评估' }}
            </div>
            <div style="color: #f5a031"></div>
          </div>
          <div class="seeReport-one-tips-result" @click="onToReport(p.DctSign)">
            <span v-for="(k, index2) in p.GaugeData" :key="index2">
              <span>{{ k.Name }}(</span>
              <span style="color: #29b7a3" v-if="k.SumPoint >= 0"
                >{{ k.SumPoint }}分</span
              >
              <span style="color: #ff5656" v-else>未评估</span>
              <span>)</span>
              <span v-if="index2 != p.GaugeData.length - 1">、</span>
            </span>
          </div>
        </div>
      </div>
    </div>
    <u-empty
      v-if="list.length === 0"
      mode="data"
      icon="http://cdn.uviewui.com/uview/empty/car.png"
      text="当前没有任何数据"
    >
    </u-empty>
  </div>
</template>

<script>
const app = getApp();
import { GetDctSendGaugeScheme } from '@/api/training.js';
import { dateFormat } from '@/utils/validate.js';
export default {
  data() {
    return {
      list: [],
    };
  },
  onShow() {
    this.getReport();
  },
  methods: {
    onToReport(id) {
      console.log('item', id);
      if (id) {
        uni.navigateTo({
          url: '/subGauge/evaluationList?sign=' + id,
        });
      }
    },
    async getReport() {
      const res = await GetDctSendGaugeScheme({
        patId: app.globalData.userInfo.Id,
      });
      if (res.Type === 200) {
        res.Data.forEach((v) => {
          v.CreatedTime = dateFormat(v.CreatedTime);
        });
        this.list = res.Data;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.seeReport {
  width: 100%;
  height: 100vh;
  background-color: #f8f7f7;
  padding: 24rpx 32rpx;

  &-back {
    background-color: #3e404d;
    height: 400rpx;
    border-bottom-left-radius: 39%;
    border-bottom-right-radius: 62%;
  }

  &-user {
    display: flex;
    align-items: center;
  }

  &-one {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20rpx;
    background: #ffffff;
    box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.1);

    &-base {
      font-size: 34rpx;
      font-weight: 600;
      color: #333333;
      position: relative;
      padding-left: 16rpx;
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    &-base:before {
      content: '';
      position: absolute;
      left: -16rpx;
      top: 50%;
      transform: translateY(-50%);
      width: 6px;
      height: 6px;
      border-radius: 50%;
      border: 3px solid #29b7a3;
      background-color: #29b7a3;
    }

    &-top {
      font-size: 16px;
    }

    &-bom {
      color: gray;
      font-size: 14px;
      margin-top: 10px;
    }

    &-title {
      font-weight: 600;
      font-size: 18px;
      margin-bottom: 10px;
    }

    &-tips {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 16rpx;

      &-result {
        background: rgba(245, 246, 250, 1);
        border-radius: 24rpx;
        padding: 24rpx;
        margin-top: 16rpx;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }

      &-titleTop {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10rpx;

        &-name {
          position: relative;
          padding-left: 10rpx;
        }

        &-name:before {
          content: '';
          position: absolute;
          left: -10rpx;
          top: 50%;
          transform: translateY(-50%);
          width: 4rpx;
          height: 4rpx;
          border-radius: 50%;
          border: 4rpx solid #29b7a3;
          background-color: #29b7a3;
        }
      }
    }

    &-fj {
      display: flex;
      justify-items: flex-start;
      align-items: center;
      margin-bottom: 20rpx;
    }
  }
}
</style>
