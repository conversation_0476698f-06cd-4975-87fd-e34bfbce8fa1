<template>
  <view class="container">
    <block v-if="list.length">
      <view
        class="container-box"
        v-for="(item, index) in list"
        :key="index"
        :class="[item.Id === navigateToId ? 'navigateToClass' : '']"
      >
        <text
          class="container-box-time"
          :class="[
            item.State === 2 ? 'container-box-item1' : 'container-box-item2',
          ]"
          >{{ item.EndDateStr }}</text
        >
        <view
          class="container-box-item"
          :class="[
            item.State === 2 ? 'container-box-item1' : 'container-box-item2',
            item.Id === chooseItemId ? 'list-navigate1' : '',
          ]"
        >
          <view class="flex-start-center">
            <view class="container-box-item-icon" v-if="item.State === 2">
              ✔
            </view>
            <view class="container-box-item-icon1" v-else></view>
            <text class="container-box-item-title">{{ item.Name }}</text>
          </view>
          <text class="container-box-item-remark"
            >备注：{{ item.Remark || '' }}</text
          >
          <view
            class="container-box-item-btn"
            v-if="item.State === 1"
            @click="handleSureClick(item)"
            >确认</view
          >
        </view>
      </view>
      <view style="height: 20px"></view>
    </block>
    <u-empty
      mode="data"
      icon="http://cdn.uviewui.com/uview/empty/data.png"
      text="当前没有数据"
      v-else
    />
    <u-calendar
      title="请选择完善手术时间"
      :show="showCalendar"
      :mode="single"
      @confirm="confirm"
      @close="showCalendar = false"
      :monthNum="13"
      :min-date="minDate"
      :max-date="maxDate"
      :default-date="[defaultDate]"
    ></u-calendar>
  </view>
</template>

<script>
const app = getApp();
const dayjs = require('dayjs');
const localizedFormat = require('dayjs/plugin/localizedFormat');
const weekday = require('dayjs/plugin/weekday');
const zhCn = require('dayjs/locale/zh-cn'); // 引入中文语言包
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
dayjs.extend(localizedFormat);
dayjs.extend(weekday);
dayjs.locale(zhCn); // 设置为中文
dayjs.extend(isSameOrAfter);
import {
  queryBackRemind,
  updateBackRemind,
  checkUserHaveStartDate,
  fillUserStartDate,
} from '@/api/consult.js';
import { ItemGroupBy } from '@/utils/utils.js';
export default {
  data() {
    return {
      showCalendar: false,
      list: [],
      navigateToId: '',
      maxDate: '',
      minDate: '',
      defaultDate: '',
      chooseItemId: '',
    };
  },
  onLoad() {
    this.maxDate = this.$dateFormat(new Date(), 'YYYY-MM-DD', false);
    this.defaultDate = this.$dateFormat(new Date(), 'YYYY-MM-DD', false);
    this.minDate = dayjs().subtract(1, 'year').format('YYYY-MM-DD');
    this.onGetListData();
    // 检查患者是否有未填写手术时间的术后提醒
    // this.onCheckIsNotStartDate()
  },
  methods: {
    confirm(e) {
      uni.showLoading({
        title: this.$loadingMsg,
        mask: true,
      });
      fillUserStartDate({
        UserId: app.globalData.userInfo.Id,
        StartDate: e[0],
      }).then((res) => {
        if (res.Type === 200) {
          uni.hideLoading();
          uni.showToast({
            title: res.Content,
            icon: 'none',
          });
          this.showCalendar = false;
          this.onGetListData(false);
        } else {
          uni.hideLoading();
          uni.showModal({
            title: '温馨提示',
            content: res.Content,
            icon: 'none',
            showCancel: false,
          });
        }
      });
    },
    async onCheckIsNotStartDate() {
      const res = await checkUserHaveStartDate({
        userId: app.globalData.userInfo.Id,
      });
      if (res.Type === 200) {
        if (res.Data) {
          // true 表示有未填写的
          this.showCalendar = true;
        }
      }
    },
    handleSureClick(item) {
      const copyData = JSON.parse(JSON.stringify(item));
      delete copyData.EndDateStr;
      delete copyData.EndDateStr2;
      uni.showLoading({
        title: this.$loadingMsg,
        mask: true,
      });
      updateBackRemind({
        ...copyData,
        State: 2,
      })
        .then((res) => {
          if (res.Type === 200) {
            uni.showToast({
              title: res.Content,
              icon: 'none',
            });
            this.onGetListData(false);
          } else {
            uni.showModal({
              title: '温馨提示',
              content: res.Content,
              icon: 'none',
              showCancel: false,
            });
          }
          uni.hideLoading();
        })
        .catch((err) => {
          uni.hideLoading();
          uni.showModal({
            title: '温馨提示',
            content: err.Content,
            icon: 'none',
            showCancel: false,
          });
        });
    },
    async onGetListData(flag = true) {
      const params = {
        Ids: null,
        Keyword: null,
        Type: null,
        RxTemplateId: null,
        StartTime: null,
        EndTime: null,
        PageIndex: 1,
        PageSize: 1000,
        UserId: app.globalData.userInfo.Id,
        OrgId: null,
      };
      if (flag) {
        uni.showLoading({
          title: this.$loadingMsg,
          mask: true,
        });
      }
      const res = await queryBackRemind(params);
      if (res.Type === 200) {
        if (res.Data.TotalCount > 0) {
          res.Data.Data.forEach((item) => {
            item.EndDateStr = dayjs(item.EndDate).format('YYYY.MM.DD ddd');
            item.EndDateStr2 = dayjs(item.EndDate).format('YYYY-MM-DD');
          });
          // 找出离今天最近的一次的Id
          const today = dayjs();
          // 过滤掉今天之前的
          const filterData = res.Data.Data.filter((item) =>
            dayjs(item.EndDateStr2).isSameOrAfter(today, 'day')
          );
          console.log('filterData', filterData);
          // 过滤填写过的数据
          const finishData = filterData.filter((s) => s.State === 1);
          console.log('finishData', finishData);
          if (filterData.length) {
            const closestItem = filterData.reduce((closest, current) =>
              dayjs(current.EndDateStr2).diff(today) <
              dayjs(closest.EndDateStr2).diff(today)
                ? current
                : closest
            );
            this.navigateToId = closestItem.Id;
          } else {
            this.navigateToId = res.Data.Data[res.Data.Data.length - 1].Id;
          }
          if (finishData.length) {
            // 计算每个项目的 EndTime 和今天的时间差，找出最接近今天的项目
            const closestItem = finishData.reduce((closest, current) =>
              dayjs(current.EndDateStr2).diff(today) <
              dayjs(closest.EndDateStr2).diff(today)
                ? current
                : closest
            );
            this.chooseItemId = closestItem.Id;
          }
          // 对数据进行分组
          this.onGroupData(res.Data.Data, flag);
        } else {
          uni.hideLoading();
        }
      } else {
        uni.hideLoading();
        uni.showModal({
          title: '温馨提示',
          content: res.Content,
          icon: 'none',
          showCancel: false,
        });
      }
    },
    onGroupData(finialData, flag) {
      this.list = finialData;
      if (flag) {
        this.$nextTick(() => {
          let query = uni.createSelectorQuery();
          query.in(this).select('.navigateToClass').boundingClientRect();
          query.exec((res) => {
            wx.pageScrollTo({ scrollTop: res[0].top - 12, duration: 300 });
          });
        });
      }
      uni.hideLoading();
    },
  },
};
</script>

<style lang="scss" scoped>
.list-navigate1 {
  background: #ffffff;
  border-radius: 24rpx;
  border: 4rpx solid #29b7a3;
  display: flexbox;
}
page {
  height: 100vh;
}
.container {
  padding: 32rpx;
  height: 100%;
  &-box {
    margin-bottom: 32rpx;

    &-time {
      font-weight: 600;
      font-size: 34rpx;
      color: #999999;
      line-height: 48rpx;
    }
    &-item1 {
      color: #999999;
    }
    &-item2 {
      color: #333333 !important;
    }
    &-item {
      margin-top: 24rpx;
      background: #ffffff;
      border-radius: 24rpx;
      padding: 32rpx;

      &-title {
        font-weight: 600;
        font-size: 34rpx;
        line-height: 48rpx;
        margin-left: 24rpx;
        flex: 1;
      }

      &-icon {
        width: 40rpx;
        height: 40rpx;
        background: rgba(230, 230, 230, 1);
        color: white;
        line-height: 40rpx;
        text-align: center;
        border-radius: 50%;
      }
      &-icon1 {
        width: 40rpx;
        height: 40rpx;
        border: 6rpx solid #29b7a3;
        border-radius: 50%;
      }

      &-remark {
        font-weight: 400;
        font-size: 28rpx;
        line-height: 40rpx;
      }

      &-btn {
        background: #29b7a3;
        box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);
        border-radius: 40rpx;
        border: 2rpx solid #29b7a3;
        font-weight: 400;
        font-size: 28rpx;
        color: #ffffff;
        line-height: 48rpx;
        text-align: center;
        width: 120rpx;
        height: 48rpx;
        margin-top: 16rpx;
        float: right;
      }
    }

    &-item::after {
      content: '';
      display: table;
      clear: both;
    }
  }
}
</style>
