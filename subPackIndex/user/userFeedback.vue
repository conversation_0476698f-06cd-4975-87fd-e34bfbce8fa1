<template>
  <view class="container">
    <u-cell
      title="反馈类型"
      :clickable="true"
      :isLink="true"
      @click="showAheet"
    >
      <span slot="value">{{ query.Type }}</span>
    </u-cell>
    <p style="padding: 10px 12px; font-size: 14px">
      问题描述 <span style="color: red">*</span>
    </p>
    <u--textarea
      v-model="query.Description"
      placeholder="请反馈你使用小程序时遇见的问题，如需咨询医疗类问题，请返回首页-医生/治疗师进行咨询"
      count
      height="200"
    ></u--textarea>
    <p style="margin: 16rpx; font-size: 14px">
      上传图片（请提供问题截图，以便我们尽快处理，最多上传九张）
    </p>

    <u-upload
      :fileList="query.Screenshots"
      @afterRead="afterRead"
      @delete="deletePic"
      name="5"
      multiple
      :maxCount="9"
      width="100"
      height="100"
      customStyle="marginLeft:16rpx"
    ></u-upload>
    <u-button
      type="success"
      @click="save"
      shape="circle"
      text="提交"
      customStyle="width:90%;bottom: 30px;position: fixed; left: 50%;transform: translateX(-50%);z-index:999"
    >
    </u-button>
    <u-action-sheet
      :actions="backBug"
      @select="selectClick"
      title="反馈类型"
      :show="show"
      :safeAreaInsetBottom="true"
      :closeOnClickOverlay="true"
      @close="close"
    ></u-action-sheet>
    <u-toast ref="uToast"></u-toast>
    <u-popup
      :show="showPrivacyPopup"
      :closeOnClickOverlay="false"
      :round="10"
      mode="bottom"
      @close="closePrivacyPopup()"
    >
      <view style="padding: 40rpx">
        <p
          style="
            text-align: center;
            margin-bottom: 40rpx;
            font-size: 20px;
            font-weight: 700;
          "
        >
          提示
        </p>
        <text>
          在你使用服务之前，请仔细阅读<text
            @click="onClickPrivacyPopupTitle()"
            style="color: #29b7a3"
            >{{ PrivacyPopupTitle }}</text
          >。如果你同意{{ PrivacyPopupTitle }}，请点击“同意”开始使用。
        </text>
        <view class="display-style" style="margin-top: 40rpx">
          <u-button
            text="拒绝"
            @click="handleRefusePrivacyAuthorization()"
          ></u-button>
          <!-- <u-button type="primary" id="agree-btn" text="同意" openType="agreePrivacyAuthorization"
						@agreeprivacyauthorization="handleAgreePrivacyAuthorization()"></u-button> -->
          <button
            id="agree-btn"
            class="btn-agree"
            open-type="agreePrivacyAuthorization"
            @agreeprivacyauthorization="handleAgreePrivacyAuthorization()"
          >
            同意
          </button>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { createFeedback } from '@/api/satisfactory.js';
import { userType } from '@/api/dictionary.js';
import { uploadFileToServer } from '../../services/UploadService';
export default {
  data() {
    return {
      fileList5: [],
      okNextBer: true,
      backBug: [
        {
          name: '11',
          age: 12,
        },
        {
          name: '22',
        },
      ],
      show: false,
      query: {
        Type: '',
        Source: 20, // 先写死  这个之后后端要给提供一个新的
        Screenshots: [],
        Description: '',
      },
    };
  },
  onLoad() {
    this.saveWXCallBack();
    this.getUserTypeList();
  },
  methods: {
    close() {
      this.show = false;
    },
    async save() {
      if (!this.okNextBer) {
        this.$refs.uToast.show({
          message: '请等待图片上传完成再点击',
          type: 'error',
        });
        return;
      }
      if (!this.query.Type) {
        this.$refs.uToast.show({
          message: '请选择反馈类型',
          type: 'error',
        });
        return;
      }
      if (!this.query.Description) {
        this.$refs.uToast.show({
          message: '请输入内容描述',
          type: 'error',
        });
        return;
      }
      let arr = [];
      let newArr = JSON.parse(JSON.stringify(this.query.Screenshots));
      newArr.forEach((e) => {
        arr.push(e.url);
      });
      this.query.Screenshots = arr;
      let res = await createFeedback(this.query);
      if (res.Type == 200) {
        this.$refs.uToast.show({
          message: '提交反馈成功',
          type: 'success',
        });
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      } else {
        this.$refs.uToast.show({
          message: res.Content,
          type: 'error',
        });
      }
    },
    selectClick(e) {
      console.log('e', e);
      this.query.Type = e.Key;
    },
    showAheet() {
      this.show = true;
      console.log(111111111);
    },
    async getUserTypeList() {
      let res = await userType();
      console.log('999999', res);
      if (res.Type === 200) {
        res.Data.forEach((e) => {
          if (!e.name) {
            e.name = e.Key;
          }
        });
        this.backBug = res.Data;
      }
    },
    async afterRead(event) {
      // 当设置 mutiple 为 true 时, file 为数组格式，否则为对象格式
      let lists = [].concat(event.file);
      let fileListLen = this.query.Screenshots.length;
      lists.map((item) => {
        this.query.Screenshots.push({
          ...item,
          status: 'uploading',
          message: '上传中',
        });
        this.okNextBer = false;
      });
      for (let i = 0; i < lists.length; i++) {
        const result = await uploadFileToServer(lists[i].url);
        if (result) {
          if (i === lists.length - 1) {
            this.okNextBer = true;
          }
        }
        let item = this.query.Screenshots[fileListLen];
        this.query.Screenshots.splice(
          fileListLen,
          1,
          Object.assign(item, {
            status: 'success',
            message: '',
            url: result.data.Data,
          })
        );
        fileListLen++;
      }
    },
    // 删除图片
    deletePic(event) {
      this.query.Screenshots.splice(event.index, 1);
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  height: 100vh;
  background-color: #f7f7f7;
  padding-top: 10px;

  /deep/ .u-cell {
    background-color: #fff;
  }
}
</style>
