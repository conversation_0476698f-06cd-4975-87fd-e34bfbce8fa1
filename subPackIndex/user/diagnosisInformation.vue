<template>
  <view class="container">
    <view class="container-box">
      <view class="display-style" style="margin-bottom: 24rpx; color: #333333">
        <p>姓名：{{ userInfo.Name || '' }}</p>
        <p>性别：{{ userInfo.Sex || '' }}</p>
        <p>年龄：{{ userInfo.Age || '' }}</p>
      </view>
      <p style="margin-bottom: 24rpx; color: #333333">
        出生日期：{{ userInfo.Birthday || '' }}
      </p>
      <p style="margin-bottom: 24rpx; color: #333333">
        就诊科室：{{ itemList.departmentName || '' }}
      </p>
      <p style="margin-bottom: 24rpx; color: #333333">
        就诊医生：{{ itemList.doctorName || '' }}
      </p>
    </view>

    <view class="container-box">
      <view class="container-box-each" v-if="itemList.Diagnosis.length > 0">
        <p style="font-weight: 600; font-size: 18px; margin-bottom: 16rpx">
          主诊断
        </p>
        <p style="color: #333333; margin-bottom: 32rpx">
          {{ itemList.Diagnosis[0].diaDesc }}
        </p>
      </view>
      <!-- <view class="container-box-each">
				<p style="font-weight: 600;font-size: 18px;margin-bottom: 16rpx;">主诉</p>
				<p style="color: #333333;margin-bottom: 32rpx;">无</p>
			</view>
			<view class="container-box-each">
				<p style="font-weight: 600;font-size: 18px;margin-bottom: 16rpx;">现病史</p>
				<p style="color: #333333;margin-bottom: 32rpx;">无</p>
			</view>
			<view class="container-box-each">
				<p style="font-weight: 600;font-size: 18px;margin-bottom: 16rpx;">既往史</p>
				<p style="color: #333333;margin-bottom: 32rpx;">无</p>
			</view>
			<view class="container-box-each">
				<p style="font-weight: 600;font-size: 18px;margin-bottom: 16rpx;">过敏史</p>
				<p style="color: #333333;margin-bottom: 32rpx;">无</p>
			</view>
			<view class="container-box-each">
				<p style="font-weight: 600;font-size: 18px;margin-bottom: 16rpx;">血压</p>
				<p style="color: #333333;margin-bottom: 32rpx;">无</p>
			</view>
			<view class="container-box-each">
				<p style="font-weight: 600;font-size: 18px;margin-bottom: 16rpx;">体征</p>
				<p style="color: #333333;margin-bottom: 32rpx;">无</p>
			</view> -->
      <view
        class="container-box-each"
        v-for="(item, index2) in disposalList"
        :key="index2"
      >
        <p style="font-weight: 600; font-size: 18px; margin-bottom: 16rpx">
          处置{{ index2 + 1 }}
        </p>
        <view style="color: #333333">
          <p v-for="(o, index3) in item.disposalEach" :key="index3">
            {{ o.text }}
          </p>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
const app = getApp();
import { getHBSZYYDisposal } from '@/api/tenant.js';
import { dateFormat } from '@/utils/validate.js';
export default {
  data() {
    return {
      userInfo: app.globalData.userInfo,
      itemList: {},
      obj: {},
      disposalList: [],
    };
  },
  methods: {
    async getDisposalInfo() {
      const data = {
        UserId:
          'cb78b473-ac5b-4396-a37c-8f8e1c1d5af6' || app.globalData.userInfo.Id,
        PatCard: this.obj.PatCard,
        PatId: this.obj.PatId,
        AdmId: this.itemList.admId,
        QueryStart: this.obj.QueryStart,
        QueryEnd: this.obj.QueryEnd,
      };
      let res = await getHBSZYYDisposal(data);
      if (res.Type === 200) {
        let disposalList = [];
        res.Data.Data.forEach((e) => {
          let strArr = [];
          e.PrescDetails.forEach((k, index) => {
            // str += `(${index + 1})` + k.drugName + ',' + k.instruc + ',' + k.freq
            strArr.push({
              text:
                k.drugName +
                k.dose +
                k.doseUnit +
                ',' +
                k.instruc +
                ',' +
                k.freq +
                '。',
            });
          });
          disposalList.push({
            disposalEach: strArr,
          });
        });
        this.disposalList = disposalList;
      }
    },
  },
  onLoad({ item, objInfo }) {
    const itemList = JSON.parse(decodeURIComponent(item));
    const obj = JSON.parse(decodeURIComponent(objInfo));
    this.obj = obj;
    this.itemList = itemList;
    app.globalData.userInfo.Birthday = dateFormat(
      app.globalData.userInfo.Birthday,
      'YYYY-MM-DD'
    );
    this.getDisposalInfo();
  },
};
</script>

<style scoped lang="scss">
.container {
  padding: 32rpx;

  .container-box {
    background-color: white;
    padding: 32rpx;
    border-radius: 16rpx;
    margin-bottom: 32rpx;

    .container-box-each {
      margin-bottom: 16rpx;

      /deep/ .u-fade-enter-active {
        margin-right: 7rpx;
        margin-bottom: 10rpx;
      }
    }
  }
}
</style>
