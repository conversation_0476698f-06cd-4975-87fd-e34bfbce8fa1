<template>
  <view class="box">
    <view class="box-avatar">
      <u-cell
        size="large"
        title="头像"
        :isLink="true"
        style="margin-bottom: 20upx"
      >
        <button
          class="avatar-wrapper"
          slot="value"
          open-type="chooseAvatar"
          @chooseavatar="onChooseAvatar"
        >
          <image
            class="avatar"
            :src="option.HeadImg"
            mode="aspectFill"
            style="width: 40px; height: 40px"
          >
          </image>
        </button>
      </u-cell>
      <u-cell
        size="large"
        title="用户名"
        :value="option.UserName"
        :isLink="true"
        @click="goToUpdate('UserName', option.UserName)"
      ></u-cell>
      <u-cell
        size="large"
        title="昵称"
        :value="option.NickName || ''"
        :isLink="true"
        @click="goToUpdate('NickName', option.NickName)"
      ></u-cell>
      <u-cell
        size="large"
        title="实名信息"
        :isLink="true"
        @click="goToUpdate('smxx', '')"
      >
        <!-- <u-tag @click.stop="goToUpdate('smxx','')" :text="isAuthen" slot="right-icon" plain shape="circle" v-if="isAuthen === '已实名'"></u-tag>
				<u-tag type="info" @click.stop="goToUpdate('smxx','')" :text="isAuthen" slot="right-icon" plain shape="circle" v-if="isAuthen === '未实名'"></u-tag> -->
        <view
          :class="isAuthen === '已实名' ? 'box-avatar-sm' : 'box-avatar-sm1'"
          slot="right-icon"
        >
          {{ isAuthen }}
        </view>
      </u-cell>
      <!-- <u-cell	size="large" title="密码设置" :isLink="true" @click="goToUpdate('mmsz','')"></u-cell> -->
      <u-cell
        size="large"
        title="手机绑定"
        :value="option.PhoneNumber || ''"
        :isLink="true"
        @click="goToUpdate('sjbd', option.PhoneNumber)"
      ></u-cell>
    </view>
    <u-popup
      :show="showPrivacyPopup"
      :closeOnClickOverlay="false"
      :round="10"
      mode="bottom"
      @close="closePrivacyPopup()"
    >
      <view style="padding: 40rpx">
        <p
          style="
            text-align: center;
            margin-bottom: 40rpx;
            font-size: 20px;
            font-weight: 700;
          "
        >
          提示
        </p>
        <text>
          在你使用服务之前，请仔细阅读<text
            @click="onClickPrivacyPopupTitle()"
            style="color: #29b7a3"
            >{{ PrivacyPopupTitle }}</text
          >。如果你同意{{ PrivacyPopupTitle }}，请点击“同意”开始使用。
        </text>
        <view class="display-style" style="margin-top: 40rpx">
          <u-button
            text="拒绝"
            @click="handleRefusePrivacyAuthorization()"
          ></u-button>
          <button
            id="agree-btn"
            class="btn-agree"
            open-type="agreePrivacyAuthorization"
            @agreeprivacyauthorization="handleAgreePrivacyAuthorization()"
          >
            同意
          </button>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { uploadFileToServer } from '../../services/UploadService';
import { userUpdateInfo } from '@/api/passport.js';
import { dateFormat } from '@/utils/validate.js';
const app = getApp();
export default {
  data() {
    return {
      option: {},
      faceSrc: '',
      isAuthen: '未实名',
    };
  },
  onLoad(option) {
    this.option = app.globalData.userInfo;
    this.isAuthen =
      app.globalData.userInfo.WorkflowStatus == 2
        ? '已实名'
        : app.globalData.userInfo.WorkflowStatus == 0
        ? '未认证'
        : '认证中';
  },
  onHide() {
    this.faceSrc = '';
    this.saveWXCallBack();
  },
  onShow() {
    this.option = app.globalData.userInfo;
  },
  methods: {
    onChooseAvatar(e) {
      e.detail.avatarUrl && this.uploadFace(e.detail.avatarUrl);
    },
    goToUpdate(type, data) {
      uni.navigateTo({
        url: `./userUpdate?type=${type}&data=${data}`,
      });
    },
    //修改头像接口
    async uploadFace(localTempPath) {
      let res = await uploadFileToServer(localTempPath);
      console.log('获取服务器图片地址成功', res.data);
      if (res.data.Type == 200) {
        this.option.HeadImg = res.data.Data;
        let rsp = await userUpdateInfo({
          HeadImg: res.data.Data,
        });
        if (rsp.Type == 200) {
          let copyData = uni.getStorageSync('userInfoLoging');
          copyData.HeadImg = res.data.Data;
          app.globalData.userInfo = copyData;
          uni.removeStorage('userInfoLoging');
          uni.setStorageSync('userInfoLoging', copyData);
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  height: 100vh;
  background-color: #f7f7f7;

  .box-avatar {
    background-color: white;

    &-sm {
      padding: 8rpx 16rpx;
      border-radius: 30rpx;
      text-align: center;
      color: #29b7a3;
      font-size: 12px;
      border: 1px solid;
    }

    &-sm1 {
      padding: 8rpx 16rpx;
      border-radius: 30rpx;
      text-align: center;
      color: gray;
      font-size: 12px;
      border: 1px solid;
    }
  }

  .avatar-wrapper {
    padding: 0 10rpx !important;
    background: none !important;
    width: 50%;
    text-align: right;
  }

  button::after {
    border: none;
  }
}
</style>
