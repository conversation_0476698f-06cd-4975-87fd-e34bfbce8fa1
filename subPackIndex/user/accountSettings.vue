<template>
  <view class="container">
    <u-cell
      title="账号与安全"
      :isLink="true"
      url="/subPackIndex/user/index"
    ></u-cell>
    <u-cell
      v-if="enableCancelAuth"
      title="取消授权"
      @click="showModal5 = true"
    ></u-cell>
    <u-cell
      title="关于"
      :isLink="true"
      url="/subPackIndex/user/about"
      :border="false"
    ></u-cell>

    <u-button
      v-if="userInfo.Id"
      type="primary"
      text="退出登录"
      @click="showModal3 = true"
      shape="circle"
      customStyle="width:90%;bottom: 30px;position: fixed; left: 50%;transform: translateX(-50%);fontSize:16px;z-index:99"
    >
    </u-button>

    <u-modal
      :show="showModal3"
      title="提示"
      content="您确定要退出登录吗？"
      confirmText="确定"
      cancelText="取消"
      confirmColor="#29B7A3"
      @confirm="logOut"
      @cancel="showModal3 = false"
      :showCancelButton="true"
    ></u-modal>

    <u-modal
      :show="showModal5"
      title="提示"
      content="确定取消授权吗？"
      confirmText="确定"
      cancelText="取消"
      confirmColor="#29B7A3"
      @confirm="cancelAuth"
      @cancel="showModal5 = false"
      :showCancelButton="true"
    >
    </u-modal>
  </view>
</template>

<script>
import MpAddress from '@/components/mp-address/mp-address.vue';
import { cancelAuthorize, getHWRefreshToken } from '@/api/supplier.js';
const app = getApp();
export default {
  components: {
    MpAddress,
  },
  data() {
    return {
      userInfo: {},
      enableCancelAuth: false,
      showModal3: false,
      showModal5: false,
      showWebView: false,
    };
  },
  onLoad() {
    if (app.globalData.userInfo.Id) {
      this.userInfo = app.globalData.userInfo;
      getHWRefreshToken({ userId: app.getAccountId() }).then((r) => {
        if (r.Type === 200 && !!r.Data) {
          this.enableCancelAuth = true;
        }
      });
    }
  },
  methods: {
    async logOut() {
      await getApp().logout();
      uni.reLaunch({
        url: '/pages/index/index',
      });
    },
    async cancelAuth() {
      this.showModal5 = false;
      uni.showLoading({
        mask: true,
      });
      const res = await cancelAuthorize(app.getAccountId());
      uni.hideLoading();
      if (res.Type !== 200) {
        uni.showToast({
          title: res.Content ?? '接口错误',
          icon: 'none',
        });
        return;
      }

      uni.showToast({
        title: '取消授权成功',
        icon: 'success',
      });
      this.enableCancelAuth = false;
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  /deep/ .u-cell {
    background-color: white;
  }
}
</style>
