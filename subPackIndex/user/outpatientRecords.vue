<template>
  <view class="container">
    <view class="container-search display-style2">
      <view
        class="container-search-each display-style1"
        @click="chooseDate('QueryStart')"
      >
        <u--image
          :showLoading="true"
          src="/static/images/calendar.png"
          width="20px"
          height="20px"
          @click="click"
        >
        </u--image>
        <p class="container-search-each-month">{{ month.QueryStart }}</p>
        <p style="margin-left: 20rpx">{{ query.QueryStart }}</p>
      </view>
      <p>-</p>
      <view
        class="container-search-each display-style1"
        @click="chooseDate('QueryEnd')"
      >
        <u--image
          :showLoading="true"
          src="/static/images/calendar.png"
          width="20px"
          height="20px"
          @click="click"
        >
        </u--image>
        <p class="container-search-each-month">{{ month.QueryEnd }}</p>
        <p style="margin-left: 20rpx">{{ query.QueryEnd }}</p>
      </view>
    </view>

    <u-list v-if="indexList.length > 0">
      <u-list-item v-for="(item, index) in indexList" :key="item.idNo">
        <view class="container-box display-style" @click="toSeeDetail(index)">
          <view class="container-box-left">
            <p
              style="
                color: #29b7a3;
                font-size: 16px;
                font-weight: 600;
                margin-bottom: 32rpx;
              "
              v-if="item.Diagnosis.length > 0"
            >
              {{ item.Diagnosis[0].diaDesc }}
            </p>
            <p style="color: #333333; margin-bottom: 32rpx">
              就诊科室：{{ item.departmentName }}
            </p>
            <p style="color: #333333; margin-bottom: 32rpx">
              就诊医生：{{ item.doctorName }}
            </p>
            <p style="color: #333333">就诊时间：{{ item.admitDate }}</p>
          </view>
          <u-icon name="arrow-right" customStyle="flex:1"></u-icon>
        </view>
      </u-list-item>
    </u-list>
    <u-empty
      mode="data"
      icon="http://cdn.uviewui.com/uview/empty/data.png"
      text="当前没有门诊记录"
      v-else
    >
    </u-empty>

    <u-toast ref="uToast"></u-toast>
    <u-calendar
      :show="show"
      :monthNum="24"
      @confirm="confirm"
      @close="show = false"
    ></u-calendar>
  </view>
</template>

<script>
const app = getApp();
import dayjs from 'dayjs';
import { getOfflineOutpatientService } from '@/api/tenant.js';
export default {
  data() {
    const d = new Date();
    const year = d.getFullYear();
    let month = d.getMonth() + 1;
    month = month < 10 ? `0${month}` : month;
    let date = d.getDate();
    date = date < 10 ? `0${date}` : date;
    return {
      show: false,
      month: {
        QueryEnd: month,
        QueryStart:
          month - 1 == 0 ? 12 : month - 1 > 10 ? month - 1 : '0' + (month - 1),
      },
      type: '',
      query: {
        UserId:
          app.globalData.userInfo.Id || 'cb78b473-ac5b-4396-a37c-8f8e1c1d5af6', //'cb78b473-ac5b-4396-a37c-8f8e1c1d5af6'
        QueryStart: `${month - 1 == 0 ? year - 1 : year}-${month - 1 == 0 ? 12 : month - 1 > 10 ? month - 1 : '0' + (month - 1)}-${date}`,
        QueryEnd: `${year}-${month}-${date}`,
      },
      indexList: [],
      obj: {},
    };
  },
  onLoad() {
    this.getData();
  },
  methods: {
    async getData() {
      let res = await getOfflineOutpatientService(this.query);
      if (res.Type == 200 && res.Data.Data.length > 0) {
        res.Data.Data.forEach((e) => {
          this.indexList.push(e);
        });
        this.obj = {
          PatCard: res.Data.PatCard,
          PatId: res.Data.PatId,
        };
      }
    },
    chooseDate(type) {
      this.show = true;
      this.type = type;
    },
    confirm(date) {
      console.log('date', date[0]);
      console.log('date', dayjs(date[0]).valueOf());
      if (
        this.type == 'QueryStart' &&
        dayjs(date[0]).valueOf() >= dayjs(this.query.QueryEnd).valueOf()
      ) {
        this.$refs.uToast.show({
          message: '开始时间必须小于结束时间',
          type: 'error',
          position: 'top',
        });
        return;
      }
      if (
        this.type == 'QueryEnd' &&
        dayjs(this.query.QueryStart).valueOf() >= dayjs(date[0]).valueOf()
      ) {
        console.log(111111);
        this.$refs.uToast.show({
          message: '开始时间必须小于结束时间',
          type: 'error',
          position: 'top',
        });
        return;
      }
      this.query[this.type] = date[0];
      this.month[this.type] = date[0].split('-')[1];
      this.show = false;
      this.rest();
    },
    rest() {
      this.indexList = [];
      this.getData();
    },
    toSeeDetail(index) {
      const item = this.indexList[index];
      console.log(item);
      const itemInfo = encodeURIComponent(JSON.stringify(item));
      this.obj = {
        ...this.obj,
        ...this.query,
      };
      const objInfo = encodeURIComponent(JSON.stringify(this.obj));
      uni.navigateTo({
        url: './diagnosisInformation?item=' + itemInfo + '&objInfo=' + objInfo,
      });
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  padding: 32rpx;
  overflow: hidden;

  /deep/ .u-list {
    max-height: calc(100vh - 80px);
    height: auto;
  }

  &-search {
    width: 100%;
    height: 53px;
    background-color: white;
    border-radius: 16rpx;
    padding: 32rpx;

    &-each {
      position: relative;

      &-month {
        position: absolute;
        z-index: 99;
        left: 3px;
        color: #1890ff;
        font-size: 12px;
        top: 6px;
      }
    }
  }

  &-box {
    padding: 32rpx;
    border-radius: 16rpx;
    background-color: white;
    margin-top: 32rpx;

    &-left {
      width: 80%;
    }
  }
}
</style>
