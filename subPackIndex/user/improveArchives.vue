<template>
  <view class="container">
    <u-cell :border="false" :customStyle="{ paddingLeft: '15px' }">
      <u-image
        slot="icon"
        :showLoading="true"
        shape="circle"
        :src="userInfo.HeadImg"
        width="50px"
        height="50px"
      >
      </u-image>
      <view class="container-top-title" slot="title" style="marginleft: 15px">
        <p>{{ userInfo.Name }}</p>
        <p style="margin-top: 10rpx; font-size: 14px; color: #999999">
          {{ userInfo.Sex }} {{ age }}
        </p>
      </view>
    </u-cell>

    <u-subsection
      :list="list"
      :current="current"
      @change="sectionChange"
      activeColor="#29B7A3"
      inactiveColor="#999999"
      bgColor="#ffffff"
      mode="button"
    ></u-subsection>

    <view style="height: 10upx"></view>
    <!-- current=0 -->
    <view v-show="current == 0">
      <u-cell title="家庭地址">
        <u--input
          slot="value"
          inputAlign="right"
          placeholder="请填写"
          border="none"
          v-model="info.Address"
        >
        </u--input>
      </u-cell>
      <u-cell title="工作单位">
        <u--input
          slot="value"
          inputAlign="right"
          placeholder="请填写"
          border="none"
          v-model="info.WorkUnit"
        >
        </u--input>
      </u-cell>
      <u-cell title="联系人">
        <u--input
          slot="value"
          inputAlign="right"
          placeholder="请填写"
          border="none"
          v-model="info.ContactPerson"
        >
        </u--input>
      </u-cell>
      <u-cell title="联系人关系">
        <u--input
          slot="value"
          inputAlign="right"
          placeholder="请填写"
          border="none"
          v-model="info.ContactRelation"
        >
        </u--input>
      </u-cell>
      <u-cell title="联系人电话">
        <u--input
          slot="value"
          inputAlign="right"
          placeholder="请填写"
          border="none"
          v-model="info.ContactPhone"
        >
        </u--input>
      </u-cell>

      <view style="height: 10upx"></view>

      <u-cell title="身高">
        <u-input
          slot="value"
          inputAlign="right"
          placeholder="请填写"
          border="none"
          v-model="info.Height"
        >
          <u--text text="cm" slot="suffix" type="info"></u--text>
        </u-input>
      </u-cell>
      <u-cell title="体重">
        <u-input
          slot="value"
          inputAlign="right"
          placeholder="请填写"
          border="none"
          v-model="info.Weight"
        >
          <u--text text="kg" slot="suffix" type="info"></u--text>
        </u-input>
      </u-cell>
      <u-cell title="血型" :isLink="true" @click="chooseActionSheet('Blood')">
        <span slot="value">{{ info.Blood || '' }}</span>
      </u-cell>
      <u-cell title="民族" :isLink="true" @click="chooseActionSheet('Nation')">
        <span slot="value">{{ info.Nation || '' }}</span>
      </u-cell>
      <u-cell title="籍贯">
        <u--input
          slot="value"
          inputAlign="right"
          placeholder="请填写籍贯"
          border="none"
          v-model="info.NativePlace"
        >
        </u--input>
      </u-cell>
      <u-cell
        title="教育程度"
        :isLink="true"
        @click="chooseActionSheet('Education')"
      >
        <span slot="value">{{ info.Education || '' }}</span>
      </u-cell>
      <u-cell
        title="职业"
        :isLink="true"
        @click="chooseActionSheet('Professional')"
      >
        <span slot="value">{{ info.Professional || '' }}</span>
      </u-cell>
      <u-cell
        title="工作状态"
        :isLink="true"
        @click="chooseActionSheet('WorkState')"
      >
        <span slot="value">{{ info.WorkState || '' }}</span>
      </u-cell>
      <u-cell
        title="经济收入"
        :isLink="true"
        @click="chooseActionSheet('EconomicIncome')"
      >
        <span slot="value">{{ info.EconomicIncome || '' }}</span>
      </u-cell>
      <u-cell
        title="婚姻状态"
        :isLink="true"
        @click="chooseActionSheet('Marital')"
      >
        <span slot="value">{{ info.Marital || '' }}</span>
      </u-cell>
      <u-cell
        title="生育状态"
        :isLink="true"
        @click="chooseActionSheet('Give')"
      >
        <span slot="value">{{ info.Give || '' }}</span>
      </u-cell>
      <u-cell
        title="残疾情况"
        :isLink="true"
        @click="chooseActionSheet('IsDisability')"
      >
        <span slot="value">{{ info.IsDisability ? '是' : '否' }}</span>
      </u-cell>
      <u-cell
        title="残疾类型"
        :isLink="true"
        @click="chooseActionSheet('DisabilityType')"
        v-if="info.IsDisability"
      >
        <span slot="value">{{ info.DisabilityType || '' }}</span>
      </u-cell>
      <u-cell
        title="残疾等级"
        :isLink="true"
        @click="chooseActionSheet('DisabilityLevel')"
        v-if="info.IsDisability"
      >
        <span slot="value">{{ info.DisabilityLevel || '' }}</span>
      </u-cell>

      <view style="height: 10upx"></view>

      <view class="container-textarea">
        <p style="font-size: 15px; color: #303133; margin-bottom: 4px">
          既往史
        </p>
        <view class="u-page__tag-item">
          <u-tag
            :text="item.Key"
            :plain="!item.checked"
            type="primary"
            :name="item.Id"
            @click="JWScheckboxClick(item)"
            v-for="(item, index) in JWSList"
            :key="item.Id"
          >
          </u-tag>
        </view>
        <u-textarea
          v-model="info.PreviousHistory.Text"
          :maxlength="200"
          :height="140"
          placeholder="请填写您过往所患疾病名称"
        >
        </u-textarea>
      </view>

      <view style="height: 10upx"></view>

      <view class="container-textarea">
        <p style="font-size: 15px; color: #303133; margin-bottom: 4px">
          过敏史
        </p>
        <view class="u-page__tag-item">
          <u-tag
            :text="item.Key"
            :plain="!item.checked"
            type="primary"
            :name="item.Id"
            @click="GMScheckboxClick(item)"
            v-for="(item, index) in GMSList"
            :key="item.Id"
          >
          </u-tag>
        </view>
        <u-textarea
          v-model="info.AllergicHistory.Text"
          :maxlength="200"
          :height="140"
          placeholder="请填写过敏药物"
        >
        </u-textarea>
      </view>

      <view style="height: 10upx"></view>

      <view class="container-textarea">
        <p style="font-size: 15px; color: #303133; margin-bottom: 4px">
          家族病史
        </p>
        <u--textarea
          v-model="info.FamilyHistory"
          count
          :maxlength="200"
          :height="140"
          placeholder="请输入你的家族病史"
        >
        </u--textarea>
      </view>
    </view>

    <view v-show="current == 1">
      <view class="container-textarea">
        <p style="font-size: 15px; color: #303133; margin-bottom: 4px">
          饮食习惯(多选)
        </p>
        <u-radio-group
          v-model="info.DietaryHabit"
          placement="row"
          @change="groupChange($event, 'DietaryHabit')"
        >
          <u-radio
            activeColor="#29B7A3"
            :customStyle="{ marginBottom: '8px' }"
            v-for="(item, index) in actionSheet.DietaryHabit"
            :key="index"
            :label="item.name"
            :name="item.name"
          >
          </u-radio>
        </u-radio-group>
        <u-divider></u-divider>
        <u-checkbox-group
          v-model="info.DietaryHabit1"
          placement="row"
          @change="checkboxChange($event, 'DietaryHabit1')"
        >
          <u-checkbox
            activeColor="#29B7A3"
            :customStyle="{ marginBottom: '8px' }"
            v-for="(item, index) in actionSheet.DietaryHabit1"
            :key="index"
            :label="item.name"
            :name="item.name"
            :checked="item.checked"
          >
          </u-checkbox>
        </u-checkbox-group>
      </view>

      <view class="container-textarea">
        <p style="font-size: 15px; color: #303133; margin-bottom: 4px">
          吸烟情况(单选)
        </p>
        <u-radio-group
          v-model="info.Smoking"
          placement="row"
          @change="groupChange($event, 'Smoking')"
        >
          <u-radio
            activeColor="#29B7A3"
            :customStyle="{ marginBottom: '8px' }"
            v-for="(item, index) in actionSheet.Smoking"
            :key="index"
            :label="item.name"
            :name="item.name"
          >
          </u-radio>
        </u-radio-group>

        <u-cell title="开始吸烟年龄" v-if="info.Smoking != '从不吸烟'">
          <u--input
            slot="value"
            inputAlign="right"
            placeholder="请填写(岁)"
            border="none"
            v-model="info.StartSmokingAge"
          >
          </u--input>
        </u-cell>
        <u-cell title="戒烟年龄" v-if="info.Smoking == '已戒烟'">
          <u--input
            slot="value"
            inputAlign="right"
            placeholder="请填写(岁)"
            border="none"
            v-model="info.QuitSmokingAge"
          >
          </u--input>
        </u-cell>
        <u-cell title="日平均" v-if="info.Smoking == '吸烟'">
          <u--input
            slot="value"
            inputAlign="right"
            placeholder="请填写(支)"
            border="none"
            v-model="info.DaySmoking"
          >
          </u--input>
        </u-cell>
      </view>

      <view class="container-textarea">
        <p style="font-size: 15px; color: #303133; margin-bottom: 4px">
          饮酒习惯(单选)
        </p>
        <u-radio-group
          v-model="info.Drink"
          placement="row"
          @change="groupChange($event, 'Drink')"
        >
          <u-radio
            activeColor="#29B7A3"
            :customStyle="{ marginBottom: '8px' }"
            v-for="(item, index) in actionSheet.Drink"
            :key="index"
            :label="item.name"
            :name="item.name"
          >
          </u-radio>
        </u-radio-group>

        <view v-if="info.Drink != '从不'">
          <u-cell title="开始饮酒年龄">
            <u--input
              slot="value"
              inputAlign="right"
              placeholder="请填写(岁)"
              border="none"
              v-model="info.StartDrinkAge"
            >
            </u--input>
          </u-cell>
          <u-cell title="日平均饮酒量">
            <u--input
              slot="value"
              inputAlign="right"
              placeholder="请填写(两)"
              border="none"
              v-model="info.DayDrink"
            >
            </u--input>
          </u-cell>
        </view>
      </view>

      <view class="container-textarea" v-if="info.Drink != '从不'">
        <p style="font-size: 15px; color: #303133; margin-bottom: 4px">
          是否戒酒(单选)
        </p>
        <u-radio-group
          v-model="info.IsAbstinence"
          placement="row"
          @change="groupChange($event, 'IsAbstinence')"
        >
          <u-radio
            activeColor="#29B7A3"
            :customStyle="{ marginBottom: '8px' }"
            v-for="(item, index) in actionSheet.IsAbstinence"
            :key="index"
            :label="item.name"
            :name="item.name"
          >
          </u-radio>
        </u-radio-group>
        <u-cell title="戒酒年龄" v-if="info.IsAbstinence == '是'">
          <u--input
            slot="value"
            inputAlign="right"
            placeholder="请填写(岁)"
            border="none"
            v-model="info.AbstinenceAge"
          >
          </u--input>
        </u-cell>
      </view>

      <view class="container-textarea" v-if="info.Drink != '从不'">
        <p style="font-size: 15px; color: #303133; margin-bottom: 4px">
          近一年是否嗜酒(单选)
        </p>
        <u-radio-group
          v-model="info.IsYearIntemperance"
          placement="row"
          @change="groupChange($event, 'IsYearIntemperance')"
        >
          <u-radio
            activeColor="#29B7A3"
            :customStyle="{ marginBottom: '8px' }"
            v-for="(item, index) in actionSheet.IsYearIntemperance"
            :key="index"
            :label="item.name"
            :name="item.name"
          >
          </u-radio>
        </u-radio-group>
      </view>

      <view class="container-textarea" v-if="info.Drink != '从不'">
        <p style="font-size: 15px; color: #303133; margin-bottom: 4px">
          饮酒种类(多选)
        </p>
        <u-checkbox-group
          v-model="info.WineType"
          placement="row"
          @change="checkboxChange($event, 'WineType')"
        >
          <u-checkbox
            activeColor="#29B7A3"
            :customStyle="{ marginBottom: '8px' }"
            v-for="(item, index) in actionSheet.WineType"
            :key="index"
            :label="item.name"
            :name="item.name"
            :checked="item.checked"
          >
          </u-checkbox>
        </u-checkbox-group>
      </view>
    </view>

    <u-action-sheet
      @select="select"
      closeOnClickOverlay="close"
      @close="close"
      :actions="list1"
      :show="show"
    >
    </u-action-sheet>
    <view style="height: 100px"></view>
    <u-button
      @click="Save"
      type="success"
      shape="circle"
      text="保存"
      customStyle="width:90%;bottom: 30px;position: fixed; left: 50%;transform: translateX(-50%);backgroundColor:#29B7A3;color:#fff;z-index:9999"
    >
    </u-button>
    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script>
const app = getApp();
import { getUserArchivesInfo, updateArchives } from '@/api/record.js';
import actionSheet from './data/localData.js';
import CiTiao from '@/mixin/CiTiao.js';
export default {
  mixins: [CiTiao],
  data() {
    return {
      age: '',
      userInfo: {},
      list: ['个人资料', '生活习惯'],
      current: 0,
      list1: [],
      show: false,
      Id: '',
      info: {
        IsDisability: false,
        PreviousHistory: {
          Text: '',
        },
        AllergicHistory: {
          Text: '',
        },
        FamilyHistory: '',
      },
      selectType: '',
      actionSheet: {},
      type: '',
      leftData: [],
      GMSList: [],
      JWSList: [],
    };
  },
  onLoad({ age }) {
    this.age = age;
    this.userInfo = app.globalData.userInfo;
    this.getTages(this.getInfo);
    // this.getInfo()
    this.actionSheet = actionSheet;
  },
  methods: {
    JWScheckboxClick({ Id }) {
      // this.JWSList[Id].checked = !this.JWSList[Id].checked
      this.JWSList.map((v) => {
        if (v.Id === Id) {
          v.checked = !v.checked;
        }
      });
    },
    GMScheckboxClick({ Id }) {
      // this.JWSList[Id].checked = !this.JWSList[Id].checked
      this.GMSList.map((v) => {
        if (v.Id === Id) {
          v.checked = !v.checked;
        }
      });
    },
    groupChange(e, type) {
      console.log('groupe', e);
      this.info[type] = e;
    },
    //
    checkboxChange(e, type) {
      console.log('checkboxe', e);
      console.log('checkboxtype', type);
    },
    // 选择
    select(e) {
      console.log('e', e);
      const name = e.name;
      const type = this.selectType;
      if (type) {
        this.info[type] = name;
      }
      if (type == 'IsDisability') {
        this.info.IsDisability = name == '是' ? true : false;
      }
    },
    // 取消选择
    close() {
      this.show = false;
    },
    // 获取信息
    async getInfo() {
      let data = {
        PageCondition: {
          PageIndex: 1,
          PageSize: 10,
        },
        FilterGroup: {
          Rules: [
            {
              Field: 'UserId',
              Value: app.globalData.userInfo.Id,
              Operate: 3,
            },
          ],
        },
      };
      uni.showLoading({
        title: '正在加载数据',
      });
      let res = await getUserArchivesInfo(data);
      if (res.Data.Rows.length > 0) {
        this.type = 'Update';
        if (res.Data.Rows[0].DietaryHabit1) {
          res.Data.Rows[0].DietaryHabit1 = JSON.parse(
            res.Data.Rows[0].DietaryHabit1
          );
          actionSheet.DietaryHabit1.forEach((e) => {
            if (res.Data.Rows[0].DietaryHabit1.find((k) => k == e) > -1) {
              e.checked = true;
            }
          });
        }
        if (res.Data.Rows[0].WineType) {
          res.Data.Rows[0].WineType = JSON.parse(res.Data.Rows[0].WineType);
          actionSheet.WineType.forEach((e) => {
            if (res.Data.Rows[0].WineType.find((k) => k == e) > -1) {
              e.checked = true;
            }
          });
        }
        if (res.Data.Rows[0].IsAbstinence != null) {
          res.Data.Rows[0].IsAbstinence = res.Data.Rows[0].IsAbstinence
            ? '是'
            : '否';
        }
        if (res.Data.Rows[0].IsYearIntemperance != null) {
          res.Data.Rows[0].IsYearIntemperance = res.Data.Rows[0]
            .IsYearIntemperance
            ? '是'
            : '否';
        }
        if (!res.Data.Rows[0].PreviousHistory) {
          // 既往使
          res.Data.Rows[0].PreviousHistory = '';
        } else {
          res.Data.Rows[0].PreviousHistory = JSON.parse(
            res.Data.Rows[0].PreviousHistory
          );
          const findData = res.Data.Rows[0].PreviousHistory.Tags;
          findData.forEach((s) => {
            this.JWSList.forEach((k) => {
              if (k.Key === s) {
                k.checked = true;
              }
            });
          });
        }
        if (!res.Data.Rows[0].AllergicHistory) {
          // 过敏史
          res.Data.Rows[0].AllergicHistory = '';
        } else {
          res.Data.Rows[0].AllergicHistory = JSON.parse(
            res.Data.Rows[0].AllergicHistory
          );
          const findData = res.Data.Rows[0].AllergicHistory.Tags;
          findData.forEach((s) => {
            this.GMSList.forEach((k) => {
              if (k.Key === s) {
                k.checked = true;
              }
            });
          });
        }
        if (!res.Data.Rows[0].FamilyHistory) {
          res.Data.Rows[0].FamilyHistory = '';
        }
        this.info = res.Data.Rows[0];
      } else {
        this.type = 'Create';
      }
      this.$nextTick(() => {
        uni.hideLoading();
      });
    },
    // 选择
    chooseActionSheet(type) {
      this.selectType = type;
      // if(type == 'Blood'){
      // 	this.list1 = actionSheet.Blood
      // }
      this.list1 = actionSheet[type];

      this.show = true;
    },
    // 保存修改
    async Save() {
      let reqInfo = JSON.parse(JSON.stringify(this.info));
      if (reqInfo.DietaryHabit1?.length > 0) {
        reqInfo.DietaryHabit1 = JSON.stringify(reqInfo.DietaryHabit1);
      }
      if (reqInfo.IsAbstinence) {
        reqInfo.IsAbstinence = reqInfo.IsAbstinence == '是' ? true : false;
      }
      if (reqInfo.IsYearIntemperance) {
        reqInfo.IsYearIntemperance =
          reqInfo.IsYearIntemperance == '是' ? true : false;
      }
      if (reqInfo.WineType?.length > 0) {
        reqInfo.WineType = JSON.stringify(reqInfo.WineType);
      }
      if (!reqInfo.UserId) {
        reqInfo.UserId = app.globalData.userInfo.Id;
      }
      const jwsTags = {
        Tags: [],
        Text: '',
      };
      const jwsChoose = this.JWSList.filter((o) => o.checked);
      if (jwsChoose.length > 0) {
        jwsChoose.forEach((m) => {
          jwsTags.Tags.push(m.Key);
        });
      }
      jwsTags.Text = reqInfo.PreviousHistory.Text;
      const gmsTags = {
        Tags: [],
        Text: '',
      };
      const gmsChoose = this.GMSList.filter((o) => o.checked);
      if (gmsChoose.length > 0) {
        gmsChoose.forEach((m) => {
          gmsTags.Tags.push(m.Key);
        });
      }
      gmsTags.Text = reqInfo.AllergicHistory.Text;

      reqInfo.AllergicHistory = JSON.stringify(gmsTags);
      reqInfo.PreviousHistory = JSON.stringify(jwsTags);
      console.log('data', reqInfo);
      let res = await updateArchives(this.type, [reqInfo]);
      if (res.Type == 200) {
        this.$refs.uToast.show({
          message: '操作成功',
          type: 'success',
        });
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      } else {
        this.$refs.uToast.show({
          message: res.Content,
          type: 'error',
        });
      }
    },
    // 切换选项卡
    sectionChange(index) {
      this.current = index;
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  padding: 10px;

  .container-textarea {
    background-color: white;
    padding: 4px 30rpx;
  }

  /deep/ .u-cell {
    background-color: white;
  }

  /deep/ .u-divider {
    margin: 10rpx 0;
  }

  .container-top-title {
    font-size: 16px;
    margin-left: 20rpx;
  }

  .u-page__tag-item {
    display: flex;
    flex-wrap: wrap;

    .u-transition {
      margin-right: 20rpx;
      margin-bottom: 10rpx;
    }

    /deep/ .u-fade-enter-active {
      margin-right: 10rpx;
      margin-bottom: 10rpx;
    }
  }
}
</style>
