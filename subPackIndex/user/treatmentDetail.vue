<template>
  <view class="container">
    <u-toast ref="uToast"></u-toast>
    <MpLoadingPage :loadingType="loadingType">
      <!-- 头部的消息提示 -->
      <view class="container-title" v-if="detail.OrderState == 2">
        还有{{ time }}自动确认收货
      </view>

      <!-- 收获信息 -->
      <view
        class="container-address"
        v-if="detail.OrderAddresss && detail.OrderAddresss.length > 0"
      >
        <p>
          收货人：{{ detail.OrderAddresss[0].Name }}
          {{ detail.OrderAddresss[0].Tel }}
        </p>
        <p
          style="
            margin-top: 10px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          "
        >
          收货地址：{{ detail.OrderAddresss[0].ProvinceName
          }}{{ detail.OrderAddresss[0].CityName
          }}{{ detail.OrderAddresss[0].CountyName
          }}{{ detail.OrderAddresss[0].Address }}
        </p>
      </view>

      <!-- 费用详情信息 -->
      <view class="container-detail">
        <u-cell :border="false">
          <span slot="title" style="font-size: 16px">项目治疗费用</span>
          <span slot="value" style="color: #29b7a3; font-size: 16px"
            >￥{{ detail.TotalAmount }}</span
          >
        </u-cell>

        <view
          v-for="(item, index) in detail.TreatOrderMoOutputDtos"
          :key="item.MoItemId"
        >
          <u-cell :border="false">
            <view
              slot="title"
              style="font-weight: 600; font-size: 16px"
              v-show="item.MoItemChargeMode == 1"
            >
              {{ item.MoName }} {{ '(部位' + item.Part + ')' }}
            </view>
            <view
              slot="title"
              style="font-weight: 600; font-size: 16px"
              v-show="item.MoItemChargeMode != 1"
            >
              {{ item.MoName }}
            </view>
            <view
              slot="label"
              style="font-size: 16px; color: #999999; margin-top: 24rpx"
              v-show="item.MoItemChargeMode === 3"
            >
              {{ item.FreqDay }}天{{ item.Freq }}次,共{{ item.MoDay }}天
            </view>
            <view
              slot="label"
              style="font-size: 16px; color: #999999; margin-top: 24rpx"
              v-show="item.MoItemChargeMode === 5"
            >
              {{ item.MoMonth }}个月
            </view>
            <view
              slot="label"
              style="font-size: 16px; color: #999999; margin-top: 24rpx"
              v-show="
                item.MoItemChargeMode === 1 || item.MoItemChargeMode === 2
              "
            >
              {{ item.FreqDay }}天{{ item.Freq }}次,共{{ item.TotalCount }}次
            </view>
            <view slot="value" style="text-align: right">
              <!-- <p style="font-size: 16px;" v-show="item.MoItemChargeMode === 1">
								<span>￥{{item.Price * item.Part > 0 ? (item.Price * item.Part).toFixed(2) : item.Price * item.Part}}</span>
								<span style="text-decoration: line-through;color:#b5b5b5"
									v-if="item.ShowPrice > item.Price">￥{{item.ShowPrice * item.Part > 0 ? (item.ShowPrice * item.Part).toFixed(2) : item.ShowPrice * item.Part}}</span>
							</p>
							<p style="font-size: 16px;" v-show="item.MoItemChargeMode != 1">
								<span>￥{{item.Price > 0 ? (item.Price).toFixed(2) : item.Price}}</span>
								<span style="text-decoration: line-through;color:#b5b5b5"
									v-if="item.ShowPrice > item.Price">￥{{item.ShowPrice > 0 ? (item.ShowPrice).toFixed(2) : item.ShowPrice}}</span>
							</p> -->
              <p
                style="color: #999999; font-size: 16px"
                v-show="item.MoItemChargeMode === 4"
              >
                x1
              </p>
              <p
                style="color: #999999; font-size: 16px"
                v-show="item.MoItemChargeMode === 3"
              >
                x{{ item.MoDay }}
              </p>
              <p
                style="color: #999999; font-size: 16px"
                v-show="
                  item.MoItemChargeMode === 1 || item.MoItemChargeMode === 2
                "
              >
                x{{ item.TotalCount }}
              </p>
              <p
                style="color: #999999; font-size: 16px"
                v-show="item.MoItemChargeMode === 5"
              >
                x{{ item.MoMonth }}
              </p>
            </view>
          </u-cell>
        </view>

        <!-- 设备使用保证金 -->
        <u-cell
          title="设备使用保证金"
          customStyle="{marginTop:20px}"
          v-if="detail.RentDataInfos && detail.RentDataInfos.length > 0"
        >
          <span slot="value" style="color: #29b7a3; font-size: 16px"
            >￥{{ detail.RentDataMoney }}</span
          >
        </u-cell>

        <!--  -->
        <u-cell customStyle="{marginTop:20px}">
          <span slot="title" style="font-size: 16px">订单金额</span>
          <span slot="value" style="color: red; font-size: 16px"
            >￥{{ accAdd(detail.RentDataMoney, detail.TotalAmount) }}</span
          >
        </u-cell>

        <u-cell customStyle="{marginTop:20px}" :border="false">
          <span slot="title" style="font-size: 16px">支付方式</span>
          <span slot="value" style="color: #999999; font-size: 16px">{{
            detail.PayType === 3
              ? '线下支付'
              : detail.Payment.Provider === 'WeChat'
                ? '微信支付'
                : ''
          }}</span>
        </u-cell>

        <u-cell customStyle="{marginTop:20px}" :border="false">
          <span slot="title" style="font-size: 16px">订单编号</span>
          <span slot="value" style="color: #999999; font-size: 16px">{{
            detail.TreatOrderNo
          }}</span>
        </u-cell>

        <u-cell customStyle="{marginTop:20px}" :border="false">
          <span slot="title" style="font-size: 16px">创建时间</span>
          <span slot="value" style="color: #999999; font-size: 16px">{{
            detail.CreatedTime || ''
          }}</span>
        </u-cell>

        <u-cell customStyle="{marginTop:20px}" :border="false">
          <span slot="title" style="font-size: 16px">支付时间</span>
          <span slot="value" style="color: #999999; font-size: 16px">{{
            detail.PayTime || ''
          }}</span>
        </u-cell>

        <u-cell
          customStyle="{marginTop:20px}"
          :border="false"
          v-if="detail.OrderState !== 1"
        >
          <span slot="title" style="font-size: 16px">发货时间</span>
          <span slot="value" style="color: #999999; font-size: 16px">{{
            detail.DeliveryTime || ''
          }}</span>
        </u-cell>

        <u-cell
          customStyle="{marginTop:20px}"
          :border="false"
          v-if="detail.OrderState !== 1 && detail.OrderState !== 2"
        >
          <span slot="title" style="font-size: 16px">收货时间</span>
          <span slot="value" style="color: #999999; font-size: 16px">{{
            detail.CollectTime || ''
          }}</span>
        </u-cell>
      </view>

      <view style="height: 80px"></view>
      <view
        class="container-footer"
        v-if="detail.OrderState == 0 && detail.RxExpireMinutes > 0"
      >
        <view
          class="right-button"
          v-if="resources.some((v) => v === detail.Payment.PayAlias)"
          @click="toPayMoeny"
        >
          去支付</view
        >
        <view class="right-button look" @click="cancelOrReceived('cancel')"
          >取消订单</view
        >
      </view>
      <view
        class="container-footer"
        v-if="detail.OrderState == 2 && detail.AutoReceiving > 0"
      >
        <view class="right-button" @click="cancelOrReceived('received')"
          >确认收货</view
        >
        <view class="right-button look" @click="toLogistics">查看物流</view>
      </view>

      <view
        class="container-footer"
        v-if="detail.OrderState == 3 && detail.RentDataMoney > 0"
      >
        <view class="right-button" @click="onDepositRefund">押金退还</view>
        <view class="right-button look" @click="toLogistics">查看物流</view>
      </view>
    </MpLoadingPage>
  </view>
</template>

<script>
import config from '@/config';
import { getTreatOrderInfo } from '@/api/consult.js';
import { cancelOrReceivedOrder } from '@/api/order.js';
import { dateFormat, toHoursAndMinutes } from '@/utils/validate.js';
export default {
  data() {
    return {
      loadingType: 'loading',
      detail: {},
      time: '',
      id: '',
      resources: [
        config.clientId,
        `com.kangfx.wx.mp.patient-${config.resources}`,
      ],
    };
  },
  onLoad({ id }) {
    this.id = id;
  },
  onShow() {
    this.getOrderDetail(this.id);
  },
  methods: {
    // 押金退还
    onDepositRefund() {
      uni.navigateTo({
        url:
          '/subPackIndex/user/deviceBack?PrescriptionId=' +
          this.detail.TreatOrderMoOutputDtos[0].PrescriptionId,
      });
    },
    accAdd(arg1, arg2) {
      var r1, r2, m;
      try {
        r1 = arg1.toString().split('.')[1].length;
      } catch (e) {
        r1 = 0;
      }
      try {
        r2 = arg2.toString().split('.')[1].length;
      } catch (e) {
        r2 = 0;
      }
      m = Math.pow(10, Math.max(r1, r2));
      return (arg1 * m + arg2 * m) / m;
    },
    // 查看物流
    toLogistics() {
      // const expressNumber = this.detail.TreatOrder.OrderExpresses.map(v => v.ExpressNum);
      const OrderExpressesStr = encodeURIComponent(
        JSON.stringify(
          this.detail.TreatOrder.OrderExpresses.filter((s) => s.Type === 0)
        )
      );
      if (this.detail.TreatOrder.OrderExpresses.length > 0) {
        uni.navigateTo({
          url:
            '/subLogistics/index?OrderExpresses=' +
            OrderExpressesStr +
            '&phone=' +
            this.detail.OrderAddresss[0].Tel,
        });
      }
    },
    toPayMoeny() {
      uni.navigateTo({
        url: '/subPrescription/cashier?id=' + this.detail.Id,
      });
    },
    async getOrderDetail(prescriptionId) {
      let res = await getTreatOrderInfo(prescriptionId);
      if (res.Type == 200) {
        if (res.Data.OrderState == 0) {
          res.Data.OrderStateName = '待支付';
        } else if (res.Data.OrderState == 1) {
          res.Data.OrderStateName = '待发货';
        } else if (res.Data.OrderState == 2) {
          res.Data.OrderStateName = '待收货';
        } else if (res.Data.OrderState == 3) {
          res.Data.OrderStateName = '已完成';
        } else if (res.Data.OrderState == 4) {
          res.Data.OrderStateName = '已取消';
        }
        uni.setNavigationBarTitle({
          title: '治疗订单-' + res.Data.OrderStateName,
        });
        if (res.Data.AutoReceiving > 0) {
          this.time = toHoursAndMinutes(res.Data.AutoReceiving, 1);
        }
        res.Data.CreatedTime = dateFormat(res.Data.CreatedTime);
        res.Data.PayTime = res.Data.PayTime && dateFormat(res.Data.PayTime);
        res.Data.DeliveryTime =
          res.Data.DeliveryTime && dateFormat(res.Data.DeliveryTime);
        res.Data.CollectTime =
          res.Data.CollectTime && dateFormat(res.Data.CollectTime);
        this.detail = res.Data;
        this.loadingType = 'success';
      } else {
        this.loadingType = res.Content;
      }
    },
    async cancelOrReceived(type) {
      let params = null;
      if (type === 'cancel') {
        params = [this.detail.TreatOrderNo];
      } else if (type === 'received') {
        params = {
          OrderNos: [this.detail.TreatOrderNo],
        };
      }
      let res = await cancelOrReceivedOrder(type, params);
      if (res.Type == 200) {
        this.$refs.uToast.show({
          message: '操作成功',
          type: 'success',
        });
        setTimeout(() => {
          // let pages = getCurrentPages();
          // const prePage = pages[pages.length - 2] //上一个页面
          // if (prePage.$vm.reset) {
          // 	prePage.$vm.reset()
          // }
          uni.navigateBack();
        }, 1500);
      } else {
        this.$refs.uToast.show({
          message: res.Content,
          type: 'error',
        });
      }
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  padding: 10px;
  position: relative;

  .container-title {
    width: 100%;
    height: 40px;
    color: white;
    text-align: center;
    line-height: 40px;
    background-color: #29b7a3;
    font-size: 14px;
    border-radius: 10px;
  }

  .container-address {
    width: 100%;
    background-color: white;
    padding: 10px;
    border-radius: 10px;
    font-size: 15px;
    margin-top: 10px;
    border-radius: 14px;
  }

  .container-detail {
    width: 100%;
    background-color: white;
    padding: 10px;
    border-radius: 10px;
    margin-top: 20px;
  }

  .container-footer {
    position: fixed;
    bottom: 0px;
    width: 100%;
    height: 60px;
    background-color: white;

    .right-button {
      width: 80px;
      height: 25px;
      border-radius: 20px;
      text-align: center;
      line-height: 25px;
      background-color: #00aeb7;
      color: white;
      font-size: 14px;
      position: absolute;
      right: 120px;
      bottom: 17px;
    }

    .look {
      right: 20px;
      bottom: 17px;
    }
  }
}
</style>
