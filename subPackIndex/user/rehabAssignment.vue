<template>
  <view class="container">
    <view class="container-base">
      <view class="container-base-day">
        <view class="date-header">
          <view class="arrow-button left" @tap="changeWeek(-1)">
            <image
              class="arrow-icon"
              src="/static/left-icon.png"
              mode="aspectFit"
            ></image>
          </view>
          <text>{{ currentYear }}年{{ currentMonth }}月</text>
          <view
            class="arrow-button right"
            @tap="changeWeek(1)"
            :class="{ disabled: isCurrentWeek }"
          >
            <image
              class="arrow-icon"
              src="/static/right-icon.png"
              mode="aspectFit"
            ></image>
          </view>
        </view>
        <view class="week-row">
          <view
            class="week-item"
            v-for="(day, dayIndex) in weekDays"
            :key="dayIndex"
          >
            <text>{{ day }}</text>
          </view>
        </view>

        <!-- 替换swiper为普通视图 -->
        <view class="days-container">
          <view
            class="day-item"
            v-for="(item, dayIndex) in currentWeekData"
            :key="dayIndex"
            :class="{
              'empty': !item.date,
              'selected': item.selected,
              'today': item.isToday,
              'other-month': item.isOtherMonth,
            }"
            @tap="selectDate(item)"
          >
            <view class="day-content" v-if="item.date">
              <text>{{ item.date }}</text>
            </view>
          </view>
        </view>
      </view>
      <view class="container-base-today" @click="handleToPlanDetail">
        <text class="container-base-today-title">今日任务</text>
        <block v-if="actionList.length">
          <view
            class="container-base-today-item"
            v-for="(item, index) in actionList"
            :key="index"
          >
            <image
              :src="item.ActionUnitImgURL"
              class="container-base-today-item-image"
            ></image>
            <view class="container-base-today-item-text">
              <text class="p-style11 container-base-today-item-text-top">{{
                item.Name
              }}</text>
              <text
                class="p-style11 container-base-today-item-text-bom"
                v-if="item.ActionTime"
                >{{ item.ActionTime }}</text
              >
            </view>
            <view class="container-base-today-item-tool">
              <view class="container-base-today-item-tool-top">
                <text>{{ item.TrainingActionDetailDate.FinishCount }}</text>
                <text>/</text>
                <text>{{ item.TrainingActionDetailDate.ShouldCount }}</text>
              </view>
              <view
                class="container-base-today-item-tool-bom"
                v-if="
                  item.TrainingActionDetailDate.FinishCount <
                  item.TrainingActionDetailDate.ShouldCount
                "
              >
                去完成
              </view>
            </view>
          </view>
        </block>
        <block v-else>
          <view class="container-base-today-nodata">
            <text class="container-base-today-nodata-subtitle"
              >您今天还没有开始训练哦</text
            >
            <view class="container-base-today-nodata-btn"> 前往训练 </view>
          </view>
        </block>
      </view>
    </view>
    <view class="container-box">
      <view class="container-box-data" v-if="option.flowType === 'Oa'">
        <text class="container-box-data-title">多功能骨关节理疗仪治疗数据</text>
        <u-divider />
        <view class="container-box-data-wapper">
          <view class="container-box-data-wapper-box">
            <text class="container-box-data-wapper-box-title">治疗次数</text>
            <view
              style="
                display: flex;
                align-items: baseline;
                justify-content: center;
              "
            >
              <text class="container-box-data-wapper-box-number">{{
                treatmentData.Count
              }}</text>
              <text class="container-box-data-wapper-box-count">次</text>
            </view>
          </view>
          <view class="container-box-data-wapper-box">
            <text class="container-box-data-wapper-box-title">总时长</text>
            <view
              style="
                display: flex;
                align-items: baseline;
                justify-content: center;
              "
            >
              <text class="container-box-data-wapper-box-number">{{
                treatmentData.Total
              }}</text>
              <text class="container-box-data-wapper-box-count">分钟</text>
            </view>
          </view>
          <view class="container-box-data-wapper-box">
            <text class="container-box-data-wapper-box-title">平均时长</text>
            <view
              style="
                display: flex;
                align-items: baseline;
                justify-content: center;
              "
            >
              <text class="container-box-data-wapper-box-number">{{
                treatmentData.Avg
              }}</text>
              <text class="container-box-data-wapper-box-count">分钟/次</text>
            </view>
          </view>
        </view>
      </view>
      <view
        class="container-base-today-title"
        style="margin-top: 24rpx; margin-bottom: 0"
        >科普小知识</view
      >
      <view class="container-box-list">
        <view
          class="container-box-list-item"
          v-for="(item, index) in list"
          :key="index"
          @click="handleRecoveryMissionClick(item)"
        >
          <image :src="item.Img" class="container-box-list-item-image"></image>
          <text class="container-box-list-item-text">{{ item.Title }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
const app = getApp();
import { getTrainingExecByUserId, statisticsReport } from '@/api/training.js';
import { getRecoveryMission } from '@/api/consult.js';
import { getMissionImageUrl } from '../../utils/mission';
export default {
  data() {
    return {
      option: {},
      currentDate: new Date(),
      currentYear: 0,
      currentMonth: 0,
      selectedDate: null, // 选中的日期对象
      selectedDateStr: '', // 选中日期的字符串格式 yyyy-MM-dd
      weekDays: ['日', '一', '二', '三', '四', '五', '六'],
      currentWeekData: [], // 当前显示的周数据
      currentWeekStartDate: null, // 当前周的开始日期
      isCurrentWeek: false, // 是否当前周（用于禁用右箭头）
      realCurrentWeekStartDate: null, // 实际当前周的开始日期
      isHaveTodayMission: false,
      params: {
        userId: '',
      },
      actionList: [],
      list: [],
      treatmentData: {
        Avg: 0,
        Count: 0,
        Total: 0,
      },
    };
  },
  async onLoad(option) {
    await app.isLaunchFinish();
    this.option = option;
    this.initCalendar();
    if (option.visitActionId) {
      this.params.visitActionId = option.visitActionId;
    }
    this.params.userId = app.globalData.userInfo.Id;
    uni.showLoading({
      title: '加载中',
      mask: true,
    });
    // 获取用户的打卡数据
    await this.handleGetUserVisitActionList();
    // 获取用户的治疗数据
    await this.handleGetUserTreatmentData();
    // 获取宣教数据
    this.handleGetRecoveryMission();
    uni.hideLoading();
  },
  methods: {
    handleRecoveryMissionClick(item) {
      uni.navigateTo({
        url: `/subPropaganda/detail?id=${item.RecoveryMissionId}`,
      });
    },
    async handleGetUserTreatmentData() {
      const res = await statisticsReport({
        visitId: this.params.visitActionId,
        loadDetail: false,
      });
      if (res.Type === 200 && res.Data) {
        this.treatmentData.Avg = (res.Data.Report.Data.Avg / 60).toFixed(1);
        this.treatmentData.Count = res.Data.Report.Data.Count;
        this.treatmentData.Total = (res.Data.Report.Data.Total / 60).toFixed(1);
      }
    },
    async handleGetRecoveryMission() {
      const res = await getRecoveryMission({
        PrimaryId: this.params.visitActionId,
        Source: 6,
        SendDate: this.selectedDateStr,
      });
      if (res.Type === 200) {
        res.Data.forEach((s, index) => {
          if (!s.Img) {
            s.Img = getMissionImageUrl(index);
          }
        });
        this.list = res.Data;
      }
    },
    handleToPlanDetail() {
      uni.reLaunch({
        url: '/pages/plan/index',
      });
    },
    async handleGetUserVisitActionList() {
      const res = await getTrainingExecByUserId(this.params);
      if (res.Type === 200) {
        if (res.Data && res.Data.length) {
          this.actionList = res.Data[0].TrainingActionDetailByDateType0s;
        }
      }
    },
    // 初始化日历
    initCalendar() {
      const today = new Date();

      // 获取当前日期所在的周
      const currentWeekDate = this.getWeekStartDate(today);
      // 保存实际当前周的开始日期
      this.realCurrentWeekStartDate = new Date(currentWeekDate);
      // 当前显示的周开始日期
      this.currentWeekStartDate = new Date(currentWeekDate);

      // 更新年月显示
      this.updateYearMonthDisplay(currentWeekDate);

      // 生成周数据
      this.currentWeekData = this.generateWeekData(currentWeekDate);

      // 默认选中当天
      this.selectDefaultDate(today);

      // 检查是否是当前周
      this.checkIsCurrentWeek();
    },

    // 默认选择今天的日期
    selectDefaultDate(today) {
      // 设置选中日期对象
      this.selectedDate = {
        year: today.getFullYear(),
        month: today.getMonth() + 1,
        day: today.getDate(),
      };

      // 设置日期字符串格式 (yyyy-MM-dd)
      this.selectedDateStr = this.formatDate(today);

      // 更新UI显示
      this.updateSelectedDay();
    },

    // 格式化日期为字符串 yyyy-MM-dd
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    // 获取某一天所在周的周日(一周的第一天)
    getWeekStartDate(date) {
      const newDate = new Date(date);
      const day = newDate.getDay(); // 0是周日，1是周一
      newDate.setDate(newDate.getDate() - day); // 设置为当周的周日
      return newDate;
    },

    // 生成一周的数据
    generateWeekData(startDate) {
      const weekData = [];
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // 今天的日期，用于比较是否是今天
      const todayTime = today.getTime();

      // 从周日开始，生成一周的数据
      for (let i = 0; i < 7; i++) {
        const date = new Date(startDate);
        date.setDate(startDate.getDate() + i);
        date.setHours(0, 0, 0, 0);

        const dayObj = {
          date: date.getDate(),
          year: date.getFullYear(),
          month: date.getMonth() + 1,
          isToday: date.getTime() === todayTime,
          selected: false,
          isOtherMonth: date.getMonth() + 1 !== this.currentMonth,
          timestamp: date.getTime(), // 添加时间戳便于比较
          fullDate: this.formatDate(date), // 添加完整日期字符串
        };

        weekData.push(dayObj);
      }

      return weekData;
    },

    // 更新年月显示
    updateYearMonthDisplay(date) {
      // 获取日期所在周的中间日期（周三）作为月份显示参考
      const middleOfWeek = new Date(date);
      middleOfWeek.setDate(date.getDate() + 3); // 周日+3=周三

      this.currentYear = middleOfWeek.getFullYear();
      this.currentMonth = middleOfWeek.getMonth() + 1;
    },

    // 检查当前显示的周是否是当前周（实际日期）
    checkIsCurrentWeek() {
      const currentStart = this.currentWeekStartDate.getTime();
      const realCurrentStart = this.realCurrentWeekStartDate.getTime();
      this.isCurrentWeek = currentStart === realCurrentStart;
    },

    // 点击箭头切换周
    changeWeek(direction) {
      // 如果是向右且已经是当前周，则忽略
      if (direction === 1 && this.isCurrentWeek) return;

      // 计算新的周开始日期
      const newWeekStartDate = new Date(this.currentWeekStartDate);
      newWeekStartDate.setDate(newWeekStartDate.getDate() + direction * 7);

      // 更新当前周的开始日期
      this.currentWeekStartDate = newWeekStartDate;

      // 更新年月显示
      this.updateYearMonthDisplay(newWeekStartDate);

      // 生成新周数据
      this.currentWeekData = this.generateWeekData(newWeekStartDate);

      // 更新选中状态
      this.updateSelectedDay();

      // 检查是否是当前周
      this.checkIsCurrentWeek();
    },

    // 选择日期
    selectDate(item) {
      if (!item.date) return;

      // 更新选中日期对象
      this.selectedDate = {
        year: item.year,
        month: item.month,
        day: item.date,
      };

      // 更新日期字符串
      this.selectedDateStr = item.fullDate;

      // 更新UI显示
      this.updateSelectedDay();
      this.params.startTime = this.selectedDateStr;
      this.params.endTime = this.selectedDateStr;
      this.handleGetUserVisitActionList();
      // 获取宣教数据
      this.handleGetRecoveryMission();
    },

    // 更新选中的日期
    updateSelectedDay() {
      if (!this.selectedDate) return;

      // 更新选中状态
      this.currentWeekData.forEach((day) => {
        day.selected =
          day.date &&
          day.year === this.selectedDate.year &&
          day.month === this.selectedDate.month &&
          day.date === this.selectedDate.day;
      });
    },

    // 获取当前选中日期（供父组件调用）
    getSelectedDate() {
      return {
        dateStr: this.selectedDateStr,
        dateObj: this.selectedDate,
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  background: #f7f6f6;

  &-base {
    width: 100%;
    background: white;
    padding: 30rpx;
    box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);

    &-day {
      width: 100%;
      position: relative;
    }

    &-today {
      margin-top: 24rpx;

      &-title {
        font-weight: 500;
        font-size: 32rpx;
        color: #333333;
        line-height: 34rpx;
      }

      &-nodata {
        background-image: url('./static/img-mission-bg.png');
        width: 100%;
        height: 178rpx;
        margin-top: 24rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center center;

        &-subtitle {
          font-weight: 400;
          font-size: 30rpx;
          color: #29b7a3;
          line-height: 34rpx;
          margin-top: 36rpx;
        }

        &-btn {
          background: #ffffff;
          border-radius: 25rpx;
          font-weight: 500;
          font-size: 26rpx;
          color: #29b7a3;
          line-height: 34rpx;
          width: 168rpx;
          height: 50rpx;
          text-align: center;
          line-height: 50rpx;
          margin-top: 24rpx;
        }
      }

      &-item {
        margin-top: 24rpx;
        background: #f7f7fa;
        border-radius: 16rpx;
        padding: 24rpx;
        display: flex;
        justify-content: flex-start;
        align-items: center;

        &-image {
          width: 120rpx;
          height: 120rpx;
          border-radius: 24rpx;
        }

        &-text {
          flex: 1;
          margin-left: 22rpx;

          &-top {
            font-weight: 400;
            font-size: 30rpx;
            color: #333333;
            line-height: 34rpx;
          }

          &-bom {
            margin-top: 18rpx;
            font-weight: 400;
            font-size: 22rpx;
            color: #999999;
            line-height: 34rpx;
          }
        }

        &-tool {
          margin-left: 12rpx;

          &-top {
            font-weight: 400;
            font-size: 22rpx;
            color: #333333;
            line-height: 34rpx;
            text-align: right;
            margin-bottom: 20rpx;
          }

          &-bom {
            background: #dff4f1;
            border-radius: 25rpx;
            border: 1px solid #29b7a3;
            font-weight: 500;
            font-size: 24rpx;
            color: #29b7a3;
            padding: 14rpx 28rpx;
          }
        }
      }
    }
  }

  &-box {
    padding: 32rpx;

    &-data {
      background: #ffffff;
      box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);
      border-radius: 16rpx;
      padding: 34rpx;

      &-title {
        font-weight: 400;
        font-size: 30rpx;
        color: #333333;
        line-height: 34rpx;
        margin-bottom: 28rpx;
      }

      &-wapper {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 70rpx;

        &-box {
          display: flex;
          flex-direction: column;
          align-items: center;

          &-title {
            font-weight: 400;
            font-size: 22rpx;
            color: #666666;
            line-height: 34rpx;
            margin-bottom: 16rpx;
          }

          &-number {
            font-weight: 500;
            font-size: 38rpx;
            line-height: 34rpx;
            color: #29b7a3;
          }

          &-count {
            font-weight: 400;
            font-size: 22rpx;
            color: #29b7a3;
            line-height: 34rpx;
            display: block;
          }
        }
      }
    }

    &-list {
      margin-top: 30rpx;

      &-item {
        margin-bottom: 30rpx;
        background: #ffffff;
        box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);
        border-radius: 16rpx;
        padding: 28rpx;
        display: flex;
        align-items: center;
        justify-content: flex-start;

        &-image {
          width: 126rpx;
          height: 126rpx;
        }

        &-text {
          flex: 1;
          font-weight: 400;
          font-size: 30rpx;
          color: #333333;
          line-height: 34rpx;
          margin-left: 24rpx;
        }
      }
    }
  }
}

.date-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 0 20rpx 0;
  font-size: 28rpx;
  color: #333;
}

.arrow-button {
  width: 50rpx;
  height: 50rpx;
  border-radius: 25rpx;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;

  &.left {
    left: 0;
  }

  &.right {
    right: 0;

    &.disabled {
      opacity: 0.5;
      background-color: #e0e0e0;
    }
  }

  .arrow-icon {
    width: 100%;
    height: 100%;
  }
}

.week-row {
  display: flex;
  padding: 10rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.week-item {
  flex: 1;
  text-align: center;
  font-size: 24rpx;
  color: #666;
}

.days-container {
  display: flex;
  padding: 10rpx 0;
  height: 100rpx;
}

.day-item {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  margin: 10rpx 0;
}

.day-content {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #333;
}

.empty {
  visibility: hidden;
}

.other-month .day-content {
  color: #999;
}

.selected .day-content {
  background-color: #29c298;
  color: white;
  border-radius: 50%;
}

.today .day-content {
  border: 1rpx solid #29c298;
  border-radius: 50%;
}
</style>
