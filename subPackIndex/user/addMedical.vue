<template>
  <view class="container">
    <u-cell :border="false">
      <u-avatar :src="userInfo.HeadImg" slot="icon"></u-avatar>
      <span slot="title">{{ userInfo.Name }} {{ userInfo.Sex }} {{ age }}</span>
    </u-cell>

    <u-cell-group>
      <u-cell
        @click="showpicker = this.disabled ? false : true"
        :isLink="this.disabled ? false : true"
        :border="false"
      >
        <view slot="title">
          <p>就诊时间<span style="color: red">*</span></p>
        </view>
        <span slot="value">{{ reqdata.date }}</span>
      </u-cell>
    </u-cell-group>

    <u-cell-group>
      <u-cell :border="false">
        <view slot="title">
          <p>就诊医院<span style="color: red">*</span></p>
        </view>
        <span slot="value">
          <u--input
            inputAlign="right"
            :disabled="disabled"
            placeholder="请填写(必填)"
            border="none"
            v-model="reqdata.hospi"
          ></u--input>
        </span>
      </u-cell>
    </u-cell-group>

    <u-cell-group>
      <u-cell :border="false">
        <view slot="title">
          <p>就诊科室<span style="color: red">*</span></p>
        </view>
        <span slot="value">
          <u--input
            inputAlign="right"
            :disabled="disabled"
            placeholder="请填写(必填)"
            border="none"
            v-model="reqdata.dep"
          ></u--input>
        </span>
      </u-cell>
    </u-cell-group>

    <u-cell-group>
      <u-cell title="医生" :border="false">
        <span slot="value">
          <u--input
            inputAlign="right"
            :disabled="disabled"
            placeholder="请填写(非必填)"
            border="none"
            v-model="reqdata.doc"
          ></u--input>
        </span>
      </u-cell>
    </u-cell-group>

    <p style="margin: 10upx 14px; font-size: 15px">诊断</p>
    <u--textarea
      v-model="reqdata.diagnosis"
      :maxlength="999"
      :disabled="disabled"
      placeholder="请输入内容(非必填)"
      height="140"
    ></u--textarea>

    <p style="margin: 10upx 14px; font-size: 15px">症状描述</p>
    <u--textarea
      v-model="reqdata.dec"
      :maxlength="999"
      :disabled="disabled"
      placeholder="请输入内容(非必填)"
      height="140"
    >
    </u--textarea>

    <p style="margin: 10upx 14px; font-size: 15px">添加资料</p>

    <u-upload
      :disabled="disabled"
      v-if="!disabled"
      :fileList="fileList5"
      @afterRead="afterRead"
      @delete="deletePic"
      name="5"
      multiple
      :maxCount="9"
      width="100"
      height="100"
    ></u-upload>

    <view
      class="display-style1"
      style="flex-wrap: wrap"
      v-if="fileList5.length > 0 && disabled"
    >
      <u--image
        :showLoading="true"
        style="margin-bottom: 20rpx; margin-right: 20rpx"
        :src="i.url"
        width="100px"
        height="100px"
        v-for="(i, index) in fileList5"
        :key="i.url"
        @click="preImages"
      ></u--image>
    </view>

    <view style="height: 100px"></view>
    <u-button
      type="success"
      @click="save"
      shape="circle"
      text="提交"
      customStyle="width:90%;bottom: 30px;position: fixed; left: 50%;transform: translateX(-50%);z-index:999"
      v-if="!disabled"
    ></u-button>

    <u-datetime-picker
      :show="showpicker"
      mode="date"
      @confirm="dateChoose"
      v-model="pickerValue"
      @cancel="showpicker = false"
      :maxDate="Date.now()"
      :immediateChange="true"
    ></u-datetime-picker>
    <u-toast ref="uToast"></u-toast>
    <u-popup
      :show="showPrivacyPopup"
      :closeOnClickOverlay="false"
      :round="10"
      mode="bottom"
      @close="closePrivacyPopup()"
    >
      <view style="padding: 40rpx">
        <p
          style="
            text-align: center;
            margin-bottom: 40rpx;
            font-size: 20px;
            font-weight: 700;
          "
        >
          提示
        </p>
        <text>
          在你使用服务之前，请仔细阅读<text
            @click="onClickPrivacyPopupTitle()"
            style="color: #29b7a3"
            >{{ PrivacyPopupTitle }}</text
          >。如果你同意{{ PrivacyPopupTitle }}，请点击“同意”开始使用。
        </text>
        <view class="display-style" style="margin-top: 40rpx">
          <u-button
            text="拒绝"
            @click="handleRefusePrivacyAuthorization()"
          ></u-button>
          <!-- <u-button type="primary" id="agree-btn" text="同意" openType="agreePrivacyAuthorization"
						@agreeprivacyauthorization="handleAgreePrivacyAuthorization()"></u-button> -->
          <button
            id="agree-btn"
            class="btn-agree"
            open-type="agreePrivacyAuthorization"
            @agreeprivacyauthorization="handleAgreePrivacyAuthorization()"
          >
            同意
          </button>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
const app = getApp();
import { dateFormat, guid } from '@/utils/validate.js';

import { getVisit } from '@/api/record.js';
import { setUserCase } from '@/api/record.js';
import { uploadFileToServer } from '../../services/UploadService';
import { MedicalClientEvent } from '../../utils/eventKeys.js';
export default {
  data() {
    return {
      age: '',
      userInfo: {},
      showpicker: false,
      pickerValue: Number(new Date()),
      reqdata: {
        date: '',
        hospi: '',
        dep: '',
        doc: '',
        diagnosis: '',
        dec: '',
      },
      fileList5: [],
      okNextBer: true,
      id: '',
      disabled: false,
      chooseDate: '',
    };
  },
  onLoad(option) {
    this.saveWXCallBack();
    this.orderId = option.orderId;
    //代表是修改或者是查看
    if (option.orderId) {
      this.getData();
      uni.setNavigationBarTitle({
        title: '修改病历',
      });
      if (option.type == 'disable') {
        this.disabled = true;
        uni.setNavigationBarTitle({
          title: '查看病历',
        });
      }
    } else {
      this.id = guid();
    }
    this.age = option.age;
    this.userInfo = app.globalData.userInfo;
  },
  methods: {
    // 获取病历
    async getData() {
      let res = await getVisit({
        visitId: this.orderId,
      });
      if (res.Type == 200) {
        if (
          res.Data.VisitReports &&
          res.Data.VisitReports.length > 0 &&
          res.Data.VisitReports[0].Id
        ) {
          this.id = res.Data.VisitReports[0].Id;
        } else {
          this.id = guid();
        }
        let diagnosis = '';
        let dec = '';
        res.Data.VisitDiagnoses.forEach((e) => {
          if (e.DiagnoseTypeName.includes('诊断')) {
            diagnosis = e.DiagnoseName;
          }
          if (e.DiagnoseTypeName == '症状描述') {
            dec = e.DiagnoseName;
          }
        });
        this.reqdata = {
          date: dateFormat(res.Data.Vist.InDate, 'YYYY-MM-DD'),
          hospi: res.Data.Vist.OrganizationName,
          dep: res.Data.Vist.DepartmentName,
          doc: res.Data.Vist.DoctorName,
          diagnosis,
          dec: dec || res.Data.Vist.ChiefComplaint,
        };
        this.chooseDate = dateFormat(res.Data.Vist.InDate, 'YYYY-MM-DD', false);
        res.Data.VisitReportDetails.forEach((e) => {
          e.url = e.ReportUrl;
        });
        this.fileList5 = res.Data.VisitReportDetails;
        this.pickerValue = this.reqdata.date;
      }
    },
    // 选择时间
    dateChoose({ value, mode }) {
      this.showpicker = false;
      let newTime = dateFormat(value, 'YYYY-MM-DD', false);
      this.chooseDate = this.$dateFormat(value, 'YYYY-MM-DD', false);
      this.reqdata.date = newTime;
    },
    // 新增图片
    async afterRead(event) {
      // 当设置 mutiple 为 true 时, file 为数组格式，否则为对象格式
      let lists = [].concat(event.file);
      let fileListLen = this[`fileList${event.name}`].length;
      lists.map((item) => {
        this[`fileList${event.name}`].push({
          ...item,
          status: 'uploading',
          message: '上传中',
        });
        this.okNextBer = false;
      });
      for (let i = 0; i < lists.length; i++) {
        const result = await uploadFileToServer(lists[i].url);
        if (result) {
          if (i === lists.length - 1) {
            this.okNextBer = true;
          }
        }
        let item = this[`fileList${event.name}`][fileListLen];
        this[`fileList${event.name}`].splice(
          fileListLen,
          1,
          Object.assign(item, {
            status: 'success',
            message: '',
            url: result.data.Data,
          })
        );
        fileListLen++;
      }
    },
    // 删除图片
    deletePic(event) {
      this[`fileList${event.name}`].splice(event.index, 1);
    },
    //提交
    async save() {
      if (this.fileList5.length > 0 && !this.okNextBer) {
        this.$refs.uToast.show({
          message: '请等待图片上传完成再点击',
          type: 'error',
        });
        return;
      }
      if (!this.reqdata.date) {
        this.$refs.uToast.show({
          message: '请先选择就诊时间',
          type: 'error',
        });
        return;
      }
      if (!this.reqdata.hospi) {
        this.$refs.uToast.show({
          message: '请先输入就诊医院',
          type: 'error',
        });
        return;
      }
      if (!this.reqdata.dep) {
        this.$refs.uToast.show({
          message: '请先输入就诊科室',
          type: 'error',
        });
        return;
      }
      // if(this.fileList5.length == 0){
      // 	this.$refs.uToast.show({
      // 		message: '请先上传资料',
      // 		type: 'error'
      // 	})
      // 	return
      // }
      let VisitReportDetails = [];
      this.fileList5.forEach((e) => {
        VisitReportDetails.push({
          VisitReportId: this.id,
          ReportUrl: e.url,
        });
      });
      let data = {
        Vist: {
          Id: this.orderId,
          Source: '0',
          PatientId: app.globalData.userInfo.Id,
          Name: this.userInfo.Name,
          Age: this.age,
          Sex: this.userInfo.Sex,
          DepartmentName: this.reqdata.dep,
          DoctorName: this.reqdata.doc,
          InDate: this.chooseDate,
          OrganizationName: this.reqdata.hospi,
          IsSelfBuild: true,
        },
        VisitDiagnoses: [
          {
            DiagnoseTypeName: '诊断',
            DiagnoseName: this.reqdata.diagnosis,
            IsMain: true,
          },
          {
            DiagnoseTypeName: '症状描述',
            DiagnoseName: this.reqdata.dec,
          },
        ],
        VisitReports: [
          {
            Id: this.id,
          },
        ],
        VisitReportDetails: VisitReportDetails,
      };
      console.log('发送请求的data', data);
      let res = await setUserCase(data);
      if (res.Type == 200) {
        this.$refs.uToast.show({
          message: res.Content,
          type: 'success',
        });

        // 通知，该病历已更新
        uni.$emit(MedicalClientEvent.updateMedical, this.orderId);
        uni.navigateBack();
      } else {
        this.$refs.uToast.show({
          message: res.Content,
          type: 'error',
        });
      }
    },
    preImages() {
      let arr = [];
      this.fileList5.forEach((e) => {
        arr.push(e.url);
      });
      uni.previewImage({
        current: 0,
        urls: arr,
      });
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  padding: 10px;

  /deep/ .u-cell {
    background-color: white;
  }

  /deep/ .u-transition {
    margin-bottom: 20rpx;
    margin-right: 20rpx;
  }

  /deep/ .u-input__content__field-wrapper__field {
    background-color: white;
  }

  /deep/ .u-cell__body__content {
    flex: none !important;
  }

  /deep/ ._span {
    flex: 1 !important;
    text-align: right;
  }
}
</style>
