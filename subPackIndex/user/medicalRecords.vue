<template>
  <view class="container">
    <!-- 头部卡片 -->
    <view class="container-top">
      <u-cell :border="false">
        <u-image
          slot="icon"
          :showLoading="true"
          shape="circle"
          :src="image"
          width="65px"
          height="65px"
        >
        </u-image>
        <view class="container-top-title" slot="title">
          <p>{{ name }}</p>
          <p style="margin-top: 10px">{{ age }} {{ sex }}</p>
        </view>
        <u-button
          text="默认"
          slot="value"
          customStyle="width:70px;backgroundColor:rgba(255,255,255,0.4);color:#fff;border:none"
        ></u-button>
      </u-cell>
      <u-divider :dashed="true" customStyle="marginTop:-10px"></u-divider>
      <view class="container-bottom" @click="toPerfect"> 完善档案 </view>
      <view class="container-top-circle circle-left"></view>
      <view class="container-top-circle circle-right"></view>
    </view>

    <!-- 筛选 -->
    <u-cell :border="false">
      <view class="container-time" slot="title" @click="chooseTime">
        <span>{{ title }}</span>
        <u-icon name="arrow-down" color="#000000" size="16"></u-icon>
      </view>
      <view style="color: #29b7a3" slot="value" @click="addMedical">
        添加病历
      </view>
    </u-cell>

    <!-- 列表 -->
    <u-list @scrolltolower="scrolltolower" v-if="userList.length > 0">
      <u-list-item v-for="(item, index) in userList" :key="item.Id">
        <view class="container-box" @click="toseeInfo(index)">
          <u-cell :border="false">
            <view class="container-box-text" slot="title">
              <p style="margin-bottom: 6px">{{ item.InDate }}</p>
              <p
                style="
                  margin-bottom: 6px;
                  color: #666666;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                "
              >
                {{ item.OrganizationName }}
                {{ item.DepartmentName || '' }}
              </p>
            </view>
            <p
              style="
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                margin-bottom: 6px;
                color: #666666;
              "
              slot="label"
            >
              {{ item.DoctorName || '' }} 诊断:{{
                item.DiagnoseDesc[0].DiagnoseName || ''
              }}
            </p>

            <view class="container-icon" slot="value" @click.stop="">
              <u-icon
                name="edit-pen-fill"
                color="#29B7A3"
                size="30"
                @click.stop="update(index)"
                v-if="item.IsSelfBuild"
                customStyle="marginRight:10px"
              ></u-icon>
              <u-icon
                name="trash-fill"
                color="red"
                size="30"
                @click.stop="delMedical(index)"
                v-if="item.IsSelfBuild"
              ></u-icon>
            </view>
          </u-cell>
        </view>
      </u-list-item>
      <u-list-item>
        <view style="height: 200rpx"></view>
      </u-list-item>
    </u-list>
    <view class="btnButtomStyle" @click="handleToArchives"> 个人健康档案 </view>
    <u-empty
      mode="data"
      icon="http://cdn.uviewui.com/uview/empty/data.png"
      text="当前没有病历档案"
      v-if="userList.length == 0"
    ></u-empty>
    <u-action-sheet
      :actions="list"
      title="筛选条件"
      :show="show"
      @select="selectSheet"
      :closeOnClickAction="close"
      :closeOnClickOverlay="true"
      @close="close"
    >
    </u-action-sheet>
    <u-modal
      :show="show1"
      title="温馨提示"
      :showCancelButton="true"
      content="确定要删除吗?"
      @confirm="confirm"
      @cancel="close"
    ></u-modal>
    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script>
const app = getApp();
import { delMedical } from '@/api/record.js';
import { getUserCaseInfo } from '@/api/record.js';
import { dateFormat } from '@/utils/validate.js';
import { MedicalClientEvent } from '@/utils/eventKeys.js';

export default {
  data() {
    return {
      query: {
        userId: app.globalData.userInfo.Id,
        dateSerachType: 2,
        type: 0,
        pageindex: 1,
        pagesize: 10,
      },
      userList: [],
      image: app.globalData.userInfo.HeadImg,
      title: '最近三个月',
      list: [
        {
          name: '最近一个月',
          title: '最近一个月',
          dateSerachType: 1,
        },
        {
          name: '最近三个月',
          title: '最近三个月',
          dateSerachType: 2,
        },
        {
          name: '半年内',
          title: '半年内',
          dateSerachType: 3,
        },
        {
          name: '全部',
          title: '全部',
          dateSerachType: 4,
        },
      ],
      show: false,
      show1: false,
      name: app.globalData.userInfo.Name,
      age: app.globalData.userInfo.Age,
      sex: app.globalData.userInfo.Sex,
      delId: '',
    };
  },

  // 监听病历更新事件
  handleMedicalUpdate: (id) => {},

  onLoad() {
    this.getUserList();

    const that = this;
    // 监听病历更新事件
    this.handleMedicalUpdate = (id) => {
      console.debug('监听病历更新事件', id);
      that.getUserList(1);
    };
    uni.$on(MedicalClientEvent.updateMedical, this.handleMedicalUpdate);
  },

  onUnload() {
    uni.$off(MedicalClientEvent.updateMedical, this.handleMedicalUpdate);
  },

  methods: {
    handleToArchives() {
      uni.navigateTo({
        url: '/subGauge/loginByUser',
      });
    },
    // 点击完成病历
    toPerfect() {
      uni.navigateTo({
        url: './improveArchives?age=' + this.age,
      });
    },
    // 添加病历
    addMedical() {
      uni.navigateTo({
        url: './addMedical?age=' + this.age,
      });
    },
    async confirm() {
      console.log('id', this.delId);
      let res = await delMedical(this.delId);
      if (res.Type == 200) {
        this.$refs.uToast.show({
          message: '删除成功',
          type: 'success',
        });
        this.show1 = false;
        this.userList = [];
        this.query.pageindex = 1;
        this.getUserList();
      } else {
        this.$refs.uToast.show({
          message: res.Content,
          type: 'error',
        });
      }
    },
    // 获取患者最近的就诊列表
    async getUserList(type) {
      if (type) {
        this.query.pageindex = 1;
        this.userList = [];
      }
      uni.showLoading({
        title: '正在加载数据',
      });
      let res = await getUserCaseInfo(this.query);
      if (res.Type == 200 && res.Data.length > 0) {
        res.Data.forEach((e) => {
          e.InDate = dateFormat(e.InDate, 'YYYY-MM-DD');
          e.DiagnoseDesc = e.VisitDiagnoses.filter((k) =>
            k.DiagnoseTypeName.includes('诊断')
          );
          this.userList.push(e);
        });
      }
      this.$nextTick(() => {
        uni.hideLoading();
      });
    },
    scrolltolower() {
      this.query.pageindex++;
      this.getUserList();
    },
    // 选择时间
    chooseTime() {
      this.show = true;
    },
    selectSheet(e) {
      this.title = e.title;
      this.query.dateSerachType = e.dateSerachType;
      this.query.pageindex = 1;
      this.userList = [];
      this.getUserList();
      this.close();
    },
    close() {
      this.show = false;
      this.show1 = false;
    },
    // 删除某个病历
    delMedical(index) {
      this.delId = this.userList[index].Id;
      this.show1 = true;
    },
    update(index) {
      uni.navigateTo({
        url:
          './addMedical?age=' +
          this.age +
          '&orderId=' +
          this.userList[index].Id,
      });
    },
    toseeInfo(index) {
      let url = '';
      if (this.userList[index].IsSelfBuild) {
        url =
          './addMedical?age=' +
          this.age +
          '&orderId=' +
          this.userList[index].Id +
          '&type=disable';
        // if (this.userList[index].VisitReports.length > 0 && this.userList[index].VisitReports[0].Id) {
        // 	url += '&ID=' + this.userList[index].VisitReports[0].Id
        // }
      } else {
        url =
          '/subPackIndex/user/outpatientArchives?visitId=' +
          this.userList[index].Id;
      }

      uni.navigateTo({
        url,
      });
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  padding: 10px;
  overflow: hidden;

  /deep/ .u-cell__body__content {
    width: 80% !important;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  /deep/ .u-cell__title {
    width: 80%;
  }

  /deep/ .u-list {
    max-height: calc(100vh - 190px);
    height: auto;
    padding-bottom: 40rpx;
  }

  .container-box {
    /deep/ .u-cell {
      // background-color: white;
      box-shadow: -1px -1px 4px -2px rgba(0, 0, 0, 0.3) inset;
      border-radius: 12rpx;
    }

    .container-box-text {
      font-size: 16px;
      // overflow: hidden;
      // text-overflow:ellipsis;
      // white-space: nowrap;
      // width: 70%
    }

    .container-icon {
      display: flex;
      align-items: center;
      justify-content: space-around;
    }

    background-color: white;
    margin-bottom: 10rpx;
  }

  .container-time {
    font-size: 18px;
    display: flex;
    align-items: center;
    font-weight: 700;
  }

  .container-top {
    width: 100%;
    border-radius: 10px;
    padding-bottom: 10px;
    position: relative;

    .container-top-circle {
      position: absolute;
      width: 20px;
      height: 20px;
      background-color: white;
      top: 50%;
      transform: translateY(-50%);
      border-radius: 50%;
      overflow: hidden;
    }

    .circle-left {
      left: -10px;
    }

    .circle-right {
      right: -10px;
    }

    .container-bottom {
      width: 90%;
      height: 50px;
      text-align: center;
      line-height: 50px;
      color: #29b7a3;
      border-radius: 10px;
      background-color: white;
      font-size: 16px;
      margin: 0 auto;
      font-weight: 600;
    }

    background-image: linear-gradient(to right, #46c5ba, #47cbc4);

    .container-top-title {
      color: white;
      font-size: 18px;
      margin-left: 20px;
    }
  }
}
</style>
