<template>
  <view class="container">
    <view
      :style="{ top: (showImagePos.top || 0) + 'px' }"
      style="
        z-index: 10;
        width: 266rpx;
        height: 56rpx;
        position: absolute;
        right: 32rpx;
      "
      v-if="showCamraImage"
      @click="showCamraImage = false"
    >
      <image
        src="../static/tic.png"
        style="width: 100%; height: 100%; position: absolute"
      >
      </image>
      <span
        style="
          color: white;
          text-align: center;
          line-height: 56rpx;
          font-size: 12px;
          left: 0;
          right: 0;
          position: absolute;
        "
        >点击相机可自动识别<span style="margin-left: 5px">x</span>
      </span>
    </view>
    <u-navbar
      title="诊后报到"
      @rightClick="rightClick"
      @leftClick="leftClick"
    ></u-navbar>
    <view
      class="display-style3 container-topTip"
      v-if="userInfo.WorkflowStatus !== 2"
    >
      <u-icon name="info-circle-fill" color="#EFB022" size="28"></u-icon>
      <text style="margin-left: 10rpx; color: #989898; font-size: 28rpx"
        >实名就诊，请填写身份证和名字</text
      >
    </view>
    <u-cell
      :border="false"
      customStyle="borderRadius:16rpx"
      v-if="userInfo.WorkflowStatus === 2"
    >
      <p slot="title">
        <span style="color: #29b7a3; margin-right: 24rpx">{{
          userInfo.Name
        }}</span>
        (本人 {{ userInfo.Sex }} {{ userInfo.Age + '岁' }})
      </p>
    </u-cell>

    <u-cell-group v-else>
      <u-cell>
        <view slot="title">
          <p>姓名<span style="color: red">*</span></p>
        </view>
        <u-input
          @blur="onNameChange"
          slot="value"
          placeholder="请输入真实姓名"
          border="none"
          v-model="smQuery.Name"
          inputAlign="right"
        ></u-input>
      </u-cell>
      <u-cell :border="false" customStyle="justify-content: space-between">
        <view slot="title">
          <p>性别<span style="color: red">*</span></p>
        </view>
        <view slot="value" style="display: flex">
          <view
            class="u-page__tag-item"
            style="margin-right: 10rpx"
            v-for="(item, index) in sexList"
            :key="index"
          >
            <MPTag
              shape="circle"
              iconPosition="right"
              :icon="item.name === '男' ? 'man' : 'woman'"
              :text="item.name"
              :plain="!item.checked"
              type="success"
              :name="index"
              @click="radioClick"
            >
            </MPTag>
          </view>
        </view>
      </u-cell>
      <u-cell>
        <view slot="title" id="targetComponent">
          <p>证件号码</p>
        </view>
        <u-input
          slot="value"
          placeholder="请输入真实证件号码"
          type="idcard"
          border="none"
          v-model="smQuery.UserCertificates[0].CertificateValue"
          inputAlign="right"
          @blur="IdCardblur"
        >
        </u-input>
        <ocr-navigator
          slot="right-icon"
          @onSuccess="ocrSuccess"
          certificateType="idCard"
          :opposite="false"
        >
          <u-icon name="camera" color="#24BAA3" size="28"></u-icon>
        </ocr-navigator>
      </u-cell>
      <u-cell>
        <view slot="title">
          <p>年龄<span style="color: red">*</span></p>
        </view>
        <u-input
          slot="value"
          placeholder="请输入真实年龄"
          border="none"
          v-model="smQuery.Age"
          inputAlign="right"
          type="number"
          @blur="Ageblur"
        ></u-input>
      </u-cell>
    </u-cell-group>

    <p style="font-size: 36rpx; margin: 32rpx 0; font-weight: 700">
      请选择就诊疾病
      <span style="font-weight: 600; font-size: 30rpx">(点击选择)</span>
    </p>
    <view class="u-demo-block__content">
      <view
        class="u-page__tag-item"
        v-for="(item, index) in Entry"
        :key="item.Id"
      >
        <u-tag
          v-if="showMore ? true : index < showCount"
          :text="item.Key"
          :plain="!item.checked"
          size="large"
          :name="item.Id"
          @click="checkboxClick"
          type="success"
        >
        </u-tag>
      </view>
      <text
        @click="showMore = !showMore"
        style="color: #29b7a3"
        v-if="Entry.length > showCount"
        >{{ showMore ? '收起' : '更多' }}</text
      >

      <view
        class="u-demo-block__content-select"
        style="margin-top: 20rpx"
        v-show="isInEntryList.length > 0"
      >
        <u-cell
          :title="o.Key"
          v-for="o in isInEntryList"
          @click="cellClick(o)"
          :key="o.Id"
        ></u-cell>
      </view>
    </view>
    <u-textarea
      count
      v-model="query.Describing"
      placeholder="请填写其他的疾病"
      :maxlength="300"
      :height="100"
      confirmType="return"
      @input="describingChange"
    >
    </u-textarea>

    <p style="font-size: 36rpx; margin: 32rpx 0; font-weight: 700">病历资料</p>

    <u-cell-group>
      <u-cell @click="showpicker = true" :border="false" :isLink="true">
        <view slot="title">
          <p>线下就诊时间<span style="color: red">*</span></p>
        </view>
        <view slot="value" style="flex: 1; text-align: right">{{ date }}</view>
      </u-cell>
    </u-cell-group>
    <u-cell-group>
      <u-cell :border="false" :value="null">
        <view slot="title">
          <span>就诊医院<span style="color: red">*</span></span>
        </view>
        <span slot="value">
          <u--input
            readonly
            inputAlign="right"
            placeholder="请填写(必填)"
            border="none"
            v-model="query.HospitName"
          >
          </u--input>
        </span>
      </u-cell>
    </u-cell-group>
    <u-cell-group>
      <u-cell @click="onselectDaptOrDoc('Dapt')" :border="false" :isLink="true">
        <view slot="title">
          <p>就诊科室<span style="color: red">*</span></p>
        </view>
        <span slot="value" style="flex: 1; text-align: right">{{
          query.HospitDepartmentName ? query.HospitDepartmentName : '请选择'
        }}</span>
      </u-cell>
    </u-cell-group>
    <u-cell-group v-if="onIsShowChooseDoctor()">
      <u-cell @click="onselectDaptOrDoc('Doc')" :border="false" :isLink="true">
        <view slot="title">
          <p>就诊医生</p>
        </view>
        <span slot="value" style="flex: 1; text-align: right">
          {{ query.HospitDoctName ? query.HospitDoctName : '请选择' }}
        </span>
      </u-cell>
    </u-cell-group>

    <p style="font-size: 36rpx; margin: 32rpx 0; font-weight: 700">
      影像资料(最多上传9张)
    </p>

    <u-upload
      :fileList="fileList5"
      @afterRead="afterRead"
      @delete="deletePic"
      name="5"
      multiple
      :maxCount="9"
      width="100"
      height="100"
      customStyle="marginLeft:30rpx"
    ></u-upload>

    <view class="bomm-btn-style" style="height: 100px"></view>
    <u-button
      :loading="buttonDisable"
      type="success"
      @click="onSave"
      shape="circle"
      text="提交"
      customStyle="width:90%;bottom: 30px;position: fixed; left: 50%;transform: translateX(-50%);fontSize:16px;z-index:99"
    >
    </u-button>

    <u-modal
      :show="show"
      title="温馨提示"
      content="返回后将不保存已填写信息,确定返回吗？"
      @confirm="confirm1"
      @cancel="show = false"
      :showCancelButton="true"
    ></u-modal>

    <u-datetime-picker
      :show="showpicker"
      mode="date"
      @confirm="dateChoose"
      v-model="pickerValue"
      @cancel="showpicker = false"
      :maxDate="maxDate"
      :formatter="formatter"
    ></u-datetime-picker>

    <u-datetime-picker
      :show="selectBri"
      mode="date"
      @confirm="selectBriChoose"
      v-model="pickerValue1"
      @cancel="selectBri = false"
      :maxDate="maxDate"
      :minDate="-1483257600000"
      :formatter="formatter"
    >
    </u-datetime-picker>

    <u-picker
      :show="selectSexOrBri"
      :columns="Sexcolumns"
      @confirm="confirmPicker($event, 'Sex')"
      @cancel="selectSexOrBri = false"
      :closeOnClickOverlay="true"
    ></u-picker>

    <u-picker
      :show="selectDaptOrDoc"
      :columns="DaptOrDoclumns"
      @confirm="confirmSelectDaptOrDoc($event, DaptOrDoc)"
      @cancel="selectDaptOrDoc = false"
      :closeOnClickOverlay="true"
      keyName="Name"
    ></u-picker>
    <u-toast ref="uToast"></u-toast>
    <u-popup
      :show="showPrivacyPopup"
      :closeOnClickOverlay="false"
      :round="10"
      mode="bottom"
      @close="closePrivacyPopup()"
    >
      <view style="padding: 40rpx">
        <p
          style="
            text-align: center;
            margin-bottom: 40rpx;
            font-size: 20px;
            font-weight: 700;
          "
        >
          提示
        </p>
        <text>
          在你使用服务之前，请仔细阅读<text
            @click="onClickPrivacyPopupTitle()"
            style="color: #29b7a3"
            >{{ PrivacyPopupTitle }}</text
          >。如果你同意{{ PrivacyPopupTitle }}，请点击“同意”开始使用。
        </text>
        <view class="display-style" style="margin-top: 40rpx">
          <u-button
            text="拒绝"
            @click="handleRefusePrivacyAuthorization()"
          ></u-button>
          <!-- <u-button type="primary" id="agree-btn" text="同意" openType="agreePrivacyAuthorization"
						@agreeprivacyauthorization="handleAgreePrivacyAuthorization()"></u-button> -->
          <button
            id="agree-btn"
            class="btn-agree"
            open-type="agreePrivacyAuthorization"
            @agreeprivacyauthorization="handleAgreePrivacyAuthorization()"
          >
            同意
          </button>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
const app = getApp();
import { dateFormat, guid } from '@/utils/validate.js';
import { setUserCase } from '@/api/record.js';
import {
  SetUserClaims,
  saveUserAuthenticationAudit,
  getUserProfile,
  updatePart,
  setOrCanDoc,
  getLatestAuth,
} from '@/api/passport.js';

import {
  idCardTrue,
  idCardGetSex,
  idCardGetBri,
  briGetAge,
} from '@/utils/utils';
import { newReadDict, GetDiseaseList } from '@/api/dictionary.js';
import { SaveTags, GetTags } from '@/api/record.js';
import { userSendMessageToDoc } from '@/api/message.js';
import { automaticApproval } from '@/api/identity.js';
import MPTag from '@/components/mp-tag/u-tag.vue';
import { uploadFileToServer } from '../../services/UploadService';
import { queryDepartments } from '../../api/passport.department';
import { getOrgUserOrDeptUserByRoleType } from '../../api/passport.user.department';
export default {
  components: {
    MPTag,
  },
  data() {
    return {
      maxDate: new Date().getTime(),
      sexList: [
        {
          name: '男',
          checked: false,
        },
        {
          name: '女',
          checked: false,
        },
      ],
      showCamraImage: false,
      showCount: 20,
      showMore: false,
      selectDaptOrDoc: false,
      DaptOrDoc: null,
      pickerValue1: -28800000,
      selectBri: false,
      selectSexOrBri: false,
      Sexcolumns: [['男', '女']],
      Entry: [],
      smQuery: {
        UserId: app.globalData.userInfo.Id,
        Name: '',
        Sex: '',
        Birthday: '',
        Age: null,
        UserCertificates: [
          {
            CertificateType: 'idCard',
            CertificateValue: '',
          },
        ],
      },
      show: false,
      userInfo: {},
      docInfo: {
        RoleTypes: [],
      },
      query: {
        chooseDate: '',
        HospitName: '',
        HospitDepartmentName: '',
        HospitDoctName: '',
        Describing: '',
      },
      showpicker: false,
      pickerValue: Number(new Date()),
      date: '',
      fileList5: [],
      okNextBer: false,
      DaptOrDoclumns: [],
      selectDaptListArr: [],
      selectDcoListArr: [],
      reqDepaList: [],
      HospitDoctNameId: '',
      buttonDisable: false,
      isInEntryList: [],
      showImagePos: {},
      pageType: '',
    };
  },
  onLoad({ docInfo, type }) {
    if (type) {
      // 表示是否是通过扫描二维码进入的
      this.pageType = type;
    }
    this.saveWXCallBack();
    this.userInfo = app.globalData.userInfo;
    if (this.userInfo.Name) {
      this.smQuery.Name = this.userInfo.Name;
    }
    if (this.userInfo.Sex) {
      this.smQuery.Sex = this.userInfo.Sex;
      this.sexList.map((item, index) => {
        item.checked = item.name === this.userInfo.Sex ? true : false;
      });
    }
    if (this.userInfo.Age) {
      this.smQuery.Age = this.userInfo.Age;
    }
    if (
      this.userInfo.UserCertificates &&
      this.userInfo.UserCertificates.length > 0 &&
      this.userInfo.UserCertificates[0].CertificateValue
    ) {
      this.smQuery.UserCertificates[0].CertificateValue =
        this.userInfo.UserCertificates[0].CertificateValue;
    }
    this.docInfo = JSON.parse(decodeURIComponent(docInfo));
    this.query.HospitName = this.docInfo.OrganizationName;
    this.query.HospitDepartmentName = this.docInfo.DepartmentName;
    this.docOrganizationId = this.docInfo.OrganizationId;
    if (!this.docInfo.RoleTypes.includes('doctor')) {
      this.query.HospitDoctName = '';
    } else {
      this.query.HospitDoctName = this.docInfo.Name;
    }
    this.date = dateFormat(Date.now(), 'YYYY-MM-DD', false);
    this.query.chooseDate = dateFormat(
      Date.now(),
      'YYYY-MM-DD HH:mm:ss',
      false
    );
    this.onGetDiseaseCode();
    this.selectDaptList(this.docInfo.OrganizationId);
  },
  onReady() {
    if (this.userInfo.WorkflowStatus !== 2) {
      this.$nextTick(() => {
        const query = uni.createSelectorQuery().in(this);
        query
          .select('#targetComponent')
          .boundingClientRect((rect) => {
            // rect 包含了组件的位置信息
            console.log('组件位置信息：', rect);
            if (rect && rect.id) {
              this.showImagePos = {
                top: rect.top - 30,
              };
            }
          })
          .exec();
      });
      this.showCamraImage = true;
    } else {
      this.showCamraImage = false;
    }
  },
  methods: {
    onIsShowChooseDoctor() {
      return !this.docInfo?.RoleTypes.includes('therapist');
    },
    onNameChange(e) {
      getApp().changeUserInfo('Name', e);
    },
    radioClick(name) {
      this.sexList.map((item, index) => {
        item.checked = index === name ? true : false;
      });
      this.smQuery.Sex = this.sexList[name].name;
      getApp().changeUserInfo('Sex', this.sexList[name].name);
    },
    ocrSuccess(e) {
      console.log('识别结果', e);
      this.$log.info('使用了身份证识别，并识别成功');
      if (e.type === 'onSuccess') {
        const data = e.detail;
        this.smQuery.UserCertificates[0].CertificateValue = data.id.text;
        this.smQuery.Sex = data.gender.text;
        this.smQuery.Name = data.name.text;
        this.smQuery.Birthday = data.birth.text;
        this.smQuery.Age = briGetAge(data.birth.text);
        getApp().changeUserInfo('Name', data.name.text);
        getApp().changeUserInfo('Sex', data.gender.text);
        getApp().changeUserInfo('Age', this.smQuery.Age);
        getApp().changeUserInfo('idCard', data.id.text);
      }
    },
    cellClick(e) {
      console.log('选中', e);
      const index = this.Entry.findIndex((v) => v.Id === e.Id);
      console.log('index', index);
      if (index > -1) {
        this.showCount++;
        this.checkboxClick(e.Id, true, () => {
          const index1 = this.Entry.findIndex((v) => v.Id === e.Id);
          console.log('index1', index1);
          console.log('this.Entry123', this.Entry);
          this.$nextTick(() => {
            this.Entry.splice(
              this.showCount - 1,
              0,
              this.Entry.splice(index1, 1)[0]
            );
          });
        });
      }
      this.isInEntryList = [];
      this.query.Describing = this.query.Describing.split(/[,， ]+/)
        .slice(0, this.query.Describing.split(/[,， ]+/).length - 1)
        .join(' ');
    },
    describingChange(key) {
      // 以空格或者逗号分割
      const lastKeyWord = key.split(/[,， ]+/)[key.split(/[,， ]+/).length - 1];
      if (lastKeyWord !== '' && lastKeyWord !== ',' && lastKeyWord !== '，') {
        const isInEntryList = this.Entry.filter((v) =>
          v.Key.includes(lastKeyWord)
        );
        this.isInEntryList = isInEntryList;
        if (isInEntryList.length > 0) {
        }
      } else {
        this.isInEntryList = [];
      }
    },
    async GetTagsInfo() {
      const data = {
        UserIds: [app.globalData.userInfo.Id],
        OrganizationId: this.docInfo.OrganizationId,
        Source: 0,
      };
      const res = await GetTags(data);
      const userSet = [];
      if (res.Type === 200) {
        res.Data.forEach((e) => {
          const isHave = this.Entry.some((v) => v.Key === e.Tag);
          if (isHave) {
            const index = this.Entry.findIndex((o) => o.Key === e.Tag);
            console.log('index', index);
            this.Entry[index].checked = true;
            this.Entry.sort((obj1, obj2) => {
              if (obj1.checked && !obj2.checked) {
                return -1;
              } else if (!obj1.checked && obj2.checked) {
                return 1;
              } else {
                return 0;
              }
            });
          } else {
            userSet.push(e.Tag);
          }
        });
      }
      let uniqueArr = [...new Set(userSet)];
      this.query.Describing = uniqueArr.join(' ');
    },
    IdCardblur(e) {
      getApp().changeUserInfo('idCard', e);
      if (e && !idCardTrue(e)) {
        this.$refs.uToast.show({
          message: '身份证格式输入错误',
          type: 'error',
        });
        return;
      } else if (e && idCardTrue(e)) {
        this.smQuery.Sex = idCardGetSex(e);
        this.smQuery.Birthday = idCardGetBri(e);
        this.smQuery.Age = briGetAge(this.smQuery.Birthday);
      }
    },
    Ageblur(e) {
      console.log('e', e);
      const newDate = dateFormat(new Date(), 'YYYY-MM-01', false).split('-');
      newDate[0] = String(dateFormat(new Date(), 'YYYY') - e);
      this.smQuery.Birthday = newDate.join('-');
    },
    async getDepaDocList(id) {
      const data = {
        organizationId: this.docOrganizationId,
        departmentId: id,
        roleType: 'doctor',
      };
      const res = await getOrgUserOrDeptUserByRoleType(data);
      if (res.Type === 200) {
        const arr = [];
        res.Data.forEach((e) =>
          arr.push({
            Name: e.Name,
            Value: e.Id,
          })
        );
        this.selectDcoListArr = [arr];
      }
    },
    confirmSelectDaptOrDoc($event, type) {
      if (type === 'Dapt') {
        if (this.query.HospitDepartmentName != $event.value[0].Name) {
          this.query.HospitDoctName = '';
        }
        this.query.HospitDepartmentName = $event.value[0].Name;
        this.getDepaDocList($event.value[0].Value);
      } else if (type === 'Doc') {
        console.log('$event', $event);
        this.query.HospitDoctName = $event.value[0].Name;
        this.HospitDoctNameId = $event.value[0].Value;
      }
      this.selectDaptOrDoc = false;
    },
    onselectDaptOrDoc(type) {
      this.DaptOrDoclumns = [];
      if (type === 'Dapt') {
        this.DaptOrDoc = 'Dapt';
        this.DaptOrDoclumns = this.selectDaptListArr;
      } else if (type === 'Doc') {
        this.DaptOrDoc = 'Doc';
        this.DaptOrDoclumns = this.selectDcoListArr;
      }
      this.selectDaptOrDoc = true;
    },
    // 获取当前医院下的所有科室
    async selectDaptList(hospId) {
      const res = await queryDepartments({
        OrgId: hospId,
        IsEnabled: true,
        Pageable: false,
        SingleOne: false,
      });
      if (res.Data) {
        const arr = [];
        res.Data.forEach((e) =>
          arr.push({
            Name: e.Name,
            Value: e.Id,
          })
        );
        this.reqDepaList = res.Data;
        this.selectDaptListArr = [arr];
        if (this.query.HospitDepartmentName) {
          const chooseDepaId = res.Data.filter(
            (k) => k.Name === this.query.HospitDepartmentName
          )[0].Id;
          this.getDepaDocList(chooseDepaId);
        }
        // this.DaptOrDoclumns = arr;
      }
    },
    formatter(type, value) {
      if (type === 'year') {
        return `${value}年`;
      }
      if (type === 'month') {
        return `${value}月`;
      }
      if (type === 'day') {
        return `${value}日`;
      }
      return value;
    },
    // 选择时间
    selectBriChoose({ value, mode }) {
      this.selectBri = false;
      let newTime = dateFormat(value, 'YYYY-MM-DD', false);
      this.smQuery.Birthday = newTime;
    },
    confirmPicker($event, type) {
      if (type === 'Sex') {
        this.smQuery.Sex = $event.value[0];
      }
      this.selectSexOrBri = false;
    },
    async onGetDiseaseCode() {
      const req = {
        PageCondition: {
          PageIndex: 1,
          PageSize: 99,
          SortConditions: [
            {
              SortField: 'Id',
              ListSortDirection: 1,
            },
          ],
        },
        FilterGroup: {
          Rules: [],
          Groups: [
            {
              Rules: [
                {
                  Field: 'Code',
                  Value: 'DiseaseTag',
                  Operate: 11,
                },
              ],
              Operate: 2,
            },
          ],
          Operate: 1,
        },
      };
      let res = await GetDiseaseList(req);
      if (res.Type === 200) {
        this.onGetEntry(res.Data?.Rows[0].Id * 1, res.Data.Rows[0].Code);
      }
    },
    async onGetEntry(id, Codes) {
      const data = {
        OrganizationId: this.docInfo.OrganizationId,
        DepartmentId: this.docInfo.DepartmentId,
        Codes: [Codes],
        LoadDefaultOrganization: false, //=true  会匹配 OrganizationId=null的 项
        LoadDefaultDepartment: true, //=true  会匹配 DepartmentId=null的 项
        PageIndex: 1,
        PageSize: 99999,
      };
      const res = await newReadDict(data);
      if (res.Type === 200) {
        res.Data.forEach((e) => {
          e.checked = false;
        });
        this.Entry = res.Data;
        // 获取患者的疾病标签
        this.GetTagsInfo();
      }
    },
    checkboxClick(Id, flag, cb) {
      console.log('this.Entry', this.Entry);
      const index = this.Entry.findIndex((v) => v.Id === Id);
      console.log(index);
      if (index > -1) {
        this.Entry[index].checked = flag ? true : !this.Entry[index].checked;
        cb && cb();
      }
    },
    // 选择时间
    dateChoose({ value, mode }) {
      this.showpicker = false;
      let newTime = dateFormat(value, 'YYYY-MM-DD', false);
      this.query.chooseDate = dateFormat(value, 'YYYY-MM-DD HH:mm:ss', false);
      this.date = newTime;
    },
    leftClick() {
      this.show = true;
    },
    confirm1() {
      this.show = false;
      let pages = getCurrentPages();
      if (pages.length > 1) {
        uni.navigateBack();
      } else {
        uni.reLaunch({
          url: '/pages/index/index',
        });
      }
    },
    // 新增图片
    async afterRead(event) {
      // 当设置 mutiple 为 true 时, file 为数组格式，否则为对象格式
      let lists = [].concat(event.file);
      let fileListLen = this[`fileList${event.name}`].length;
      lists.map((item) => {
        this[`fileList${event.name}`].push({
          ...item,
          status: 'uploading',
          message: '上传中',
        });
        this.okNextBer = false;
      });
      for (let i = 0; i < lists.length; i++) {
        const result = await uploadFileToServer(lists[i].url);
        if (result) {
          if (i === lists.length - 1) {
            this.okNextBer = true;
          }
        }
        let item = this[`fileList${event.name}`][fileListLen];
        this[`fileList${event.name}`].splice(
          fileListLen,
          1,
          Object.assign(item, {
            status: 'success',
            message: '',
            url: result.data.Data,
          })
        );
        fileListLen++;
      }
    },
    // 删除图片
    deletePic(event) {
      this[`fileList${event.name}`].splice(event.index, 1);
    },
    onSave() {
      if (app.globalData.userInfo.WorkflowStatus != 2) {
        if (!this.smQuery.Name) {
          this.$refs.uToast.show({
            message: '请输入您的名字',
            type: 'error',
          });
          return;
        }
        if (!this.smQuery.Sex) {
          this.$refs.uToast.show({
            message: '请选择您的性别',
            type: 'error',
          });
          return;
        }
        if (!this.smQuery.Age) {
          this.$refs.uToast.show({
            message: '请输入您的年龄',
            type: 'error',
          });
          return;
        }
        // if (!this.smQuery.Birthday) {
        // 	this.$refs.uToast.show({
        // 		message: "请选择您的出生日期",
        // 		type: "error",
        // 	});
        // 	return
        // }
        if (
          this.smQuery.UserCertificates[0].CertificateValue &&
          !idCardTrue(this.smQuery.UserCertificates[0].CertificateValue)
        ) {
          this.$refs.uToast.show({
            message: '身份证格式输入错误',
            type: 'error',
          });
          return;
        }
      }
      if (!this.query.chooseDate) {
        this.$refs.uToast.show({
          message: '请选择就诊时间',
          type: 'error',
        });
        return;
      }
      if (!this.query.HospitName) {
        this.$refs.uToast.show({
          message: '请填写就诊医院',
          type: 'error',
        });
        return;
      }
      if (!this.query.HospitDepartmentName) {
        this.$refs.uToast.show({
          message: '请填写就诊科室',
          type: 'error',
        });
        return;
      }
      const falg = this.Entry.some((v) => v.checked);
      if (!this.query.Describing && !falg) {
        this.$refs.uToast.show({
          message: '请填写或选择就诊疾病',
          type: 'error',
        });
        return;
      }
      if (this.fileList5.length > 0 && !this.okNextBer) {
        this.$refs.uToast.show({
          message: '请等待图片上传完成再点击',
          type: 'error',
        });
        return;
      }
      this.buttonDisable = true;
      try {
        if (
          app.globalData.userInfo.WorkflowStatus != 2 &&
          this.smQuery.UserCertificates[0].CertificateValue
        ) {
          // 认证患者信息
          this.SaveAuthen();
          return;
        } else if (
          app.globalData.userInfo.WorkflowStatus != 2 &&
          !this.smQuery.UserCertificates[0].CertificateValue
        ) {
          this.updatePartDetail();
        }
        this.onRelationship();
      } catch (e) {
        this.buttonDisable = false;
      }
    },
    // 患者认证
    async SaveAuthen() {
      this.smQuery.Sex = idCardGetSex(
        this.smQuery.UserCertificates[0].CertificateValue
      );
      this.smQuery.Birthday = idCardGetBri(
        this.smQuery.UserCertificates[0].CertificateValue
      );
      let res = await saveUserAuthenticationAudit([this.smQuery]);
      if (res.Type == 200) {
        // 系统自动审核通过 调这个接口获取用户最新的数据（主要是拿到用户认证的字段WorkflowStatus为2）现在是后端自动通过 不需要前端调接口了
        // this.getAutomaticApproval()
        this.getAuthen();
      } else {
        this.updatePartDetail();
        this.$refs.uToast.show({
          message: res.Content,
          type: 'error',
        });
      }
    },
    async getAutomaticApproval() {
      let res = await automaticApproval({
        UserId: app.globalData.userInfo.Id,
      });
      if (res.Type == 200) {
        this.getAuthen();
      } else {
        this.updatePartDetail();
      }
    },
    // 获取患者是否实名
    async getAuthen() {
      let res = await getLatestAuth({
        Status: [0, 2],
      });
      if (res.Type == 200) {
        res.Data.Age =
          new Date().getFullYear() -
          this.$dateFormat(res.Data.Birthday, 'YYYY-MM-DD', false).split(
            '-'
          )[0];
        app.globalData.userInfo = res.Data;
        uni.setStorageSync('userInfoLoging', res.Data);
        this.onRelationship();
      } else {
        this.updatePartDetail();
      }
    },
    async onRelationship() {
      const DescribingTage = this.query.Describing.split(/[,， ]+/);
      const chooseTage = this.Entry.filter((v) => v.checked);
      chooseTage.forEach((v) => {
        DescribingTage.push(v.Value);
      });
      let sendInfo = {
        DiseaseLabel: DescribingTage,
        UserId: this.userInfo.Id,
        UserName: this.userInfo.Name || this.smQuery.Name || '',
      };
      let data = [];
      if (this.pageType !== 'scanDept') {
        data = [
          {
            UserId: this.userInfo.Id, //患者UserId
            ClaimType: 'VisitedRegister',
            ClaimValue: this.docInfo.UserId, //医生UserId
            AdditionInfo: {
              Stateful: true,
              NotifyClientAlways: true,
              Info: JSON.stringify(sendInfo),
            },
          },
        ];
        if (
          this.docInfo.Name != this.query.HospitDoctName &&
          this.HospitDoctNameId
        ) {
          data.push({
            UserId: this.userInfo.Id, //患者UserId
            ClaimType: 'BindDoctor',
            ClaimValue: this.HospitDoctNameId, // 用户自己选择的医生UserId
          });
        }
      } else {
        // 如果是扫描的科室二维码
        const resUser = await getUserProfile({
          Keyword: '',
          RoleTypes: ['doctor', 'therapist', 'nurse'],
          OrgIds: [this.docInfo.OrganizationId],
          DtoTypeName: 'QueryUserOutputDto3',
          IsEnabled: true,
          Pageable: true,
          SingleOne: false,
          Scopeable: false,
          PageIndex: 1,
          PageSize: 500,
          DeptIds: [this.docInfo.DepartmentId],
        });
        if (resUser.Type === 200) {
          const userIds = resUser.Data.Row.map((s) => s.Id);
          userIds.forEach((v) => {
            sendInfo.InviterType = 8;
            data.push({
              UserId: this.userInfo.Id, //患者UserId
              ClaimType: 'VisitedRegister',
              ClaimValue: v, //医生UserId
              AdditionInfo: {
                Stateful: true,
                NotifyClientAlways: true,
                Info: JSON.stringify(sendInfo),
              },
            });
            data.push({
              UserId: this.userInfo.Id,
              ClaimType: 'Follow',
              ClaimValue: v,
            });
          });
        }
      }
      let res = await SetUserClaims(data);
      if (res.Type >= 200) {
        if (this.pageType !== 'scanDept') {
          this.followedDoc();
        }
        this.onReport(guid());
      } else {
        this.updatePartDetail();
      }
    },
    updatePartDetail() {
      const data = [
        {
          UserId: app.globalData.userInfo.Id,
          Birthday: this.smQuery.Birthday,
          Name: this.smQuery.Name,
          Sex: this.smQuery.Sex,
        },
      ];
      updatePart(data);
    },
    followedDoc() {
      let data = {
        UserId: app.globalData.userInfo.Id,
        FollowUserId: this.docInfo.UserId,
      };
      setOrCanDoc('setfollow', data);
    },
    sendMessageToDoc() {
      const data = {
        ReceiveIds: [this.docInfo.UserId],
        Type: 31, //患者报到
        SubType: 0,
        Title: '患者报到',
        Content: `您有新的患者，${
          app.globalData.userInfo.Name || this.smQuery.Name
        }向您报到！`,
        IsPersistant: true, //需要持久化
        Extras: JSON.stringify({
          patientId: app.globalData.userInfo.Id,
          patientName: app.globalData.userInfo.Name || this.smQuery.Name,
        }),
      };
      userSendMessageToDoc(data);
      if (this.docInfo.AssistantId) {
        const data1 = {
          ReceiveIds: [this.docInfo.AssistantId],
          Type: 31, //患者报到
          SubType: 0,
          Title: '患者报到提醒',
          Content: `${
            app.globalData.userInfo.Name || this.smQuery.Name
          }患者已向${this.docInfo.Name}医生报到！`,
          IsPersistant: true, //需要持久化
          Extras: JSON.stringify({
            patientId: app.globalData.userInfo.Id,
            patientName: app.globalData.userInfo.Name || this.smQuery.Name,
          }),
        };
        userSendMessageToDoc(data1);
      }
    },
    async onReport(uid) {
      let VisitReportDetails = [];
      this.fileList5.forEach((e) => {
        VisitReportDetails.push({
          VisitReportId: uid,
          ReportUrl: e.url,
        });
      });
      let arr = this.Entry.filter((e) => e.checked);
      let VisitTags = [];
      let Codes = '';
      let Names = '';
      arr.forEach((k, index) => {
        VisitTags.push({
          UserId: app.globalData.userInfo.Id,
          OrganizationId: this.docInfo.OrganizationId,
          DoctorId: '',
          Source: 0,
          Class: '疾病标签',
          Tag: k.Key,
          From: 0,
        });
        Codes += k.Key + (index === arr.length - 1 ? '' : ',');
        Names += k.Key + (index === arr.length - 1 ? '' : ',');
      });
      // const obj = {
      // 	"TypeName": "疾病标签",
      // 	Codes,
      // 	Names
      // }
      if (this.query.Describing) {
        const DescribingTage = this.query.Describing.split(/[,， ]+/);
        DescribingTage.forEach((v) => {
          VisitTags.push({
            UserId: app.globalData.userInfo.Id,
            OrganizationId: this.docInfo.OrganizationId,
            DoctorId: '',
            Source: 0,
            Class: '疾病标签',
            Tag: v,
            From: 1,
          });
        });
        Names = Names
          ? Names + ',' + this.query.Describing
          : this.query.Describing;
      }
      let data = {
        Vist: {
          Source: '0',
          PatientId: this.userInfo.Id,
          Name: this.userInfo.Name || this.smQuery.Name,
          Age: null,
          Sex:
            this.userInfo.Sex ||
            idCardGetSex(this.smQuery.UserCertificates[0].CertificateValue),
          DepartmentName: this.query.HospitDepartmentName,
          DoctorName: this.query.HospitDoctName,
          InDate: this.query.chooseDate,
          OrganizationName: this.query.HospitName,
          IsSelfBuild: true,
        },
        CheckExists: true,
        // VisitTag: obj,
        VisitReports: [
          {
            Id: uid,
          },
        ],
        VisitDiagnoses: [
          {
            DiagnoseTypeName: '诊断',
            DiagnoseName: Names,
            IsMain: true,
          },
          {
            DiagnoseTypeName: '症状描述',
            DiagnoseName: Names,
          },
        ],
        VisitReportDetails: VisitReportDetails,
      };
      if (this.userInfo.Age >= 0) {
        data.Vist.Age = this.userInfo.Age;
      } else if (
        idCardGetBri(this.smQuery.UserCertificates[0].CertificateValue) > 0
      ) {
        data.Vist.Age = idCardGetBri(
          this.smQuery.UserCertificates[0].CertificateValue
        );
      } else if (this.smQuery.Age) {
        data.Vist.Age = this.smQuery.Age;
      }
      let res = await setUserCase(data);
      if (res.Type >= 200 && res.Type <= 299) {
        const repData = await SaveTags(VisitTags);
        if (repData.Type === 200) {
          if (!app.globalData.userInfo.Name) {
            app.globalData.userInfo.Name = this.smQuery.Name;
            app.globalData.userInfo.Age = this.smQuery.Age;
            app.globalData.userInfo.Birthday = this.smQuery.Birthday;
            app.globalData.userInfo.Sex = this.smQuery.Sex;
            uni.setStorageSync('userInfoLoging', app.globalData.userInfo);
          }

          getApp().changeOrgAndMark(
            {
              orgId: this.docInfo.OrganizationId,
            },
            true
          );

          if (this.pageType !== 'scanDept') {
            this.sendMessageToDoc();
          }
          this.$log.info(
            `${this.$envVersion || ''}:患者${
              this.userInfo.Name || this.smQuery.Name
            }报到了，报到的医生是:${this.docInfo.Name}`
          );
          getApp().subscribeMessage(() => {
            uni.$emit('changFollowedDoc', true);
            this.$refs.uToast.show({
              message: '报到成功',
              type: 'success',
            });
            setTimeout(() => {
              uni.reLaunch({
                url: '/pages/index/index',
                success: () => {
                  this.buttonDisable = false;
                },
              });
            }, 2000);
          });
        } else {
          this.$refs.uToast.show({
            message: repData.Content,
            type: 'error',
          });
        }
        uni.setStorageSync('chooseOrgID', this.docInfo.OrganizationId);
        uni.setStorageSync('chooseOrgName', this.docInfo.OrganizationName);
        app.globalData.orgId = this.docInfo.OrganizationId;
        app.globalData.orgName = this.docInfo.OrganizationName;
        if (this.pageType === 'scanDept') {
          uni.setStorageSync('deptId', this.docInfo.DepartmentId);
          app.globalData.deptId = this.docInfo.DepartmentId;
        }
      } else {
        this.$refs.uToast.show({
          message: res.Content,
          type: 'error',
        });
      }
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  height: 100vh;
  background-color: #f7f7f7;
  // padding-top: 208rpx;
  padding: 0 32rpx;
  padding-top: 178rpx;
  position: relative;

  &-topTip {
    margin-bottom: 10rpx;
    background: rgba(255, 195, 0, 0.2);
    padding: 10rpx;
    align-items: center;
    justify-content: center;
  }

  .u-page__tag-item {
    margin-right: 20rpx;
    margin-bottom: 10rpx;
  }

  .u-demo-block__content {
    flex-direction: row;
    flex-wrap: wrap;
    align-items: center;
    display: flex;
    position: relative;

    &-select {
      position: absolute;
      bottom: -20px;
      left: 0;
      z-index: 99;
      box-shadow: 2.5px 3px 9px 0px rgba(0, 0, 0, 0.08);
      max-height: 200rpx;
      overflow-y: auto;
      width: 100%;
    }
  }

  /deep/ .u-cell__body {
    justify-content: space-between !important;
  }

  /deep/ .u-tag--large {
    min-height: 32px;
    height: auto;
  }

  /deep/ .u-tag__text--large {
    line-height: 20px;
  }

  /deep/ .u-cell {
    background-color: #fff !important;
  }

  /deep/ .u-line {
    border: none !important;
  }

  /deep/ .u-textarea__field {
    background-color: #fafafa !important;
    padding: 10px;
  }

  /deep/ .u-border {
    border-style: none !important;
    border-color: #fafafa !important;
  }

  /deep/ .u-cell__body__content {
    flex: none !important;
  }

  /deep/ ._span {
    flex: 20 !important;
    text-align: right;
  }

  /deep/ .u-cell__right-icon-wrap {
    text-align: right;
  }
}
</style>
