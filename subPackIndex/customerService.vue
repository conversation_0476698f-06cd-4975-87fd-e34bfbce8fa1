<template>
  <view class="container">
    <view class="container-top display-style1">
      <u-icon name="weixin-circle-fill" color="#2B9939" size="40"></u-icon>
      <span>微信</span>
    </view>
    <p class="container-text" style="margin-top: 40rpx">
      微信扫描二维码添加医助微信
    </p>
    <p class="container-text">加入康复行患者服务群</p>
    <view style="margin: 0 auto">
      <u--image
        :showLoading="true"
        :src="info[0].Value"
        width="150px"
        height="150px"
        @click="click"
      ></u--image>
    </view>

    <view class="container-top display-style1">
      <u-icon name="phone-fill" color="#2B9939" size="40"></u-icon>
      <span>电话</span>
    </view>
    <p class="container-text" style="margin-top: 40rpx">
      如果你在使用过程中有任何问题
    </p>
    <p class="container-text">欢迎致电客服</p>

    <view class="display-style1" style="margin: 40rpx auto; width: 75%">
      <u-icon name="server-fill" color="#2B9939" size="40"></u-icon>
      <view>
        <p
          v-if="CustomerServicePhone"
          style="margin-bottom: 20rpx"
          class="display-style1"
        >
          客服电话：<u-text
            mode="phone"
            :call="true"
            :text="CustomerServicePhone"
          ></u-text>
        </p>
        <p class="display-style1">
          客服电话：<u-text
            mode="phone"
            :call="true"
            :text="18508221512"
          ></u-text>
        </p>
      </view>
    </view>
    <u-button
      openType="contact"
      type="success"
      shape="circle"
      text="在线咨询"
      customStyle="width:90%;bottom: 30px;position: fixed; left: 50%;transform: translateX(-50%);backgroundColor:#29B7A3;color:#fff"
    >
    </u-button>
  </view>
</template>

<script>
const app = getApp();
import { contactUs } from '@/api/bff.js';
import { getOrganizationById } from '@/api/passport.js';
export default {
  data() {
    return {
      info: [],
      CustomerServicePhone: '',
    };
  },
  async onLoad() {
    await this.getInfo();
    this.getOrgInfoData();
  },
  methods: {
    payPhone(phoneNumber) {
      uni.makePhoneCall({
        phoneNumber,
      });
    },
    async getInfo() {
      let res = await contactUs();
      if (res.Type == 200) {
        const itemList = res.Data.filter((v) => v.Code === 'CustomerService');
        if (itemList.length > 0) {
          this.info = itemList[0].Payload;
        }
      }
    },
    async getOrgInfoData() {
      if (!app.globalData.orgId) return;
      const res = await getOrganizationById(app.globalData.orgId);
      if (res.Type === 200) {
        if (res.Data.AssistantQrCode)
          this.info[0].Value = res.Data.AssistantQrCode;
        this.CustomerServicePhone = res.Data.CustomerServicePhone;
      }
    },
    click() {
      uni.previewImage({
        current: 0,
        urls: [this.info[0].Value],
      });
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  padding: 20rpx;
  background-color: white !important;
  position: relative;

  .container-top {
    font-size: 16px;
  }

  .container-text {
    text-align: center;
  }

  /deep/ .u-image {
    margin: 40rpx auto;
  }

  /deep/ .u-text__value {
    color: blue !important;
  }
}
</style>
