<template>
  <!--  @touchmove="handletouchmove($event)" @touchstart="handletouchstart"
		@touchend="handletouchendFun" -->
  <view class="container">
    <u-subsection
      :list="list"
      :current="curNow"
      @change="sectionChange"
      activeColor="#29B7A3"
      fontSize="18"
      keyName="name"
      mode="subsection"
    >
    </u-subsection>
    <!-- 搜索 -->
    <u-search
      :placeholder="
        '请输入疾病或' + ['医生', '治疗师', '护士'][curNow] + '姓名'
      "
      :showAction="true"
      v-model="depSheare.Keyword"
      actionText="搜索"
      :animation="false"
      @search="searchDoc"
      @custom="searchDoc"
    ></u-search>

    <!-- 筛选 -->
    <view class="container-screen display-style2" style="color: #999999">
      <view class="display-style1" style="width: 35%" @click="chooseOrg">
        <p class="p-style11" style="color: #29b7a3">
          {{ orgName ? orgName : '全部' }}
        </p>
        <u-icon
          name="arrow-down-fill"
          color="#29B7A3"
          size="12"
          customStyle="marginLeft:4px;marginTop:2px"
          :customStyle="{ display: changeOrg ? '' : 'none' }"
        >
        </u-icon>
      </view>
      <view class="display-style1" style="width: 22%" @click="chooseDept">
        <p class="p-style11" style="color: #29b7a3">
          {{ depName ? depName : '科室' }}
        </p>
        <u-icon
          name="arrow-down-fill"
          color="#29B7A3"
          size="12"
          customStyle="marginLeft:4px;marginTop:2px"
        >
        </u-icon>
      </view>
    </view>

    <!-- 列表 -->
    <u-list
      @scrolltolower="scrolltolower"
      v-if="docList.length > 0"
      customStyle="padding-bottom:120rpx"
    >
      <u-list-item v-for="(item, index) in docList" :key="item.id">
        <view class="box" @tap="toSeeDoc(index)" style="position: relative">
          <view class="box-top">
            <u-avatar
              :src="item.Doctor.HeadImg"
              size="60"
              class="avatar-syyle"
            ></u-avatar>
            <view class="box-top-right">
              <view class="box-top-right-top">
                <view class="display-style" style="width: 100%">
                  <p
                    style="font-size: 18px; font-weight: 600; flex: 1"
                    class="p-style11"
                  >
                    {{ item.Doctor.Name }}
                    <span
                      style="
                        font-size: 14px;
                        color: #666666;
                        margin-left: 20upx;
                        margin-top: 4px;
                      "
                      >{{ item.Doctor.WorkerTitle }}</span
                    >
                  </p>

                  <block v-if="!option.scenType">
                    <p
                      style="color: #29b7a3; font-size: 18px; font-weight: 600"
                      v-if="item.IsEnable"
                    >
                      <span v-if="item.ShowCost" style="color: #c4c4c4">
                        <span style="text-decoration: line-through"
                          >￥{{ item.ShowCost }}</span
                        >
                        <span>/</span>
                      </span>
                      <span style="color: #29b7a3">{{
                        item.RichTextCost === 0
                          ? '免费'
                          : '￥' + item.RichTextCost
                      }}</span>
                    </p>
                  </block>
                </view>
              </view>
              <view class="box-top-right-top">
                <p style="font-size: 14px; color: #666666; width: 65%">
                  {{
                    item.Doctor.PracticeOrganizationName ||
                    item.Doctor.OrganizationName
                  }}
                  <span style="margin-left: 20upx">{{
                    item.Doctor.DepartmentName
                  }}</span>
                </p>
                <view v-if="!option.scenType">
                  <p
                    style="color: #666666; font-size: 18px; font-weight: 600"
                    v-if="!item.IsEnable"
                  >
                    休息中
                  </p>
                </view>
              </view>
            </view>
            <view
              class="box-top-img1"
              v-if="item.ShowCost && item.IsEnable && !option.scenType"
            >
              限时
            </view>
          </view>
          <image
            src="/static/images/follow.png"
            mode=""
            class="box-top-img"
            style="width: 64rpx; height: 64rpx"
            v-if="item.Doctor.Followed"
          ></image>
          <view class="box-top-right-top1">
            <p
              style="
                font-size: 14px;
                color: #666666;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              "
            >
              擅长：{{ item.Doctor.Skilled }}
            </p>
            <u-line :dashed="true" margin="10px 0 10px 0"></u-line>
            <view class="botton-style">
              <p>
                服务患者：<span style="color: #29b7a3"
                  >{{ item.ConsultCount }}人</span
                >
              </p>
              <p
                style="color: #29b7a3; font-size: 15px"
                v-if="curNow == 0 && item.IsEnable"
              >
                立即咨询>
              </p>
            </view>
          </view>
        </view>
      </u-list-item>

      <u-loadmore status="nomore" v-if="status === 'nomore'" />
    </u-list>
    <u-empty
      mode="data"
      icon="	https://cdn.uviewui.com/uview/empty/data.png"
      v-if="docList.length == 0"
    >
    </u-empty>
    <u-action-sheet
      :actions="depList"
      title="选择科室"
      :show="showDep"
      :safeAreaInsetBottom="true"
      closeOnClickOverlay="close"
      @close="close"
      @select="depSelect"
    ></u-action-sheet>
    <u-action-sheet
      :actions="hosList"
      title="选择医院"
      :show="showHos"
      :safeAreaInsetBottom="true"
      closeOnClickOverlay="close"
      @close="close"
      @select="hosSelect"
    ></u-action-sheet>
    <u-toast ref="uToast"></u-toast>
    <u-modal
      :show="showToReportBol"
      title="提示"
      content="请选择您线下就诊的医生,进行诊后报到"
      confirmColor="#29B7A3"
      @confirm="onConfirmShowToReport"
      :showCancelButton="false"
    >
    </u-modal>
  </view>
</template>

<script>
const app = getApp();
import Move from '@/mixin/move.js';
import { getMyFollows, findDocList } from '@/api/consult.js';
import { getUserOrganizations } from '@/api/passport.js';
import { queryDepartments } from '@/api/passport.department';
export default {
  mixins: [Move],
  data() {
    return {
      option: {},
      startData: {
        clientX: '',
        clientY: '',
      },
      orgId: '',
      docList: [],
      list: [
        {
          name: '医生',
          type: 'doctor',
        },
        {
          name: '治疗师',
          type: 'therapist',
        },
        {
          name: '护士',
          type: 'nurse',
        },
      ],
      curNow: 0,
      par: {
        orgId: '',
        departmentId: '',
        keyword: '',
        nowOfficeOrganizationId: '',
      },
      depPrams: {
        OrgId: null,
        IsEnabled: true,
        IsLocked: false,
        Pageable: false,
        SingleOne: false,
      },
      depSheare: {
        OrganizationId: '',
        DepartmentId: '',
        Keyword: '',
        pageindex: 1,
        pagesize: 10,
        roleTypes: 'doctor',
      },
      hosPrams: {
        pageindex: 1,
        pagesize: 999,
        Keyword: '',
      },
      orgName: '',
      depName: '',
      depList: [],
      hosList: [],
      showDep: false,
      showHos: false,
      changeOrg: false,
      showToReport: false, // 通过扫医生的二维码直接去报到
      showToReportBol: false,
      status: 'nomore',
    };
  },
  onLoad(option) {
    if (app.globalData.deptId) {
      this.depSheare.DepartmentId = app.globalData.deptId;
    }
    uni.$on('changFollowedDoc', () => {
      this.rest();
    });
    if (option.showToReport) {
      this.showToReport = true;
      this.showToReportBol = true;
    }
    if (app.globalData.orgId) {
      this.par.orgId = app.globalData.orgId;
      this.orgName = app.globalData.orgName;
      this.depPrams.OrgId = app.globalData.orgId;
      this.depSheare.OrganizationId = app.globalData.orgId;
      this.changeOrg = false;
      this.getDepDataList();
      if (option.Refresh == 1) {
        let pages = getCurrentPages();
        const prePage = pages[pages.length - 2]; //上一个页面
        console.log('prePage', prePage);
        if (prePage && prePage.$vm.initData) {
          prePage.$vm.initData();
        }
      }
    } else {
      this.changeOrg = true;
    }
    if (option.index != '' && option.index != null) {
      this.sectionChange(option.index * 1);
    } else {
      this.getFinDocList();
    }
    this.option = option;
  },
  methods: {
    handletouchendFun() {
      this.handletouchend();
      if (this.StopDirection == 'left') {
        // this.query.consultWay = 2
        this.depSheare.roleType = 'therapist';
        this.curNow++;
        if (this.curNow != this.list.length) {
          this.rest();
        } else {
          this.curNow = this.list.length - 1;
        }
      } else if (this.StopDirection == 'right') {
        // this.query.consultWay = 1
        this.depSheare.roleType = 'doctor';
        this.curNow--;
        if (this.curNow >= 0) {
          this.rest();
        } else {
          this.curNow = 0;
        }
      }
    },
    onConfirmShowToReport() {
      this.showToReportBol = false;
    },
    // 选择科室进行查询
    depSelect(e) {
      this.depSheare.DepartmentId = e.Id;
      if (e.name == '全部') {
        this.depName = '科室';
      } else {
        this.depName = e.name;
      }
      this.rest();
    },
    // 选择医院
    hosSelect(e) {
      this.depSheare.OrganizationId = e.Id;
      this.depPrams.OrgId = e.Id;
      this.orgName = e.name;
      uni.setStorageSync('chooseOrgID', e.Id);
      uni.setStorageSync('chooseOrgName', e.name);
      // app.globalData.orgId = e.Id
      app.globalData.orgName = e.name;
      this.getDepDataList();
      this.rest();
    },
    // 选择机构
    chooseOrg() {
      if (this.changeOrg) {
        this.getOrgList();
      }
    },
    chooseDept() {
      if (!this.depPrams.OrgId) {
        this.$refs.uToast.show({
          message: '请先选择左侧的医院',
          type: 'error',
        });
        return;
      }
      this.showDep = true;
    },
    // 关闭列表
    close() {
      this.showDep = false;
      this.showHos = false;
    },
    // 获取科室的列表
    async getDepDataList() {
      let res = await queryDepartments(this.depPrams);
      if (res.Type === 200) {
        res.Data.unshift({
          Name: '全部',
          Id: '',
        });
        res.Data.forEach((e) => {
          e.name = e.Name;
        });
        this.depList = res.Data;
        const item = res.Data.filter((v) => v.Id === app.globalData.deptId)[0];
        this.depName = item.Name;
      }
    },
    // 获取机构
    async getOrgList() {
      let res = await getUserOrganizations(this.hosPrams);
      console.log(res);
      if (res.Type == 200) {
        res.Data.unshift({
          Name: '全部',
          Id: '',
        });
        res.Data.forEach((e) => {
          e.name = e.Name;
        });
        this.hosList = res.Data;
        this.showHos = true;
      }
    },
    async getFavDoc() {
      let par = {
        orgId: this.orgId,
        userId: app.globalData.userInfo.Id,
      };
      let res = await getMyFollows(par);
      console.log('获取喜欢医生列表成功', res);
      if (res.Type == 200) {
        this.docList = res.Data;
      }
    },
    // 切换搜索类型
    sectionChange(index) {
      if (index == 0) {
        this.depSheare.roleTypes = 'doctor';
      } else if (index == 1) {
        this.depSheare.roleTypes = 'therapist';
      } else if (index == 2) {
        this.depSheare.roleTypes = 'nurse';
      }
      this.curNow = index;
      this.rest();
    },
    //点击某个医生
    toSeeDoc(index) {
      const item = this.docList[index];
      console.log('item', item);
      const data = JSON.stringify(item);
      if (this.showToReport) {
        const docInfo = encodeURIComponent(JSON.stringify(item.Doctor));
        uni.navigateTo({
          url: '/subPackIndex/user/postDiagnosisReport?docInfo=' + docInfo,
        });
        return;
      }
      uni.navigateTo({
        // url: `./docDetail?itemInfo=${encodeURIComponent(data)}`
        url: `./docDetail?docId=${item.Doctor.UserId}&needFllow=1&scenType=${this.option.scenType}&reportId=${this.option.reportId}`,
      });
    },
    async getFinDocList() {
      if (app.globalData.via) {
        this.depSheare.via = app.globalData.via;
      }
      uni.showLoading({
        title: this.$loadingMsg,
      });
      let res = await findDocList(this.depSheare);
      if (res.Type == 200 && res.Data.length > 0) {
        res.Data.forEach((e) => {
          this.docList.push(e);
        });
      } else {
        this.status = 'nomore';
      }
      if (res.Data.length < this.depSheare.pagesize) {
        this.status = 'nomore';
      } else {
        this.status = 'loadmore';
      }
      this.$nextTick(() => {
        uni.hideLoading();
      });
    },
    scrolltolower() {
      this.depSheare.pageindex++;
      this.getFinDocList();
    },
    // 搜索医生
    searchDoc() {
      const isFlag = uni.$inputValueRegExp.test(this.depSheare.Keyword);
      if (!isFlag) {
        uni.showToast({
          title: this.$errorInputValue,
          icon: 'none',
        });
        return;
      }
      this.rest();
    },
    rest() {
      this.depSheare.pageindex = 1;
      this.docList = [];
      this.getFinDocList();
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  padding: 0 10px;
  overflow: hidden;

  .container-screen {
    width: 100%;
    height: 60px;
    background-color: white;
    margin: 10px 0;
    border-radius: 4px;
  }

  /deep/ .u-list {
    max-height: calc(100vh - 140px);
    height: auto;
  }

  .container-search {
    background-color: white;
    width: 100%;
    margin-bottom: 10px;
    padding: 10px;

    /deep/ .u-input {
      padding: 0 0 0 6px !important;
    }
  }

  .search-tool {
    margin-top: 20upx;
    width: 100%;
    padding: 10upx 20upx;
  }

  .container-box {
    padding: 0 10upx;
    margin-top: 20px;

    .container-box-each {
      width: 100%;
      height: 100px;
      background-color: red;
    }
  }

  .box {
    background-color: white;
    margin-bottom: 32rpx;
    border-radius: 12upx;
    padding: 40rpx 20rpx 20rpx 20rpx;
    box-shadow: -1px -1px 4px -2px rgba(0, 0, 0, 0.3) inset;

    .box-top-img {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 99;
      transform: rotate(-90deg);
    }

    .box-top-right-top1 {
      margin-top: 20upx;

      .botton-style {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;
      }
    }

    .box-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;

      .box-top-img1 {
        position: absolute;
        top: -20rpx;
        right: 0;
        z-index: 99;
        padding: 4rpx;
        background: #29b7a3;
        color: white;
        font-size: 12px;
        width: 40px;
        text-align: center;
        border-radius: 8px 8px 8px 0px;
      }

      .box-top-right {
        flex: 1;
        margin-left: 20upx;

        .box-top-right-top {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .botton-style {
            width: 138upx;
            height: 60upx;
            background-color: #29b7a3;
            font-size: 15px;
            text-align: center;
            line-height: 60upx;
            border-radius: 30px;
          }
        }
      }
    }
  }
}
</style>
