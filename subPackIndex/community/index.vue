<template>
  <view class="container">
    <u-subsection
      :list="subsectionList"
      activeColor="#29B7A3"
      inactiveColor="#333333"
      bgColor="#ffffff"
      :current="current"
      @change="change"
      mode="subsection"
      fontSize="18px"
    ></u-subsection>
    <u-list
      @scrolltolower="scrolltolower"
      v-if="list.length > 0"
      customStyle="marginTop:20rpx"
    >
      <!-- <u-list-item v-for="(item, index) in list" :key="item.PrescriptionNo"> -->
      <u-collapse :value="collapseList">
        <u-collapse-item
          :title="'治疗编号:' + item.PrescriptionNo"
          :name="item.PrescriptionNo"
          v-for="(item, index) in list"
          :key="item.PrescriptionNo"
        >
          <u-cell-group v-for="(k, index2) in item.Value" :key="k.Id">
            <u-cell
              :border="false"
              :url="'./detail?id=' + k.Id + '&moItmeId=' + k.BaseMoItemId"
            >
              <p
                slot="title"
                style="
                  color: #29b7a3;
                  margin-bottom: 10rpx;
                  font-size: 28rpx;
                  font-weight: 600;
                "
              >
                {{ k.BaseMoItemName }}
              </p>
              <p
                slot="label"
                :style="{ display: k.MoItemMethod != 5 ? 'block' : 'none' }"
                style="
                  color: #999999;
                  margin-bottom: 10rpx;
                  font-size: 24rpx;
                  font-weight: 400;
                "
              >
                {{ k.FreqDay }}天{{ k.Freq }}次 共{{ k.TotalCount }}次
              </p>
              <view slot="value" style="text-align: right">
                <view class="flex-start-end-column">
                  <view>
                    <p style="color: #ffb800" v-if="k.NextDate">
                      下次治疗时间{{ k.NextDate }}
                    </p>
                    <p>
                      <span
                        style="
                          color: #29b7a3;
                          font-weight: 600;
                          font-size: 28rpx;
                        "
                        >{{ k.ExecuteCount }}</span
                      >/{{ k.TotalCount }}
                    </p>
                  </view>
                  <view class="display-style">
                    <view
                      class="container-healingPoint"
                      style="margin-right: 21rpx"
                      @click.stop="onToDetail(k)"
                    >
                      核销
                    </view>
                    <view
                      class="container-healingPoint"
                      @click.stop="onHealingPointClick(k)"
                    >
                      寻找治疗点
                    </view>
                  </view>
                </view>
              </view>
            </u-cell>
          </u-cell-group>
        </u-collapse-item>
      </u-collapse>
      <!-- </u-list-item> -->
    </u-list>
    <!-- <u-button @click="onToSerachCommunity" type="success" shape="circle" text="查看附近社区"
			customStyle="width:90%;bottom: 30px;position: fixed; left: 50%;transform: translateX(-50%);backgroundColor:#29B7A3;color:#fff">
		</u-button> -->
    <u-empty
      mode="data"
      icon="http://cdn.uviewui.com/uview/empty/data.png"
      :text="showText"
      v-if="list.length == 0"
    >
    </u-empty>
  </view>
</template>

<script>
const app = getApp();
import { getCommunityList } from '@/api/community.js';
import { dateFormat } from '@/utils/validate.js';
export default {
  data() {
    return {
      list: [],
      finLsit: [],
      noFinList: [],
      current: 0,
      subsectionList: ['未完成', '已完成'],
      collapseList: [],
      showText: '',
      query: {
        pageindex: 1,
        pagesize: 10,
        patientId: app.globalData.userInfo.Id,
        IsComplete: false,
      },
    };
  },
  onLoad() {
    this.getData();
  },
  methods: {
    onToDetail(k) {
      uni.navigateTo({
        url: './detail?id=' + k.Id + '&moItmeId=' + k.BaseMoItemId,
      });
    },
    onHealingPointClick(item) {
      console.log('item', item);
      uni.navigateTo({
        url: './viewCommunity?moItemId=' + item.BaseMoItemId,
      });
    },
    rest() {
      this.current = 0;
      this.showText = '';
      this.list = [];
      this.collapseList = [];
      this.query.pageindex = 1;
      this.query.IsComplete = false;
      this.getData();
    },
    async getData() {
      uni.showLoading({
        title: this.$loadingMsg,
      });
      let res = await getCommunityList(this.query);
      if (res.Type == 200 && res.Data) {
        res.Data.Data.forEach((e) => {
          e.Value.forEach((k) => {
            k.NextDate = k.NextDate && dateFormat(k.NextDate, 'MM-DD');
          });
          if (!e.IsComplete) {
            this.noFinList.push(e);
          } else {
            this.finLsit.push(e);
          }
          this.collapseList.push(e.PrescriptionNo);
          if (this.current == 0) {
            this.list.push(e);
          } else {
            this.list.push(e);
          }
        });
        if (this.list.length == 0 && !this.current) {
          this.showText = '当前没有社区治疗项目';
        }
        if (this.list.length == 0 && this.current) {
          this.showText = '当前没有已完成的社区治疗项目';
        }
      }
      this.$nextTick(() => {
        uni.hideLoading();
      });
    },
    change(index) {
      this.current = index;
      this.collapseList = [];
      this.list = [];
      this.query.pageindex = 1;
      if (index == 0) {
        this.query.IsComplete = false;
      } else {
        this.query.IsComplete = true;
      }
      this.getData();
    },
    scrolltolower() {
      this.query.pageindex++;
      this.getData();
    },
    onToLookInfo(item) {
      console.log('item', item);
      uni.navigateTo({
        url: './detail?id=' + item.Id,
      });
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  padding: 20rpx;
  overflow: hidden;
  &-healingPoint {
    background: #f5f5f5;
    border-radius: 40rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #29b7a3;
    line-height: 34rpx;
    padding: 10rpx 20rpx;
    margin-top: 26rpx;
  }

  /deep/ .u-collapse-item__content__text {
    padding: 0;
  }

  /deep/ .u-cell__title-text {
    font-weight: 600;
  }

  /deep/ .u-list {
    height: calc(100% - 90rpx) !important;
  }

  /deep/ .u-subsection {
    height: 90rpx;
  }

  /deep/ .u-collapse {
    // background-color: white;
  }

  /deep/ .u-subsection__item {
    border: none !important;
  }

  /deep/ .u-subsection {
    background-color: white !important;
    border-radius: 16rpx;
  }

  /deep/ .u-collapse-item {
    background-color: white;
    margin-bottom: 32rpx;
    border-radius: 16rpx;
    padding: 0 32rpx;
  }

  /deep/ .u-cell {
  }

  /deep/ .u-cell__body {
    padding: 20rpx 0 !important;
  }
  /deep/ .u-line {
    display: none !important;
  }
}
</style>
