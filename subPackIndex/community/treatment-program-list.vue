<template>
  <view class="container">
    <view class="flex-start-center-wrap">
      <view
        class="container-item"
        v-for="i in treatmentProgramList"
        :key="i.Id"
        @click="onSeeDetail(i)"
      >
        <image class="container-item-img" :src="i.Images"></image>
        <text class="container-item-name">{{ i.DealName }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import { getOrganizationMainDeals } from '@/api/passport.js';
export default {
  data() {
    return {
      treatmentProgramList: [],
    };
  },
  onLoad({ orgId }) {
    // 获取机构的主营项目
    this.onGetOrganizationMainDeals(orgId);
  },
  methods: {
    onSeeDetail(item) {
      uni.navigateTo({
        url:
          './treatment-program-detail?remark=' +
          encodeURIComponent(JSON.stringify(item.Introduce)) +
          '&name=' +
          item.DealName,
      });
    },
    async onGetOrganizationMainDeals(OrgId) {
      const res = await getOrganizationMainDeals({
        OrgId,
      });
      if (res.Type === 200) {
        this.treatmentProgramList = res.Data;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  padding: 32rpx;
  &-item {
    width: 332rpx;
    position: relative;
    margin-bottom: 24rpx;
    margin-right: 22rpx;
    &-img {
      width: 332rpx;
      height: 184rpx;
      border-radius: 8rpx;
    }
    &-name {
      position: absolute;
      bottom: 0;
      width: 332rpx;
      background: linear-gradient(
        180deg,
        rgba(0, 0, 0, 0) 0%,
        rgba(8, 8, 8, 0.5) 100%
      );
      border-radius: 0rpx 0rpx 8rpx 8rpx;
      font-weight: 600;
      font-size: 24rpx;
      color: #ffffff;
      line-height: 34rpx;
      text-align: center;
      z-index: 9;
      left: 0;
      padding: 6rpx 0;
    }
  }
}
.container-item:nth-child(2n) {
  margin-right: 0 !important;
}
</style>
