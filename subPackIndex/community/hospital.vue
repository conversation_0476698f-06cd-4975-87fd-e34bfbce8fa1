<template>
  <view class="container">
    <!-- 机构信息 -->
    <view class="container-org">
      <view class="container-org-info flex-start-center">
        <image class="container-org-info-img" :src="orgInfo.HeadImg"></image>
        <view class="container-org-info-name">
          {{ orgInfo.Name }}
        </view>
      </view>
      <u-divider />
      <view class="container-org-address flex-between-center">
        <view class="container-org-address-detail">
          <text class="container-org-address-detail-label">{{
            orgInfo.Address || '暂无地址信息'
          }}</text>
        </view>
        <view class="flex-between-center">
          <view class="flex-start-center-column" @click="onOpenMap">
            <image
              class="container-org-address-avtor"
              src="/subPackIndex/static/dingwei.png"
              mode=""
            ></image>
            <text class="container-org-address-text">导航</text>
          </view>
          <view
            class="flex-start-center-column"
            style="margin-left: 16rpx"
            @click="onCallPhone"
          >
            <image
              class="container-org-address-avtor"
              src="/subPackIndex/static/dianhua.png"
              mode=""
            ></image>
            <text class="container-org-address-text">电话</text>
          </view>
        </view>
      </view>
    </view>
    <!-- 团队成员 -->
    <view class="container-doctorList" v-if="docInfo.totle">
      <view class="container-doctorList-title flex-between-center">
        <view class="container-doctorList-title-spacer"></view>
        <text class="container-doctorList-title-name"
          >团队成员({{ docInfo.totle }})</text
        >
        <view class="flex-start-center" @click="onSeeAllList('docInfo')">
          <text class="container-doctorList-title-more">查看全部</text>
          <u-icon name="arrow-right" size="20" color="#29B7A3"></u-icon>
        </view>
      </view>

      <u-scroll-list :indicator="false">
        <view
          class="container-doctorList-item"
          v-for="(item, index) in docInfo.list"
          :key="item.Id"
          @click="onDoctorClick(index)"
        >
          <image
            class="container-doctorList-item-img"
            :src="item.Doctor.HeadImg"
          ></image>
          <view class="flex-start-center-column">
            <text class="container-doctorList-item-name">{{
              item.Doctor.Name
            }}</text>
            <text class="container-doctorList-item-workTitle">{{
              item.Doctor.WorkerTitle
            }}</text>
          </view>
        </view>
      </u-scroll-list>
    </view>
    <!-- 治疗项目 -->
    <view class="container-doctorList" v-if="treatmentProgram.totle">
      <view class="container-doctorList-title flex-between-center">
        <view class="container-doctorList-title-spacer"></view>
        <text class="container-doctorList-title-name"
          >治疗项目({{ treatmentProgram.totle }})</text
        >
        <view
          class="flex-start-center"
          @click="onSeeAllList('treatmentProgram')"
        >
          <text class="container-doctorList-title-more">查看全部</text>
          <u-icon name="arrow-right" size="20" color="#29B7A3"></u-icon>
        </view>
      </view>

      <u-scroll-list :indicator="false">
        <view
          class="container-doctorList-item1"
          @click="onProgramClick(item)"
          v-for="(item, index) in treatmentProgram.list"
          :key="item.Id"
        >
          <view class="container-doctorList-item1-box">
            <image
              class="container-doctorList-item1-box-img"
              :src="item.Images"
            ></image>
            <text class="container-doctorList-item1-box-name">{{
              item.DealName
            }}</text>
          </view>
        </view>
      </u-scroll-list>
    </view>
    <!-- 机构介绍 -->
    <view class="container-doctorList">
      <view class="container-doctorList-title flex-between-center">
        <view class="container-doctorList-title-spacer"></view>
        <text class="container-doctorList-title-name">机构介绍</text>
      </view>
      <u-parse :content="orgInfo.Remark"></u-parse>
    </view>
    <block v-if="isShowReservationButton">
      <view style="height: 140rpx"></view>
      <view class="btnButtomStyle" @click="handleToOhterMiniProgram">
        治疗预约
      </view>
    </block>
  </view>
</template>

<script>
import {
  getOrganizationById,
  getOrganizationMainDeals,
} from '@/api/passport.js';
import { findDocList } from '@/api/consult.js';
import { getDict } from '@/api/dictionary.js';
export default {
  data() {
    return {
      orgId: '',
      orgInfo: {},
      docInfo: {
        list: [],
        totle: 0,
      },
      dictList: [],
      treatmentProgram: {
        list: [],
        totle: 0,
      },
      isShowReservationButton: false,
      reservationOrgIds: [
        '77c3301b-25ad-4a90-8b08-d41f659a569e',
        '53c6df17-ba27-4983-a21a-2f3b5eb8fcb4',
      ],
    };
  },
  async onLoad({ Id }) {
    this.orgId = Id;
    // 获取机构信息
    this.onGetHospital(Id);
    // 获取字典数据
    await this.onGetDict(Id);
    // 获取医生信息
    this.onGetDoctor(Id);
    // 获取主营项目
    this.onGetOrganizationMainDeals(Id);
  },
  methods: {
    handleToOhterMiniProgram() {
      uni.navigateToMiniProgram({
        appId: 'wx97a53f7810692bff',
        path: 'pages/home/<USER>',
      });
    },
    /**
     * 查看全部
     */
    onSeeAllList(type) {
      if (type === 'docInfo') {
        uni.navigateTo({
          url: `../doctor-list?orgId=${this.orgId}&role=all`,
        });
      } else if (type === 'treatmentProgram') {
        uni.navigateTo({
          url: `./treatment-program-list?orgId=${this.orgId}`,
        });
      }
    },
    /**
     * 点击某个医生今日详情
     */
    onDoctorClick(index) {
      console.log('index', index);
      const item = this.docInfo.list[index];
      uni.navigateTo({
        url: `/subPackIndex/docDetail?docId=${item.Doctor.UserId}`,
      });
    },
    onProgramClick(item) {
      uni.navigateTo({
        url:
          './treatment-program-detail?remark=' +
          encodeURIComponent(JSON.stringify(item.Introduce)) +
          '&name=' +
          item.DealName,
      });
    },
    async onGetOrganizationMainDeals(OrgId) {
      const res = await getOrganizationMainDeals({
        OrgId,
      });
      if (res.Type === 200) {
        const list = res.Data.splice(0, 5);
        this.treatmentProgram.list = list;
        this.treatmentProgram.totle = list.length;
      }
    },
    async onGetDict(Id) {
      const res = await getDict({
        code: 'WorkerTitleDict',
        orgId: Id,
      });
      console.log('res.', res);
      if (res.Type === 200) {
        this.dictList = res.Data;
      }
    },
    async onGetDoctor(Id) {
      const res = await findDocList({
        OrganizationId: Id,
        PageIndex: 1,
        PageSize: 5,
        Keyword: '',
        pageAble: true,
      });
      if (res.Type === 200) {
        this.docInfo.list = res.Data.Rows;
        this.docInfo.totle = res.Data.Total;
      } else {
        this.docInfo.list = [];
        this.docInfo.totle = 0;
      }
    },
    onOpenMap() {
      if (!this.orgInfo.LatLon) {
        uni.showToast({
          title: '未查询到经纬度信息！',
          icon: 'none',
        });
        this.$log.warn(
          `${this.$envVersion}: ${this.orgInfo.Name}未设置经纬度信息`
        );
        return;
      }
      const LatLon = this.orgInfo.LatLon.split(',');
      uni.openLocation({
        latitude: LatLon[1] * 1,
        longitude: LatLon[0] * 1,
        scale: 18,
        address: this.orgInfo.Address,
        fail: (e) => {
          console.log('e', e);
          this.$log.info(`${this.$envVersion}:打开地图失败`, e);
        },
        success: (success) => {
          console.log('success', success);
          this.$log.info(`${this.$envVersion}:打开地图成功`);
        },
      });
    },
    onCallPhone() {
      uni.makePhoneCall({
        phoneNumber: this.orgInfo.Phone,
      });
    },
    async onGetHospital(Id) {
      const res = await getOrganizationById(Id);
      if (res.Type === 200) {
        this.orgInfo = res.Data;
        this.isShowReservationButton = this.reservationOrgIds.includes(
          this.orgId
        );
        uni.setNavigationBarTitle({
          title: res.Data.Name,
        });
      }
    },
  },
};
</script>

<style scoped lang="scss">
/deep/ .u-divider {
  margin: 24rpx 0 !important;
}

.container {
  &-doctorList {
    margin-top: 24rpx;
    background: white;
    padding: 32rpx;

    &-item1 {
      width: 288rpx;
      margin-right: 24rpx;

      &-box {
        width: 288rpx;
        position: relative;

        &-img {
          width: 288rpx;
          height: 144rpx;
          border-radius: 8rpx;
        }

        &-name {
          position: absolute;
          bottom: 0;
          width: 288rpx;
          background: linear-gradient(
            180deg,
            rgba(0, 0, 0, 0) 0%,
            rgba(8, 8, 8, 0.5) 100%
          );
          border-radius: 0rpx 0rpx 8rpx 8rpx;
          font-weight: 600;
          font-size: 24rpx;
          color: #ffffff;
          line-height: 34rpx;
          text-align: center;
          z-index: 9;
          left: 0;
          padding: 6rpx 0;
        }
      }
    }

    &-item {
      width: 192rpx;
      margin-right: 24rpx;
      border-radius: 0rpx 0rpx 4rpx 4rpx;
      border: 2rpx solid #e4e4e4;

      &-workTitle {
        font-weight: 400;
        font-size: 20rpx;
        color: #666666;
        line-height: 28rpx;
        margin-top: 12rpx;
        margin-bottom: 12rpx;
      }

      &-name {
        font-weight: 600;
        font-size: 24rpx;
        color: #343434;
        line-height: 34rpx;
      }

      &-img {
        width: 192rpx !important;
        height: 192rpx !important;
      }
    }

    &-title {
      margin-bottom: 24rpx;

      &-spacer {
        width: 8rpx;
        height: 44rpx;
        background: #29b7a3;
      }

      &-name {
        flex: 1;
        font-weight: 600;
        font-size: 32rpx;
        color: #343434;
        line-height: 44rpx;
        margin-left: 16rpx;
      }

      &-more {
        font-weight: 400;
        font-size: 28rpx;
        color: #29b7a3;
        line-height: 40rpx;
        margin-right: 4rpx;
      }
    }
  }

  &-org {
    background: white;
    padding: 24rpx 32rpx;

    &-info {
      &-img {
        width: 104rpx;
        height: 104rpx;
        border-radius: 50%;
      }

      &-name {
        font-weight: 600;
        font-size: 32rpx;
        color: #333333;
        line-height: 44rpx;
        margin-left: 24rpx;
      }
    }

    &-address {
      padding-right: 24rpx;

      &-detail {
        flex: 1;
        margin-right: 40rpx;

        &-title {
          font-weight: 400;
          font-size: 24rpx;
          color: #999999;
          line-height: 34rpx;
          margin-bottom: 8rpx;
        }

        &-label {
          font-weight: 600;
          font-size: 24rpx;
          color: #343434;
          line-height: 34rpx;
        }
      }

      &-text {
        font-weight: 400;
        font-size: 20rpx;
        color: #999999;
        line-height: 28rpx;
        margin-top: 8rpx;
      }

      &-avtor {
        width: 40rpx;
        height: 40rpx;
      }
    }
  }
}
</style>
