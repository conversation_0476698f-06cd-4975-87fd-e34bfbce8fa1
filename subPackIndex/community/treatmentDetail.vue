<template>
  <view class="container">
    <view class="container-top">
      <view class="container-top-user flex-start-center">
        <u--image
          width="112rpx"
          height="112rpx"
          :src="patInfo.PatImg"
          shape="circle"
        ></u--image>
        <view class="container-top-user-content">
          <view>
            <text class="container-top-user-name">{{ patInfo.Name }}</text>
            <text class="container-top-user-label">{{ patInfo.Age }}</text>
            <text class="container-top-user-label">{{ patInfo.Sex }}</text>
          </view>
          <view style="margin-top: 22rpx">
            <text class="container-top-user-leftname">诊断： </text>
            <text class="container-top-user-rightname">{{
              patInfo.UserRxIcdsStr
            }}</text>
          </view>
        </view>
      </view>
    </view>
    <view class="container-list">
      <view class="container-list-title">治疗项目</view>
      <view
        class="container-list-box"
        v-for="item in treatList"
        :key="item.StrId"
      >
        <view class="container-list-box-top">
          <view class="flex-between-center">
            <text style="flex: 1">{{ item.BaseMoItemName }}</text>
            <text>{{ item.ExecuteCount }}/{{ item.TotalCount }}</text>
          </view>
          <view style="margin-top: 8rpx">
            <text>频次：</text>
            <text class="container-list-box-top-label"
              >{{ item.FreqDay }}天{{ item.Freq }}次</text
            >
          </view>
          <view style="margin-top: 8rpx">
            <text>穴位：</text>
            <text class="container-list-box-top-label">{{
              item.MoItemDetailsStr
            }}</text>
          </view>
        </view>
        <view
          class="container-list-box-detail"
          v-if="
            item.IsOpen && item.ExecuteRecords && item.ExecuteRecords.length > 0
          "
        >
          <u-divider />
          <view class="container-list-box-detail-list">
            <view
              class="container-list-box-detail-list-item"
              v-for="(o, index) in item.ExecuteRecords"
              :key="o.StrId"
            >
              <view class="flex-start-center">
                <u-icon name="map" size="24" />
                <text
                  style="margin-left: 14rpx"
                  class="container-list-box-detail-list-item-flod"
                  >{{ o.OrgName }}</text
                >
              </view>
              <view class="container-list-box-detail-list-item-content">
                <view>
                  <text class="container-list-box-detail-list-item-flod"
                    >执行人：</text
                  >
                  <text class="container-list-box-detail-list-item-nolor">{{
                    o.DoctorName
                  }}</text>
                </view>
                <view class="container-list-box-detail-list-item-nolor">{{
                  o.CreatedTime
                }}</view>
              </view>
              <u-divider v-if="index !== item.ExecuteRecords.length - 1" />
            </view>
          </view>
        </view>
        <u-divider
          @click="handleLineClick(item)"
          :text="item.IsOpen ? '点击收起' : '点击展开'"
        />
      </view>
    </view>
  </view>
</template>

<script>
import { getMoItemByPrescriptionId } from '../../api/community.js';
import { getPrescriptionInfo } from '../../api/consult.js';
import { dateFormat } from '../../utils/validate.js';

export default {
  data() {
    return {
      treatList: [],
      patInfo: {},
    };
  },

  async onLoad(option) {
    uni.showLoading({
      title: '请求中...',
      mask: true,
    });
    const rs = await Promise.all([
      this.loadMoItemByPrescriptionId(option.prescriptionId),
      this.loadPrescriptionInfo(option.prescriptionId),
    ]);
    uni.hideLoading();
    const fail = rs.find((s) => s.Type !== 200);
    if (fail) {
      uni.showToast({
        title: fail.Content,
        icon: 'none',
      });
    }
  },

  methods: {
    /** 获取医嘱患者信息 */
    async loadPrescriptionInfo(PrescriptionId) {
      const res = await getPrescriptionInfo({ PrescriptionId });
      if (res.Type === 200) {
        const newData = res.Data;
        if (res.Data && res.Data.UserRxIcds) {
          newData.UserRxIcdsStr = res.Data.UserRxIcds.map(
            (s) => s.DiagnoseName
          ).join('、');
        } else {
          newData.UserRxIcdsStr = '';
        }

        this.patInfo = newData;
      }

      return res;
    },

    /** 获取医嘱详情 */
    async loadMoItemByPrescriptionId(prescriptionId) {
      const res = await getMoItemByPrescriptionId(prescriptionId);
      if (res.Type === 200) {
        res.Data.forEach((s) => {
          s.MoItemDetailsStr = s.MoItemDetails.map((k) => k.Name).join('、');
          s.IsOpen = false;
          s.ExecuteRecords.forEach((v) => {
            v.CreatedTime = dateFormat(v.CreatedTime, 'YYYY-MM-DD HH:mm');
          });
          s.ExecuteRecords = s.ExecuteRecords.filter((s) => s.State === 1);
        });
        this.treatList = res.Data;
      }

      return res;
    },

    /** 点击展开或者收起 */
    handleLineClick(item) {
      const isOpen = item.IsOpen;
      const strId = item.StrId;
      const treatList = this.treatList;
      const index = treatList.findIndex((s) => s.StrId === strId);
      treatList[index].IsOpen = !isOpen;
      this.treatList = treatList;
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  &-top {
    padding: 32rpx;
    &-user {
      padding: 32rpx;
      background: #ffffff;
      border-radius: 32rpx;
      &-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        width: 100%;
        margin-left: 24rpx;
      }
      &-name {
        font-weight: 600;
        font-size: 32rpx;
        color: #323233;
        line-height: 44rpx;
      }
      &-label {
        font-weight: 400;
        font-size: 24rpx;
        color: #969799;
        line-height: 34rpx;
        margin-left: 16rpx;
      }
      &-leftname {
        font-weight: 600;
        font-size: 24rpx;
        color: #323233;
        line-height: 34rpx;
      }
      &-rightname {
        font-weight: 400;
        font-size: 24rpx;
        color: #646466;
        line-height: 34rpx;
      }
    }
  }
  &-list {
    background: #ffffff;
    border-radius: 32rpx 32rpx 0rpx 0rpx;
    padding: 32rpx;
    &-title {
      font-weight: 600;
      font-size: 0.88rem;
      color: #323233;
      line-height: 40rpx;
      margin-bottom: 16rpx;
    }
    &-box {
      background: #f7f8fa;
      border-radius: 24rpx;
      margin-bottom: 24rpx;
      padding-bottom: 32rpx;
      &-top {
        padding: 32rpx;
        font-weight: 600;
        font-size: 0.88rem;
        color: #323232;
        line-height: 46rpx;
        &-label {
          font-weight: 400 !important;
          font-size: 0.75rem !important;
          color: #646466 !important;
          line-height: 34rpx !important;
        }
      }
      &-detail {
        &-list {
          padding: 0 32rpx;
          &-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 8rpx;
            margin-bottom: 24rpx;
          }
          &-item {
            margin-top: 24rpx;
            &-line {
              margin-bottom: 24rpx;
            }
            &-flod {
              font-weight: 600;
              font-size: 28rpx;
              color: #333333;
              line-height: 40rpx;
            }
            &-nolor {
              font-weight: 400;
              font-size: 24rpx;
              color: #646466;
              line-height: 34rpx;
            }
          }
        }
      }
    }
  }
}

.flex-start-center {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
</style>
