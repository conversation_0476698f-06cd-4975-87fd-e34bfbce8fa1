<template>
  <view class="container">
    <view
      class="container-box"
      v-if="data.TotalCount > data.ExecuteCount + (data.RefundCount || 0)"
    >
      <view v-if="data.MoItemMethod !== 5">
        <p style="color: #29b7a3; font-weight: 600">
          共{{ data.TotalCount }}次，剩余{{
            data.TotalCount - data.ExecuteCount - (data.RefundCount || 0)
          }}次
        </p>
        <p style="margin-top: 32rpx">
          频率：{{ data.FreqDay }}天{{ data.Freq }}次
        </p>
        <p style="margin-top: 32rpx" v-if="data.MoItemMethod == 1">
          <span>穴位：</span>
          <span v-for="(item, index) in data.MoItemDetails" :key="item.Id"
            >{{ item.Name }}
            <span v-if="index != data.MoItemDetails.length - 1">,</span></span
          >
        </p>
      </view>
      <canvas
        class="myQrcode"
        style="width: 280px; height: 280px"
        canvas-id="myQrcode"
      ></canvas>
    </view>

    <view class="container-execute">
      <view class="container-execute-title flex-between-center">
        <view class="container-execute-title-spacer"></view>
        <text class="container-execute-title-name"
          >治疗记录（{{ data.ExecuteCount }}/{{ data.TotalCount }}）</text
        >
        <view
          class="flex-start-center"
          v-if="data.ExecuteCount > 0"
          @click="onSeeExecuteList"
        >
          <text class="container-execute-title-more">查看全部</text>
          <u-icon name="arrow-right" size="20" color="#29B7A3"></u-icon>
        </view>
      </view>

      <view v-for="(item, index) in data.ExecuteRecords" :key="item.Id">
        <view class="flex-start-center container-execute-list">
          <u-icon name="map" size="20" color="#29B7A3"></u-icon>
          <text class="container-execute-list-org">{{ item.OrgName }}</text>
        </view>
        <view class="flex-between-center">
          <text class="container-execute-docname"
            >执行人：{{ item.DoctorName }}</text
          >
          <text class="container-execute-time">{{ item.CreatedTime }}</text>
        </view>
        <u-divider
          v-if="
            index !== data.ExecuteRecords.length - 1 &&
            data.ExecuteRecords.length > 1
          "
        />
      </view>
    </view>

    <view class="container-execute">
      <view class="container-execute-title flex-between-center">
        <view class="container-execute-title-spacer"></view>
        <text class="container-execute-title-name"
          >可用治疗点（{{ communityInfo.totle }}家）</text
        >
        <view class="flex-start-center" @click.stop="onHealingPointClick">
          <text class="container-execute-title-more">查看全部</text>
          <u-icon name="arrow-right" size="20" color="#29B7A3"></u-icon>
        </view>
      </view>

      <view v-for="(item, index) in communityInfo.list" :key="index">
        <view
          class="flex-start-center container-execute-list"
          @click="onSeeDetail(item)"
        >
          <image
            class="container-execute-list-img"
            :src="item.HeadImg"
            mode=""
          ></image>
          <view class="container-execute-list-mid">
            <view class="container-execute-list-mid-title">{{
              item.Name
            }}</view>
            <view
              class="container-execute-list-mid-address"
              v-if="item.Address"
              >{{ item.Address }}</view
            >
          </view>
          <view class="flex-between-center">
            <view
              class="flex-start-center-column"
              @click.stop="onOpenMap(item)"
            >
              <image
                class="container-execute-list-avtor"
                src="/subPackIndex/static/dingwei.png"
                mode=""
              ></image>
              <text class="container-execute-list-text">导航</text>
            </view>
            <view
              class="flex-start-center-column"
              style="margin-left: 16rpx"
              @click.stop="onCallPhone(item)"
            >
              <image
                class="container-execute-list-avtor"
                src="/subPackIndex/static/dianhua.png"
                mode=""
              ></image>
              <text class="container-execute-list-text">电话</text>
            </view>
          </view>
        </view>
        <u-divider v-if="index !== 2" />
      </view>
    </view>

    <view class="container-execute" v-if="data.BaseMoIteRemark">
      <view class="container-execute-title flex-between-center">
        <view class="container-execute-title-spacer"></view>
        <text class="container-execute-title-name">治疗须知</text>
      </view>
      <view style="height: 24rpx"></view>
      <u-parse :content="data.BaseMoIteRemark"></u-parse>
    </view>

    <block v-if="isShowReservationButton">
      <view style="height: 140rpx"></view>
      <view class="btnButtomStyle" @click="handleToOhterMiniProgram">
        治疗预约
      </view>
    </block>
  </view>
</template>

<script>
import { getCommunityDetail } from '@/api/community.js';
import { dateFormat } from '@/utils/validate.js';
import { getMoItemTreat } from '@/api/content.js';
const drawQrcode = require('./qrcode.js');
import { CommunityClientEvent } from '@/utils/eventKeys.js';
import { getShortenUrl } from '@/api/bff.js';
export default {
  data() {
    return {
      id: '',
      data: {
        ExecuteCount: 0,
        TotalCount: 0,
      },
      moItmeId: '',
      query: {
        Lon: '',
        Lat: '',
      },
      communityInfo: {
        list: [],
        totle: 0,
      },
      reservationList: [
        {
          appid: 'wx97a53f7810692bff',
          url: 'pages/home/<USER>',
          orgIds: [
            '77c3301b-25ad-4a90-8b08-d41f659a569e',
            '53c6df17-ba27-4983-a21a-2f3b5eb8fcb4',
          ],
          isDefualt: false,
        },
      ],
      isShowReservationButton: false,
    };
  },
  onLoad({ id, moItmeId }) {
    this.id = id;
    this.moItmeId = moItmeId;
    this.getLocalTions();
    this.getInfo(id);
    uni.$on(CommunityClientEvent.refreshMoItemExec, (data) => {
      console.log('社区医嘱被执行了', data);
      let pages = getCurrentPages();
      const prePage = pages[pages.length - 2]; //上一个页面
      if (prePage && prePage.$vm.rest) {
        prePage.$vm.rest();
      }
      this.getInfo(id);
    });
  },
  methods: {
    handleToOhterMiniProgram() {
      const isDefualt = this.reservationList.find((item) => item.isDefualt);
      if (isDefualt) {
        uni.navigateToMiniProgram({
          appId: isDefualt.appid,
          path: isDefualt.url,
        });
      }
    },
    onSeeExecuteList() {
      uni.navigateTo({
        url: './execute-list?id=' + this.id,
      });
    },
    onHealingPointClick() {
      uni.navigateTo({
        url: './viewCommunity?moItemId=' + this.moItmeId,
      });
    },
    onSeeDetail(item) {
      uni.navigateTo({
        url: './hospital?Id=' + item.Id,
      });
    },
    onCallPhone(item) {
      if (!item.Phone) {
        uni.showModal({
          title: '温馨提示',
          content: '未查询到治疗点的联系电话',
          showCancel: false,
        });
        uni.$log.warn(`${this.$envVersion}:未查询到治疗点的联系电话`, item);
        return;
      }
      uni.makePhoneCall({
        phoneNumber: item.Phone,
      });
    },
    onOpenMap(item) {
      if (!item.LatLon || !item.Address) {
        uni.showModal({
          title: '温馨提示',
          content: '未查询到治疗点的地址信息',
          showCancel: false,
        });
        uni.$log.warn(`${this.$envVersion}:未查询到治疗点的地址信息`, item);
        return;
      }
      const LatLon = item.LatLon.split(',');
      uni.openLocation({
        latitude: LatLon[1] * 1,
        longitude: LatLon[0] * 1,
        scale: 18,
        address: item.Address,
        fail: (e) => {
          this.$log.info(`${this.$envVersion}:打开地图失败`, e);
        },
        success: (success) => {
          this.$log.info(`${this.$envVersion}:打开地图成功`);
        },
      });
    },
    getLocalTions() {
      uni.getLocation({
        type: 'wgs84',
        success: (res) => {
          if (res.errMsg == 'getLocation:ok') {
            let prams = res.longitude + ',' + res.latitude;
            this.query.Lon = res.longitude;
            this.query.Lat = res.latitude;
          }
          this.onGetMoItemTreat();
        },
        fail: (err) => {
          console.log('err', err);
          this.onGetMoItemTreat();
        },
      });
    },
    async onGetMoItemTreat() {
      const res = await getMoItemTreat({
        ...this.query,
        moItemId: this.moItmeId,
      });
      if (res.Type === 200 && res.Data) {
        const count = JSON.parse(JSON.stringify(res.Data.EligibleOrgs));
        const list = res.Data.EligibleOrgs.splice(0, 2);
        this.communityInfo.list = list;
        this.communityInfo.totle = count.length;
        // 遍历 reservationList，每个 reservation 的 orgIds 中是否存在 list 中的 Id
        this.reservationList.forEach((reservation) => {
          const hasMatch = list.some((item) =>
            reservation.orgIds.includes(item.Id)
          );
          if (hasMatch) {
            reservation.isDefualt = true;
          }
        });
        this.isShowReservationButton = this.reservationList.some(
          (s) => s.isDefualt
        );
      }
    },
    async getInfo(id) {
      uni.showLoading({
        title: '加载中...',
      });
      let res = await getCommunityDetail({
        id,
      });
      if (res.Type == 200) {
        if (res.Data.ExecuteRecords) {
          const filterData = res.Data.ExecuteRecords.filter(
            (s) => s.State === 1
          );
          if (filterData.length > 0) {
            filterData.forEach((e) => {
              e.CreatedTime = dateFormat(e.CreatedTime, 'YYYY-MM-DD HH:mm');
            });
          }
          res.Data.ExecuteRecords = filterData.splice(0, 2);
        }
        this.data = res.Data;
        this.$nextTick(() => {
          uni.hideLoading();
        });
        this.getQrCode();
        uni.setNavigationBarTitle({
          title: res.Data.BaseMoItemName,
        });
      }
    },
    async getQrCode() {
      let data = {
        Code: '203',
        Data: {
          PatientId: this.data.PatientId,
          MoItemId: this.data.StrId,
        },
      };
      let text = '';
      const str =
        'https://oss-biz.kangfx.com/doctor?type=zlzx&params=' +
        encodeURIComponent(JSON.stringify(data));
      const res = await getShortenUrl({
        LongUrl: str,
      });
      if (res.Type === 200) {
        text = res.Data;
      } else {
        text = str;
      }
      // const str = JSON.stringify(data)
      drawQrcode({
        width: 280,
        height: 280,
        canvasId: 'myQrcode',
        text,
      });
    },
  },
  onUnload() {
    // console.log('关闭了')
    uni.$off(CommunityClientEvent.refreshMoItemExec);
  },
};
</script>

<style scoped lang="scss">
.container {
  canvas {
    margin: 32rpx auto;
    margin-bottom: 0;
  }

  &-execute {
    padding: 32rpx;
    background-color: white;
    margin-bottom: 24rpx;

    &-list {
      margin: 24rpx 0;

      &-text {
        font-weight: 400;
        font-size: 20rpx;
        color: #999999;
        line-height: 28rpx;
        margin-top: 8rpx;
      }

      &-avtor {
        width: 40rpx;
        height: 40rpx;
      }

      &-mid {
        flex: 1;
        margin-left: 24rpx;

        &-title {
          font-weight: 600;
          font-size: 28rpx;
          color: #333333;
          line-height: 40rpx;
        }

        &-address {
          font-weight: 400;
          font-size: 24rpx;
          color: #999999;
          line-height: 34rpx;
          margin-top: 8rpx;
        }
      }

      &-img {
        width: 104rpx;
        height: 104rpx;
        border-radius: 50%;
      }

      &-org {
        font-weight: 600;
        font-size: 28rpx;
        color: #333333;
        line-height: 40rpx;
        margin-left: 14rpx;
      }
    }

    &-docname {
      font-weight: 400;
      font-size: 28rpx;
      color: #333333;
      line-height: 40rpx;
    }

    &-time {
      font-weight: 400;
      font-size: 28rpx;
      color: #999999;
      line-height: 40rpx;
    }

    &-title {
      &-spacer {
        width: 8rpx;
        height: 44rpx;
        background: #29b7a3;
      }

      &-name {
        flex: 1;
        font-weight: 600;
        font-size: 32rpx;
        color: #343434;
        line-height: 44rpx;
        margin-left: 16rpx;
      }

      &-more {
        font-weight: 400;
        font-size: 28rpx;
        color: #29b7a3;
        line-height: 40rpx;
        margin-right: 4rpx;
      }
    }
  }

  /deep/ .u-cell {
    background-color: white;
  }

  &-box {
    padding: 56rpx 32rpx;
    width: 100%;
    background-color: white;
    padding: 32rpx;
    border-radius: 8rpx;
    margin-bottom: 32rpx;
  }

  &-eachList {
    padding: 32rpx;
    border-radius: 8rpx;
    background-color: white;
    margin-bottom: 32rpx;

    &-bom {
      margin-top: 20rpx;
    }
  }

  /deep/ .u-image {
    margin: 32rpx auto;
  }
}
</style>
