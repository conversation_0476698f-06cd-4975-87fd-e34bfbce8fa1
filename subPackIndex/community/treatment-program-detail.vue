<template>
  <view class="container">
    <u-parse :content="detail"></u-parse>
  </view>
</template>

<script>
export default {
  data() {
    return {
      detail: '',
    };
  },
  onLoad({ remark, name }) {
    // 获取机构的主营项目
    this.detail = JSON.parse(decodeURIComponent(remark));
    uni.setNavigationBarTitle({
      title: name,
    });
  },
};
</script>

<style scoped lang="scss">
.container {
  padding: 32rpx;
}
</style>
