<template>
  <view class="container">
    <view class="container-search display-style1">
      <view class="container-search-left display-style1" @click="toggleRegion">
        <u-icon name="map"></u-icon>
        <span>{{ district || '定位中' }}</span>
      </view>
      <u-search
        v-model="searchKey"
        :showAction="true"
        actionText="搜索"
        @custom="search"
        :animation="false"
      >
      </u-search>
    </view>

    <view class="container-list" v-if="communityList.length > 0">
      <view
        v-for="(item, index) in communityList"
        :key="item.Id"
        style="
          margin-bottom: 24rpx;
          padding: 24rpx;
          background: white;
          border-radius: 16rpx;
        "
      >
        <view
          class="flex-start-center container-execute-list"
          @click="toSeeHospitol(item)"
        >
          <image
            class="container-execute-list-img"
            :src="item.HeadImg"
            mode=""
          ></image>
          <view
            class="container-execute-list-mid flex-center-start-column text-max1"
          >
            <view class="container-execute-list-mid-title">{{
              item.Name
            }}</view>
            <view
              class="container-execute-list-mid-address text-max1"
              v-if="item.Address"
              >{{ item.Address }}</view
            >
          </view>
          <view class="flex-start-center">
            <u-icon name="map" size="20" color="#29B7A3"></u-icon>
            <text class="container-execute-list-text">{{
              item.DistanceName
            }}</text>
          </view>
        </view>
      </view>
    </view>
    <u-empty
      mode="data"
      icon="http://cdn.uviewui.com/uview/empty/data.png"
      text="没有符合条件的治疗点"
      v-if="communityList.length == 0"
    >
    </u-empty>
    <u-button
      text="切换地区"
      shape="circle"
      type="primary"
      customStyle="margin-top:20rpx;width:60%;font-size:18px"
      v-if="communityList.length == 0"
      @click="toggleRegion"
    >
    </u-button>

    <view class="container-recommend" v-if="defCommunityList.length > 0">
      <p>为您推荐其他治疗点</p>
      <u-list @scrolltolower="scrolltolower" customStyle="marginTop:20rpx">
        <u-list-item v-for="(item, index) in defCommunityList" :key="item.Id">
          <u-cell :isLink="true" @click="toSeeHospitol(item)">
            <text
              class="text-max1 container-execute-list-mid-title"
              slot="title"
            >
              {{ item.Name }}
            </text>
            <text
              class="container-execute-list-mid-address text-max1"
              slot="label"
              >{{ item.Address || '' }}</text
            >
            <!-- <u--image :showLoading="true" :src="item.HeadImg" width="40px" height="40px" slot="icon">
						</u--image> -->
            <image
              style="margin-right: 24rpx"
              class="container-execute-list-img"
              :src="item.HeadImg"
              mode=""
              slot="icon"
            ></image>
            <view class="flex-start-center" slot="value">
              <u-icon
                name="map"
                size="20"
                color="#29B7A3"
                v-if="item.DistanceName"
              ></u-icon>
              <text class="container-execute-list-text">{{
                item.DistanceName
              }}</text>
            </view>
          </u-cell>
        </u-list-item>
      </u-list>
    </view>
    <u-empty
      mode="data"
      icon="http://cdn.uviewui.com/uview/empty/data.png"
      text="没有符合条件的治疗点"
      v-if="!defCommunityList.length && !communityList.length"
    />
    <u-popup
      :show="showPrivacyPopup"
      :closeOnClickOverlay="false"
      :round="10"
      mode="bottom"
      @close="closePrivacyPopup()"
    >
      <view style="padding: 40rpx">
        <p
          style="
            text-align: center;
            margin-bottom: 40rpx;
            font-size: 20px;
            font-weight: 700;
          "
        >
          提示
        </p>
        <text>
          在你使用服务之前，请仔细阅读<text
            @click="onClickPrivacyPopupTitle()"
            style="color: #29b7a3"
            >{{ PrivacyPopupTitle }}</text
          >。如果你同意{{ PrivacyPopupTitle }}，请点击“同意”开始使用。
        </text>
        <view class="display-style" style="margin-top: 40rpx">
          <u-button
            text="拒绝"
            @click="handleRefusePrivacyAuthorization()"
          ></u-button>
          <!-- <u-button type="primary" id="agree-btn" text="同意" openType="agreePrivacyAuthorization"
						@agreeprivacyauthorization="handleAgreePrivacyAuthorization()"></u-button> -->
          <button
            id="agree-btn"
            class="btn-agree"
            open-type="agreePrivacyAuthorization"
            @agreeprivacyauthorization="handleAgreePrivacyAuthorization()"
          >
            同意
          </button>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { getLocaDetail } from '@/api/other.js';
import { getMoItemTreat } from '@/api/content.js';
export default {
  data() {
    return {
      district: '',
      adcode: '',
      city: '',
      searchKey: '',
      communityList: [],
      defCommunityList: [],
      moItemId: '',
      query: {
        moItemId: '',
        Lon: '',
        Lat: '',
        CountryCode: '',
        Keyword: '',
        PageIndex: 1,
        PageSize: 20,
      },
    };
  },
  onLoad({ moItemId }) {
    this.query.moItemId = moItemId;
    this.saveWXCallBack();
    this.getLocalTions();
  },
  methods: {
    getLocalTions() {
      uni.getLocation({
        type: 'wgs84',
        success: (res) => {
          console.log('res', res);
          if (res.errMsg == 'getLocation:ok') {
            let prams = res.longitude + ',' + res.latitude;
            this.query.Lon = res.longitude;
            this.query.Lat = res.latitude;
            this.getLocaInfoDetail(prams);
          }
        },
        fail: (err) => {
          console.log('err', err);
          this.getCommunityList();
        },
      });
    },
    async getLocaInfoDetail(prams) {
      let res = await getLocaDetail(prams);
      if (res.district) {
        this.district = res.district;
        this.city = res.city;
        this.adcode = res.adcode;
        this.query.CountryCode = res.adcode;
        this.getCommunityList();
      }
    },
    async getCommunityList() {
      let res = await getMoItemTreat(this.query);
      if (res.Type === 200 && res.Data) {
        res.Data.EligibleOrgs.forEach((s) => {
          s.DistanceName =
            s.Distance !== null ? s.Distance.toFixed(2) + 'km' : '';
        });
        res.Data.OtherOrgs.forEach((s) => {
          s.DistanceName =
            s.Distance !== null ? s.Distance.toFixed(2) + 'km' : '';
        });
        this.communityList = res.Data.EligibleOrgs;
        this.defCommunityList = res.Data.OtherOrgs;
      }
    },
    search() {
      this.query.Keyword = this.searchKey;
      this.rest();
    },
    rest() {
      const isFlag = uni.$inputValueRegExp.test(this.searchKey);
      if (!isFlag) {
        uni.showToast({
          title: this.$errorInputValue,
          icon: 'none',
        });
        return;
      }
      this.query.PageIndex = 1;
      this.communityList = [];
      this.getCommunityList();
    },
    scrolltolower() {
      this.query.PageIndex++;
      this.getCommunityList();
    },
    toSeeHospitol(item) {
      // const hospitolInfo = encodeURIComponent(JSON.stringify(item, ['Name', 'Address', 'Phone', 'Work',
      // 	'Remark']))
      uni.navigateTo({
        url: './hospital?Id=' + item.Id,
      });
    },
    //切换区域
    toggleRegion() {
      uni.navigateTo({
        url:
          './toggleRegionPage?nowCity=' + this.city + '&nowCode=' + this.adcode,
      });
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  &-execute {
    padding: 32rpx;
    background-color: white;

    &-list {
      &-text {
        font-weight: 400;
        font-size: 20rpx;
        color: #999999;
        line-height: 28rpx;
        margin-top: 8rpx;
      }

      &-avtor {
        width: 40rpx;
        height: 40rpx;
      }

      &-mid {
        flex: 1;
        margin-left: 24rpx;

        &-title {
          font-weight: 600;
          font-size: 28rpx;
          color: #333333;
          line-height: 40rpx;
        }

        &-address {
          font-weight: 400;
          font-size: 24rpx;
          color: #999999;
          line-height: 34rpx;
          margin-top: 8rpx;
        }
      }

      &-img {
        width: 104rpx;
        height: 104rpx;
        border-radius: 50%;
      }

      &-org {
        font-weight: 600;
        font-size: 28rpx;
        color: #333333;
        line-height: 40rpx;
        margin-left: 14rpx;
      }
    }
  }

  /deep/ .u-empty {
    margin-top: 84px !important;
  }

  &-recommend {
    padding: 20rpx;
  }

  /deep/ .u-list {
    max-height: calc(100vh - 160rpx);
    height: auto;
  }

  /deep/ .u-cell {
    background-color: white;
  }

  &-search {
    position: fixed;
    background-color: white;
    padding: 20rpx;
    left: 0;
    right: 0;
    top: 0;
    margin: 0 auto;
    z-index: 999;
  }

  &-list {
    padding: 32rpx;
    margin-top: 84px;
  }
}
</style>
