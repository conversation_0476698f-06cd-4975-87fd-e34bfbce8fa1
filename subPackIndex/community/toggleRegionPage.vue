<template>
  <view class="container">
    <u-index-list :index-list="indexList" :custom-nav-height="0">
      <template v-slot:header>
        <view class="container-search1">
          <u-search
            v-model="searchKey"
            :showAction="true"
            actionText="搜索"
            @custom="search"
            :animation="false"
          >
          </u-search>
        </view>
      </template>

      <u-index-item v-for="(item, index) in itemArr" :key="index">
        <u-index-anchor :text="indexList[index]"></u-index-anchor>
        <!-- 这里不要使用 u-cell， 性能太差了 -->
        <!-- 	<view v-for="(cell, index2) in item" :key="index2">
					<u-cell :isLink="true" @click="onChooseAres(cell)">
						<p slot="title">{{cell.value}}</p>
					</u-cell>
				</view> -->
        <view
          v-for="(cell, index2) in item"
          :key="index2"
          class="cell"
          hover-class="cell-hover"
          :hover-stay-time="250"
          @tap="onChooseAres(cell)"
        >
          <text>{{ cell.value }}</text>
        </view>
      </u-index-item>
    </u-index-list>

    <u-action-sheet
      :actions="list"
      :title="title"
      :show="show"
      :safeAreaInsetBottom="true"
      @close="show = false"
      @select="onSelect"
    ></u-action-sheet>
  </view>
</template>

<script>
import { GetAllCities } from '@/api/dictionary.js';
import { getAddressList } from '@/api/dictionary.js';
export default {
  data() {
    return {
      /** 索引列表*/
      indexList: [],
      /** 城市分组列表 */
      itemArr: [],
      /** 子集列表 */
      list: [],
      title: '',
      show: false,
      searchKey: '',
      rspArr: [],
    };
  },
  onLoad({ nowCity, nowCode }) {
    this.addressQuery = {
      FilterGroup: {
        Rules: [
          {
            Field: 'DictId', //写死
            Value: '28', //写死
            Operate: 3, //写死
          },
          {
            Field: 'ParentId', //写死
            Value: '86', //父级ID,最顶级是86(中国)
            Operate: 3, //写死
          },
        ],
        Operate: 1,
      },
      PageCondition: {
        pageSize: 1000,
        SortConditions: [
          {
            SortField: 'OrderNumber',
          },
        ],
      },
    };
    this.nowCity = nowCity;
    this.nowCode = nowCode;
    this.cachedAddressMap = new Map();
    this.getCityList();
  },
  methods: {
    search() {
      const isFlag = uni.$inputValueRegExp.test(this.searchKey);
      if (!isFlag) {
        uni.showToast({
          title: this.$errorInputValue,
          icon: 'none',
        });
        return;
      }
      uni.showLoading({
        title: '正在加载数据',
      });
      const filterArr = this.rspArr.filter((e) =>
        e.value.includes(this.searchKey)
      );
      const data = this.groupData(filterArr);
      console.log('data', data);
      this.indexList = data.indexs ?? [];
      if (!this.searchKey) {
        this.itemArr = [...data.datas];
        // this.itemArr = [];
        // this.blockRendering(data.datas, 0)
      } else {
        this.itemArr = data.datas;
      }
      uni.hideLoading();
    },
    async getCityList() {
      let res = await GetAllCities();
      if (res.Type == 200) {
        uni.showLoading({
          title: '正在加载数据',
        });
        this.rspArr = res.Data;
        const data = this.groupData(res.Data);
        this.indexList = data.indexs;
        this.itemArr = [...data.datas];
        // this.blockRendering(data.datas, 0)
        this.$nextTick(() => {
          uni.hideLoading();
        });
      }
    },
    // blockRendering(list, index) {
    // 	let currentIndex = index;
    // 	let count = 0;
    // 	let label = `render`;
    // 	while (currentIndex < list.length && count < 100) {
    // 		const item = list[currentIndex];
    // 		label += '_' + currentIndex;
    // 		count += item.length;
    // 		this.itemArr.push(item);
    // 		currentIndex++;
    // 		console.log(currentIndex, count)
    // 	}

    // 	if (!this.searchKey) {
    // 		if (index < list.length) {
    // 			label += `:${count}`
    // 			// console.time(label)
    // 			this.$nextTick(() => {
    // 				// console.timeEnd(label)
    // 				setTimeout(() => {
    // 					this.blockRendering(list, currentIndex)
    // 				}, 100);
    // 			})
    // 		}
    // 	}
    // },
    groupData(data) {
      // console.log(data)
      const result = new Map();
      for (let i = 0; i < data.length; i++) {
        const item = data[i];
        // console.log(item)
        const key = item.firstletter;
        if (result.has(key)) {
          result.get(key).push(
            Object.freeze({
              key: item.key,
              value: item.value,
            })
          );
        } else {
          result.set(key, [
            Object.freeze({
              key: item.key,
              value: item.value,
            }),
          ]);
        }
      }
      return {
        indexs: [...result.keys()],
        datas: [...result.values()],
      };
    },
    async onChooseAres(item) {
      console.log('点击的', item);
      // console.time(item.key)
      if (this.showItem) return;
      this.showItem = item;
      const key = item.key;
      if (!this.cachedAddressMap.has(key)) {
        uni.showLoading({
          title: '正在加载数据',
        });
        this.addressQuery.FilterGroup.Rules[1].Value = key;
        let res = await getAddressList(this.addressQuery);

        if (res.Type === 200) {
          // this.columns[2] = res1.Rows
          const result = [
            Object.freeze({
              name: '全部',
              Key: item.key,
              Value: item.value,
            }),
          ];
          res.Data.Rows.forEach((e) => {
            result.push(
              Object.freeze({
                name: e.Value,
                Key: e.Key,
                Value: e.Value,
              })
            );
          });
          this.cachedAddressMap.set(key, result);
        } else {
          uni.showToast({
            icon: 'none',
            title: res.Content ?? ' 错误',
          });
          return;
        }
        uni.hideLoading();
      }
      this.title = item.value;
      this.list = this.cachedAddressMap.get(key);
      this.show = true;
      this.$nextTick(() => {
        // console.timeEnd(item.key);
        this.showItem = null;
      });
    },
    onSelect(item) {
      console.log('item', item);
      const key = item.Key;
      let pages = getCurrentPages();
      const prePage = pages[pages.length - 2]; //上一个页面
      console.log('prePage', prePage);
      if (prePage.$vm.rest) {
        if (item.name != '全部') {
          prePage.$vm.district = item.Value;
        } else {
          prePage.$vm.district = this.title;
        }
        prePage.$vm.query.CountryCode = key;
        prePage.$vm.rest(); //this.query.FilterGroup.Rules[1].Value = res.adcode
        uni.navigateBack();
      }
    },
    onChooseNowAre() {
      let pages = getCurrentPages();
      const prePage = pages[pages.length - 2]; //上一个页面
      if (prePage.$vm.rest) {
        prePage.$vm.district = this.nowCity;
        prePage.$vm.query.CountryCode = this.nowCode;
        prePage.$vm.rest();
        uni.navigateBack();
      }
    },
  },
};
</script>

<style scoped lang="scss">
.cell {
  /* line-height: $u-cell-line-height; */
  /* color: $u-cell-value-color; */
  /* background-color: $u-cell-clickable-color; */
  box-sizing: border-box;
  padding: 10px 15px;
  font-size: 15px;
  color: #303133;
  border-bottom-width: 1px;
  border-bottom-style: solid;
  border-color: #d6d7d9;
}

.cell-hover {
  background-color: #f3f4f6;
}
</style>
