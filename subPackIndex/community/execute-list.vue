<template>
  <view class="container">
    <view class="container-box">
      <view class="flex-between-center">
        <text class="container-box-moName">{{ data.BaseMoItemName }}</text>
        <text>
          <text class="container-box-executeCount">{{
            data.ExecuteCount
          }}</text>
          /
          <text class="container-box-totleCount">{{ data.TotalCount }}</text>
        </text>
      </view>
      <view>
        <view class="container-box-label">
          <text class="container-box-label-left">频次：</text>
          <text class="container-box-label-right"
            >{{ data.FreqDay }}天{{ data.Freq }}次</text
          >
        </view>
        <view class="container-box-label" v-if="data.MoItemMethod === 1">
          <text class="container-box-label-left">穴位：</text>
          <text
            class="container-box-label-right"
            v-for="(item, index) in data.MoItemDetails"
            :key="item.Id"
            >{{ item.Name }}
            <text
              v-if="
                index != data.MoItemDetails.length - 1 && data.MoItemDetails
              "
              >,</text
            ></text
          >
        </view>
      </view>
      <view
        class="container-box-item"
        v-for="item in data.ExecuteRecords"
        :key="item.StrId"
      >
        <u-divider />
        <view class="container-box-item-info">
          <view class="flex-start-center container-execute-list">
            <u-icon name="map" size="20" color="#29B7A3"></u-icon>
            <text class="container-execute-list-org">{{ item.OrgName }}</text>
          </view>
          <view class="flex-between-center">
            <text class="container-execute-docname"
              >执行人：{{ item.DoctorName }}</text
            >
            <text class="container-execute-time">{{ item.CreatedTime }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getCommunityDetail } from '@/api/community.js';
export default {
  data() {
    return {
      data: {},
    };
  },
  onLoad({ id }) {
    this.onGetCommunityDetail(id);
  },
  methods: {
    async onGetCommunityDetail(id) {
      let res = await getCommunityDetail({
        id,
      });
      if (res.Type == 200) {
        if (res.Data.ExecuteRecords) {
          const filterData = res.Data.ExecuteRecords.filter(
            (s) => s.State === 1
          );
          if (filterData.length > 0) {
            filterData.forEach((e) => {
              e.CreatedTime = this.$dateFormat(
                e.CreatedTime,
                'YYYY-MM-DD HH:mm'
              );
            });
          }
          res.Data.ExecuteRecords = filterData;
        }
        this.data = res.Data;
        uni.setNavigationBarTitle({
          title: res.Data.BaseMoItemName,
        });
      }
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  padding: 32rpx;
  &-execute {
    &-docname {
      font-weight: 400;
      font-size: 28rpx;
      color: #333333;
      line-height: 40rpx;
    }
    &-time {
      font-weight: 400;
      font-size: 28rpx;
      color: #999999;
      line-height: 40rpx;
    }
    &-list {
      margin: 24rpx 0;
      &-org {
        font-weight: 600;
        font-size: 28rpx;
        color: #333333;
        line-height: 40rpx;
        margin-left: 14rpx;
      }
    }
  }
  &-box {
    background: #ffffff;
    border-radius: 16rpx;
    padding: 32rpx;
    &-item {
      margin-top: 24rpx;
      &-info {
        margin-top: 24rpx;
      }
    }
    &-label {
      margin-top: 16rpx;
      &-left {
        font-weight: 600;
        font-size: 24rpx;
        color: #333333;
        line-height: 34rpx;
      }
      &-right {
        font-weight: 400;
        font-size: 24rpx;
        color: #666666;
        line-height: 34rpx;
      }
    }
    &-moName {
      font-weight: 600;
      font-size: 32rpx;
      color: #343434;
      line-height: 44rpx;
    }
    &-executeCount {
      font-weight: 600;
      font-size: 28rpx;
      color: #29b7a3;
      line-height: 40rpx;
    }
    &-totleCount {
      font-weight: 600;
      font-size: 24rpx;
      line-height: 40rpx;
    }
  }
}
</style>
