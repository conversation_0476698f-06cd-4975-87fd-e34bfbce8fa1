<template>
  <view class="container">
    <u-cell :border="false">
      <u-avatar :src="userInfo.HeadImg" slot="icon" :size="60"></u-avatar>
      <p
        slot="title"
        style="margin-left: 20rpx; font-weight: 600; font-size: 15px"
      >
        {{ userInfo.Name }}
      </p>
      <p
        slot="label"
        style="
          margin-left: 20rpx;
          color: #333333;
          font-size: 15px;
          margin-top: 16rpx;
        "
      >
        <span>{{ userInfo.Age }}岁</span
        ><span style="margin-left: 10px">{{ userInfo.Sex }}</span>
      </p>
    </u-cell>
    <view class="container-dec">
      <p style="font-weight: 600; margin: 0 0 24rpx 0; font-size: 15px">
        症状描述
      </p>
      <p style="color: #333333; font-size: 15px">
        {{ data.Consult.Describing }}
      </p>

      <p style="font-weight: 600; margin: 32rpx 0 24rpx 0; font-size: 15px">
        线下就诊时间
      </p>
      <p style="color: #333333; font-size: 15px">
        {{ data.Consult.OfflineDate }}
      </p>

      <p style="font-weight: 600; margin: 32rpx 0 24rpx 0; font-size: 15px">
        线下诊断
      </p>
      <p style="color: #333333; font-size: 15px">
        {{ data.Consult.OfflineDiagnosis || '' }}
      </p>

      <p
        style="font-weight: 600; margin: 32rpx 0 24rpx 0"
        v-if="data.Urls.length > 0"
      >
        图片资料
      </p>
      <view class="display-style1" style="flex-wrap: wrap">
        <u--image
          @click="preImage(index)"
          :showLoading="true"
          :src="i.Url"
          width="100px"
          height="100px"
          v-for="(i, index) in data.Urls"
          :key="index"
        ></u--image>
      </view>
    </view>
  </view>
</template>

<script>
const app = getApp();
import { getConsultRecordInfo } from '@/api/consult.js';
import { dateFormat } from '@/utils/validate.js';
export default {
  data() {
    return {
      data: {
        Urls: [],
      },
      userInfo: {},
    };
  },
  onLoad({ id }) {
    this.getData(id);
    app.globalData.userInfo.Birthday = dateFormat(
      app.globalData.userInfo.Birthday,
      'YYYY-MM-DD'
    );
    this.userInfo = app.globalData.userInfo;
  },
  methods: {
    async getData(id) {
      let res = await getConsultRecordInfo(id);
      if (res.Type == 200) {
        res.Data.Consult.OfflineDate = dateFormat(
          res.Data.Consult.OfflineDate,
          'YYYY-MM-DD'
        );
        this.data = res.Data;
      }
    },
    preImage(index) {
      let arr = [];
      this.data.Urls.forEach((e) => {
        arr.push(e.Url);
      });
      uni.previewImage({
        current: index,
        urls: arr,
      });
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  padding: 32rpx;
  /deep/ .u-cell__body {
    background-color: white;
    border-radius: 16rpx;
    padding: 36rpx;
  }
  .container-dec {
    margin-top: 20rpx;
    padding: 0 32rpx;
    padding-top: 32rpx;
    background-color: white;
    border-radius: 16rpx;
    /deep/ .u-transition {
      margin-bottom: 20rpx;
      margin-right: 20rpx;
    }
  }
}
</style>
