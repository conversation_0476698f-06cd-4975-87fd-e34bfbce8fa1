<template>
  <view class="container">
    <view class="">
      <u-search
        v-model="selectParams.Keyword"
        @custom="handleCustomClick"
        :showAction="true"
        actionText="搜索"
        :animation="false"
      ></u-search>
    </view>
    <view class="container-select">
      <view class="container-select-left">
        <block v-for="(item, index) in departmentList" :key="index">
          <view
            @click="handleDeptClick(item)"
            class="container-select-left-item"
            :class="
              selectParams.DepartmentId == item.Id
                ? 'container-select-left-selectStyle'
                : ''
            "
          >
            {{ item.Name }}
          </view>
        </block>
      </view>
      <view class="container-select-right">
        <u-list @scrolltolower="scrolltolower">
          <u-list-item v-for="(item, index) in doctorList" :key="index">
            <u-cell :title="item.Name" @click="handleDoctorClick(item)">
            </u-cell>
          </u-list-item>
        </u-list>
      </view>
    </view>
  </view>
</template>

<script>
import {
  getDepartmentList,
  getDoctorList,
} from '@/utils/commonInterfaceParameters.js';
export default {
  data() {
    return {
      roleType: null,
      departmentList: [],
      doctorList: [],
      selectParams: {
        Keyword: '',
        DepartmentId: null,
        RoleType: null,
        PageSize: 20,
        PageIndex: 1,
        OrgIds: null,
      },
    };
  },
  async onLoad({ roleType, orgId }) {
    this.roleType = roleType || null;
    this.orgId = orgId || null;
    this.selectParams.OrgIds = [orgId];
    this.selectParams.RoleType = roleType;
    // 获取科室
    await this.onGetDepartmentList();
    // 获取医生
    this.onGetDoctroList();
  },
  methods: {
    handleDeptClick(item) {
      this.selectParams.DepartmentId = item.Id;
      this.selectParams.PageIndex = 1;
      this.doctorList = [];
      this.onGetDoctroList();
    },
    async onGetDepartmentList() {
      const list = await getDepartmentList({
        OrgId: this.orgId,
      });
      list.unshift({
        Id: null,
        Name: '全部',
      });
      this.departmentList = list;
    },
    async onGetDoctroList() {
      uni.showLoading({
        title: this.$loadingMsg,
        mask: true,
      });
      const list = await getDoctorList(this.selectParams);
      this.doctorList = [...this.doctorList, ...list];
      uni.hideLoading();
    },
    handleCustomClick() {
      this.selectParams.PageIndex = 1;
      this.doctorList = [];
      this.onGetDoctroList();
    },
    scrolltolower() {
      this.selectParams.PageIndex++;
      this.onGetDoctroList();
    },
    handleDoctorClick(item) {
      const pages = getCurrentPages();
      const prevPage = pages[pages.length - 2];
      if (prevPage.$vm.handleUpdateSelectDoctor) {
        prevPage.$vm.handleUpdateSelectDoctor(item);
      }
      uni.navigateBack();
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .u-list-item {
  text-align: center !important;
  background: white !important;
}
.container {
  display: flex;
  flex-direction: column;

  &-select {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    &-left {
      width: 40%;
      height: 100%;

      &-item {
        text-align: center;
        height: 80rpx;
        line-height: 80rpx;
        position: relative;
        font-size: 28rpx;
      }

      &-selectStyle {
        background: white !important;
      }

      &-selectStyle::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 2px;
        height: 100%;
        background-color: #19b491;
      }
    }

    &-right {
      width: 60%;
      height: 100%;
    }
  }
}
</style>
