<template>
  <view class="container">
    <!-- 搜索 -->
    <view class="container-search">
      <u-input
        placeholder="请输入医生姓名"
        customStyle="{backgroundColor:#FAFAFA}"
        v-model="query.Keyword"
      >
        <u-button
          slot="suffix"
          @tap="onSearchDoctorList"
          type="success"
          icon="search"
        ></u-button>
      </u-input>
    </view>

    <!-- 列表 -->
    <u-list
      @scrolltolower="scrolltolower"
      v-if="docList.length > 0"
      customStyle="padding-bottom:160rpx"
    >
      <u-list-item v-for="(item, index) in docList" :key="item.id">
        <view
          class="box"
          @tap="onToSeeDoctorDetail(index)"
          style="position: relative"
        >
          <view class="box-top">
            <u-avatar
              :src="item.Doctor.HeadImg"
              size="60"
              class="avatar-syyle"
            ></u-avatar>
            <view class="box-top-right">
              <view class="box-top-right-top">
                <view class="display-style" style="width: 100%">
                  <p
                    style="font-size: 18px; font-weight: 600; flex: 1"
                    class="p-style11"
                  >
                    {{ item.Doctor.Name }}
                    <span
                      style="
                        font-size: 14px;
                        color: #666666;
                        margin-left: 20upx;
                        margin-top: 4px;
                      "
                      >{{ item.Doctor.WorkerTitle }}</span
                    >
                  </p>

                  <p
                    style="color: #29b7a3; font-size: 18px; font-weight: 600"
                    v-if="item.IsEnable"
                  >
                    <span v-if="item.ShowCost" style="color: #c4c4c4">
                      <span style="text-decoration: line-through"
                        >￥{{ item.ShowCost }}</span
                      >
                      <span>/</span>
                    </span>
                    <span style="color: #29b7a3">{{
                      item.RichTextCost === 0
                        ? '免费'
                        : '￥' + item.RichTextCost
                    }}</span>
                  </p>
                </view>
              </view>
              <view class="box-top-right-top">
                <p style="font-size: 14px; color: #666666; width: 65%">
                  {{
                    item.Doctor.PracticeOrganizationName ||
                    item.Doctor.OrganizationName
                  }}
                  <span style="margin-left: 20upx">{{
                    item.Doctor.DepartmentName || ''
                  }}</span>
                </p>
                <view>
                  <p
                    style="color: #666666; font-size: 18px; font-weight: 600"
                    v-if="!item.IsEnable"
                  >
                    休息中
                  </p>
                </view>
              </view>
            </view>
            <view class="box-top-img1" v-if="item.ShowCost && item.IsEnable">
              限时
            </view>
          </view>
          <image
            src="/static/images/follow.png"
            mode=""
            class="box-top-img"
            style="width: 64rpx; height: 64rpx"
            v-if="item.Doctor.Followed"
          ></image>
          <view class="box-top-right-top1">
            <p
              style="
                font-size: 14px;
                color: #666666;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              "
            >
              擅长：{{ item.Doctor.Skilled }}
            </p>
            <!-- <u-divider></u-divider> -->
            <u-line :dashed="true" margin="10px 0 10px 0"></u-line>
            <view class="botton-style">
              <!-- <p v-if="curNow == 0">已接诊：<span style="color:#29B7A3">{{item.ConsultCount}}人</span></p> -->
              <p>
                服务患者：<span style="color: #29b7a3"
                  >{{ item.ConsultCount }}人</span
                >
              </p>
              <p
                style="color: #29b7a3; font-size: 15px"
                v-if="curNow == 0 && item.IsEnable"
              >
                立即咨询>
              </p>
            </view>
          </view>
        </view>
      </u-list-item>

      <u-loadmore status="nomore" v-if="status === 'nomore'" />
    </u-list>
  </view>
</template>

<script>
import { findDocList } from '@/api/consult.js';
export default {
  data() {
    return {
      query: {
        Keyword: '',
        OrganizationId: '',
        pageindex: 1,
        pagesize: 10,
        roleTypes: null,
      },
      docList: [],
      status: 'nomore',
    };
  },
  onLoad({ orgId, role }) {
    this.query.OrganizationId = orgId;
    this.query.roleTypes = role === 'all' ? null : role;
    // 获取医生列表
    this.onGetDoctorList();
  },
  methods: {
    //点击某个医生
    onToSeeDoctorDetail(index) {
      const item = this.docList[index];
      uni.navigateTo({
        url: `./docDetail?docId=${item.Doctor.UserId}&needFllow=1`,
      });
    },
    // 搜索医生
    onSearchDoctorList() {
      const isFlag = uni.$inputValueRegExp.test(this.query.Keyword);
      if (!isFlag) {
        uni.showToast({
          title: this.$errorInputValue,
          icon: 'none',
        });
        return;
      }
      this.rest();
    },
    rest() {
      this.query.pageindex = 1;
      this.docList = [];
      this.onGetDoctorList();
    },
    scrolltolower() {
      this.query.pageindex++;
      this.onGetDoctorList();
    },
    async onGetDoctorList() {
      uni.showLoading({
        title: this.$loadingMsg,
      });
      let res = await findDocList(this.query);
      if (res.Type == 200 && res.Data.length > 0) {
        res.Data.forEach((e) => {
          this.docList.push(e);
        });
      } else {
        this.status = 'nomore';
      }
      if (res.Data.length < this.query.pagesize) {
        this.status = 'nomore';
      } else {
        this.status = 'loadmore';
      }
      this.$nextTick(() => {
        uni.hideLoading();
      });
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  padding: 0 10px;
  overflow: hidden;

  .container-screen {
    width: 100%;
    height: 60px;
    background-color: white;
    margin: 10px 0;
    border-radius: 4px;
  }

  /deep/ .u-list {
    height: auto;
  }

  .container-search {
    background-color: white;
    width: 100%;
    margin-bottom: 10px;
    padding: 10px;

    /deep/ .u-input {
      padding: 0 0 0 6px !important;
    }
  }

  .search-tool {
    margin-top: 20upx;
    width: 100%;
    padding: 10upx 20upx;
  }

  .container-box {
    padding: 0 10upx;
    margin-top: 20px;

    .container-box-each {
      width: 100%;
      height: 100px;
      background-color: red;
    }
  }

  .box {
    background-color: white;
    margin-bottom: 32rpx;
    border-radius: 12upx;
    padding: 40rpx 20rpx 20rpx 20rpx;
    box-shadow: -1px -1px 4px -2px rgba(0, 0, 0, 0.3) inset;

    .box-top-img {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 99;
      transform: rotate(-90deg);
    }

    .box-top-right-top1 {
      margin-top: 20upx;

      .botton-style {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;
      }
    }

    .box-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;

      .box-top-img1 {
        position: absolute;
        top: -20rpx;
        right: 0;
        z-index: 99;
        padding: 4rpx;
        background: #29b7a3;
        color: white;
        font-size: 12px;
        width: 40px;
        text-align: center;
        border-radius: 8px 8px 8px 0px;
      }

      .box-top-right {
        flex: 1;
        margin-left: 20upx;

        .box-top-right-top {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .botton-style {
            width: 138upx;
            height: 60upx;
            background-color: #29b7a3;
            font-size: 15px;
            text-align: center;
            line-height: 60upx;
            border-radius: 30px;
          }
        }
      }
    }
  }
}
</style>
