<template>
  <view class="container">
    <u-parse :content="obj.htmlInfo" v-if="url"></u-parse>
    <view class="container-hospitol" v-if="itemInfo.Name">
      <view class="container-hospitol-address">
        <view class="display-style1">
          <image
            style="width: 56rpx; height: 56rpx"
            src="/subPackIndex/static/dingwei.png"
            mode=""
          ></image>
          <p style="margin-left: 20rpx; flex: 1">
            {{ itemInfo.Address || '暂无信息' }}
          </p>
        </view>
        <view class="display-style1" style="margin-top: 20rpx">
          <image
            style="width: 56rpx; height: 56rpx"
            src="/subPackIndex/static/dianhua.png"
            mode=""
          ></image>
          <p style="margin-left: 20rpx; flex: 1">
            <u--text
              mode="phone"
              :call="true"
              :text="itemInfo.Phone || '暂无信息'"
            ></u--text>
          </p>
        </view>
      </view>

      <view class="container-hospitol-workOrIntro" v-if="itemInfo.Work">
        <p style="color: #29b7a3; font-size: 14px">工作时间：</p>
        <u-parse :content="itemInfo.Work || '暂无信息'"></u-parse>
      </view>
      <view class="container-hospitol-workOrIntro" style="margin-top: 20rpx">
        <p style="color: #29b7a3; font-size: 14px">医院简介：</p>
        <u-parse :content="itemInfo.Remark || '暂无信息'"></u-parse>
      </view>
    </view>
  </view>
</template>

<script>
const app = getApp();
import { getHopDetail } from '@/api/other.js';
export default {
  data() {
    return {
      obj: {
        htmlInfo: '',
      },
      itemInfo: {},
    };
  },
  onLoad({ url, item }) {
    if (url) {
      this.getHopInfo(decodeURIComponent(url));
    } else {
      const itemInfo = JSON.parse(decodeURIComponent(item));
      this.itemInfo = itemInfo;
      uni.setNavigationBarTitle({
        title: itemInfo.Name,
      });
    }
  },
  methods: {
    async getHopInfo(url) {
      let res = await getHopDetail(url);
      console.log('res', res);
      if (res.Type !== 200) {
        uni.showToast({
          title: res.Content,
          icon: 'none',
        });
        return;
      }
      this.obj.htmlInfo = res.Data;
      setTimeout(() => {
        uni.setNavigationBarTitle(
          {
            title: app.globalData.orgName,
          },
          50
        );
      });
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  padding: 20rpx;
  &-hospitol {
    &-address {
      background-color: white;
      padding: 20rpx;
    }
    &-workOrIntro {
      background-color: white;
      padding: 20rpx;
    }
  }
}
</style>
