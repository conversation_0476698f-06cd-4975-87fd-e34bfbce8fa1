<template>
  <view class="container">
    <u-list @scrolltolower="scrolltolower" v-if="newList.length > 0">
      <u-list-item v-for="(item, index) in newList" :key="item.Id">
        <view class="container-news">
          <p style="color: #666666; margin-bottom: 24rpx">
            {{ item.CreatedTime }}
          </p>
          <view class="container-news-box">
            <p style="color: #29b7a3; font-weight: 600">{{ item.Title }}</p>
            <p style="color: #333333; margin-top: 16rpx">{{ item.Content }}</p>
          </view>
        </view>
      </u-list-item>
    </u-list>
    <u-empty
      mode="data"
      icon="http://cdn.uviewui.com/uview/empty/data.png"
      text="当前消息"
      v-if="newList.length == 0"
    />
  </view>
</template>

<script>
const app = getApp();
import { getNewsList, getNoReadNews } from '@/api/message.js';
import { dateFormat } from '@/utils/validate.js';
export default {
  data() {
    return {
      newList: [],
      query: {
        PageCondition: {
          PageIndex: 1,
          PageSize: 20,
          SortConditions: [
            {
              SortField: 'CreatedTime',
              ListSortDirection: 1,
            },
          ],
        },
        FilterGroup: {
          Groups: [
            {
              Rules: [
                {
                  Field: 'UserId',
                  Value: app.globalData.userInfo.Id,
                  Operate: 3,
                },
              ],
            },
            {
              Rules: [
                {
                  Field: 'Type',
                  Value: '18',
                  Operate: 3,
                },
                {
                  Field: 'Type',
                  Value: '22',
                  Operate: 3,
                },
                {
                  Field: 'Type',
                  Value: '23',
                  Operate: 3,
                },
                {
                  Field: 'Type',
                  Value: '24',
                  Operate: 3,
                },
                {
                  Field: 'Type',
                  Value: '25',
                  Operate: 3,
                },
                {
                  Field: 'Type',
                  Value: '26',
                  Operate: 3,
                },
                {
                  Field: 'Type',
                  Value: '28',
                  Operate: 3,
                },
                {
                  Field: 'Type',
                  Value: '45',
                  Operate: 3,
                },
                {
                  Field: 'Type',
                  Value: '46',
                  Operate: 3,
                },
                {
                  Field: 'Type',
                  Value: '49',
                  Operate: 3,
                },
              ],
              Operate: 2,
            },
          ],
          Operate: 1,
        },
      },
    };
  },
  onLoad() {
    console.log(this);
    this.getNews();
    this.getNoRead();
  },
  methods: {
    async getNoRead() {
      console.log(this, 'getNoRead');
      await getNoReadNews([18, 22, 23, 24, 25, 26, 28, 45, 46, 49]);
      this.$store.commit('message/updateUnreadMessageCount', 0);
    },
    async getNews() {
      uni.showLoading({
        title: this.$loadingMsg,
      });
      let res = await getNewsList(this.query);
      if (res.Type == 200) {
        res.Data.Rows.forEach((e) => {
          e.CreatedTime = dateFormat(e.CreatedTime);
          this.newList.push(e);
        });
      }
      this.$nextTick(() => {
        uni.hideLoading();
      });
    },
    scrolltolower() {
      this.query.PageCondition.PageIndex++;
      this.getNews();
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  padding: 0 32rpx;

  /deep/ .u-cell {
    background-color: white;
  }

  .container-news {
    margin-top: 32rpx;
    text-align: center;

    .container-news-box {
      background-color: white;
      padding: 32rpx 0 16rpx 32rpx;
      border-radius: 8rpx;
      text-align: left;
      font-size: 16px;
    }
  }
}
</style>
