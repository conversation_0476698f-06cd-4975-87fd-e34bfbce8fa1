<template>
  <view class="container">
    <view>
      <span class="container-title" ref="title">{{ title }}</span>
      <view
        class="container-bg"
        :style="{ width: componentWidth + 'px' }"
      ></view>
    </view>
    <slot></slot>
  </view>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      componentWidth: 0,
    };
  },
  mounted() {
    uni
      .createSelectorQuery()
      .in(this)
      .select('.container-title') // 指定组件的类名或者唯一标识符
      .fields({ size: true }, (rect) => {
        this.componentWidth = rect.width; // 获取到宽度后保存到组件数据中
      })
      .exec();
  },
};
</script>

<style scoped lang="scss">
.container {
  position: relative;
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  &-bg {
    height: 20rpx;
    background: linear-gradient(90deg, #29b7a3 0%, rgba(83, 220, 208, 0) 100%);
    z-index: 0;
    position: absolute;
    bottom: 0rpx;
  }
  &-title {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 600;
    font-size: 40rpx;
    color: #000000;
    font-style: normal;
    z-index: 2;
    position: relative;
  }
}
</style>
