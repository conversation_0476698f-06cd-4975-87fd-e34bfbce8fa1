<template>
	<view>
		<u-loading-page :loading="isLoading"></u-loading-page>
		<slot v-if="loadingType === 'success'"></slot>
		<text v-if="!isLoading && loadingType !== 'success'">{{loadingType}}</text>
	</view>
</template>

<script>
	import props from './props.js';
	export default {
		...props,
		computed: {
			isLoading() {
				return this.loadingType === 'loading'
			}
		}
	}
</script>

<style>
</style>