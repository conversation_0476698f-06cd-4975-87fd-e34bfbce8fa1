<template>
  <div
    style="
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    "
  >
    <canvas
      id="canvas"
      canvas-id="canvas-id"
      :style="{
        width: size + 'px',
        height: size + 'px',
      }"
    ></canvas>
  </div>
</template>

<script>
const drawQrcode = require('./qrcode.js');
const kEnableDebug = true;
export default {
  props: {
    text: {
      type: String,
      default: '123',
    },
    size: {
      type: Number,
      default: 280,
    },
  },
  onReady() {
    console.debug(this);
    // 不能用canvas 2d, qrcode 库不支持
    const query = uni.createSelectorQuery().in(this);
    query
      .select(`#canvas`)
      .context((res) => {
        // console.debug(res.context);
        drawQrcode({
          width: this.size,
          height: this.size,
          ctx: res.context,
          text: this.text,
        });
      })
      .exec();
  },
};
</script>

<style lang="scss" scoped></style>
