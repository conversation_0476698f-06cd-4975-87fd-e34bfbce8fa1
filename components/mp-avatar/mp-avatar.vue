<template>
  <mp-image
    :src="src"
    :mode="mode"
    :width="width"
    :height="height"
    shape="circle"
    errorImageSrc="/static/common/default-avatar.png"
    :errorImageWidth="errorImageWidth"
    :errorImageHeight="errorImageHeight"
    loadingSize="20"
    bgColor="#f7f7f7"
    @click="_onClick"
    @tap="_tap"
  />
</template>

<script>
export default {
  name: 'mp-avatar',
  emits: ['click', 'tap'],
  props: {
    // 图片地址
    src: {
      type: String,
      default: '',
    },
    // 裁剪模式
    mode: {
      type: String,
      default: 'aspectFill',
    },
    // 宽度，单位任意
    width: {
      type: [String, Number],
      default: '40px',
    },
    // 高度，单位任意
    height: {
      type: [String, Number],
      default: '40px',
    },
    // 错误提示图宽度，单位任意
    errorImageWidth: {
      type: [String, Number],
      default: '40px',
    },
    // 错误提示图高度，单位任意
    errorImageHeight: {
      type: [String, Number],
      default: '40px',
    },
  },
  data() {
    return {};
  },
  methods: {
    _onClick() {
      this.$emit('click');
    },
    _tap() {
      this.$emit('tap');
    },
  },
};
</script>

<style></style>
