<template>
  <view class="imt-audio">
    <view class="top">
      <view class="audio-control-wrapper">
        <image :src="poster" mode="aspectFill" class="cover"></image>
        <image :src="require('./static/loading.png')" v-if="playState ==='loading'" class="play loading"></image>
        <block v-else>
          <image :src="require('./static/playbtn.png')" alt="play" @click="play" class="play"
            v-if="playState === 'pause'"></image>
          <image :src="require('./static/pausebtn.png')" alt="pause" @click="pause" class="play" v-else></image>
        </block>
      </view>
    </view>
    <view class="audio-wrapper">
      <view class="titlebox">
        <view class="title text-max1">{{name}}</view>
      </view>
      <slider class="audio-slider" block-size="12" :max="duration" :value="currentTime" @change="sliderChange"
        block-color="#999999" @changing="sliderChanging"></slider>
      <view class="slidebox">
        <view>{{formatSeconds(currentTime)}} / {{formatSeconds(duration)}}</view>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        src: "",
        poster: "",
        name: "标题",
        singer: "",
        duration: 10,
        currentTime: 0,
        playState: "pause", //"loading"/"playing"/"pause"
        isSliderChanging: false,
        audioCtx: null
      };
    },
    created() {
      this.audioCtx = uni.createInnerAudioContext()
      this.audioCtx.onTimeUpdate((e) => {
        const currentTime = this.audioCtx.currentTime
        this.currentTime = currentTime
      })
      this.audioCtx.onPlay(() => console.log("播放"))
      this.audioCtx.onPause(() => console.log("暂停"))
      this.audioCtx.onStop(() => console.log("停止"))
      this.audioCtx.onEnded(() => {
        console.log("结束")
        this.playState = 'pause',
          this.currentTime = 0
        this.audioCtx.seek(0)
      })
    },
    destroyed() {
      this.audioCtx.destroy()
      this.audioCtx = null
    },
    methods: {
      setSrc(value) {
        this.audioCtx.src = value
        let v1 = setInterval(() => {
          console.log("this.audioCtx.duration", this.audioCtx.duration)
          if (this.audioCtx.duration > 0) {
            this.duration = this.audioCtx.duration
            clearInterval(v1)
          }
        }, 500)
      },
      setPoster(value) {
        this.poster = value;
      },
      setName(value) {
        this.name = value;
      },
      play() {
        if (this.audioCtx) {
          this.audioCtx.play();
          this.playState = "playing"
        }
      },
      pause() {
        this.audioCtx.pause();
        this.playState = "pause"
      },
      sliderChange(e) {
        this.isSliderChanging = false;
        if (this.audioCtx) {
          this.audioCtx.seek(e.detail.value);
          this.currentTime = e.detail.value
        }
      },
      formatSeconds(seconds) {
        var result = typeof seconds === "string" ? parseFloat(seconds) : seconds;
        if (isNaN(result))
          return "";
        let h = Math.floor(result / 3600) < 10 ?
          "0" + Math.floor(result / 3600) :
          Math.floor(result / 3600);
        let m = Math.floor((result / 60) % 60) < 10 ?
          "0" + Math.floor((result / 60) % 60) :
          Math.floor((result / 60) % 60) + (Number(h)) * 60;
        let s = Math.floor(result % 60) < 10 ?
          "0" + Math.floor(result % 60) :
          Math.floor(result % 60);
        return `${m}:${s}`;
      },
      sliderChanging() {
        this.isSliderChanging = true;
      },
    },
  }
</script>

<style lang="scss">
  @import './index.scss';
</style>
