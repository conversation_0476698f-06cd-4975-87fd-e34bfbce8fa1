@mixin textoverflow() {
	display: -webkit-box;
	overflow: hidden;
	text-overflow: ellipsis;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 1;
}
@keyframes rowup {
	0% {
		-webkit-transform: translate(-50%, -50%) rotate(0deg);
		transform-origin: center center;
	}

	100% {
		-webkit-transform: translate(-50%, -50%) rotate(360deg);
		transform-origin: center center;
	}
}
.imt-audio{
	background: #ffffff;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
	width: 100%;
	overflow: hidden;
	display: flex;
	box-sizing: border-box;
	position:relative;
  padding: 12rpx;
  border-radius: 12rpx;
	.top {
		width: 140rpx;
		position: relative;
	}

	.audio-wrapper {
		display: flex;
		flex-direction: column;
    justify-content: space-between;
		flex: 1;
		color: #fff;
		margin-left: 20rpx;
    width: 100%;
    height: 100%;

		.titlebox {
			display: flex;
      color: #000000;
			.title {
				font-size: 30rpx;
				@include textoverflow;
			}

			.singer {
				margin-left: 20rpx;
				font-size: 28rpx;
				max-width: 50%;
				@include textoverflow;
			}
		}
	}
	.slidebox {
		display: flex;
		justify-content: space-between;
		width: 96%;
    color: #333;
	}
	.uni-slider-tap-area {
		padding: 0;
	}
	.uni-slider-wrapper {
		min-height: 0;
	}
	.uni-slider-handle-wrapper {
		height: 6px;
	}
	.audio-slider {
    margin: 0 !important;
	}


	.cover {
		width: 120rpx;
		height: 120rpx;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		animation-fill-mode: forwards;
		-webkit-animation-fill-mode: forwards;
	}

	.play {
		width: 80rpx;
		height: 80rpx;
		z-index: 99;
		background: rgba(0, 0, 0, 0.4);
		border-radius: 50%;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		&.loading{
			width: 60rpx;
			height: 60rpx;
			animation: rotating_theme3 2s linear infinite;
		}
	}
}

@keyframes rotating {
	0% {
		  transform: rotateZ(0deg)
	}
	100% {
		  transform: rotateZ(360deg)
	}
}
@keyframes rotating_theme3 {
	0% {
		  transform: translate(-50%, -50%) rotateZ(0deg)
	}
	100% {
		  transform: translate(-50%, -50%) rotateZ(360deg)
	}
}

.hItem
{
	margin-left: 16rpx;
}

.extrButton
{
	font-size: 36rpx;
}
