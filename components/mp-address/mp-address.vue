<template>
  <block>
    <u-cell>
      <view slot="title">
        <text class="title-name-style">收货人姓名</text>
        <text style="color: red">*</text>
      </view>
      <u-input
        slot="value"
        clearable
        placeholder="请输入收货人姓名"
        border="none"
        v-model="query.Name"
        inputAlign="right"
      ></u-input>
    </u-cell>
    <u-cell>
      <view slot="title">
        <text class="title-name-style">收货人手机号</text>
        <text style="color: red">*</text>
      </view>
      <u-input
        slot="value"
        clearable
        placeholder="请输入收货人手机号"
        type="number"
        border="none"
        v-model="query.Tel"
        inputAlign="right"
      ></u-input>
    </u-cell>
    <u-cell is-link>
      <view slot="title">
        <text class="title-name-style">所在地区</text>
        <text style="color: red">*</text>
      </view>
      <view slot="value" style="flex: 1; text-align: right" @click="changeArea">
        <text>{{ addressTogeter ? addressTogeter : '请选择' }}</text>
      </view>
    </u-cell>
    <u-textarea
      v-model="query.Address"
      @input="textareaInput($event)"
      :maxlength="80"
      placeholder="请输入详细地址"
      count
      :height="120"
    ></u-textarea>
    <u-picker
      :immediateChange="true"
      :show="show"
      ref="uPicker"
      keyName="Value"
      :columns="columns"
      @change="changeHandler"
      @cancel="close"
      @confirm="confirm"
      :defaultIndex="defaultPickerIndex"
    ></u-picker>
    <u-modal
      :show="showRich"
      title="提示"
      @confirm="confirmContent"
      @cancel="sendData"
      :showCancelButton="true"
      confirmText="修改并保存"
      cancelText="忽略"
    >
      <view class="slot-content">
        <rich-text :nodes="content"></rich-text>
      </view>
    </u-modal>
  </block>
</template>

<script>
const app = getApp();
import { getRegionCode, getAddressList } from '@/api/dictionary.js';
import { getLocaDetail, getSSQByAddress } from '@/api/other.js';
export default {
  name: 'mp-address',
  props: {
    needAddress: {
      type: Boolean,
      default: true,
    },
    onMpAddressUserName: {
      type: String,
      default: '',
    },
  },
  watch: {
    onMpAddressUserName: {
      handler(newVal) {
        this.query.Name = newVal;
      },
      immediate: true,
    },
  },
  methods: {
    textareaInput(e) {
      this.$emit('textareaInput', e);
    },
  },
  data() {
    return {
      defaultPickerIndex: [0, 0, 0],
      content: '',
      showRich: false,
      show: false,
      getAddressObj: {},
      query: {
        Name: '',
        Tel: app.globalData.userInfo.PhoneNumber || '',
        Province: '',
        City: '',
        County: '',
        ProvinceName: '',
        CityName: '',
        CountyName: '',
        Address: '',
        UserId: app.globalData.userInfo.Id,
        IsDefault: false,
      },
      addressTogeter: '',
      columns: [[], [], []],
      addressQuery: {
        FilterGroup: {
          Rules: [
            {
              Field: 'DictId', //写死
              Value: '28', //写死
              Operate: 3, //写死
            },
            {
              Field: 'ParentId', //写死
              Value: '86', //父级ID,最顶级是86(中国)
              Operate: 3, //写死
            },
          ],
          Operate: 1,
        },
        PageCondition: {
          PageSize: 1000,
          SortConditions: [
            {
              SortField: 'OrderNumber',
            },
          ],
        },
      },
    };
  },
  created() {
    this.getLocalTions();
    this.getUserLocalAddress();
  },
  methods: {
    checkIsFinish() {
      let errMessage = '';
      let type = '';
      if (
        !this.query.Name ||
        !this.query.Tel ||
        !this.query.ProvinceName ||
        !this.query.Address
      ) {
        errMessage = '请完善收货地址';
        type = 'base';
      }
      const reg = /^1[3-9]\d{9}$/;
      if (!reg.test(this.query.Tel)) {
        uni.showModal({
          title: '提示',
          content: '手机号格式不正确',
          showCancel: false,
        });
        errMessage = '手机号格式不正确';
        type = 'phone';
      }
      if (errMessage) {
        return {
          error: true,
          type,
        };
      }
      return {
        error: false,
        type,
      };
    },
    onCheckIsAddress() {
      return this.query.Address;
    },
    async onGetSSQByAddress() {
      const res = await getSSQByAddress({
        address: this.query.Address,
        city: this.query.CityName,
      });
      console.log('res', res);
      if (res.status === '1' && res.geocodes.length > 0) {
        let flag = false;
        let data;
        const matchingData = res.geocodes.filter(
          (v) =>
            v.city === this.query.CityName &&
            v.district === this.query.CountyName
        );
        if (matchingData.length === 0) {
          flag = true;
          data = res.geocodes[0];
        }
        if (flag) {
          this.content = `识别到您填写地址所在地区为<span style="color: red">${data.province}${data.city}${data.district}</span>，是否修改?`;
          this.getAddressObj = {
            province: data.province,
            city: data.city,
            district: data.district,
          };
          this.showRich = true;
          uni.hideLoading();
        } else {
          this.$emit('changeAddress', this.query);
        }
      } else {
        this.$emit('changeAddress', this.query);
      }
    },
    sendData() {
      this.showRich = false;
      this.$emit('changeAddress', this.query);
    },
    changeArea() {
      this.show = true;
    },
    textareaInput(e) {
      if (e.length > 80) {
        this.query.Address = e.slice(0, 80);
      }
    },
    async getUserLocalAddress() {
      console.log('123');
      let res = await getAddressList(this.addressQuery);
      console.log('res', res);
      if (res.Type === 200) {
        this.columns[0] = res.Data.Rows;
        // 获取市级
        this.addressQuery.FilterGroup.Rules[1].Value = res.Data.Rows[0].Key;
        let res2 = await getAddressList(this.addressQuery);
        if (res2.Type === 200) {
          this.columns[1] = res2.Data.Rows;
          //获取区级
          this.addressQuery.FilterGroup.Rules[1].Value = res2.Data.Rows[0].Key;
          let res3 = await getAddressList(this.addressQuery);
          if (res3.Type === 200) {
            this.columns[2] = res3.Data.Rows;
          }
        }
      }
    },
    async changeHandler(e, isShow = true) {
      // 省级变了
      if (e.columnIndex == 0) {
        // 获取省级的key
        const Sid = e.value[0].Key;
        this.columns[1] = [];
        this.columns[2] = [];
        this.addressQuery.FilterGroup.Rules[1].Value = Sid;
        let res1 = await getAddressList(this.addressQuery);
        if (res1.Type != 200) {
          return;
        }
        if (res1.Data.Rows.length > 0) {
          if (isShow) {
            this.show = false;
            this.show = true;
          }
          this.columns[1] = res1.Data.Rows;
          this.addressQuery.FilterGroup.Rules[1].Value = res1.Data.Rows[0].Key;
          let res2 = await getAddressList(this.addressQuery);
          if (res2.Data.Rows.length > 0) {
            if (isShow) {
              this.show = false;
              this.show = true;
            }
            this.columns[2] = res2.Data.Rows;
          }
        }
      } else if (e.columnIndex == 1) {
        // 市级改变了
        const Sid = e.value[1].Key;
        this.addressQuery.FilterGroup.Rules[1].Value = Sid;
        let res1 = await getAddressList(this.addressQuery);
        if (res1.Data.Rows.length > 0) {
          if (isShow) {
            this.show = false;
            this.show = true;
          }
          this.columns[2] = res1.Data.Rows;
        }
      }
    },
    close() {
      this.show = false;
    },
    confirm(e) {
      this.query.Province = e.value[0].Key;
      this.query.ProvinceName = e.value[0].Value;
      if (e.value[2]) {
        this.query.City = e.value[1].Key;
        this.query.County = e.value[2].Key;
        this.query.CityName = e.value[1].Value;
        this.query.CountyName = e.value[2].Value;
        this.addressTogeter =
          e.value[0].Value + e.value[1].Value + e.value[2].Value;
      } else {
        this.query.City = e.value[0].Key;
        this.query.County = e.value[1].Key;
        this.query.CityName = e.value[0].Value;
        this.query.CountyName = e.value[1].Value;
        this.addressTogeter =
          e.value[0].Value + e.value[0].Value + e.value[1].Value;
      }
      this.show = false;
    },
    getLocalTions() {
      uni.getLocation({
        type: 'wgs84',
        success: (res) => {
          if (res.errMsg == 'getLocation:ok') {
            let prams = res.longitude + ',' + res.latitude;
            this.getLocaInfoDetail(prams);
          }
        },
        fail: (err) => {},
      });
    },
    async getLocaInfoDetail(prams) {
      let res = await getLocaDetail(prams);
      this.query.ProvinceName = res.province;
      this.query.CityName = res.city;
      this.query.CountyName = res.district;
      this.addressTogeter =
        res.province + res.city + (res.district ? res.district : '');
      if (res.province) {
        const provinceId = this.columns[0].filter(
          (v) => v.Value === res.province
        )[0].Key;
        const provinceIdIndex = this.columns[0].findIndex(
          (v) => v.Value === res.province
        );
        if (provinceIdIndex > -1) {
          this.defaultPickerIndex[0] = provinceIdIndex;
          await this.changeHandler(
            {
              columnIndex: 0,
              value: [this.columns[0][provinceIdIndex]],
            },
            false
          );
        }
        this.query.Province = provinceId;
        if (provinceId) {
          this.addressQuery.FilterGroup.Rules[1].Value = provinceId;
          let res2 = await getAddressList(this.addressQuery);
          if (res2.Data.Total > 0) {
            const cityId = res2.Data.Rows.filter((v) => v.Value === res.city)[0]
              .Key;
            const cityIdIndex = this.columns[1].findIndex(
              (v) => v.Value === res.city
            );
            if (cityIdIndex > -1) {
              this.defaultPickerIndex[1] = cityIdIndex;
              await this.changeHandler(
                {
                  columnIndex: 1,
                  value: ['', this.columns[1][cityIdIndex]],
                },
                false
              );
            }
            this.query.City = cityId;
            if (cityId) {
              this.addressQuery.FilterGroup.Rules[1].Value = cityId;
              let res3 = await getAddressList(this.addressQuery);
              if (res3.Data.Total > 0) {
                const countyId = res3.Data.Rows.filter(
                  (v) => v.Value === res.district
                )[0].Key;
                const countyIdIndex = this.columns[2].findIndex(
                  (v) => v.Value === res.district
                );
                if (countyIdIndex > -1) {
                  this.defaultPickerIndex[2] = countyIdIndex;
                  await this.changeHandler(
                    {
                      columnIndex: 2,
                      value: ['', '', this.columns[2][countyIdIndex]],
                    },
                    false
                  );
                }
                this.query.County = countyId;
              }
            }
          }
        }
      }
    },
    confirmContent() {
      const data = this.getAddressObj;
      this.query.ProvinceName = data.province;
      this.query.CityName = data.city;
      this.query.CountyName = data.district;
      const obj = {
        province: data.province,
        city: data.city,
        county: data.district,
      };
      if (data.province === data.city) {
        obj.county = data.district;
        delete obj.city;
      }
      this.getPCCCode({
        province: data.province,
        city: data.city,
        county: data.district,
      });
    },
    async getPCCCode(data) {
      this.showRich = false;
      const res = await getRegionCode(data);
      if (res.Type === 200) {
        this.query.Province = res.Data.ProvinceCode;
        this.query.City = res.Data.CityCode;
        this.query.County = res.Data.CountyCode;
        this.$emit('changeAddress', this.query);
      }
    },
  },
};
</script>

<style scoped lang="scss">
/deep/ .u-border {
  background: #ffffff !important;
  border-radius: 24rpx !important;
  border: 2rpx solid #29b7a3 !important;
  padding: 24rpx 32rpx !important;
}
</style>
