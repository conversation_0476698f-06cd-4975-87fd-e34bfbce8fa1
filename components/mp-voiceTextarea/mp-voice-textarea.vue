<template>
  <block>
    <view class="wapper">
      <view class="wapper-left">
        <u-textarea
          v-model="inputValue"
          @input="$emit('input', inputValue)"
          :customStyle="{
            backgroundColor: '#F5F6FA !important',
            borderRadius: '24rpx 0 0 24rpx',
          }"
          border="none"
          :placeholder="placeholder"
          autoHeight
          :showConfirmBar="false"
          :adjustPosition="false"
        ></u-textarea>
      </view>
      <view class="wapper-right" @touchstart="handleVoiceClick">
        <image
          src="https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/static/frontend/mp-patient/voice.png"
          class="wapper-right-image"
        />
      </view>
    </view>
    <u-modal
      :show="showModal"
      title="温馨提示"
      @confirm="handleFinish"
      @cancel="handleCancel"
      :showCancelButton="true"
      confirmText="说完了"
      confirmColor="#29B7A3"
    >
      <SpeakCss slot="default" style="padding: 30px" />
    </u-modal>
  </block>
</template>

<script>
const plugin = requirePlugin('WechatSI');
const manager = plugin.getRecordRecognitionManager();
import SpeakCss from '../mp-speakCss/speakCss.vue';
export default {
  props: {
    value: {
      type: String,
      default: '',
    },
    placeholder: {
      type: String,
      default: '描述您的疾病或症状',
    },
  },
  data() {
    return {
      showModal: false,
      inputValue: '',
    };
  },
  components: {
    SpeakCss,
  },
  watch: {
    value: {
      handler(newValue) {
        this.inputValue = newValue;
      },
      immediate: true,
    },
  },
  created() {
    this.initRecord();
  },
  methods: {
    handleVoiceClick() {
      uni.getSetting({
        success: (res) => {
          console.log('res.authSetting', res.authSetting['scope.record']);
          if (res.authSetting['scope.record'] == false) {
            uni.authorize({
              scope: 'scope.record',
              success() {
                this.showModal = true;
                manager.start({
                  lang: 'zh_CN',
                });
              },
              fail() {
                uni.showModal({
                  content: '检测到您没打开录音功能权限，是否去设置打开？',
                  confirmText: '确认',
                  cancelText: '取消',
                  success: (res) => {
                    if (res.confirm) {
                      uni.openSetting({
                        success: (res) => {
                          if (res.authSetting) {
                            this.showModal = true;
                            manager.start({
                              lang: 'zh_CN',
                            });
                          } else {
                            return;
                          }
                        },
                      });
                    } else {
                      return false;
                    }
                  },
                });
              },
            });
          } else {
            this.showModal = true;
            manager.start({
              lang: 'zh_CN',
            });
          }
        },
      });
      console.log('=======开始====');
    },
    handleCancel() {
      this.showModal = false;
      manager.stop();
    },
    handleFinish() {
      this.endStreamRecord();
      this.showModal = false;
    },
    initRecord() {
      //有新的识别内容返回，则会调用此事件
      manager.onRecognize = (res) => {
        console.log('onRecognize', res);
        let text = res.result;
        if (text) {
          console.log('识别结果', text);
        }
      };
      // 识别结束事件
      manager.onStop = (res) => {
        console.log('onStop', res);
        let text = res.result;
        if (text) {
          console.log('识别结果', text);
          const result = this.inputValue + text;
          this.$emit('input', result);
        }
      };
    },
    endStreamRecord() {
      console.log('=======结束====');
      manager.stop();
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ textarea {
  padding: 10px !important;
}

.wapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f5f6fa;
  border-radius: 24rpx;

  &-left {
    flex: 1;
  }

  &-right {
    &-image {
      width: 44rpx;
      height: 44rpx;
      margin: 0 20rpx;
    }
  }
}
</style>
