<template>
  <view
    class="image-container"
    :style="{
      'width': width,
      'height': height,
      'border-radius': shape == 'circle' ? '50%' : radius,
      'background-color': bgColor,
    }"
  >
    <image
      :style="{
        'width': width,
        'height': height,
        'border-radius': shape == 'circle' ? '50%' : radius,
        'background-color': bgColor,
      }"
      :src="src"
      :mode="mode"
      v-if="!isError"
      @click="_onClick"
      @tap="_onTap"
      @load="_imageLoad"
      @error="_imageError"
    >
    </image>
    <view
      class="loading-container"
      :style="{
        top: loadingTop + 'px',
        left: loadingLeft + 'px',
      }"
      v-if="loading"
    >
      <u-loading-icon :size="loadingSize" color="#333333" />
    </view>
    <image
      :style="{
        'width': errorImageWidth,
        'height': errorImageHeight,
        'border-radius': shape == 'circle' ? '50%' : radius,
        'background-color': bgColor,
      }"
      :src="errorImageSrc"
      v-if="!loading && isError"
    ></image>
  </view>
</template>

<script>
export default {
  name: 'mp-image',
  emits: ['click', 'tap'],
  props: {
    // 图片地址
    src: {
      type: String,
      default: '',
    },
    // 裁剪模式
    mode: {
      type: String,
      default: 'aspectFill',
    },
    // 宽度，单位px
    width: {
      type: [String, Number],
      default: '100px',
    },
    // 高度，单位px
    height: {
      type: [String, Number],
      default: '100px',
    },
    // 图片形状，circle-圆形，square-方形
    shape: {
      type: String,
      default: 'square',
    },
    // 圆角，单位px
    radius: {
      type: [String, Number],
      default: 0,
    },
    // 背景颜色，用于深色页面加载图片时，为了和背景色融合
    bgColor: {
      type: String,
      default: '#f3f4f6',
    },
    // 错误提示图宽度，单位px
    errorImageWidth: {
      type: [String, Number],
      default: '60px',
    },
    // 错误提示图高度，单位px
    errorImageHeight: {
      type: [String, Number],
      default: '60px',
    },
    // 错误图片
    errorImageSrc: {
      type: String,
      default: '/static/common/default-image.png',
    },
    // 加载中图标大小，单位px
    loadingSize: {
      type: Number,
      default: 24,
    },
  },
  data() {
    return {
      // 加载中
      loading: true,
      // 加载失败
      isError: false,
    };
  },
  watch: {
    src: {
      immediate: true, // 表示监听开始后，立即调用
      handler(n) {
        if (!n) {
          // 如果传入null，或者""，或者undefined，标记为错误状态
          this.isError = true;
          this.loading = false;
        } else {
          this.isError = false;
          this.loading = true;
        }
      },
    },
  },
  computed: {
    loadingTop() {
      let height = parseFloat(this.height.toString().replace(/[^0-9]/gi, ''));
      let loadingSize = parseFloat(this.loadingSize);
      return height / 2 - loadingSize / 2;
    },
    loadingLeft() {
      let width = parseFloat(this.width.toString().replace(/[^0-9]/gi, ''));
      let loadingSize = parseFloat(this.loadingSize);
      return width / 2 - loadingSize / 2;
    },
  },
  methods: {
    _onClick() {
      this.$emit('click');
    },
    _onTap() {
      this.$emit('tap');
    },
    _imageLoad(e) {
      // console.log('图片加载完成', e);
      this.loading = false;
      this.isError = false;
    },
    _imageError(e) {
      // console.log('图片加载失败', e);
      this.loading = false;
      this.isError = true;
    },
  },
};
</script>

<style lang="scss">
.image-container {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.loading-container {
  position: absolute;
}
</style>
