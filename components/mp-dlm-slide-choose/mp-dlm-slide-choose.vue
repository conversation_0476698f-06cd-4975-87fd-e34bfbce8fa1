<template>
  <view class="rulerContainer">
    <view class="text-top-icon" id="text-top-icon">
      <image class="icon-top-img" src="/static/images/23down.png" />
    </view>
    <swiper
      @animationfinish="finishRuler"
      :current="current"
      :display-multiple-items="30"
      :acceleration="true"
      style="height: 100rpx"
      active-class="showItem"
    >
      <!-- 留60刻度 以便可以左滑到底、右滑到0-->
      <swiper-item
        v-for="(item, index) in maxValue + 60"
        :key="index"
        style="width: 110%"
      >
        <view class="swiper-item" v-if="index < maxValue + 1">
          <view
            class="zoro-line"
            :class="
              index % 10 == 0 || index % 10 == 5 ? 'num-line-6' : 'num-line-3'
            "
          ></view>
          <view
            v-if="(index / 10) % 1 == 0 && index - 30 >= 0 && !decimal"
            class="zoro-line-num"
            >{{ -30 + index }}</view
          >
          <view
            v-if="(index / 10) % 1 == 0 && index - 30 >= 0 && decimal"
            class="zoro-line-num"
            >{{ (-30 + index) / 10 }}
          </view>
        </view>
      </swiper-item>
    </swiper>
  </view>
</template>
<script>
export default {
  name: 'SwiperRuler',
  props: {
    // 选中的页码
    current: {
      type: [Number, String],
      default: 1,
    },
    // 尺子大小 10cm 传100,10in 传100
    maxValue: {
      type: Number,
      default: 100,
    },
    // 是否显示小数
    decimal: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  created() {},
  methods: {
    finishRuler(e) {
      // this.$emit('finishRuler', e.detail.current - 15)
      this.$emit(
        'finishRuler',
        this.decimal ? (e.detail.current - 15) / 10 : e.detail.current - 15
      );
      // if(this.decimal) {

      // }
    },
  },
};
</script>
<style scoped>
/* 在公共样式添加此代码 */
/* .rulerContainer .uni-swiper-slide-frame{
		left:50%;
		width:16rpx!important;
	} */
/* 在公共样式添加此代码 */
/deep/ swiper-item {
  overflow: visible !important;
}

.rulerContainer {
  text-align: center;
}

.rulerContainer uni-swiper {
  height: 150rpx;
}

.rulerContainer .icon-top-img {
  width: 32rpx;
  height: 54rpx;
}

.swiper-item {
  width: 100%;
  height: 80rpx;
  margin: 0;
}

.rulerContainer uni-swiper-item {
  overflow: initial;
}

.rulerContainer .zoro-line {
  position: relative;
  width: 100%;
  text-align: left;
}

.rulerContainer .zoro-line::before {
  display: block;
  content: '';
  height: 43rpx;
  position: absolute;
  border-left: 1px solid #b5b5b5;
  top: 0;
  left: 0;
}

.rulerContainer .num-line-3::before,
.rulerContainer .num-line-5::before,
.rulerContainer .num-line-7::before,
.rulerContainer .num-line-9::before,
.rulerContainer .num-line-11::before,
.rulerContainer .num-line-13::before {
  height: 16rpx;
}

.rulerContainer .num-line-6::before,
.rulerContainer .num-line-10::before {
  height: 32rpx;
  border-width: 2px;
}

.rulerContainer .num-line-1::before,
.rulerContainer .num-line-15::before {
  height: 16rpx;
  border-width: 2px;
}

.rulerContainer .num-line-12::before,
.rulerContainer .num-line-4::before {
  height: 38rpx;
}

.rulerContainer .num-line-8::before {
  height: 48rpx;
  border-width: 2px;
}

.rulerContainer .num-line-2::before,
.rulerContainer .num-line-14::before {
  height: 32rpx;
}

.rulerContainer .zoro-line-num {
  top: 50rpx;
  left: -50%;
  text-align: center;
  position: relative;
  color: #b3b3b3;
  font-size: 24rpx;
}

.rulerContainer .big-line-num {
  left: -14rpx;
}

.rulerContainer .text-num-init {
  text-align: left;
  width: 100%;
  display: block;
  height: 100%;
}

.showItem {
  color: red;
}
</style>
