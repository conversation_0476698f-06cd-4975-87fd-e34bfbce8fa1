<template>
  <view class="loader">
    <view class="span"></view>
    <view class="span"></view>
    <view class="span"></view>
    <view class="span"></view>
    <view class="span"></view>
    <view class="span"></view>
    <view class="span"></view>
    <view class="span"></view>
    <view class="span"></view>
    <view class="span"></view>
    <view class="span"></view>
    <view class="span"></view>
    <view class="span"></view>
    <view class="span"></view>
    <view class="span"></view>
  </view>
</template>

<script></script>

<style scoped lang="scss">
.loader {
  position: absolute;
  top: 0px;
  bottom: 0px;
  left: 0px;
  right: 0px;
  margin: auto;
  width: 175rpx;
  height: 100rpx;
  display: flex;
  justify-content: center;
}

.loader .span {
  display: block;
  background: #ccc;
  width: 7px;
  height: 10%;
  border-radius: 14px;
  margin-right: 5px;
  float: left;
  margin-top: 25%;
}

.loader .span:last-child {
  margin-right: 0px;
}

.loader .span:nth-child(1) {
  animation: load 1s 1.4s infinite linear;
}

.loader .span:nth-child(2) {
  animation: load 1s 1.2s infinite linear;
}

.loader .span:nth-child(3) {
  animation: load 1s 1s infinite linear;
}

.loader .span:nth-child(4) {
  animation: load 1s 0.8s infinite linear;
}

.loader .span:nth-child(5) {
  animation: load 1s 0.6s infinite linear;
}

.loader .span:nth-child(6) {
  animation: load 1s 0.4s infinite linear;
}

.loader .span:nth-child(7) {
  animation: load 1s 0.2s infinite linear;
}

.loader .span:nth-child(8) {
  animation: load 1s 0s infinite linear;
}

.loader .span:nth-child(9) {
  animation: load 1s 0.2s infinite linear;
}

.loader .span:nth-child(10) {
  animation: load 1s 0.4s infinite linear;
}

.loader .span:nth-child(11) {
  animation: load 1s 0.6s infinite linear;
}

.loader .span:nth-child(12) {
  animation: load 1s 0.8s infinite linear;
}

.loader .span:nth-child(13) {
  animation: load 1s 1s infinite linear;
}

.loader .span:nth-child(14) {
  animation: load 1s 1.2s infinite linear;
}

.loader .span:nth-child(15) {
  animation: load 1s 1.4s infinite linear;
}

@keyframes load {
  0% {
    background: #29b7a3;
    margin-top: 25%;
    height: 10%;
  }

  50% {
    background: #29b7a497;
    height: 100%;
    margin-top: 0%;
  }

  100% {
    background: #29b7a3;
    height: 10%;
    margin-top: 25%;
  }
}
</style>
