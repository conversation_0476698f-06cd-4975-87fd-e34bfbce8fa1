{"name": "mp-patient", "version": "1.0.0", "description": "康复行小程序", "author": "kfx", "main": "main.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch --minimize", "p-check": "prettier . --check", "p": "prettier . --white"}, "dependencies": {"crypto-js": "^4.2.0", "dayjs": "^1.11.6", "kfx-im": "git+https://gitlab-dev.kangfx.com/kangfx/frontend/kfx-im-js.git#semver:^1.0.5", "signalr-uni": "^5.1.1"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "copy-webpack-plugin": "^5.1.2", "prettier": "3.5.3", "webpack-bundle-analyzer": "^4.10.2"}}