<template>
  <view class="container">
    <u-navbar :title="title" @leftClick="leftClick" :placeholder="true">
    </u-navbar>

    <p>{{ data.Remark || '' }}</p>

    <view v-for="(item, index) in dataPro" :key="item.Id">
      <view style="margin-top: 20rpx">
        <p>
          {{ index + 1 }}.{{ item.Title }}
          <span style="color: red; margin-left: 10rpx" v-if="item.IsRequired"
            >*</span
          >
        </p>
        <u-radio-group
          v-model="item.value"
          placement="column"
          :disabled="disabled"
          v-if="item.ProblemType === 1"
        >
          <u-radio
            :customStyle="{ marginBottom: '8px' }"
            v-for="(k, index2) in item.GaugeProblemDetails"
            :key="k.Id"
            :label="k.ProblemOption"
            :name="k.Id"
            @change="radioGroupChange($event, index, index2)"
            @click="radioGroupClick($event, index, index2)"
          >
          </u-radio>
        </u-radio-group>
        <u-checkbox-group
          v-model="item.value"
          placement="column"
          :disabled="disabled"
          v-if="item.ProblemType === 2"
        >
          <u-checkbox
            :customStyle="{ marginBottom: '8px' }"
            v-for="(k, index2) in item.GaugeProblemDetails"
            :key="k.Id"
            :label="k.ProblemOption"
            :name="k.Id"
            @change="checkboxGroupChange($event, index, index2)"
          >
          </u-checkbox>
        </u-checkbox-group>
        <u-textarea
          :disabled="disabled"
          v-model="item.GaugeProblemDetails[0].Answer"
          placeholder="请输入内容"
          v-if="item.ProblemType === 3"
        ></u-textarea>
        <u-input
          @blur="numberInputBlur"
          :disabled="disabled"
          v-model="item.GaugeProblemDetails[0].Answer"
          v-if="item.ProblemType === 4"
          customStyle="background:white"
        >
        </u-input>
      </view>
    </view>
    <u-modal
      :show="show"
      title="温馨提示"
      content="本次评估还未完成,退出后已填写的内容不会保存,确认要退出吗"
      @cancel="cancel"
      @confirm="confirm"
      :showCancelButton="true"
    ></u-modal>
    <u-toast ref="uToast"></u-toast>

    <view class="bomm-btn-style"></view>
    <u-button
      @click="save"
      type="success"
      shape="circle"
      text="提交"
      customStyle="width:90%;bottom: 30px;position: fixed; left: 50%;transform: translateX(-50%);backgroundColor:#29B7A3;color:#fff"
      v-if="State == 0"
    >
    </u-button>
  </view>
</template>

<script>
const app = getApp();
import { getGaugeById } from '../api/training.js';
import { InsertPatEvaluateGauge, DctGetPatGaugeById } from './api.js';
export default {
  data() {
    return {
      id: '', // 量表id
      data: {},
      show: false,
      radiovalue: '',
      DctSendSign: '',
      RelatedId: '',
      goReq: false,
      next: true,
      type: 0, // 1:院内-就诊流程 2:院外-问诊流程
      roomType: 0, // 1:居家指导群 2:问诊群
      disabled: false,
      sendMessage: '',
      title: '自主评估',
      canNext: false,
      dataPro: [],
      BusinessId: '',
      State: null,
    };
  },
  onLoad(option) {
    if (option.id) {
      this.State = option.State;
      this.id = option.id;
      this.RelatedId = option.RelatedId;
      this.getDetail();
    } else {
      this.disabled = true;
      this.BusinessId = option.BusinessId;
      this.getDetailFin();
    }
  },
  methods: {
    numberInputBlur(value) {
      const rex = /^-?\d*\.?\d{0,2}$/;
      if (!rex.test(value)) {
        this.$refs.uToast.show({
          message: '输入的数字必须是整数或者带两位小数',
          type: 'error',
        });
      }
    },
    radioGroupClick(e, index, index2) {
      console.log(e, index, index2);
      if (
        this.data.GaugeProblems[index].GaugeProblemDetails[index2].Answer === 1
      ) {
        this.data.GaugeProblems[index].GaugeProblemDetails[index2].Answer = 0;
      } else {
        this.data.GaugeProblems[index].GaugeProblemDetails[index2].Answer = 1;
      }
    },
    radioGroupChange(e, index, index2) {
      this.data.GaugeProblems[index].GaugeProblemDetails.forEach((e) => {
        e.Answer = 0;
      });
      this.data.GaugeProblems[index].GaugeProblemDetails[index2].Answer = 1;
    },
    checkboxGroupChange(e, index, index2) {
      if (e) {
        this.data.GaugeProblems[index].GaugeProblemDetails[index2].Answer = 1;
      } else {
        this.data.GaugeProblems[index].GaugeProblemDetails[index2].Answer = 0;
      }
    },
    async getDetailFin() {
      const res = await DctGetPatGaugeById({
        patGaugeId: this.BusinessId,
      });
      if (res.Type === 200) {
        const obj = res.Data[0];
        obj.PatGaugeProblems.forEach((v) => {
          v.GaugeProblemDetails = v.PatGaugeProblemDetails;
        });
        obj.GaugeProblems = obj.PatGaugeProblems;
        console.log('obj.GaugeProblems', obj.GaugeProblems);
        obj.GaugeProblems.forEach((o) => {
          if (o.ProblemType === 1) {
            o.value =
              o.GaugeProblemDetails.filter((k) => k.Answer == '1')[0]?.Id ||
              null;
          } else if (o.ProblemType === 2) {
            const arr = o.GaugeProblemDetails.filter((k) => k.Answer == '1');
            let arr1 = [];
            arr.forEach((m) => {
              arr1.push(m.Id);
            });
            o.value = arr1;
          }
        });
        this.data = obj;
        this.dataPro = obj.GaugeProblems;
      }
    },
    async getDetail() {
      let res = await getGaugeById(this.id);
      if (res.Type == 200 && res.Data) {
        res.Data.GaugeProblems.map((v) => {
          if (v.ProblemType === 3) {
            v.GaugeProblemDetails = [
              {
                Answer: '',
              },
            ];
          }
        });
        this.data = res.Data;
        this.dataPro = res.Data.GaugeProblems;
        this.title = res.Data.Name;
      } else {
        uni.showModal({
          showCancel: false,
          content: '量表不存在',
          complete() {
            uni.navigateBack();
          },
        });
      }
    },
    async save() {
      const checkData = this.data.GaugeProblems.filter((e) => e.IsRequired);
      let selectArrNum = 0;
      let inputArrNum = 0;
      let noSelect = 0;
      let noInput = 0;
      checkData.forEach((e) => {
        if (e.ProblemType === 3) {
          inputArrNum++;
        } else {
          selectArrNum++;
        }
      });
      console.log('总数', selectArrNum, inputArrNum);
      checkData.forEach((e, index) => {
        if (e.ProblemType != 3 && e.ProblemType != 4) {
          // 单选/多选
          if (e.GaugeProblemDetails.findIndex((o) => o.Answer === 1) == -1) {
            // 没找到
            noSelect++;
          }
        } else {
          if (
            e.GaugeProblemDetails[0].Answer === '' ||
            e.GaugeProblemDetails[0].Answer === null
          ) {
            noInput++;
          }
        }
      });
      console.log('未选择', noSelect, noInput);
      if (noSelect > 0 || noInput > 0) {
        this.next = false;
      } else {
        this.next = true;
      }
      if (!this.next) {
        console.log('还没有选择完');
        this.$refs.uToast.show({
          message: '请先选择完之后再提交',
          type: 'error',
        });
        return;
      }
      const numberInputArr = checkData.filter((p) => p.ProblemType === 4);
      const re = /^-?\d*\.?\d{0,2}$/;
      const flag = numberInputArr.some(
        (i) => !re.test(i.GaugeProblemDetails[0].Answer)
      );
      if (flag) {
        this.$refs.uToast.show({
          message: '数字类型输入有问题',
          type: 'error',
        });
        return;
      }

      this.data.RelatedId = this.RelatedId;
      this.data.Source = 4;
      delete this.data.Remark;
      this.data.PatGaugeProblems = JSON.parse(
        JSON.stringify(this.data.GaugeProblems)
      );
      this.data.PatGaugeProblems.forEach((e) => {
        e.PatGaugeProblemDetails = JSON.parse(
          JSON.stringify(e.GaugeProblemDetails)
        );
        e.PatGaugeProblemDetails.forEach((k) => {
          delete k.Id;
          delete k.ProlemId;
        });
      });
      this.data.PatGaugeResults = this.data.GaugeResults;
      this.data.PatGaugeDiseaseRelations = [];
      this.data.GaugeDiseaseRelations.forEach((e) => {
        delete e.Id;
        delete e.EvaluateGaugeId;
        delete e.CreatedTime;
      });
      this.data.PatGaugeProblems.map((e) => {
        if (e.ProblemType != 3 && !e.IsRequired) {
          if (
            e.PatGaugeProblemDetails.findIndex((o) => o.Answer === 1) === -1
          ) {
            console.log(e.PatGaugeProblemDetails);
            e.PatGaugeProblemDetails.map((k) => {
              k.Answer = null;
            });
          }
        }
      });

      console.log('this.data', this.data);
      this.saveGauge();
    },
    async saveGauge() {
      uni.showToast({
        title: '提交中...',
        mask: true,
      });

      let res = await InsertPatEvaluateGauge({
        ...this.data,
        PatientId: app.globalData.userInfo.Id,
      });
      if (res.Type != 200) {
        uni.hideLoading();
        uni.showToast({
          title: res.Content,
          icon: 'none',
        });
        return;
      }
      this.$refs.uToast.show({
        message: '操作成功',
        type: 'success',
      });
      let pages = getCurrentPages();
      let prePage = pages[pages.length - 2];
      if (prePage.$vm.loadNewData) {
        prePage.$vm.loadNewData();
      }
      if (prePage.$vm.GetPatNotOpera) {
        prePage.$vm.GetPatNotOpera.TotalCount = 0;
      }
      setTimeout(() => {
        uni.navigateBack();
      }, 1000);
    },
    leftClick() {
      if (this.disabled) {
        uni.navigateBack();
      }
      this.show = true;
    },
    cancel() {
      this.show = false;
    },
    confirm() {
      this.show = false;
      uni.navigateBack();
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  padding: 10px;

  /deep/ .u-radio {
    width: 100% !important;
  }

  /deep/ .u-radio__icon-wrap {
    width: 30px !important;
    height: 30px !important;
  }

  /deep/ .u-checkbox__icon-wrap {
    width: 30px !important;
    height: 30px !important;
  }

  /deep/ .u-radio__text {
    font-size: 17px !important;
    flex: 1;
  }

  /deep/ .u-checkbox__text {
    font-size: 17px !important;
    flex: 1;
  }

  /deep/ .u-radio-group {
    margin-top: 20rpx;
    margin-left: 20rpx;
  }

  /deep/ .u-checkbox-group {
    margin-top: 20rpx;
    margin-left: 20rpx;
  }

  /deep/ .u-checkbox__text {
    line-height: normal !important;
  }

  /deep/ .u-radio__text {
    line-height: normal !important;
  }
}
</style>
