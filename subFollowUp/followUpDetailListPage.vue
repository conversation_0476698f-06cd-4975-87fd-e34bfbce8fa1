<template>
  <scroll-view
    class="scroll-content"
    scroll-y="true"
    enable-flex="true"
    enable-back-to-top="true"
  >
    <!-- 无随访任务 -->
    <u-empty
      mode="data"
      icon="http://cdn.uviewui.com/uview/empty/data.png"
      v-if="dataList.length == 0"
    >
    </u-empty>

    <!-- 明细列表 -->
    <view class="list-view">
      <u-list>
        <u-list-item v-for="(item, index) in dataList" :key="index">
          <!-- 宣教 -->
          <view
            class="list-item"
            hover-class="hover-class"
            @click="onCheckMission(item)"
            v-if="item.Type == 2"
          >
            <u--image
              :src="item.RecoveryImgUrl"
              width="70rpx"
              height="70rpx"
              radius="8rpx"
            ></u--image>
            <text class="mission-item-title">{{ item.RelatedName }}</text>
            <text
              class="mission-item-state"
              :style="{ color: item.State == 1 ? '#29B7A3' : '#FF5656' }"
            >
              {{ item.State == 1 ? '已读' : '未读' }}
            </text>
          </view>
          <!-- 量表、问卷 -->
          <view
            class="list-item"
            hover-class="hover-class"
            @click="onCheckQuestionnaireOrAssess(item)"
            v-else
          >
            <image
              class="other-item-icon"
              src="/static/images/lb.png"
              v-if="item.Type == 1"
            ></image>
            <image
              class="other-item-icon"
              src="/subFollowUp/static/follow-up.png"
              v-else
            ></image>
            <text class="other-item-title">{{ item.RelatedName }}</text>
            <view class="other-item-column">
              <text
                class="other-item-state"
                :style="{
                  color:
                    item.State == 1
                      ? '#29B7A3'
                      : item.State == 0
                        ? '#FF5656'
                        : '#989898',
                }"
              >
                {{
                  item.State == 1
                    ? '已填写'
                    : item.State == 0
                      ? '未填写'
                      : '已超期'
                }}
              </text>
              <text class="other-item-time" v-if="item.State == 1">{{
                item.OperaTime
              }}</text>
            </view>
          </view>
        </u-list-item>
      </u-list>
    </view>

    <!-- 上拉提示尾部 -->
    <view
      class="pull-foot-view"
      :style="{ display: !haveMore && dataList.length > 0 ? '' : 'none' }"
    >
      没有更多了
    </view>
  </scroll-view>
</template>

<script>
import { getFollowUpPlanDetailList } from '@/api/consult';
import { arrayNotEmpty, dataIsValid, stringNotEmpty } from '@/utils/utils';
import { dateFormat } from '@/utils/validate.js';
import { FollowUpClientEvent } from '@/utils/eventKeys';
import { getMissionImageUrl } from '../utils/mission';

const app = getApp();
export default {
  data() {
    return {
      pageIndex: 1,
      haveMore: true,

      // 随访明细列表
      dataList: [],
    };
  },
  onLoad() {
    uni.startPullDownRefresh();

    // 新增随访任务通知
    this.addNewFollowUpFunction = function (e) {
      uni.startPullDownRefresh();
    };
    uni.$on(FollowUpClientEvent.add, this.addNewFollowUpFunction);
  },
  onPullDownRefresh: async function () {
    uni.showLoading({
      title: '正在加载...',
      mask: true,
    });
    await this.loadNewData();
    uni.hideLoading();
    uni.stopPullDownRefresh();
  },
  onReachBottom: async function () {
    uni.showLoading({
      title: '正在加载...',
      mask: true,
    });
    await this.loadMoreData();
    uni.hideLoading();
  },
  onUnload() {
    uni.$off(FollowUpClientEvent.add, this.addNewFollowUpFunction);
  },
  methods: {
    // 点击宣教详情
    async onCheckMission(item) {
      if (!stringNotEmpty(item.RelatedId)) {
        uni.showToast({
          title: '宣教id为空',
          icon: 'none',
        });
        return;
      }

      // 打开宣教详情页，打开即默认为已读
      uni.navigateTo({
        url:
          '/subPropaganda/detail?id=' +
          item.RelatedId +
          '&ShowId=' +
          item.ShowId,
        success: () => {
          this.updateDetailState(item, null);
        },
      });
    },
    // 点击查看量表/问卷
    async onCheckQuestionnaireOrAssess(item) {
      let title = item.Type == 1 ? '量表' : '问卷';
      if (item.State == 0 || item.State == 2) {
        // 未填写、已过期，如果已过期则只看，未填写则可操作
        if (!stringNotEmpty(item.RelatedId)) {
          uni.showToast({
            title: `${title}id为空`,
            icon: 'none',
          });
          return;
        }

        if (!stringNotEmpty(item.ShowId)) {
          uni.showToast({
            title: `${title}id为空`,
            icon: 'none',
          });
          return;
        }

        // 跳转量表/问卷填写页面
        // 只有item.State == 0 才能编辑，item.State == 2 为只读
        let that = this;
        uni.navigateTo({
          url: `./autonomy?id=${item.RelatedId}&RelatedId=${item.ShowId}&State=${item.State}`,
          events: {
            // 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
            acceptDataFromOpenedPage: function (data) {
              console.debug('接收回传的数据', data);
            },
          },
        });
      } else {
        // 已填写，只能查看
        if (!stringNotEmpty(item.BusinessId)) {
          uni.showToast({
            title: `${title}id为空`,
            icon: 'none',
          });
          return;
        }

        // 跳转量表/问卷查看页面，查看填写的内容
        let that = this;
        uni.navigateTo({
          url: `/subFollowUp/autonomy?BusinessId=${item.BusinessId}`,
        });
      }
    },
    // 重新加载数据
    async loadNewData() {
      this.pageIndex = 1;
      this.haveMore = true;
      this.dataList = [];
      await this.loadData();
    },
    // 加载更多数据
    async loadMoreData() {
      if (!this.haveMore) return;
      this.pageIndex++;
      await this.loadData();
    },
    // 更新明细已读/已填写状态
    updateDetailState(entity, businessId) {
      entity.State = 1;
      if (entity.Type != 2) {
        // 量表和问卷需要额外更新的数据
        entity.BusinessId = businessId;
        entity.OperaTime = dateFormat(Date(), 'YYYY-MM-DD');
      }
    },
    // 获取随访明细列表
    async loadData() {
      let userId = app.globalData.userInfo.Id;
      let r = await getFollowUpPlanDetailList(
        null,
        '',
        userId,
        this.pageIndex,
        20
      );

      if (r.Type == 200) {
        let list = r.Data.Data;
        if (!arrayNotEmpty(list)) {
          this.haveMore = false;
          return;
        }

        list.forEach((item, index) => {
          item.OperaTime = item.OperaTime
            ? dateFormat(item.OperaTime, 'YYYY-MM-DD')
            : '';
          const indexId = item.ShowId.charAt(item.ShowId.length - 1) * 1;
          item.RecoveryImgUrl =
            item.RecoveryImgUrl || getMissionImageUrl(indexId);
        });

        if (this.pageIndex == 1) {
          this.dataList = list;
        } else {
          this.dataList.push(...list);
        }
      } else {
        uni.showToast({
          title: r.Content,
          icon: 'none',
        });
      }

      return r;
    },
  },
};
</script>

<style lang="scss" scoped>
@import url('../common/common.css');

.scroll-content {
  padding: 32rpx 24rpx;
}

.scroll-content-no-data {
  background-color: #f7f7f7;
  display: flex;
  justify-content: center;
  align-items: center;
}

.list-view {
  padding-bottom: 32rpx;
}

.list-view /deep/ .u-list {
  height: auto !important;
}

.list-item {
  margin-bottom: 24rpx;
  padding: 32rpx 24rpx;
  border-radius: 16rpx;
  background-color: white;
  display: flex;
  flex-direction: row;
  align-items: center;
  box-shadow: 0 4rpx 8rpx 0 #29b7a31a;
}

.mission-item {
  &-title {
    padding: 0 32rpx;
    font-size: 28rpx;
    color: #333333;
    flex: 1;
  }

  &-state {
    font-size: 24rpx;
  }
}

.other-item {
  &-icon {
    width: 70rpx;
    height: 82rpx;
  }

  &-title {
    padding: 0 32rpx;
    font-size: 28rpx;
    color: #333333;
    flex: 1;
  }

  &-column {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  &-state {
    font-size: 28rpx;
    padding-bottom: 16rpx;
  }

  &-time {
    font-size: 20rpx;
    color: #999999;
  }
}
</style>
