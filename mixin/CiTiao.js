/**
 *  本 mixin 内封装了获取平台下的词条
 *
 */

import { getCILeftList, getCIList } from '@/api/dictionary.js';

export default {
  data() {
    return {
      leftData: [],
      GMSList: [],
      JWSList: [],
    };
  },
  created() {},
  methods: {
    async getTages(cb) {
      const data = {
        PageCondition: {
          PageIndex: 1,
          PageSize: 99,
          SortConditions: [
            {
              SortField: 'Id',
              ListSortDirection: 1,
            },
          ],
        },
        FilterGroup: {
          Rules: [],
          Groups: [
            {
              Rules: [
                {
                  Field: 'Code',
                  Value: 'PastHistory',
                  Operate: 11,
                },
                {
                  Field: 'Code',
                  Value: 'AllergyHistory',
                  Operate: 11,
                },
              ],
              Operate: 2,
            },
          ],
          Operate: 1,
        },
      };
      const res = await getCILeftList(data);
      if (res.Type === 200) {
        this.leftData = res.Data.Rows;
        this.getRightData(cb);
      }
    },
    getRightData(cb) {
      const data = {
        PageCondition: {
          PageIndex: 1,
          PageSize: 999,
          SortConditions: [
            {
              SortField: 'OrderNumber',
              ListSortDirection: 0,
            },
          ],
        },
        FilterGroup: {
          Rules: [
            {
              Field: 'DictId',
              Value: this.leftData[0].Id * 1,
              Operate: 3,
            },
            {
              Field: 'IsEnabled',
              Value: true,
              Operate: 3,
            },
            {
              Field: 'IsPublish',
              Value: true,
              Operate: 3,
            },
          ],
          Groups: [
            {
              Rules: [
                {
                  Field: 'Key',
                  Value: '',
                  Operate: 11,
                },
                {
                  Field: 'Value',
                  Value: '',
                  Operate: 11,
                },
                {
                  Field: 'PinyinCode',
                  Value: '',
                  Operate: 11,
                },
              ],
              Operate: 2,
            },
          ],
          Operate: 1,
        },
      };
      const data2 = {
        PageCondition: {
          PageIndex: 1,
          PageSize: 999,
          SortConditions: [
            {
              SortField: 'OrderNumber',
              ListSortDirection: 0,
            },
          ],
        },
        FilterGroup: {
          Rules: [
            {
              Field: 'DictId',
              Value: this.leftData[1].Id * 1,
              Operate: 3,
            },
            {
              Field: 'IsEnabled',
              Value: true,
              Operate: 3,
            },
            {
              Field: 'IsPublish',
              Value: true,
              Operate: 3,
            },
          ],
          Groups: [
            {
              Rules: [
                {
                  Field: 'Key',
                  Value: '',
                  Operate: 11,
                },
                {
                  Field: 'Value',
                  Value: '',
                  Operate: 11,
                },
                {
                  Field: 'PinyinCode',
                  Value: '',
                  Operate: 11,
                },
              ],
              Operate: 2,
            },
          ],
          Operate: 1,
        },
      };

      Promise.all([getCIList(data), getCIList(data2)])
        .then((responses) => {
          console.log('responses', responses);
          const Code1 =
            responses[0].Data.Rows.length > 0 &&
            this.leftData.filter(
              (o) => o.Id == responses[0].Data.Rows[0].DictId
            )[0].Code;
          const Code2 =
            responses[1].Data.Rows.length > 0 &&
            this.leftData.filter(
              (o) => o.Id == responses[1].Data.Rows[0].DictId
            )[0].Code;
          if (Code1 === 'AllergyHistory') {
            console.log('responses[0]', responses[0]);
            responses[0].Data.Rows.forEach((v) => {
              v.checked = false;
            });
            this.GMSList = responses[0].Data.Rows;
          }
          if (Code2 === 'PastHistory') {
            responses[1].Data.Rows.forEach((v) => {
              v.checked = false;
            });
            this.JWSList = responses[1].Data.Rows;
          }
          cb && cb();
        })
        .catch((error) => {
          console.log('error', error);
          // 处理错误情况
        });
    },
  },
};
