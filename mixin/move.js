/*
	监听小程序左滑右滑
*/
export default {
  data() {
    return {
      lastX: 0,
      SlidingDirection: null,
      StopDirection: null,
      downPull: false, // 是否有下拉加载或者左右点击切换的情况
    };
  },
  methods: {
    handletouchmove(event) {
      let currentX = event.changedTouches[0].pageX;
      let tx = currentX - this.lastX;
      if (tx < -20) {
        // 向左滑动
        this.SlidingDirection = 'left';
      } else if (tx > 20) {
        //向右滑动
        this.SlidingDirection = 'right';
      }
      this.lastX = currentX;
    },
    handletouchstart(event) {
      //滑动开始
      this.lastX = event.changedTouches[0].pageX;
    },
    handletouchend() {
      //停止滑动
      this.StopDirection = this.SlidingDirection;
    },
  },
};
