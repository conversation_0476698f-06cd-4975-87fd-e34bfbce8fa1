export default {
  data() {
    return {
      showPrivacyPopup: false, // 是否需要弹出协议
      PrivacyPopupTitle: '', // 小程序协议名称
      resolvePrivacyAuthorization: null, // wx.onNeedPrivacyAuthorization的回调
    };
  },
  created() {},
  methods: {
    // 打开弹窗
    openPrivacyPopup() {
      const app = getApp();
      this.PrivacyPopupTitle = app.globalData.PrivacyProtocol.title;
      this.showPrivacyPopup = true;
    },
    // 关闭弹窗
    closePrivacyPopup() {
      this.showPrivacyPopup = false;
    },
    // 用户点击同意
    handleAgreePrivacyAuthorization() {
      console.log('点击了同意');
      this.resolvePrivacyAuthorization({
        buttonId: 'agree-btn',
        event: 'agree',
      });
      this.showPrivacyPopup = false;
    },
    // 用户点击拒绝
    handleRefusePrivacyAuthorization() {
      uni.showModal({
        content: '如果拒绝,我们将无法获取您的信息, 包括手机号、位置信息、相册等该小程序十分重要的功能,您确定要拒绝吗?',
        success: (res) => {
          if (res.confirm) {
            const app = getApp();
            console.log('点击了拒绝', this.resolvePrivacyAuthorization);
            this.resolvePrivacyAuthorization({
              event: 'disagree',
            });
            this.closePrivacyPopup();
            uni.$log.warn(
              `用户${app.globalData.userInfo?.Name || ''}拒绝了隐私请求，无法使用相关微信的Api`
            );
          }
        },
      });
    },
    // 打开隐私协议
    onClickPrivacyPopupTitle() {
      wx.openPrivacyContract({});
    },
    // 监听调用微信api的函数
    saveWXCallBack() {
      if (wx.onNeedPrivacyAuthorization) {
        wx.onNeedPrivacyAuthorization((resolve) => {
          this.resolvePrivacyAuthorization = resolve;
          this.openPrivacyPopup();
        });
      }
    },
  },
};
