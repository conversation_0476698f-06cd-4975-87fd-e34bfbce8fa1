declare namespace App {
  interface AppInstance<T extends AnyObject = {}> {
    /**
     * 获取当前登录用户id
     * @returns {string}
     */
    getAccountId(): string;
    /**
     * 判断是否已登录
     */
    isLoggedIn(): boolean;
    /**
     * 判断是否已认证
     */
    isCertified(): boolean;
    /**
     * 统一跳转到聊天页面
     * @param {string} id consultId
     */
    toChatPage(id: string): void;
  }
}

interface Vue {
  readonly $log: Console;
}

interface Uni {
  readonly $log: Console;
}
