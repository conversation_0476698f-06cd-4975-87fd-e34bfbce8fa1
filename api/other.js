import request from '@/libs/request';
import config from '@/config.js';

// 获取知情通知书
export function getNotice() {
  return new Promise((resolive, reject) => {
    uni.request({
      url: 'https://hwkjoss-kfx-biz.oss-cn-hangzhou.aliyuncs.com/%E4%BA%92%E8%81%94%E7%BD%91%E8%AF%8A%E7%96%97%E9%A3%8E%E9%99%A9%E5%91%8A%E7%9F%A5%E5%8F%8A%E7%9F%A5%E6%83%85%E5%90%8C%E6%84%8F%E4%B9%A6V2.html',
      data: {},
      method: 'GET',
      success: (res) => {
        if (res.statusCode == 200) {
          resolive(res.data);
        }
      },
    });
  });
}
// 获取医院简介
export function getHopDetail(url) {
  return request.get(url);
}

// 用户勇敢微信一键登录获取secctionKey config.resources ? '-' + config.resources: ''
export function getSecctionKey(parms) {
  return request.get(
    `/wechat${config.resources ? '-' + config.resources : ''}/api/mp/validatelogin?code=${parms}`
  );
  // return request.get(`/wechat/api/mp/validatelogin?code=${parms}`)
}

// 通过iv和加密手机号和secctionKey获取token
export function getPhoneToken(data) {
  return request.post(
    `/wechat${config.resources ? '-' + config.resources : ''}/api/mp/login`,
    data
  );
}

// 通过iv和加密手机号和secctionKey获取token
export function test(data) {
  return request.post(`/wechat/api/mp/DecryptPhoneNumber`, data);
}

// 获取当前城市
export function getLocaDetail(prams) {
  return new Promise((resolive, reject) => {
    uni.request({
      header: {
        'Content-Type': 'application/text',
      },
      method: 'GET',
      //注意:这里的key值需要高德地图的 web服务生成的key  只有web服务才有逆地理编码 ee1fbdaeaaba54fad923021d68ff12b2	280802ed0116fef931dbcf5e7e9278d7
      url: `https://restapi.amap.com/v3/geocode/regeo?platform=JS&s=rsv3&logversion=2.0&key=ee1fbdaeaaba54fad923021d68ff12b2&sdkversion=********&appname=https://lbs.amap.com/demo/jsapi-v2/example/geocoder/regeocoding&jscode=44e48de9fc6a5488dc1da13b10f76831&key=ee1fbdaeaaba54fad923021d68ff12b2&s=rsv3&language=zh_cn&location=${prams}&radius=1000`,
      success(res) {
        if (res.data.status == '1') {
          resolive(res.data.regeocode.addressComponent);
        } else {
          console.log('获取信息失败，请重试！');
          reject('获取信息失败，请重试！');
        }
      },
    });
  });
}
// 根据输入的地址解析省市区
export function getSSQByAddress(prams) {
  return new Promise((resolive, reject) => {
    uni.request({
      header: {
        'Content-Type': 'application/text',
      },
      method: 'GET',
      //注意:这里的key值需要高德地图的 web服务生成的key  只有web服务才有逆地理编码 ee1fbdaeaaba54fad923021d68ff12b2	280802ed0116fef931dbcf5e7e9278d7
      url: `https://restapi.amap.com/v3/geocode/geo?platform=JS&s=rsv3&logversion=2.0&key=ee1fbdaeaaba54fad923021d68ff12b2&sdkversion=********&appname=https://lbs.amap.com/demo/jsapi-v2/example/geocoder/regeocoding&jscode=44e48de9fc6a5488dc1da13b10f76831&address=${prams.address}&city=${prams.city}`,
      // url: `https://restapi.amap.com/v3/geocode/geo?key=ee1fbdaeaaba54fad923021d68ff12b2&address=${prams.address}&city=${prams.city}`,
      success(res) {
        resolive(res.data);
      },
    });
  });
}

export function getIdMB(parms) {
  return request.get(
    `/wechat-${config.resources}/api/mp/GetMsgTemplates`,
    parms
  );
}

/**
 * 长链接获取短链接
 */
export function getLoneShortLink(url) {
  return new Promise((resolive, reject) => {
    try {
      wx.request({
        url,
        data: {},
        method: 'GET',
        success: (res) => {
          console.log('res', res);
          if (res.statusCode === 200) {
            resolive(res.data);
          } else if (res.statusCode === 404) {
            resolive({
              Data: null,
              Content: '未找到二维码信息',
            });
          }
        },
        fail: (err) => {
          reject(err);
        },
      });
    } catch (error) {
      console.log('error', error);
    }
  });
}
