import request from '@/libs/request';
const FIX = '/bff';

// 获取菜单
export function getSetting(data) {
  return request.post(`${FIX}/config/getSetting`, data);
}
/** 获取配置信息*/
export function getSettingWithoutAuth(data, authorization) {
  let options;
  if (authorization) {
    options = {
      header: {
        authorization: authorization,
      },
    };
  }
  return request.post(`${FIX}/config/getSettingWithoutAuth`, data, options);
}
// 获取机构参数
export function getlist(data) {
  return request.post(`${FIX}/api/biz/getlist`, data);
}
// 获取医院回收地址
export function getDictStd(par) {
  return request.get(`${FIX}/api/v1/biz/GetByCode`, par);
}
// 联系我们
export function contactUs(prams) {
  return request.get(`${FIX}/biz/GetByCode?code=CustomerService`);
}
// 问诊协议时间
export function agreementTime() {
  return request.get(`${FIX}/biz/GetByCode?code=PatientReadingTime`);
}

// 获取退还设备信息的地址
// export function backDeviceInfo(orgId) {
// 	return request.get(`${FIX}/biz/GetByCode?code=DeviceReturningInfo&orgId=` + orgId);
// }
// 获取医院是否开启治疗师问诊
export function openTherapistConsult(Id) {
  return request.get(`${FIX}/biz/GetByCode?code=TherapistConsult&orgId=${Id}`);
}
// 获取医院是否开启护士问诊
export function openNurseConsult(Id) {
  return request.get(`${FIX}/biz/GetByCode?code=NurseConsult&orgId=${Id}`);
}
/**
 * 二维码长链接获取短链接
 */
export function getShortenUrl(data) {
  return request.post(`${FIX}/api/shortenurl/Generate`, data);
}
/**
 * 获取机构默认配置信息
 */
export function onGetOrgDefault(data) {
  return request.post(`${FIX}/biz/GetByCode`, data);
}
