/**
 * 对首页的部分接口进行聚合处理
 * */
import request from '@/libs/request';

const TRAINING_PATH = '/training';
const CONSULT_PATH = '/consult';

export function getTrainingInfoForIndex(data) {
  return request.post(`${TRAINING_PATH}/aggregate/getInfoForIndex`, data);
}
export function getConsultInfoForIndex(data) {
  return request.post(`${CONSULT_PATH}/aggregate/getInfoForIndex`, data);
}
export function getConsultDocIndex(data) {
  return request.get(`${CONSULT_PATH}/aggregate/getDocsForIndex`, data);
}
