import request from '@/libs/request';
import ApiBusinessResponse from '@/libs/ApiBusinessResponse';
const PRIX = 'message';
// 向医生发送消息（有患者问诊了）
export function userSendMessageToDoc(data) {
  return request.post(`/${PRIX}/api/Push/PushAndSaveToUser`, data);
}

// 获取消息列表
export function getNewsList(data) {
  return request.post(`/${PRIX}/Push/PageQueryPushMessage`, data);
}
// 是否有未读消息
export function getNoReadNews(data) {
  return request.post(`/${PRIX}/Push/SetReadMessageByTypes`, data);
}
/**
 * 获取最近的一条未读通知
 * @param {Array<number>} types 消息类型
 */
export async function getLastMessageInfoFromType(types) {
  const r = await request.post(
    `/${PRIX}/Push/GetLastMessageInfoFromType`,
    types,
    {
      showLoading: false,
    }
  );
  return ApiBusinessResponse.parseResponse(r);
}

/**
 *
 * @param {*} num
 * @returns
 */
export function sendVideoCall(data) {
  return request.post(`/${PRIX}/MessageBroker/push`, data);
}
