import request from '@/libs/request';
export * from './passport.identity';
export * from './passport.user';
export * from './passport.organization';
export * from './passport.department';
export * from './passport.user.department';
export * from './passport.consortium';

const PREFIX = 'passport';

/*
 *	登录
 */
export function accountLogin(data) {
  return request.post(`${PREFIX}/token/generate`, data);
}

// 获取用户信息
export function getUserInfo() {
  return request.get(`${PREFIX}/connect/userInfo`);
}

// -------------- OrganizationConsortium --------------
const organizationConsortiumPath = PREFIX + '/OrganizationConsortium';

// 通过医联体 id 获取所有医院
export function OrganizationConsortiumGet(data) {
  return request.post(`${organizationConsortiumPath}/Get`, data);
}

// -------------- UserOrganization --------------
const UserOrganizationPath = PREFIX + '/UserOrganization';
// 选择医院之后修改用户选择的医院
export function updateMark(data) {
  return request.post(`${UserOrganizationPath}/UpdateMark`, data);
}

// -------------- IM --------------
const imPath = `${PREFIX}/im`;

/**
 * 获取IM群聊中成员关系信息
 *
 * @param {String} roomId 房间号
 * @param {String} password 房间密码 可选值
 */
export async function getIMRoomRelationshipInfo(roomId, password = '') {
  let path = `${imPath}/GetRoom`;
  let params = {
    roomId: roomId,
    pass: password,
  };
  let r = await request.get(path, params);
  let result = new Promise((resolve, reject) => {
    if (r.Type == 200) {
      var map = {};
      let members = r.Data.Members;
      if (Array.isArray(members)) {
        members.forEach((item, index) => {
          map[item.UserGuid] = item.Title;
        });
        let data = {
          Type: 200,
          Data: map,
        };
        resolve(data);
      }
    }

    // 获取失败
    let data = {
      Type: r.Type,
      Content: r.Content,
    };
    resolve(data);
  });

  return result;
}
