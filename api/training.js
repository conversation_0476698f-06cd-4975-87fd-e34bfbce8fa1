import request from '@/libs/request';
const service = '/training';

// -------------------------- schemePath --------------------------
const schemePath = service + '/scheme';

/**
 * 获取今日训练方案
 *
 * @param {String} userId 用户id
 */
export function getTodayTrainingProgram(userId) {
  let path = `${schemePath}/GetTodayTrainingProgram`;
  let params = {
    userId: userId,
  };
  let r = request.get(path, params);
  return r;
}

/**
 * 根据方案id获取训练详情
 *
 * @param {String} trainingId 训练方案id
 */
export function getTrainingProgramInfoById(trainingId) {
  let path = `${schemePath}/GetTrainingProgramInfoById`;
  let params = {
    id: trainingId,
  };
  let r = request.get(path, params);
  return r;
}
export const getPatientInfoByProgramId = (data) => {
  let path = `${schemePath}/GetPatientInfoByProgramId`;
  let r = request.get(path, data);
  return r;
};

/**
 * 开启指定训练方案
 *
 * @param {String} userId 用户id
 * @param {String} trainingId 训练方案id
 * */
export function startTrainingProgram(userId, trainingProgramId) {
  let path = `${schemePath}/StartTrainingProgram`;
  let params = {
    id: trainingProgramId,
    userId: userId,
  };
  return request.get(path, params);
}

/**
 * 检查训练方案是否有效
 *
 * @param {String} trainingProgramId 训练方案id
 */
export function checkTrainingProgramValidState(trainingProgramId) {
  let path = `${schemePath}/GetTrainingProgramValidState`;
  let params = {
    programId: trainingProgramId,
  };
  let r = request.get(path, params);
  return r;
}

/**
 * 获取所有训练方案列表
 *
 * @param {String} patientId 患者id
 * */
export function getAllTrainingPlanList(patientId, pageIndex, pageSize = 10) {
  let path = `${schemePath}/GetMyselfTrainingPrograms`;
  let params = {
    userId: patientId,
    pageIndex: pageIndex,
    pageSize: pageSize,
  };
  let r = request.get(path, params);
  return r;
}

/**
 * 获取治疗性训练动作当天打卡状态
 *
 * @param {String} trainingProgramId 训练方案id
 * @param {String} actionIds 方案下的动作id
 * */
export function getTodayActionExecute(trainingProgramId, actionIds) {
  let path = `${schemePath}/GetTodayActionExecute`;
  let data = {
    TrainingProgramId: trainingProgramId,
    TrainingActions: actionIds,
  };
  let r = request.post(path, data);
  return r;
}

/**
 * 治疗性训练动作打卡
 *
 * @param {Array} punchCard 打卡数据
 */
export function homeTrainingActionPunchCard(punchCard) {
  let path = `${schemePath}/ClockIn`;
  if (Array.isArray(punchCard)) {
    let r = request.post(path, punchCard);
    return r;
  }
  return {
    Type: 0,
    Content: '参数结构错误',
  };
}

/**
 * 获取指定时间段内治疗性动作打卡记录
 *
 * @param {String} trainingProgramId 训练计划方案id
 * @param {String} actionId 训练动作id
 * @param {String} userId 用户id
 */
export function getTrainingActionRecords(trainingProgramId, actionId, userId) {
  let path = `${schemePath}/GetTrainingActionRecords`;
  let params = {
    trainingProgramId: trainingProgramId,
    trainingActionId: actionId,
    userId: userId,
  };
  let r = request.get(path, params);
  return r;
}

/**
 * 检查动作有没有绑定相对应的设备
 *
 * @param {String} trainPatientId 患者id
 * @param {String} deviceId 设备id
 * */
export function checkTrainingIsBindingDevice(trainPatientId, deviceId) {
  let path = `${schemePath}/CheckTrainingIsBindingDevice`;
  let params = {
    memberId: trainPatientId,
    deviceTypeId: deviceId,
  };
  return request.get(path, params);
}

/**
 * 检查是否可以绑定设备
 *
 * @param {Array} devices  需要绑定的设备列表
 * true - 可以继续
 * false - 绑定了同类型的训练设备
 */
export function checkBindingUserDevices(devices) {
  let path = `${schemePath}/CheckBindingUserDevice`;
  return request.post(path, devices);
}

/**
 * 绑定训练设备
 *
 * @param {Array} devices 需要绑定的设备列表
 */
export function bindingUserDevices(devices) {
  let path = `${schemePath}/BindingUserDevice`;
  return request.post(path, devices);
}

// 获取患者的设备
export function getUserDevicesList(parms) {
  let path = `${schemePath}/GetBindingUserDevice`;
  return request.get(path, parms);
}

// 解绑设备
export function unbindDevice(data) {
  let path = `${schemePath}/UnbundlingUserDevice`;
  return request.post(path, data);
}

/**
 * 创建居家训练指导群
 *
 * @param {String} trainingPlanId 训练方案id
 */
export function createTrainingChatRoom(trainingPlanId) {
  let path = `${schemePath}/CreateRoom`;
  let params = {
    programId: trainingPlanId,
  };
  return request.get(path, params);
}

/**
 * 检查续方的处方是否能执行续方
 *
 * @param {Object} params
 */
export function checkApplyContinuePreState(params) {
  let path = `${schemePath}/CheckApplyContinuePreState`;
  return request.get(path, params);
}

/**
 * 检查续方的处方是否能执行续方
 *
 * @param {Object} data
 */
export function applyContinueRx(data) {
  let path = `${schemePath}/ApplyContinueRx`;
  return request.post(path, data);
}

/**
 * 居家训练一键打卡
 *
 * @param {String} trainingPlanId 训练方案id
 * @param {String} extend 扩展字段，比如传动作id，则表示一键打卡该方案该动作今天的所有次数
 */
export function actionsOneClickIn(trainingPlanId, extend = null) {
  let path = `${schemePath}/OneClickIn`;
  var params = {
    Id: trainingPlanId,
  };
  if (extend) {
    params['Extend'] = extend;
  }
  return request.post(path, params);
}

/**
 * 获取每日任务
 */
export function getTrainingExecByUserId(params) {
  let path = `${schemePath}/GetTrainingExecByUserId`;
  return request.get(path, params);
}

/**
 * 获取用户设备的使用情况
 */
export function statisticsReport(params) {
  let path = `${schemePath}/StatisticsReport`;
  return request.get(path, params);
}

// -------------------------- gaugePath --------------------------
const gaugePath = service + '/gauge';

/**
 * 获取量表执行记录
 *
 * @param {String} gaugeSign 某量表标识
 */
export function getDoctorSendGaugeByGaugeSign(gaugeSign) {
  let path = `${gaugePath}/GetDctSendGaugeByPatSign`;
  let params = {
    gaugeSign: gaugeSign,
  };
  return request.get(path, params);
}

/**
 * 根据id获取量表基础数据(未填写的)
 *
 * @param {String} gaugeId 量表id
 */
export function getGaugeById(gaugeId) {
  let path = `${gaugePath}/GetGaugeById`;
  let params = {
    id: gaugeId,
  };
  return request.get(path, params);
}

/**
 * 根据id获取量表业务数据(已填写的)
 *
 * @param {String} gaugeId 量表id
 */
export function dctGetPatGaugeById(gaugeId) {
  let path = `${gaugePath}/DctGetPatGaugeById`;
  let params = {
    patGaugeId: gaugeId,
  };
  return request.get(path, params);
}

/**
 * 获取评估方案的量表列表
 *
 * @param {Object} sign 医生发送评估方案的标识
 */
export function getEvaList(sign) {
  let path = `${gaugePath}/GetPatGaugeSchemeByDctSendSign`;
  let params = {
    sign: sign,
  };
  return request.get(path, params);
}

/**
 * 获取评估方案的量表结果
 *
 * @param {Object} sign 医生发送评估方案的标识
 */
export function getEvaListReq(sign) {
  let path = `${gaugePath}/GetGaugeSchemeReportBySign`;
  let params = {
    sign: sign,
  };
  return request.get(path, params);
}

/**
 * 获取患者报告
 *
 * @param {Object} params
 */
export function GetDctSendGaugeScheme(params) {
  let path = `${gaugePath}/GetDctSendGaugeScheme`;
  return request.get(path, params);
}
export function GetEvaTrainingInitData(params) {
  return request.get(`${service}/api/scheme/GetEvaTrainingInitData`, params);
}

//支付完成获取计划id
export function payOverGetPlatId(id) {
  return request.get(`${service}/api/v1/scheme/GetProgramIdByVisitId`, id);
}
// 查看患者是否有续方
export function getUserNeedContinuedPrescription(parms) {
  return request.get(`${service}/Scheme/GetIsContinue`, parms);
}
