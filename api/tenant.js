import request from '@/libs/request';
const FIX = '/tenant';

// 获取线下门诊信息
export function getOfflineOutpatientService(data) {
  return request.post(`${FIX}/hbszyy/api/Hbszyy/GetVisitRecord`, data);
}
// 获取河北省中医院的患者诊疗信息的“处置”接口
export function getHBSZYYDisposal(data) {
  return request.post(`/tenant/hbszyy/api/Hbszyy/GetPrescList`, data);
}
// -----------------------------------------------------------------------------------------
const TenantHxunion_Gauge = '/tenant-hxunion/api/gauge';
export function getRiskWarningGaugePage(data) {
  return request.post(`${TenantHxunion_Gauge}/GetPage`, data);
}
export function getRiskWarningGaugeDetailById(params) {
  return request.get(`${TenantHxunion_Gauge}/GetGaugeDetailById`, params);
}
export function riskWarningWriteGauge(data) {
  return request.post(`${TenantHxunion_Gauge}/WriteGauge`, data);
}
export function getRiskWarningWriteReport(data) {
  return request.post(`${TenantHxunion_Gauge}/GetReport`, data);
}
export function getRecommendById(data) {
  return request.post(`${TenantHxunion_Gauge}/GetRecommend`, data);
}
export function getPatGauge(data) {
  return request.post(`${TenantHxunion_Gauge}/GetPatGauge`, data);
}
export function getPatGaugeById(data) {
  return request.post(`${TenantHxunion_Gauge}/GetPatGaugeById`, data);
}
export function getSignInfo(data) {
  return request.post(`${TenantHxunion_Gauge}/GetSignInfo`, data);
}
// -----------------------------------------------------------------------------------------
const TenantHxunion_Scheduler = '/tenant-hxunion/api/Scheduler';
export function getCurrentScheduleDoctor(data) {
  return request.post(`${TenantHxunion_Scheduler}/GetCurrentScheduleDoctor`, data);
}
