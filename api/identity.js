import request from '@/libs/request';

const service = '/identity';

// 获取平台下面的养老院的数据
export function GetList(par) {
  return request.get(`${service}/api/v1/Inviter/GetList`, par);
}

// 获取视频聊天的签名
export function getTXGetSign(parms) {
  return request.get(`${service}/api/TencentVideo/GetSign`, parms);
}

// 患者实名认证之后 自动审核通过
export function automaticApproval(data) {
  return request.post(`${service}/api/User/AutoAuditByUserId`, data);
}
