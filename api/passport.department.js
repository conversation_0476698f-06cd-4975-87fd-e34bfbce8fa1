import request from '@/libs/request';

const Service = 'passport';
const DepartmentPath = Service + '/department';

/**
 * @typedef {Object} DepartmentListParams
 * @property {boolean} Pageable - 返回结果是否分页
 * @property {string} DtoTypeName - 返回结果类型 DepartmentOutputDto1
 * @property {string} Keyword - 搜索关键词
 * @property {boolean} IsEnabled - 是否启用
 * @property {string[]} Ids - 科室Id
 * @property {string} OrgId - 机构Id
 * @property {string[]} DeptTypes - 科室类别
 * @property {boolean} IsLocked - 是否锁定
 */

/**
 * 获取某个医院的科室
 * @param {DepartmentListParams} data
 * @returns {Promise}
 */
export function queryDepartments(data) {
  return request.post(`${DepartmentPath}/GetList`, data);
}
