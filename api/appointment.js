import request from '@/libs/request';
const FIX = '/appointment';

// 获取学校列表
export function QuerySchoolPage(data) {
  return request.post(`${FIX}/api/School/QuerySchoolPage`, data);
}
// 获取排班日期
export function GetSchedulingPage(data) {
  return request.post(`${FIX}/api/Scheduling/GetSchedulingPage`, data);
}
// 获取时间段
export function QueryTimeRangePage(data) {
  return request.post(`${FIX}/api/TimeRange/QueryTimeRangePage`, data);
}
// 预约视光
export function SaveRecord(data) {
  return request.post(`${FIX}/api/Record/SaveRecord`, data);
}
// 获取预约记录
export function GetRecords(data) {
  return request.post(`${FIX}/api/Record/GetRecords`, data);
}
// 取消预约
export function CancelRecord(data) {
  return request.put(`${FIX}/api/Record/CancelRecord`, data);
}
// 判断该用户是否有未签到的数据
export function GetUserNotSignCount(data) {
  return request.get(`${FIX}/api/Record/GetUserNotSignCount`, data);
}
