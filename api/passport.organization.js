import request from '@/libs/request';

const Service = 'passport';
const OrganizationPath = Service + '/organization';

/**
 * @typedef {Object} OrganizationListParams
 * @property {boolean} Pageable - 返回结果是否分页
 * @property {string} DtoTypeName - 返回结果类型 QueryOrgListOutputDto4
 * @property {string} Keyword - 搜索关键词
 * @property {boolean} IsEnabled - 是否启用
 */

/**
 * 获取某个医院的科室
 * @param {OrganizationListParams} data
 * @returns {Promise}
 */
export function queryOrganizations(data) {
  return request.post(`${OrganizationPath}/GetList`, {
    Keyword: '',
    Pageable: false,
    IsEnabled: true,
    SingleOne: false,
    DtoTypeName: 'QueryOrgListOutputDto4',
    ...data,
  });
}

/**
 * 通过机构id去查询
 * @param {string} id
 */
export function getOrganizationById(id) {
  return request.get(`${OrganizationPath}/GetOrganizationById?id=${id}`);
}

/**
 * 获取机构列表 医联体
 * @param {Object} params
 */
export function getOrgListInfo1(params) {
  return request.get(
    `${OrganizationPath}/GetUserConsortiumOrganizations`,
    params
  );
}
/**
 * 获取机构列表
 * @param {Object} parms
 */
export function getUserOrganizations(parms) {
  return request.get(`${OrganizationPath}/GetUserOrganizations`, parms);
}

/**
 * 获取机构的主营项目
 */
export function getOrganizationMainDeals(data) {
  return request.post(`${OrganizationPath}/GetOrganizationMainDeals`, data);
}
