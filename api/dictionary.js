import request from '@/libs/request';
import config from '@/config';

const service = '/dictionary';
const dictItemPath = `${service}/api/dictItem`;

/**
 * 根据ID获取字典数据
 *
 * id - 字典ID
 * */
export function getDictItemById(id) {
  const path = `${dictItemPath}/GetDictItemById`;
  let params = {
    id: id,
  };
  return request.get(path, params);
}
// 获取平台的词条类型
export function getCILeftList(data) {
  return request.post(`${service}/api/Dict/ReadStd`, data);
}
// 获取平台的词条信息
export function getCIList(data) {
  return request.post(`${service}/api/DictItem/Read`, data);
}
/**
 * 通过省市区中文获取对应的code
 * @param {Object} parms
 */
export function getRegionCode(parms) {
  return request.get(`${service}/api/v1/Region/GetCode`, parms);
}
// 获取社区城市信息
export function GetAllCities() {
  return request.get(`${service}/api/v1/region/GetAllCities`);
}
// 获取问诊评价词条
export function ReadDict(data) {
  return request.post(`${service}/api/DictItem/Read`, data);
}
// 获取问诊评价词条重构
export function DictItemReadStd(data) {
  return request.post(`${service}/api/DictItem/ReadStd`, data);
}
// 获取问诊评价词条新
export function newReadDict(data) {
  return request.post(`${service}/api/v1/DictItem/GetDictItems`, data);
}
// 获取症候报到的code
export function GetDiseaseList(data) {
  return request.post(`${service}/api/Dict/ReadStd`, data);
}
// 用户反馈类型
export function userType() {
  return request.get(
    `${service}/api/dict/GetDict?code=PatientFeedbackTypeDict`
  );
}
/** 读取字典表数据 */
export function getDict(params) {
  return request.get(`${service}/api/Dict/GetDict`, params);
}
// 获取城市三级菜单
export function getAddressList(data) {
  return request.post(`${service}/api/v1/DictItem/Read`, data);
}
