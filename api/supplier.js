import request from '@/libs/request';

// ------------------- 易脑 -------------------
const serveE = '/supplier/ebrain';

// 获取脑动激光报告

// 获取易脑Vr的图表数据
export function GetReportByUserId(data) {
  return request.get(`${serveE}/api/Info/GetReportByUserId`, data);
}

// ------------------- 脊椎 -------------------
const serveI = '/supplier/Ikidcare';

/**
 * 获取脊椎报告
 *
 * @param {Object} data
 * @param {string} data.Phone 手机号
 * @param {string} data.Name 姓名
 */
export function getReportPage(data) {
  return request.post(`${serveI}/api/Info/GetReportPage`, data);
}

/**
 * 查询Cobb角列表
 *
 * @param {Object} data
 * @param {string} data.UserId 用户ID
 * @param {string} data.StartDate 开始时间
 * @param {string} data.EndDate 结束时间
 * @param {number} data.PageIndex 页码
 * @param {number} data.PageSize 每页条数
 */
export function getCobbList(data) {
  return request.post(`${serveI}/api/Info/QueryCobb`, data);
}

/**
 * 创建Cobb角
 *
 * @param {Object} data
 * @param {string} data.UserId 患者ID
 * @param {number} data.Score 分数
 * @param {string} data.CreatorId 创建人
 */
export function createCobb(data) {
  return request.post(`${serveI}/api/Info/CreateCobb`, data);
}

// ------------------- 足底压力测试 -------------------
const serveFoot = '/supplier/footscreen';

/**
 * 获取足底压力测试报告
 *
 * @param {Object} data
 * @param {string} data.Phone 手机号
 * @param {string} data.Name 姓名
 * @param {string} data.StartTime 开始时间
 * @param {string} data.EndTime 结束时间
 * @param {boolean} data.IsLatest 是否获取最新报告
 */
export function getFootReport(data) {
  return request.post(`${serveFoot}/api/Info/GetReport`, data);
}

// ------------------- 赛客呼吸 -------------------
const serveX = '/supplier/xeek';

// 获取易脑呼吸报告时间列表
export function GetXeekReportByUserId(data) {
  return request.post(`${serveX}/api/Info/GetReportByUserId`, data);
}

// 获取易脑呼吸报告详情
export function GetReportDetailById(data) {
  return request.get(`${serveX}/api/Info/GetReportDetailById`, data);
}

// 获取肺功能报告
export function GetPulmonaryReportByUserId(data) {
  return request.post(`${serveX}/api/Info/GetPulmonaryReportByUserId`, data);
}

/**
 * 获取肺功能报告关键指标
 */
export function getReportLineChartByType(data) {
  return request.post(`${serveX}/api/Info/GetReportLineChartByType`, data);
}

export const getReportLineChartByReportId = (data) => {
  return request.post(`${serveX}/api/Info/GetReportLineChartByReportId`, data);
};

// ------------------- 视光 -------------------
const serveQ = '/supplier/qingpai';

// 获取视光筛查数据列表
export function QPGetReportPage(data) {
  return request.get(`${serveQ}/api/Info/GetReport`, data);
}

// 获取视光筛查详情
export function QPGetReportById(data) {
  return request.get(`${serveQ}/api/Info/GetReportById`, data);
}

/**
 * 确认视光报告查看
 */
export function SetCheckReportUser(data) {
  return request.post(`${serveQ}/api/v1/Info/SetCheckReportUser`, data);
}

// ------------------- 睛采 -------------------
const serveJC = '/supplier/JCaiTech';

/**
 * 获取睛采列表数据
 */
export function queryJCaiTechReport(data) {
  return request.post(`${serveJC}/api/v1/info/GetReport`, data);
}

/**
 * 是否需要上传睛采
 */
export function getReportActionMapping(params) {
  return request.get(`${serveJC}/api/v1/info/GetReportActionMapping`, params);
}

// ------------------- supplier-common -------------------
const SupplierCommon = '/supplier-common';

/**
 * 绑定华为设备
 * @param {Object} params
 * @param {string} params.RedirectUri
 * @param {string} params.Code
 */
export function bindHWDevice(params) {
  return request.post(`${SupplierCommon}/api/v1/info/Callback`, params);
}

/**
 * 查询用户是否绑定设备
 * @param {Object} params
 * @param {string} params.userId
 */
export function getHWRefreshToken(params) {
  return request.get(`${SupplierCommon}/api/v1/info/GetRefreshToken`, params);
}

/**
 * 取消授权
 * @param {string} UserId
 */
export function cancelAuthorize(userId) {
  return request.post(`${SupplierCommon}/info/CancelAuthorize`, {
    UserId: userId,
  });
}

/**
 * 查询设备采样数据集
 *
 * @param {Object} params
 * @param {string} params.UserId
 * @param {string} params.StartDate
 * @param {string} params.EndDate
 * @param {1|2} params.Type 1 为明细， 2为统计
 * @param {('com.huawei.instantaneous.heart_rate'|'com.huawei.instantaneous.resting_heart_rate'|'com.huawei.instantaneous.exercise_heart_rate'|'com.huawei.instantaneous.blood_pressure')[]} params.DataTypeName
 *
 * - com.huawei.instantaneous.heart_rate 心率
 * - com.huawei.instantaneous.resting_heart_rate 静息心率
 * - com.huawei.instantaneous.exercise_heart_rate 动态心率
 * - com.huawei.instantaneous.blood_pressure 血压
 */
export function getSampleSet(params) {
  return request.post(`${SupplierCommon}/api/v1/info/SampleSet`, params);
}

// ------------------- 脑动激光 -------------------
const serveN = '/supplier/sixsixnao';
export const checkExitReport = (params) => {
  return request.post(`${serveN}/api/v1/info/CheckExitReport`, params);
};
/**
 * 获取报告
 */
export const getGaugeReport = (params) => {
  return request.post(`${serveN}/api/v1/info/GetGaugeReport`, params);
};
export const checkSyncUser = (params) => {
  return request.get(`${serveN}/api/v1/info/CheckSyncUser`, params);
};
