import request from '@/libs/request';
const FIX = '/order';
// 查询物流轨迹
export function expressQuery(data) {
  return request.post(`${FIX}/api/express/query`, data);
}
// 通过快递单号获取快递公司
export function GetExpressCompany(nums) {
  return request.get(`${FIX}/api/express/GetExpressCompany?nums=${nums}`);
}

// 获取微信支付信息
export function getWxPayInfo(data) {
  return request.post(`${FIX}/consultorder/payconsultorder`, data);
}
// 支付金额为0元的处方订单
export function payNoNeedMoeny(data) {
  return request.post(`${FIX}/consultorder/BackendPayOrder`, data);
}
// 获取订单是否需要钱和是否需要发货
export function getOrdersByOrderNo(data) {
  return request.get(`${FIX}/order/getordersbyorderno`, data);
}
// 患者支付完成之后将单子的状态设置为支付中
export function setOrderPaying(data) {
  return request.post(`${FIX}/api/Order/SetOrderPaying`, data);
}

// 获取支付类型
export function getPayType() {
  return request.get(`${FIX}/order/getpayments?company=yankang`);
}
// 获取处方的用户地址信息
export function getUserAddrInfo(data) {
  return request.post(`${FIX}/useraddress/read`, data);
}
// 取消治疗订单
export function cancelOrReceivedOrder(type, data) {
  return request.post(`${FIX}/order/${type}`, data);
}

//删除患者地址
export function delUserAddress(parms) {
  return request.get(`${FIX}/useraddress/delete`, parms);
}
// 添加患者收货地址或者修改
export function addAddress(type, data) {
  return request.post(`${FIX}/useraddress/${type}`, data);
}
/**
 * 修改治疗订单的收货地址
 */
export function setOrderAddress(data) {
  return request.post(`${FIX}/order/SetOrderAddress`, data);
}
