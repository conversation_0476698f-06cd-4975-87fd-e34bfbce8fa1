import request from '@/libs/request';

const Service = 'passport';
const UserPath = Service + '/user';

/** 判断该患者是否报到过该医生 */
export function getUserClaims(data) {
  return request.post(`${UserPath}/GetUserClaims`, data);
}
export function SetUserClaims(data) {
  return request.post(`${UserPath}/SetUserClaims`, data);
}
export function delUserClaims(data) {
  return request.post(`${UserPath}/DelUserClaims`, data);
}
/**
 * 关注某个医生或者取消关注
 *
 * @param {setfollow | cancelfollow} type
 * @param {Object} data
 * @param {string} data.UserId
 * @param {string} data.FollowUserId
 * @return {Promise<ApiBusinessResponse>}
 */
export function setOrCanDoc(type, data) {
  const claims = [{
    UserId: data.UserId,
    ClaimValue: data.FollowUserId,
    ClaimType: 'Follow',
  }, ];
  if (type === 'setfollow') {
    return SetUserClaims(claims);
  } else if (type === 'cancelfollow') {
    return delUserClaims(claims);
  } else {
    return ApiBusinessResponse.failure('不支持的 type');
  }
}
/**
 * 获取用户列表
 */
export function getUserProfile(data) {
  return request.post(`${UserPath}/GetUserProfile`, data);
}

/**
 * 获取用户信息
 *
 * @param {string[]} ids 用户ID列表
 */
export function getUsers(ids) {
  return request.post(`${UserPath}/GetUserProfile`, {
    Ids: ids,
    SingleOne: false,
    DtoTypeName: 'UserOutputDto4',
  });
}
/**
 * 检查手机号是否注册过
 * @param {string} phonenumber 手机号
 * @returns {Promise}
 */
export const checkPhoneNumberExists = (phonenumber) => {
  return request.get(`${UserPath}/CheckPhoneNumberExists`, {
    phonenumber,
  });
};
/** 用户修信息*/
export function userUpdateInfo(data) {
  return request.post(`${UserPath}/EditSingleUserInfo`, data);
}
// 患者实名认证
export function saveUserAuthenticationAudit(data) {
  return request.post(`${UserPath}/SaveUserAuthenticationAudit`, data);
}
// 查询该认证的身份证号是否被别人认证过
export function getUserByIdCard(parms) {
  return request.get(`${UserPath}/GetUserByIdCard`, parms);
}
// 获取医生获取职业资格证书接口
export function getDoctorOrganizationAuthentications(par) {
  return request.get(`${UserPath}/GetDoctorOrganizationAuthentications`, par);
}
// 将扫码的用户上传给后台
export function getrScanQRUser(data) {
  return request.post(`${UserPath}/UpdateUserScanQR`, data);
}
// 绑定其他来源的患者
export function BindUserScanQR(data) {
  return request.post(`${UserPath}/BindUserScanQR`, data);
}
// 获取患者来源
export function GetUserScanLog(par) {
  return request.get(`${UserPath}/GetUserScanLog`, par);
}

// 患者未认证的时候 修改这个人的信息
export function updatePart(data) {
  return request.post(`${UserPath}/UpdatePart`, data);
}

// 查询患者是否有认证
export function getLatestAuth(data) {
  return request.post(`${UserPath}/GetLatestAuth`, data);
}
/**
 * 被邀请人扫码或点击小程序卡片，记录与推荐人关系。
 */
export function setUserScanLog(data) {
  return request.post(`${UserPath}/SetUserScanLog`, data);
}
