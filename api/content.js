import request from '@/libs/request';

const service = '/content';

// -------------------------- instrument --------------------------

const instrumentPath = service + '/instrument';

/**
 * 根据ID获取训练设备类型
 *
 * @param {Array} deviceIds 训练设备ID列表
 * */
export async function getDevicesByIds(deviceIds) {
  let path = `${instrumentPath}/getInstrumentByIds`;
  let r = request.post(path, deviceIds, false);
  return r;
}

// -------------------------- phrase --------------------------

const phrasePath = service + '/Phrase';

/**
 * 获取常用语列表
 *
 * @param {Int} type 0:患者端、1:医生端-在线问诊、2:医生端-诊后管理、3:医生端-咨询服务(治疗师)
 * @param {Int} orgId 机构id(null获取平台维护数据，非null获取机构维护数据)
 * @param {String} userId 用户id(null获取机构/平台维护数据，非null获取机构/平台下该用户维护数据)
 * */
export async function findPhrases(type, orgId = null, userId = null) {
  let path = `${phrasePath}/Find`;
  var params = {
    Class: type,
    Enable: true,
  };
  if (orgId) {
    params.OrganizationId = orgId;
  }
  if (userId) {
    params.UserId = userId;
  }
  let r = request.post(path, params);
  return r;
}
// 获取该机构下是否有康复咨询这个医嘱
export function getHaveConsult(parmas) {
  return request.get(`${service}/api/v1/MoItem/GetHaveConsultServer`, parmas);
}
// 获取脊柱筛查宣教推荐分类
export function querySuggestLevel(parmas) {
  return request.get(
    `${service}/api/v1/RecoveryMission/querySuggestLevel`,
    parmas
  );
}
// 获取脊柱筛查宣教标题分类
export function querySuggestType(parmas) {
  return request.get(
    `${service}/api/v1/RecoveryMission/querySuggestType`,
    parmas
  );
}
// 获取脊柱筛查宣教
export function querySuggestInfo(parmas) {
  return request.get(
    `${service}/api/v1/RecoveryMission/querySuggestInfo`,
    parmas
  );
}
// 获取宣教信息列表
export function contentByType(data) {
  return request.post(
    `${service}/recoveryMission/PageQueryAllContentByType`,
    data
  );
}
// 获取小程序的用户协议和隐私政策
export function getMinAppInfo(num) {
  return request.get(
    `${service}/Protocol/GetProtocolByReleasePosition?protocolType=${num}&releasePosition=12`
  );
}

// 获取分类
export function getClassification(orgId) {
  return request.get(
    `${service}/recoveryMission/GetAllEnableRecoveryMissionTypes?isShowOperation=false&isShowDisease=true&isShowRegime=false&organizationId=${orgId}`
  );
}
// 获取宣教详情
export function GetRecoveryMissionById(parms) {
  return request.get(
    `${service}/recoveryMission/GetRecoveryMissionById`,
    parms
  );
}
/**
 * 获取医嘱对应的治疗点
 */
export function getMoItemTreat(data) {
  return request.post(`${service}/api/MoItem/GetMoItemTreat`, data);
}
