import request from '@/libs/request';
const serve = '/satisfactory';
// 获取医生的评价
export function getDocEvaluation(data) {
  return request.post(`${serve}/UserEvaluate/GetEva`, data);
}
// 获取医生平均分
export function getDocAvgStart(parmas) {
  return request.get(`${serve}/userevaluate/GetDctAverageById`, parmas);
}

// 提交评价
export function SendEvaluation(data) {
  return request.post(`${serve}/UserEvaluate/CreateSatisfactionSurvey`, data);
}
// 获取问诊满意度的id
export function GetOrgEvaContentByOrgIds(data) {
  return request.post(`${serve}/TypeManage/GetOrgEvaContentByOrgIds`, data);
}
// 获取评价标签和好评类别
export function GetEvaluationTags(parmas) {
  return request.get(`${serve}/UserEvaluate/GetTagAndScoreByDctId`, parmas);
}

// 提交用户反馈
export function createFeedback(data) {
  return request.post(`${serve}/User/CreateFeedback`, data);
}
