import request from '@/libs/request';
const fix = '/record/sign';
const FIX = '/record';
// 体征的数据录入
export function dataEntry(data) {
  return request.post(`${FIX}/sign/Add`, data);
}
// 获取体征的的列表
export function getDataEntryListByUserId(parmas) {
  return request.get(`${FIX}/sign/Get`, parmas);
}
// 获取最近的体征记录
export function getRecentRecord(parmas) {
  return request.get(`${fix}/GetMyLastSigns`, parmas);
}
// 获取 疼痛/生活质量/焦虑抑郁   列表数据
export function getSigns(data) {
  return request.post(`${fix}/GetSigns`, data);
}
// 存储患者诊后报到的标签
export function SaveTags(data) {
  return request.post(`${FIX}/api/v1/Record/SetUserTags`, data);
}
// 获取患者最近的疾病标签
export function GetTags(data) {
  return request.post(`${FIX}/api/record/GetUserTagsExtend`, data);
}
// 保存个人健康档案
export function saveIllnessRecord(data) {
  return request.post(`${FIX}/api/record/SaveIllnessRecord`, data);
}
// 获取病历详情
export function getVisit(parmas) {
  return request.get(`${FIX}/api/v1/Visit/GetVisit`, parmas);
}
// 存储本次的问诊详细
export function setUserCase(data) {
  return request.post(`${FIX}/api/visit/SaveVisit`, data);
}

// 获取患者信息和病历
export function getUserCaseInfo(data) {
  return request.post(`${FIX}/api/visit/GetVisitsByUserId`, data);
}
//删除自建病历
export function delMedical(id) {
  return request.post(`${FIX}/api/visit/DeleteVisit?id=${id}`);
}

// 获取健康档案的信息id
export function getUserArchivesInfo(data) {
  return request.post(`${FIX}/api/info/Read`, data);
}
// 修改健康档案
export function updateArchives(type, data) {
  return request.post(`${FIX}/api/info/${type}`, data);
}
// 获取用户部分信息
export function getUserInfoArch(parms) {
  return request.get(`${FIX}/api/info/GetInfo`, parms);
}
/**
 * 设置用户信息
 */
export function setUserInfo(data) {
  return request.post(`${FIX}/api/info/SetUserInfo`, data);
}
