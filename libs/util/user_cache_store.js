import { getUsers } from '@/api/passport';
import { arrayNotEmpty } from '@/utils/utils.js';
import { Completer } from './Completer';

export default class UserCacheStore {
  static _instance;
  static instance() {
    if (!this._instance) {
      this._instance = new UserCacheStore();

      setInterval(() => {
        this._instance._schedulingCallback();
      }, 1000);
    }
    return this._instance;
  }

  // 已经存储过的用户，格式：key:userId  value:userEntity
  userMap = {};

  // 等请求数据的用户，格式：key:userId  value:Completer
  toBeLoadedUserMap = {};

  // 当前正在进行的异步请求
  currentCompleter = new Completer();

  // 每秒执行定时器任务
  async _schedulingCallback() {
    let lastCompleter = this.currentCompleter;
    this.currentCompleter = new Completer();

    let toBeLoadedUserIds = Object.keys(this.toBeLoadedUserMap);
    var needToRequestUserIds = [];
    for (let key in this.toBeLoadedUserMap) {
      let value = this.toBeLoadedUserMap[key];
      if (value == lastCompleter) {
        needToRequestUserIds.push(key);
      }
    }
    let userIds = needToRequestUserIds;
    if (!arrayNotEmpty(userIds)) {
      lastCompleter.complete();
      return;
    }

    // 请求用户组信息
    // console.debug(
    // 	`_schedulingCallback-待请求用户toBeLoadedUserIds: ${toBeLoadedUserIds},\n 筛选出的用于请求UserIds: ${userIds}`);
    let r = await this._requestUsersByUserIds(userIds);

    // 已请求过的用户移除队列
    userIds.forEach((userId) => {
      if (toBeLoadedUserIds.indexOf(userId) >= 0) {
        delete this.toBeLoadedUserMap[userId];
      }
    });

    // 此处有用户id，但请求的数据为空，判定为错误
    if (r == null || !arrayNotEmpty(r)) {
      console.error(`获取用户数据错误：${r}`);
      lastCompleter.complete();
      return;
    }

    // 请求完成
    // console.debug(
    // 	`_schedulingCallback-当前已缓存的UserIds：${Object.keys(this.userMap)} \n toBeLoadedUserIds：${Object.keys(this.toBeLoadedUserMap)}`
    // );
    lastCompleter.complete();
  }

  hasCachedUser(userId) {
    return this._cachedUser(userId);
  }

  // 获取用户
  getUser(userId) {
    if (this._cachedUser(userId)) {
      let user = JSON.parse(this.userMap[userId]);
      return user;
    }

    this.loadUserByUserIds([userId]);
    return null;
  }

  // 获取用户
  async asyncGetUser(userId) {
    if (this._cachedUser(userId)) {
      let user = JSON.parse(this.userMap[userId]);
      return user;
    }
    await this.loadUserByUserIds([userId]);
    if (this._cachedUser(userId)) {
      let user = JSON.parse(this.userMap[userId]);
      return user;
    }
    return null;
  }

  /**
   * 已经缓存过该用户
   * @param {string} userId
   * @returns {boolean}
   */
  _cachedUser(userId) {
    return Object.keys(this.userMap).indexOf(userId) >= 0;
  }

  // 已经加入待请求队列中
  _isPendingRequest(userId) {
    return Object.keys(this.toBeLoadedUserMap).indexOf(userId) >= 0;
  }

  // 根据用户ids请求用户信息
  async loadUserByUserIds(userIds) {
    if (!arrayNotEmpty(userIds)) return null;

    userIds.forEach((userId) => {
      if (!this._isPendingRequest(userId) && !this._cachedUser(userId)) {
        this.toBeLoadedUserMap[userId] = this.currentCompleter;
        // console.debug(
        // 	`loadUserByUserIds-有新的userId：${userId}, 当前待请求toBeLoadedUserIds：${Object.keys(this.toBeLoadedUserMap)}`
        // );
      }
    });

    return this.currentCompleter.future;
  }

  // 根据多个用户id请求用户信息
  async _requestUsersByUserIds(userIds) {
    // console.debug('执行查询用户信息的任务', userIds);

    const ids = Array.from(userIds).filter(
      (userId) => !this._cachedUser(userId)
    );
    if (ids.length == 0) {
      return;
    }

    let r = await getUsers(ids);
    if (r.Type != 200) {
      console.warn(r.Content);
      return null;
    }
    let users = r.Data;
    if (arrayNotEmpty(users)) {
      users.forEach((user) => {
        this.userMap[user.Id] = JSON.stringify(user);
      });
    }
    // console.debug(`_requestUsersByUserIds-已请求用户数据：${r.Rows}，当前已缓存UserIds：${Object.keys(this.userMap)}`);
    return users;
  }
}
