import StoreKeys from '@/utils/storeKeys';
import { stringNotEmpty } from '@/utils/utils';

/// 会话聊天缓存管理
export default class ChatCacheManager {
  static _instance;
  static instance() {
    if (!this._instance) {
      this._instance = new ChatCacheManager();
    }
    return this._instance;
  }

  /**
   * 存储的聊天缓存，格式：
   * {
   * 	 userId: {
   * 		 sessionId: content,
   *		 ...
   *	 },
   *	 ...
   * }
   */
  chatMap = {};

  /**
   * 存储未发送聊天内容
   *
   * @param {String} sessionId 会话ID
   * @param {String} content 需要缓存的内容
   */
  setChat(sessionId, content) {
    if (!stringNotEmpty(sessionId) || !stringNotEmpty(content)) return;

    let userId = getApp().globalData.userInfo.Id;
    var userChatMap = this.chatMap[userId] || {};
    userChatMap[sessionId] = content;
    this.chatMap[userId] = userChatMap;
    console.debug('ChatCacheManager.setChat', this.chatMap);
  }

  /**
   * 根据会话房间号，获取对应缓存的聊天内容
   *
   * @param {Object} sessionId 会话ID
   */
  getChat(sessionId) {
    if (!stringNotEmpty(sessionId)) return null;

    let userId = getApp().globalData.userInfo.Id;
    var userChatMap = this.chatMap[userId];

    if (!userChatMap) return null;
    let content = userChatMap[sessionId];
    console.debug('ChatCacheManager.getChat', content);
    return content;
  }

  /**
   * 根据会话房间号，删除对应的键值对
   *
   * @param {Object} sessionId 会话ID
   */
  removeChat(sessionId) {
    if (!stringNotEmpty(sessionId)) return null;

    let userId = getApp().globalData.userInfo.Id;
    var userChatMap = this.chatMap[userId];
    if (!userChatMap) return null;

    let content = userChatMap[sessionId];
    if (content) delete userChatMap[sessionId];
    console.debug('ChatCacheManager.removeChat', this.chatMap);
  }

  /// 聊天内容本地化存储
  storageChat() {
    let that = this;
    uni.setStorage({
      key: StoreKeys.cacheChatContentMap,
      data: that.chatMap,
      complete(res) {
        console.debug('聊天内容本地化存储:', res);
      },
    });
  }

  /// 获取本地化存储的聊天内容
  getStorageChat() {
    let that = this;
    uni.getStorage({
      key: StoreKeys.cacheChatContentMap,
      success(res) {
        that.chatMap = res.data || {};
        console.debug('获取本地化存储的聊天内容', res, that.chatMap);
      },
      fail(err) {
        console.debug('获取本地存储聊天内容失败：', err);
      },
    });
  }
}
