import { stringNotEmpty } from '@/utils/utils';
import { IMApi, IMManager } from 'kfx-im';
import { ConsultClientEvent, SessionClientEvent } from '@/utils/eventKeys';

export default class SessionState {
  /**获取会话存储管理实例*/
  static _instance;
  static instance() {
    if (!this._instance) {
      let instance = new SessionState();
      this._instance = instance;
    }
    return this._instance;
  }

  _imApi = new IMApi();

  /**是否已开始监听*/
  _isStartListened = false;

  /**开始监听当前会话列表更新*/
  startListenSessions() {
    let imManager = IMManager.instance;
    if (!this._isStartListened) {
      this._isStartListened = true;
      imManager.addSessionListListener(this._handleSessionsChanged);
    }
  }

  /**
   * 处理会话更新
   *
   * @param {Object} sessions
   */
  _handleSessionsChanged(sessions) {
    console.debug('session-state: 会话列表更新');

    // 发送通知，会话列表变化
    uni.$emit(SessionClientEvent.sessionListChanged, sessions);

    // let that = SessionState._instance;
    // that._changeBadgeNumber();
  }

  /**
   * 修改未读消息数量
   *
   * 备注：该处功能未使用，同时每次会话变化都需要请求，不要要消耗，待优化
   */
  async _changeBadgeNumber() {
    console.debug('_changeBadgeNumber');
    let userId = getApp().globalData.userInfo.Id;
    let r = await this._imApi.getAllUnreadCount(userId);
    if (r.isSuccess) {
      // 获取咨询会话未读消息数量
      let consultUnreadMessageCount =
        (r.data.closedConsultUnReadCount || 0) +
        (r.data.waitConsultUnReadCount || 0) +
        (r.data.consultingUnReadCount || 0);
      // 发送通知，咨询会话未读消息数量发生变化
      uni.$emit(
        ConsultClientEvent.consultUnreadMessageCountChanged,
        consultUnreadMessageCount
      );
    }
  }

  /**
   * 根据 roomId 获取 session
   *
   * @param {String} roomId
   */
  async findSession(roomId) {
    var resolveFunc;
    var rejectFunc;
    let promise = new Promise((resolve, reject) => {
      resolveFunc = resolve;
      rejectFunc = reject;
    });

    if (!stringNotEmpty(roomId)) {
      rejectFunc('roomId为空');
      return promise;
    }

    // 设置5秒超时
    var timer;
    let timeout = setTimeout(() => {
      if (timer) {
        clearInterval(timer);
        timer = null;
      }
      clearTimeout(timeout);
      console.debug('findSession-请求超时:', roomId);
      resolveFunc(null);
    }, 5000);

    let imManager = IMManager.instance;
    let r1 = imManager
      .getSessionList()
      .find((e) => e.sessionId && e.sessionId == roomId);
    if (r1) {
      // 直接找到 session
      clearTimeout(timeout);
      timeout = null;
      console.debug('findSession-直接获取:', r1);
      resolveFunc(r1);
    } else {
      // 延时获取 session
      var loopCount = 0;
      timer = setInterval(() => {
        loopCount++;

        // 延迟加载，获取会话
        let r2 = imManager
          .getSessionList()
          .find((e) => e.sessionId && e.sessionId == roomId);
        if (r2) {
          clearInterval(timer);
          timer = null;
          clearTimeout(timeout);
          timeout = null;
          console.debug('findSession-延时获取:', r2);
          resolveFunc(r2);
        }

        // 请求会话
        if (loopCount == 2) {
          imManager.getSession(roomId);
        }
      }, 300);
    }

    return promise;
  }

  /**重置*/
  reset() {
    this._isStartListened = false;
    let imManager = IMManager.instance;
    imManager.removeSessionListListener(this._handleSessionsChanged);
  }
}
