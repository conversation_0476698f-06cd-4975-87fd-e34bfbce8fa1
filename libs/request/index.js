import appConfig from '@/config';

export class RequestAdapter {
  constructor(request) {
    this.request = request;
  }

  /**
   * @param {Object} url
   * @param {Object} params
   * @return {Promise<{Type:number,Content:string,Data:Object}>}
   */
  async get(url, params) {
    return this.request.get(url, {
      params,
    });
  }

  /**
   * @param {Object} url
   * @param {Object} data
   * @return {Promise<{Type:number,Content:string,Data:Object}>}
   */
  async post(url, data) {
    return this.request.post(url, data);
  }
  /**
   * @param {Object} url
   * @param {Object} data
   * @return {Promise<{Type:number,Content:string,Data:Object}>}
   */
  async put(url, data) {
    return this.request.put(url, data);
  }
}

const debounce = uni.$u.debounce;

let refreshTokenRequest = null;
async function refreshTokenAndRetry(response, http) {
  if (response.statusCode != 401) {
    return response;
  }

  const refresh_token = uni.getStorageSync('ref_token');
  if (!refresh_token) {
    const message = 'refresh_token 不存在，无法刷新 token';
    console.warn(message);
    debounce(showModalFun);
    throw new Error(message);
    // return response;
  }

  if (!refreshTokenRequest) {
    refreshTokenRequest = uni
      .request({
        url: appConfig.loginBaseUrl + '/connect/token',
        method: 'POST',
        data: {
          client_id: appConfig.clientId,
          client_secret: 'secret',
          grant_type: 'refresh_token',
          refresh_token: refresh_token,
        },
        header: {
          'content-type': 'application/x-www-form-urlencoded',
          'hwkj-custom-client': appConfig.clientId,
          'hwkj-custom-clientversion': appConfig.version,
        },
      })
      .then((data) => {
        console.log('refresh_token', data);
        let [err, r] = data;
        if (r.statusCode == 200) {
          uni.setStorageSync('token', r.data.access_token);
          uni.setStorageSync('ref_token', r.data.refresh_token);
          getApp().globalData.token = r.data.access_token;
        }
        refreshTokenRequest = null;
        return r;
      });
  }
  const r = await refreshTokenRequest;
  if (r.statusCode != 200) {
    console.error(r);
    const message = '刷新 token 失败';
    console.warn(message);
    debounce(showModalFun);
    throw new Error(message);
    // debounce(showModalFun);
    // return response;
  }

  /* 重新发送请求 */
  if (http) {
    const config = response.config;
    if (config.custom.retryCount == null) {
      config.custom.retryCount = 0;
    }
    if (config.custom.retryCount >= 2) {
      debounce(showModalFun);
      throw Error('token 无效');
    }
    config.custom.retryCount++;
    return http.request(config);
  } else {
    const options = response.options;
    options.header.authorization = 'Bearer ' + r.data.access_token;
    options.success = null;
    options.fail = null;
    options.complete = null;
    const data = await uni.request(options);
    const [fail, newResponse] = data;
    newResponse.options = options;
    if (newResponse.statusCode == 401) {
      console.error(newResponse);
      const message = 'token 无效';
      console.warn(message);
      debounce(showModalFun);
      throw new Error(message);
    }
    return newResponse;
  }
}

const showModalFun = () => {
  uni.showModal({
    title: '提示',
    content: '登录过期，请您重新登录',
    // showCancel: false,
    success: async (res) => {
      await getApp().logout();
      const pages = getCurrentPages();
      if (pages.length != 1 || pages[0].route != 'subGauge/transfer') {
        setTimeout(() => {
          getApp().openLoginPage({
            reLaunch: true,
          });
        }, 100);
      } else if (pages.length == 1 && pages[0].route == 'subGauge/transfer') {
        pages[0].$vm.show = true;
      }
    },
  });
  // throw new Error("statusCode 401");
};

function initHttpClient(http) {
  http.setConfig((c) => {
    /* config 为默认全局配置*/
    Object.defineProperty(c, 'baseURL', {
      get() {
        return appConfig.apiBaseUrl;
      },
    });
    c.header = {
      'hwkj-custom-client': appConfig.clientId,
      'hwkj-custom-clientversion': appConfig.version,
    };
    return c;
  });

  http.interceptors.request.use(async (config) => {
    /* 直接抛出错误可以进入响应错误处理流程 */
    // throw new Error("test");
    // Promise.reject(config) 不会进入响应错误处理流程
    // Promise.reject(new Error("test"));

    /* 添加 token */
    const token = uni.getStorageSync('token');
    if (token) {
      config.header = {
        ...config.header,
        authorization: 'Bearer ' + token,
      };
    }

    return config;
  });

  http.interceptors.response.use(
    (response) => {
      /* reject 和抛出错误,都不会进入下面的错误处理流程*/
      // throw new Error("服务器错误");
      // Promise.reject('test');

      // console.log(response);

      /* businessResponse 为 flase 时，直接返回响应数据 */
      if (response.config.custom.businessResponse === false) {
        return response;
      }

      /* 确保 businessResponse 格式 */
      if (response.data == null) {
        return {
          Type: 200,
          Content: '',
          Data: null,
          Log: 'client 添加的包装',
        };
      }
      if (
        response.data != null &&
        response.data.Type == null &&
        response.data.Data == null
      ) {
        return {
          Type: 200,
          Content: '',
          Data: response.data,
          Log: 'client 添加的包装',
        };
      }

      return response.data;
    },
    async (errorOrResponse) => {
      // console.error(errorOrResponse);

      let showToast = false;
      let message = '系统错误';
      let type = 'error';
      /* 是响应 */
      if (errorOrResponse.statusCode != undefined) {
        const response = errorOrResponse;

        // 请求成功发出且服务器也响应了状态码，但状态代码超出了 2xx 的范围
        if (response.statusCode === 401) {
          // 刷新token
          return refreshTokenAndRetry(response, http).catch((error) => {
            return {
              Type: 4000,
              Content: error.message,
            };
          });
        }

        switch (response.statusCode) {
          case 400:
            showToast = true;
            message = response.data.Message || response.data.error_description;
            type = 'error';
            // for (let mapper of response400MessageMappers) {
            //   if (mapper.reg.test(message)) {
            //     message = mapper.message;
            //     break;
            //   }
            // }
            break;
          case 403: // token 没有权限操作
            message = '您没有权限操作';
            type = 'warning';
            break;
          case 413:
            message = '您上传的文件过大,请压缩文件再试试';
            type = 'warning';
            break;
          case 429:
            message = '请求过多，请稍后再试';
            type = 'warning';
            break;
          case 500:
            message = '服务器内部错误，请稍后再试';
            type = 'warning';
            break;
        }
      } else {
        console.error(errorOrResponse);
        // 是错误
        if (errorOrResponse instanceof Error) {
          const error = errorOrResponse;
          message = error.message;
          type = 'error';
        } else {
          message = `${errorOrResponse}`;
          type = 'error';
        }
      }

      if (showToast) {
        uni.showToast({
          title: message,
          icon: 'none',
        });
      }

      return {
        Type: 4000,
        Content: message,
      };
    }
  );
}

initHttpClient(uni.$u.http);

export default new RequestAdapter(uni.$u.http);

export function createHttpClient() {
  const http = new uni.$u.http.constructor();
  initHttpClient(http);
  return http;
}
