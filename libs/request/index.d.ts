import type { HttpRequestAbstract, HttpRequestConfig } from '../luch-request';

interface APIResult<T> {
  Type: number;
  Content?: string | null;
  Data?: T | null;
}

export class RequestAdapter {
  constructor(request: HttpRequestAbstract);
  get<T>(
    url: string,
    params?: Record<string, any>,
    options?: HttpRequestConfig
  ): Promise<APIResult<T>>;
  post<T>(
    url: string,
    data?: Record<string, any>,
    options?: HttpRequestConfig
  ): Promise<APIResult<T>>;
  put<T>(
    url: string,
    data?: Record<string, any>,
    options?: HttpRequestConfig
  ): Promise<APIResult<T>>;
}

export function createHttpClient(): HttpRequestAbstract;

/**
 * 配置 request
 * @param option 配置项
 * @param option.onAuthenticationFailed 认证失败回调
 */
export function configHandler(option: {
  onAuthenticationFailed: (error: Error) => void;
}): void;

declare const request: RequestAdapter;
export default request;
