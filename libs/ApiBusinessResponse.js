export default class ApiBusinessResponse {
  constructor(data) {
    this.code = data.Type;
    this.data = data.Data;
    this.msg = data.Content || null;
    this.date = data.Timestamp || null;
  }
  code;
  data;
  msg;
  date;

  get isSuccess() {
    return this.code === 200;
  }
  get isFailure() {
    return !this.isSuccess;
  }

  static success(data, msg) {
    return new ApiBusinessResponse({
      Type: 200,
      Content: msg,
      Data: data,
    });
  }
  static failure(msg) {
    return new ApiBusinessResponse({
      Type: -1,
      Content: msg || '自定义失败',
    });
  }

  /**
   * 转换 http response 为 ApiBusinessResponse
   */
  static parseResponse(response) {
    if (response) {
      return new ApiBusinessResponse(response);
    } else {
      return ApiBusinessResponse.failure('无法解析数据');
    }
  }
}
