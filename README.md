
## 项目结构

* components 文件夹存在 UI 组件
* pages 文件夹存放页面组件
* sub 开头的文件夹为子包
* services 文件夹存放 APP 全局服务
* store 文件夹存放 APP 顶层状态
* static 文件夹存放资源文件
* utils 和 libs 文件夹中存放一些不包含业务逻辑的 js 文件。后续考虑合并在一起

*后续考虑 api 提取为单独的包来维护，为后续医生端小程序作准备*

## 实时日志

实时日志已经挂载到了 Vue.property.$log 和 uni.$log 上。

js 中可以直接使用 uni.$log 的方式进行调用。

Vue 组件中可以直接使用 $log 调用。

后续考虑和 console 结合在一起。

## uView

uView 自带一些工具方法[API](https://www.uviewui.com/js/fastUse.html)，已经挂载到了 Vue.property.$u 和 uni.$u 上。

**uni.$u.test.url 正则表达式有问题，可能会导致死循环**

## 页面通讯

* uni.$emit(eventName,OBJECT)
* uni.$on(eventName,callback)
* uni.$once(eventName,callback)
* uni.$off([eventName, callback)

注意事项

* uni.$emit、 uni.$on 、 uni.$once 、uni.$off 触发的事件都是 App 全局级别的，跨任意组件，页面，nvue，vue 等
* 使用时，注意及时销毁事件监听，比如，页面 onLoad 里边 uni.$on 注册监听，onUnload 里边 uni.$off 移除，或者一次性的事件，直接使用 uni.$once 监听
* 注意 uni.$on 定义完成后才能接收到 uni.$emit 传递的数据

## 添加 git 仓库 npm 包
例如添加 IM 库：
```sh
npm i git+https://gitlab-dev.kangfx.com/kangfx/frontend/kfx-im-js.git#semver:^1.0.0
```