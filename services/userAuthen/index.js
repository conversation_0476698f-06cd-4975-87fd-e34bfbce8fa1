import {
  dateFormat
} from '@/utils/validate.js';
import {
  automaticApproval
} from '@/api/identity.js';
import {
  saveUserAuthenticationAudit,
  updatePart,
  getLatestAuth,
} from '@/api/passport.js';

import {
  idCardGetBri
} from '@/utils/utils';
export const saveUserAuthen = async (data, showError = true) => {
  await updatePartDetail(data);
  if (data.UserCertificates[0].CertificateValue) {
    // 认证患者信息
    saveAuthen(data, showError);
  }
};
export const updatePartDetail = async (data) => {
  const app = getApp();
  let params = {
    UserId: data.UserId,
  }
  if (data.Sex) {
    params.Sex = data.Sex
  }
  if (data.Age) {
    params.Age = data.Age
    const newDate = dateFormat(new Date(), 'YYYY-MM-01').split('-');
    newDate[0] = String(dateFormat(new Date(), 'YYYY') * 1 - data.Age * 1);
    params.Birthday = newDate.join('-')
  }
  if (data.Name) {
    params.Name = data.Name
  }
  const data1 = [params];
  const res = await updatePart(data1);
  if (res.Type === 200) {
    app.changeUserInfo('Name', data.Name);
    app.changeUserInfo('Sex', data.Sex);
    app.changeUserInfo('Age', data.Age);
    app.changeUserInfo('Birthday', params.Birthday);
  }
};
const saveAuthen = async (data, showError) => {
  data.Birthday = idCardGetBri(data.UserCertificates[0].CertificateValue);
  let res = await saveUserAuthenticationAudit([data]);
  if (res.Type == 200) {
    // 系统自动审核通过 调这个接口获取用户最新的数据（主要是拿到用户认证的字段WorkflowStatus为2） 现在是后端自动通过 不需要前端调接口了
    // getAutomaticApproval()
    getAuthen();
  } else {
    if (showError) {
      uni.showModal({
        content: res.Content,
        showCancel: false,
      });
    }
  }
};
const getAutomaticApproval = async () => {
  const app = getApp();
  let res = await automaticApproval({
    UserId: app.globalData.userInfo.Id,
  });
  if (res.Type == 200) {
    getAuthen();
  }
};
// 获取患者是否实名
const getAuthen = async () => {
  const app = getApp();
  let res = await getLatestAuth({
    userId: app.globalData.userInfo.Id,
    status: [0, 2],
  });
  if (res.Type == 200) {
    res.Data.Age =
      new Date().getFullYear() -
      dateFormat(res.Data.Birthday, 'YYYY-MM-DD').split('-')[0];
    res.Data.WorkflowStatus = res.Data.Status;
    delete res.Data.Status;
    app.globalData.userInfo = res.Data;
    uni.setStorageSync('userInfoLoging', res.Data);
  }
};
