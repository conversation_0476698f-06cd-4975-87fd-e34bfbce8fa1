export default class IMUserProvider {
  _globalData;

  constructor(globalData) {
    this._globalData = globalData;
  }

  /// 用户ID，跟用户注册到IM的ID保持一致
  getUserId() {
    //console.log(`IM获取用户id：${this._globalData.userInfo.Id}`);
    return this._globalData.userInfo.Id;
  }

  /// 项目唯一标识名称
  getProject() {
    return 'kangfuxing';
  }

  /// 用户OAUTH2.0资源授权码
  getAuthToken() {
    //console.log(`IM获取用户token：${this._globalData.token}`);
    return this._globalData.token;
  }
}
