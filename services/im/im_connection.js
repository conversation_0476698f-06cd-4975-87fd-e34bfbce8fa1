import { SignalChannelState } from '@/uni_modules/kfx-signal_channel/js_sdk/index';
import { IMConnectionState } from 'kfx-im';

// connect(): Promise<void>;
// disconnect(): Promise<void>;
// onReceiveData: OnReceiveData;
// onConnectStateChanged: OnConnectStateChanged;

export default class IMConnection {
  signalChannelFactory;

  constructor(signalChannelFactory) {
    this.signalChannelFactory = signalChannelFactory;
    this._dataReceivedHandler = (values) => {
      console.debug('收到消息：' + JSON.stringify(values));
      if (this.onReceiveData) {
        this.onReceiveData(values.Content);
      }
    };
    this._signalChannelStateChangedHandler = (state) => {
      console.debug(state);
      console.info('IM连接状态改变: ' + this._getSignalChannel().state);
      if (!this.onConnectStateChanged) return;
      switch (this._getSignalChannel().state) {
        case SignalChannelState.Disconnected:
          this._handleConnectStateChanged(IMConnectionState.disconnected);
          break;
        case SignalChannelState.Connected:
          this._handleConnectStateChanged(IMConnectionState.connected);
          break;
        case SignalChannelState.Reconnecting:
          this._handleConnectStateChanged(IMConnectionState.connecting);
          break;
      }
    };
  }

  _dataReceivedHandler;
  _signalChannelStateChangedHandler;

  _getSignalChannel() {
    if (this.signalChannelFactory) return this.signalChannelFactory();
    throw '没有 signalChannelFactory';
  }

  _handleConnectStateChanged(state) {
    if (this.onConnectStateChanged) this.onConnectStateChanged(state);
  }

  onConnectStateChanged;
  onReceiveData;

  async connect() {
    console.info('IMConnection', 'connect');
    if (!this._getSignalChannel()) throw '未初始化 _getSignalChannel';

    this._getSignalChannel().on('s:send', this._dataReceivedHandler);
    this._getSignalChannel().addStateListener(
      this._signalChannelStateChangedHandler
    );

    if (this._getSignalChannel().state == SignalChannelState.Connected)
      this._handleConnectStateChanged(IMConnectionState.connected);
  }

  async disconnect() {
    console.info('IMConnection', 'disconnect');
    if (!this._getSignalChannel()) throw '未初始化 _getSignalChannel';
    this._getSignalChannel().off('s:send');
    this._getSignalChannel().removeStateListener(
      this._signalChannelStateChangedHandler
    );
    this._handleConnectStateChanged(IMConnectionState.disconnected);
  }
}
