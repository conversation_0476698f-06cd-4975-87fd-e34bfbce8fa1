import { IMContact } from 'kfx-im';
import UserCacheStore from '@/libs/util/user_cache_store';
import { dataIsValid } from '@/utils/utils';

/**
 * 给 IM 提供联系人信息，需要实现 IMContactInfoServiceInterface 接口
 */
export default class IMContactInfoService {
  getContactInfo(userId) {
    // console.log("getContactInfo");
    let user = UserCacheStore.instance().getUser(userId);
    let name = dataIsValid(user) ? user.Name : '';
    let headImg = dataIsValid(user) ? user.HeadImg : '';

    if (!user) {
      // console.log("添加待检查的 userID", userId);
      this._loadingUserIds.add(userId);
      this._startCheckLoading();
    }

    return new IMContact({
      id: userId,
      name: name,
      avatarUrl: headImg,
    });
  }
  async asyncGetContactInfo(userIds) {
    let userCacheStore = UserCacheStore.instance();
    let map = new Map();
    for (let userId of userIds) {
      // TODO：这里有问题。调用的异步方法，返回的是 promise
      let user = userCacheStore.asyncGetUser(userId);
      let name = dataIsValid(user) ? user.Name : '';
      let headImg = dataIsValid(user) ? user.HeadImg : '';
      map.set(
        userId,
        new IMContact({
          id: userId,
          name: name,
          avatarUrl: headImg,
        })
      );
    }
    return map;
  }

  async cacheContactInfo(userIds) {
    console.debug('缓存cacheContactInfo', userIds);
    let userCacheStore = UserCacheStore.instance();
    await userCacheStore.loadUserByUserIds(userIds);
  }

  listenContactInfoChanged(callback) {
    this._callback = callback;
  }

  _loadingUserIds = new Set();
  _callback;
  _checkLoadingUserIds() {
    // console.log("_checkLoadingUserIds");
    let userCacheStore = UserCacheStore.instance();
    let map = new Map();
    for (let userId of this._loadingUserIds) {
      let cached = userCacheStore.hasCachedUser(userId);
      if (cached) {
        // console.log("cached", userId, cached);
        let user = userCacheStore.getUser(userId);
        let name = dataIsValid(user) ? user.Name : '';
        let headImg = dataIsValid(user) ? user.HeadImg : '';
        map.set(
          userId,
          new IMContact({
            id: userId,
            name: name,
            avatarUrl: headImg,
          })
        );
        this._loadingUserIds.delete(userId);
      }
    }
    // console.log(map,this._loadingUserIds);
    if (this._callback && map.size > 0) {
      // console.log("通知观察者");
      this._callback(map);
    }
  }
  _checkInterval;
  _checkTimer;
  _startCheckLoading() {
    if (this._checkInterval) return;
    // console.log("_startCheckLoading");
    this._checkInterval = setInterval(() => {
      this._checkLoadingUserIds();
      if (this._loadingUserIds.size == 0) {
        this._stopCheckLoading();
      }
    }, 1000);
    this._checkTimer = setTimeout(() => {
      this._stopCheckLoading();
    }, 15000);
  }
  _stopCheckLoading() {
    if (this._checkInterval) {
      clearInterval(this._checkInterval);
      this._checkInterval = null;
    }
    if (this._checkTimer) {
      clearTimeout(this._checkTimer);
      this._checkTimer = null;
    }
  }
}
