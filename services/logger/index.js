const log = wx.getRealtimeLogManager ? wx.getRealtimeLogManager() : null;

const logger = {
  debug() {
    if (log) {
      log.debug.apply(log, arguments);
    }
    console.debug.apply(console, arguments);
  },
  info() {
    if (log) {
      log.info.apply(log, arguments);
    }
    console.info.apply(console, arguments);
  },
  warn() {
    if (log) {
      log.warn.apply(log, arguments);
    }
    console.warn.apply(console, arguments);
  },
  error() {
    if (log) {
      log.error.apply(log, arguments);
    }
    console.error.apply(console, arguments);
  },
  setFilterMsg(msg) {
    // 从基础库2.7.3开始支持
    if (!log || !log.setFilterMsg) return;
    if (typeof msg !== 'string') return;
    log.setFilterMsg(msg);
  },
  addFilterMsg(msg) {
    // 从基础库2.8.1开始支持
    if (!log || !log.addFilterMsg) return;
    if (typeof msg !== 'string') return;
    log.addFilterMsg(msg);
  },
};

// $u挂载到uni对象上
uni.$log = logger;

export default logger;
