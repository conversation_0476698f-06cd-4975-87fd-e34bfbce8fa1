import { getSignInfo } from '@/api/tenant.js';
class RiskWarningManager {
  static _instance;

  static getInstance() {
    if (!RiskWarningManager._instance) {
      RiskWarningManager._instance = new RiskWarningManager();
    }
    return RiskWarningManager._instance;
  }

  /**
   * 风险预警量表配置列表
   */
  riskWarningConfigs = [];

  /**
   * 获取风险预警配置
   */
  async requestRiskWarningConfig() {
    if (this.riskWarningConfigs.length > 0) {
      return {
        Type: 200,
        Data: this.riskWarningConfigs,
        Content: '获取成功',
      };
    }

    const r = await getSignInfo();
    if (r.Type === 200) {
      this.riskWarningConfigs = r.Data ?? [];
    }
    return r;
  }

  /**
   * 根据 Sign 获取量表配置
   */
  getRiskConfigBySign(sign) {
    if (!sign) return undefined;
    return this.riskWarningConfigs.find((item) => item.Sign === sign);
  }

  /**
   * 是否是多结论量表
   */
  isConclusion(sign) {
    if (!sign) return false;
    const config = this.getRiskConfigBySign(sign);
    return config?.OtherMap === 'Detail';
  }

  /**
   * 是否是分数量表
   */
  isScore(sign) {
    if (!sign) return false;
    const config = this.getRiskConfigBySign(sign);
    return config?.OtherMap !== 'Detail';
  }

  /**
   * 是否属于认知障碍
   */
  isCognition(sign) {
    if (!sign) return false;
    const config = this.getRiskConfigBySign(sign);
    return config?.Category === 1;
  }

  /**
   * 是否属于功能运动
   */
  isMotion(sign) {
    if (!sign) return false;
    const config = this.getRiskConfigBySign(sign);
    return config?.Category === 2;
  }
  /**
   * 是否属于分组
   */
  isGroup(sign) {
    if (!sign) return false;
    const config = this.getRiskConfigBySign(sign);
    return config?.Category === 3;
  }
}

const riskWarningManager = RiskWarningManager.getInstance();
export default riskWarningManager;
