import config from '@/config';

/**
 * 上传文件
 *
 * @param {string} filePath 需要上传的文件路径
 * @param {object?} configs 是否是水印图片，默认false
 * @param {boolean?} configs.useWaterMark 是否是水印图片，默认false
 * @param {boolean?} configs.isCompressed 是否是压缩文件，默认true
 */
export function uploadFileToServer(filePath, configs) {
  const useWaterMark = configs?.useWaterMark ?? false;
  const isCompressed = configs?.isCompressed ?? true;
  const formData = {
    'VideoProcessOptions.Convert.Enabled': isCompressed ? 'true' : 'false',
  };

  console.log('uploadParams', filePath, formData);

  return new Promise((resolive, reject) => {
    uni.uploadFile({
      url: config.uploadBaseUrl + '/api/oss/Upload',
      header: {
        authorization: uni.getStorageSync('token')
          ? 'Bearer ' + uni.getStorageSync('token')
          : '',
      },
      filePath: filePath,
      name: 'file',
      formData,
      success: (uploadFileRes) => {
        if (uploadFileRes.statusCode == 200) {
          var data = JSON.parse(uploadFileRes.data);
          if (data.Type == 200) {
            let basicUrl =
              data.Data.HostSetting.External + data.Data.PathSetting.Path;
            let fileUrl = useWaterMark
              ? uploadFileRes + data.Data.PathSetting.Transform
              : basicUrl;
            data.Data = fileUrl;
            uploadFileRes.data = data;
          }
        }

        resolive(uploadFileRes);
      },
      fail: (err) => {
        reject(err);
      },
    });
  });
}
