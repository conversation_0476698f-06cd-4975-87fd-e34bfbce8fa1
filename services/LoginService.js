import config from '@/config';
import {
  getPhoneToken
} from '@/api/other.js';
import IMUserProvider from '@/services/im/im_user_provider.js';
import {
  IMManager,
  IMState
} from 'kfx-im';
import SessionState from '@/libs/util/session_state.js';
import IMConnection from '@/services/im/im_connection.js';
import {
  dateFormat
} from '@/utils/validate.js';
import {
  getUserOrganizations
} from '@/api/passport.js';
import {
  getIdMB
} from '@/api/other.js';
import {
  ConsultClientEvent,
  ConsultServerEvent,
  MessageClientEvent,
  MessageServerEvent,
  PrescriptionClientEvent,
  PrescriptionServerEvent,
  TrainingClientEvent,
  TrainingServerEvent,
  UserServerEvent,
  UserClientEvent,
  CommunityServiceEvent,
  CommunityClientEvent,
  FollowUpClientEvent,
} from '@/utils/eventKeys.js';
import store from '@/store/index.js';
import {
  accountLogin,
  getUserProfile,
  getUserInfo
} from '@/api/passport.js';
import {
  objNotEmpty
} from '@/utils/utils';
import CryptoJS from "crypto-js";

/*
 * 获取微信 loginCode
 */
function getWXLoginCode() {
  console.log('获取微信 loginCode');
  return new Promise((resolve, reject) => {
    uni.login({
      provider: 'weixin',
      success: (loginRes) => {
        resolve(loginRes);
      },
    });
  });
}

/**
 * 初始化用户信息
 */
async function initUserInfo() {
  const r = await getUserInfo();
  if (r.Type != 200) {
    console.warn(r.Content);
    throw new Error('获取用户信息失败');
  }

  const userInfo = r.Data;
  console.log(userInfo);
  if (!userInfo.sub) {
    throw new Error('获取用户信息失败');
  }
  uni.setStorageSync('account', userInfo);
  getApp().globalData.account = userInfo;
  const userId = userInfo.sub;
  const WorkflowStatus = userInfo.user_audit_status * 1;
  const res = await getUserProfile({
    DtoTypeName: 'QueryUserFullOutputDto',
    Ids: [userId],
  });
  uni.$log.info(`${config.envVersion}:患者信息`, res);
  if (res.Type != 200) {
    throw new Error(res.Content);
  }

  if (!res.Data.Age) {
    res.Data.Age = res.Data.Birthday ?
      new Date().getFullYear() -
      dateFormat(res.Data.Birthday, 'YYYY-MM-DD').split('-')[0] :
      '';
  }
  getApp().globalData.userInfo = {
    ...res.Data,
    WorkflowStatus,
  };
  uni.setStorageSync('userInfoLoging', {
    ...res.Data,
    WorkflowStatus,
  });
}

/**
 * 初始化用户机构信息
 */
async function initUserOrgInfo() {
  const app = getApp();
  let orgId = app.globalData.orgId;
  let orgName = app.globalData.orgName;

  if (!orgId) {
    // 获取以前记录的机构
    const res = await getUserOrganizations({
      pageindex: 1,
      pagesize: 1,
      Keyword: '',
      userId: getApp().globalData.userInfo.Id,
    });
    if (
      res.Type === 200 &&
      res.Data.length > 0 &&
      res.Data[0].MarkTime != null
    ) {
      console.log('获取到机构信息', res.Data[0]);
      orgId = res.Data[0].Id;
      orgName = res.Data[0].Name;
    } else {
      console.warn('初始化用户机构信息失败:', res);
    }
  }

  console.log('初始化用户机构信息:', orgId, orgName);
  if (orgId && orgName) {
    await getApp().changeOrgAndMark({
      orgId,
      orgName,
    });
  }
}

/**
 * 初始化消息模板
 */
async function initMsgTemplate() {
  console.log('获取平台的模板ID 列表');
  let res = await getIdMB();
  if (res.Type === 200) {
    getApp().globalData.msgTemplates = res.Data;
  } else {
    console.warn('初始化消息模板失败:', res);
  }
}

/**
 * 初始化 SignalChannel 和 IM
 */
async function initSignalChannelAndIM() {
  const globalData = getApp().globalData;

  // signalChannel开始连接，并监听服务器事件
  const signalChannel = globalData.signalChannel;
  if (signalChannel) {
    await signalChannel.stop();
    signalChannel.start();
  }

  // 登录IM
  const imManager = IMManager.instance;
  if (imManager.imState == IMState.init) {
    let imConnection = new IMConnection(() => globalData.signalChannel);
    imManager.init(imConnection);
  }
  if (!imManager.isLoggedIn) {
    let imUserProvider = new IMUserProvider(globalData);
    imManager.login(imUserProvider);
  }

  // 开始监听会话数据
  const sessionState = SessionState.instance();
  sessionState.startListenSessions();

  store.dispatch('message/syncMessageStatus');

  // 监听训练方案调整事件
  signalChannel.on(TrainingServerEvent.trainingPlanChanged, (data) => {
    console.log('训练方案调整事件', data);
    try {
      const imManager = IMManager.instance;
      imManager.refreshSessionList();
    } catch (error) {
      console.error('刷新会话列表失败', error);
    }
    uni.$emit(TrainingClientEvent.trainingPlanChanged, data);
  });

  // 监听治疗订单支付成功之后的事件
  signalChannel.on(PrescriptionServerEvent.payTreatSuccessSignalR, (data) => {
    console.log('治疗订单支付成功事件', data);
    uni.$emit(PrescriptionClientEvent.payTreatSuccess, data);
  });

  // 监听消息模块事件
  signalChannel.on(MessageServerEvent.messageReceived, (data) => {
    console.log('接收到新消息事件', data);
    uni.$emit(MessageClientEvent.messageReceived, data);
    // import store from './store'
    store.dispatch('message/receiveMessage', data);

    // 新增随访任务通知
    if (data.Type == 45) {
      uni.$emit(FollowUpClientEvent.add, data);
    }
    // 医生下达方案给患者
    if (data.Type === 25) {
      uni.$emit(
        MessageClientEvent.programmeIssued,
        JSON.parse(data.Extras).PrescriptionId
      );
    }
  });

  // 监听咨询问诊状态变化事件
  signalChannel.on(ConsultServerEvent.consultStateChanged, (data) => {
    console.log('咨询问诊状态变化事件', data);
    let consultId = '';
    let state = 0;
    if (objNotEmpty(data)) {
      consultId = data.ConsultId;
      state = data.State;
    }
    uni.$emit(ConsultClientEvent.consultStateChanged, {
      consultId: consultId,
      state: state,
    });
  });

  // 监听处方状态变化事件
  signalChannel.on(PrescriptionServerEvent.prescriptionStateChanged, (data) => {
    console.log('处方状态变化事件', data);
    uni.$emit(PrescriptionClientEvent.prescriptionStateChanged, data);
  });

  // 监听密码变化事件
  signalChannel.on(UserServerEvent.passwordChanged, (data) => {
    console.log('密码变化事件', data);
    uni.$emit(UserClientEvent.passwordChanged, data);
  });

  // 监听用户注销账号事件
  signalChannel.on(UserServerEvent.userLogout, (data) => {
    console.log('用户注销账号事件', data);
    uni.$emit(UserClientEvent.userLogout, data);
  });

  if (config.videoCallEnable) {
    // 监听视频通话事件
    signalChannel.on(UserServerEvent.onVideoCall, (data) => {
      console.log('视频通话事件', data);
      uni.$emit(UserClientEvent.onVideoCall, data);
    });
  }

  // 社区治疗被执行
  signalChannel.on(CommunityServiceEvent.refreshMoItemExec, (data) => {
    console.log('拿到数据了', data);
    uni.$emit(CommunityClientEvent.refreshMoItemExec, data);
  });

  // 监听用户被踢下线
  signalChannel.on(UserServerEvent.onCloseConnectionFromServerSide, (data) => {
    console.log('onCloseConnectionFromServerSide', data);
    uni.$emit(UserClientEvent.onCloseConnectionFromServerSide, data);
  });
}

export const LOGIN_FINISH = 'UserLogin_Finish';

export async function initAfterLoginIn() {
  if (!getApp().globalData.token) {
    throw new Error('请先登录');
  }
  try {
    // throw new Error("test");
    await initUserInfo();
    await initUserOrgInfo();
    await initMsgTemplate();
    await initSignalChannelAndIM();

    // changeOrgData(getApp().globalData.orgId, getApp());

    uni.$emit(LOGIN_FINISH);

    const accountInfo = uni.getAccountInfoSync();
    if (config.envVersion === 'release') {
      uni.setStorageSync('userReLogin', accountInfo.miniProgram.version);
    } else {
      uni.setStorageSync('userReLogin', true);
    }
    return true;
  } catch (error) {
    console.error(error);
    uni.showModal({
      content: error.message,
      showCancel: false,
    });
    return false;
  }
}

const aesKey = '3ajIVAjObyxA0KpR';
const aesIV = 'i5W14eTmouZ5Aj2P';

/*
 * 账号密码登录
 */
export async function accountAndPasswordLogin(account, password) {

  const aes = CryptoJS.AES.encrypt(
    JSON.stringify({
      username: account,
      password,
    }),
    CryptoJS.enc.Utf8.parse(aesKey), {
      iv: CryptoJS.enc.Utf8.parse(aesIV),
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    }
  );
  const data = {
    appid: config.clientId,
    appsecret: 'secret',
    granttype: 'aes_password',
    secret: aes.toString(CryptoJS.format.Hex),
  };

  const rsp = await accountLogin(data);

  if (rsp.Type != 200) {
    const tip = rsp.Content || rsp.error_description || '登录失败';
    console.warn(tip);
    uni.showModal({
      content: tip,
      showCancel: false,
    });
    return false;
  }

  uni.setStorageSync('token', rsp.Data.access_token);
  uni.setStorageSync('ref_token', rsp.Data.refresh_token);
  const app = getApp();
  app.globalData.token = rsp.Data.access_token;
  const r = await initAfterLoginIn();
  return r;
}

/*
 * 微信手机号一键登录
 */
export async function phoneLogin(event) {
  const loginRes = await getWXLoginCode();
  if (event.detail.errMsg == 'getPhoneNumber:ok') {
    console.log('e', event);

    const obj = {
      FetchOpenIdCode: loginRes.code,
      FetchPhoneNumberCode: event.detail.code,
    };
    const rsp = await getPhoneToken(obj);
    const app = getApp();
    if (rsp.Type === 200) {
      uni.setStorageSync('token', rsp.Data.access_token);
      uni.setStorageSync('ref_token', rsp.Data.refresh_token);
      uni.setStorageSync('openID', rsp.Data.openId);
      app.globalData.openId = rsp.Data.openId;
      app.globalData.token = rsp.Data.access_token;
    }
    const r = await initAfterLoginIn();
    return r;
  } else {
    const tip = event.detail.errMsg || '登录失败';
    console.warn(tip);
    uni.showModal({
      content: tip,
      showCancel: false,
    });
    return false;
  }
}
