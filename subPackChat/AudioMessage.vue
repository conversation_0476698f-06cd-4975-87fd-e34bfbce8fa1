<template>
  <div
    class="root"
    @click="handleClick"
    :class="{ 'is-playing': playing, 'is-out': isOut }"
  >
    <image class="audio-image" :src="audioImage" mode="aspectFit"></image>
    <text class="duration">
      {{ duration }}
    </text>
  </div>
</template>

<script>
import { audioPlayer } from './AudioPlayer';

export default {
  props: {
    message: {
      type: Object,
      require: true,
    },
    isOut: {
      type: Boolean,
      require: true,
    },
  },
  data() {
    return {
      playing: false,
      audioImage: '',
    };
  },
  computed: {
    duration() {
      return (
        ((this.message.messageObject?.duration ?? 0) / 1000).toFixed(0) + '"'
      );
    },
    src() {
      return this.message.messageObject.url ?? this.message.messageObject.path;
    },
  },
  created() {
    this.audioImage = `/subPackChat/static/audio-${this.isOut ? 'white' : 'black'}.png`;
  },
  mounted() {
    audioPlayer.onPlay((r) => {
      // console.log('开始播放音频1', audioPlayer.src);
      this.playing = this.src === audioPlayer.src;
      if (this.playing) {
        this.audioImage = `/subPackChat/static/audio-${this.isOut ? 'white' : 'black'}.gif`;
      }
    });
    audioPlayer.onEnded((r) => {
      // console.log('音频播放结束1', audioPlayer.src);
      if (this.src === audioPlayer.src) {
        this.playing = false;
        this.audioImage = `/subPackChat/static/audio-${this.isOut ? 'white' : 'black'}.png`;
      }
    });
  },
  destroyed() {
    console.log('销毁');
  },
  methods: {
    handleClick() {
      // console.log(this.message);

      audioPlayer.src = this.src;

      audioPlayer.play(); // 播放

      // innerAudioContext.pause() // 暂停

      // innerAudioContext.stop() // 停止

      // innerAudioContext.destroy() // 释放音频资源
    },
  },
};
</script>

<style lang="scss" scoped>
.root {
  width: 250rpx;
  background-color: white;
  border-radius: 8rpx;
  color: black;
  display: flex;
  align-items: center;
  display: flex;

  .audio-image {
    width: 30px;
    height: 30px;
  }

  .duration {
    padding: 0 10rpx;
  }

  &.is-out {
    color: white;
    flex-direction: row-reverse;
    background-color: #29b7a3;
  }
}
</style>
