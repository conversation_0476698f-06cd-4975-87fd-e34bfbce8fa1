<template>
  <view class="container-view">
    <view class="header-view">
      <text style="margin-right: 24rpx">群名称</text>
      <text>{{ teamName }}</text>
    </view>
    <text class="title-view">群成员</text>
    <view style="background-color: white" v-if="members && members.length > 0">
      <u-grid
        :border="false"
        col="4"
        :customStyle="{ alignItems: 'flex-start' }"
      >
        <u-grid-item v-for="(user, index) in members" :key="user.Id">
          <view class="grid-item-view" @click="handleUserClick(user)">
            <MpAvatar
              :src="user.HeadImg"
              width="60px"
              height="60px"
              errorImageHeight="60px"
              errorImageWidth="60px"
            ></MpAvatar>
            <text>{{ user.Name }}</text>
          </view>
        </u-grid-item>
      </u-grid>
    </view>
    <u-empty
      mode="data"
      icon="http://cdn.uviewui.com/uview/empty/data.png"
      v-else
    >
    </u-empty>
  </view>
</template>

<script>
import UserCacheStore from '@/libs/util/user_cache_store';
import { IMManager } from 'kfx-im';
import { arrayNotEmpty } from '../utils/utils.js';
import MpAvatar from '@/components/mp-avatar/mp-avatar.vue';

export default {
  components: {
    MpAvatar,
  },
  data() {
    return {
      teamName: '',
      teamId: '',

      // 群组成员
      members: [],
    };
  },
  onLoad(option) {
    let teamId = option.teamId;
    let teamName = option.teamName;

    this.teamId = teamId;
    this.teamName = teamName;

    this.loadData();
  },
  methods: {
    handleUserClick(item) {
      console.log('item', item);
      const Roles = item.UserRoleDicts;
      if (!Roles || !Roles.length) return;
      const isDoctor = Roles.some(
        (s) => s === 'doctor' || s === 'nurse' || s === 'therapist'
      );
      if (isDoctor) {
        uni.navigateTo({
          url: '/subPackIndex/docDetail?docId=' + item.Id,
        });
      }
    },
    async loadData() {
      uni.showLoading({
        title: '正在加载...',
      });
      let userIds = await this.loadTeamMemberList(this.teamId);
      if (arrayNotEmpty(userIds)) {
        let userCacheStore = UserCacheStore.instance();
        await userCacheStore.loadUserByUserIds(userIds);
        this.members = userIds.map((userId) => {
          let user = userCacheStore.getUser(userId);
          return user;
        });
      }
      uni.hideLoading();
    },

    // ----------------------- 网络请求 -----------------------

    // 请求团队成员userId
    async loadTeamMemberList(teamId) {
      if (!teamId) {
        uni.showToast({
          title: '团队id为空',
          icon: 'none',
        });
        return;
      }

      let imManager = IMManager.instance;
      var data;
      try {
        data = await imManager.fetchTeamMember(teamId);
      } catch (error) {
        console.error('获取团队成员失败', error);
      }

      if (!arrayNotEmpty(data)) {
        uni.showToast({
          title: '未获取到数据',
          icon: 'none',
        });
        return null;
      }

      let userIds = data.map((e) => e.userId);
      return userIds;
    },
  },
};
</script>

<style lang="scss" scoped>
.container-view {
  display: flex;
  flex-direction: column;
  justify-content: start;
  align-items: stretch;
}

.header-view {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 24rpx;
  margin: 20rpx 0;
  font-size: 30rpx;
  color: #333333;
  background-color: white;
}

.title-view {
  padding: 32rpx 24rpx;
  margin-bottom: 2rpx;
  font-size: 30rpx;
  color: #333333;
  background-color: white;
}

.grid-item-view {
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.grid-item-view text {
  font-size: 30rpx;
  color: #666666;
}
</style>
