<template>
  <view class="body-view">
    <!-- <tuicallkit ref="TUICallKit" :config="config"></tuicallkit> -->
    <u-navbar :title="navTitle" @leftClick="onLeftClick"> </u-navbar>
    <view
      v-if="!session || Object.keys(session).length === 0"
      class="no-data-view"
      :style="{ 'margin-top': navHeight + 'px' }"
    >
      <u-empty
        mode="data"
        icon="http://cdn.uviewui.com/uview/empty/data.png"
        text="未找到会话"
      />
      <view class="no-data-button" @click="onReload">
        <text style="margin-right: 10rpx">重新加载</text>
        <u-icon name="reload" size="20" color="#29b7a3" />
      </view>
    </view>
    <view
      :style="{ 'margin-top': navHeight + 'px', 'position': 'relation' }"
      v-else
    >
      <!-- 顶部描述 -->
      <view
        class="header-view"
        v-if="state != 0"
        :style="{ top: navHeight + 'px' }"
      >
        <rich-text :nodes="getHeaderTextNodes()"></rich-text>
      </view>

      <!-- 消息列表 -->
      <view @click="onCloseMenuOrTextarea">
        <u-list
          :width="safeWidth"
          :height="scrollHeight"
          :showScrollbar="false"
          :customStyle="{
            padding: '0 32rpx',
            backgroundColor: '#F7F7F7',
            position: 'flex',
            top: '0',
            paddingTop: state != 0 ? 35 + 'px' : 0,
          }"
          :enableFlex="true"
          :upperThreshold="50"
          :scrollTop="scrollTop"
          :scrollWithAnimation="true"
          @scrolltoupper="onLoadMore"
          id="messageId"
        >
          <!-- 加载更多头 -->
          <u-list-item>
            <view :style="{ display: loadStatus != 'loadmore' ? '' : 'none' }">
              <u-loadmore :status="loadStatus" />
            </view>
          </u-list-item>

          <!-- 消息体 -->
          <u-list-item
            v-for="(message, index) in messageList"
            :key="message.id"
          >
            <!-- 自定义 - 文本类型消息-->
            <view
              class="session-custom-cell"
              v-if="message.messageType == 'text'"
            >
              <view class="time-view" v-if="message.showTime">
                <text>{{ message.dateTime }}</text>
              </view>
              <view
                class="session-body-view"
                :style="{
                  'flex-direction': message.isOutgoingMsg
                    ? 'row-reverse'
                    : 'row',
                }"
              >
                <MpAvatar
                  @click="handleUserClick(message)"
                  :src="message.senderImage"
                ></MpAvatar>
                <view
                  class="session-content-view"
                  :style="{
                    'align-items': message.isOutgoingMsg
                      ? 'flex-end'
                      : 'flex-start',
                  }"
                >
                  <text class="name"
                    ><text class="tag-text" v-if="message.isShowTag"
                      >【{{ message.tag }}】</text
                    >{{ message.senderName }}</text
                  >
                  <view
                    class="session-content-body-view"
                    :style="{
                      'flex-direction': message.isOutgoingMsg
                        ? 'row-reverse'
                        : 'row',
                    }"
                  >
                    <view
                      :class="
                        message.isOutgoingMsg
                          ? 'session-custom-text-sender-cell'
                          : 'session-custom-text-receiver-cell'
                      "
                    >
                      <text>{{ message.text }}</text>
                    </view>
                    <view
                      class="session-status-view"
                      @tap.stop="onReTrySend(message)"
                    >
                      <u-loading-icon
                        color="#000000"
                        size="20"
                        v-if="message.deliveryState == 1"
                      />
                      <uni-icons
                        type="info-filled"
                        color="red"
                        size="20"
                        v-else-if="message.deliveryState == 0"
                      />
                      <view style="width: 20px" v-else />
                    </view>
                  </view>
                </view>
              </view>
            </view>
            <view
              class="session-custom-cell"
              v-else-if="message.messageType == 'audio'"
            >
              <view class="time-view" v-if="message.showTime">
                <text>{{ message.dateTime }}</text>
              </view>
              <view
                class="session-body-view"
                :style="{
                  'flex-direction': message.isOutgoingMsg
                    ? 'row-reverse'
                    : 'row',
                }"
              >
                <MpAvatar
                  @click="handleUserClick(message)"
                  :src="message.senderImage"
                ></MpAvatar>
                <view
                  class="session-content-view"
                  :style="{
                    'align-items': message.isOutgoingMsg
                      ? 'flex-end'
                      : 'flex-start',
                  }"
                >
                  <text class="name"
                    ><text class="tag-text" v-if="message.isShowTag"
                      >【{{ message.tag }}】</text
                    >{{ message.senderName }}</text
                  >
                  <view
                    class="session-content-body-view"
                    :style="{
                      'flex-direction': message.isOutgoingMsg
                        ? 'row-reverse'
                        : 'row',
                    }"
                  >
                    <view
                      :class="
                        message.isOutgoingMsg
                          ? 'session-custom-text-sender-cell'
                          : 'session-custom-text-receiver-cell'
                      "
                    >
                      <!-- <text>音频消息</text> -->
                      <AudioMessage
                        :url="abc"
                        :message="message"
                        :is-out="message.isOutgoingMsg"
                      ></AudioMessage>
                    </view>
                    <view
                      class="session-status-view"
                      @tap.stop="onReTrySend(message)"
                    >
                      <u-loading-icon
                        color="#000000"
                        size="20"
                        v-if="message.deliveryState == 1"
                      />
                      <uni-icons
                        type="info-filled"
                        color="red"
                        size="20"
                        v-else-if="message.deliveryState == 0"
                      />
                      <view style="width: 20px" v-else />
                    </view>
                  </view>
                </view>
              </view>
            </view>
            <!-- 自定义 - 图片/视频 -->
            <view
              class="session-custom-cell"
              v-else-if="
                message.messageType == 'image' || message.messageType == 'video'
              "
            >
              <view class="time-view" v-if="message.showTime">
                <text>{{ message.dateTime }}</text>
              </view>
              <view
                class="session-body-view"
                :style="{
                  'flex-direction': message.isOutgoingMsg
                    ? 'row-reverse'
                    : 'row',
                }"
              >
                <MpAvatar :src="message.senderImage"></MpAvatar>
                <view
                  class="session-content-view"
                  :style="{
                    'align-items': message.isOutgoingMsg
                      ? 'flex-end'
                      : 'flex-start',
                  }"
                >
                  <text class="name"
                    ><text class="tag-text" v-if="message.isShowTag"
                      >【{{ message.tag }}】</text
                    >{{ message.senderName }}</text
                  >
                  <view
                    class="session-content-body-view"
                    :style="{
                      'flex-direction': message.isOutgoingMsg
                        ? 'row-reverse'
                        : 'row',
                    }"
                  >
                    <view
                      class="session-media-view"
                      v-if="message.messageType == 'image'"
                    >
                      <MpImage
                        :src="message.imageUrl"
                        :width="message.mediaWidth + 'px'"
                        :height="message.mediaHeight + 'px'"
                        bgColor="#D7F4DE"
                        @tap.stop="onPreviewImage(message.imageUrl)"
                      />
                    </view>
                    <view class="session-media-view" v-else>
                      <video
                        :style="{
                          width: message.mediaWidth + 'px',
                          height: message.mediaHeight + 'px',
                        }"
                        :src="message.videoUrl"
                        controls
                      ></video>
                    </view>
                    <view
                      class="session-status-view"
                      @tap.stop="onReTrySend(message)"
                    >
                      <u-loading-icon
                        color="#000000"
                        size="20"
                        v-if="message.deliveryState == 1"
                      />
                      <uni-icons
                        type="info-filled"
                        color="red"
                        size="20"
                        v-else-if="message.deliveryState == 0"
                      />
                      <view style="width: 20px" v-else />
                    </view>
                  </view>
                </view>
              </view>
            </view>
            <!-- 自定义 - 医生发送的量表评估消息 -->
            <view
              class="session-custom-cell"
              v-else-if="
                message.messageType == 'custom' && message.customType == 5
              "
            >
              <view class="time-view" v-if="message.showTime">
                <text>{{ message.dateTime }}</text>
              </view>
              <view
                class="session-body-view"
                :style="{
                  'flex-direction': message.isOutgoingMsg
                    ? 'row-reverse'
                    : 'row',
                }"
              >
                <MpAvatar :src="message.senderImage"></MpAvatar>
                <view
                  class="session-content-view"
                  :style="{
                    'align-items': message.isOutgoingMsg
                      ? 'flex-end'
                      : 'flex-start',
                  }"
                >
                  <text class="name"
                    ><text class="tag-text" v-if="message.isShowTag"
                      >【{{ message.tag }}】</text
                    >{{ message.senderName }}</text
                  >
                  <view
                    class="session-content-body-view"
                    :style="{
                      'flex-direction': message.isOutgoingMsg
                        ? 'row-reverse'
                        : 'row',
                    }"
                  >
                    <view
                      class="session-alter-view"
                      hover-class="hover-class"
                      hover-stay-time="70"
                      @tap.stop="onCheckAssess(message)"
                    >
                      <view
                        class="session-alter-header-view"
                        style="background-color: #36a9ce"
                      >
                        <text>{{ message.customBody.assessName }}</text>
                      </view>
                      <view class="session-center-view">
                        <text class="custom-text"
                          >请打开本量表，并认真填写后提交</text
                        >
                      </view>
                    </view>
                    <view class="session-status-view">
                      <view style="width: 20px" />
                    </view>
                  </view>
                </view>
              </view>
            </view>
            <!-- 自定义 - 医生发送的评估方案消息 -->
            <view
              class="session-custom-cell"
              v-else-if="
                message.messageType == 'custom' && message.customType == 14
              "
            >
              <view class="time-view" v-if="message.showTime">
                <text>{{ message.dateTime }}</text>
              </view>
              <view
                class="session-body-view"
                :style="{
                  'flex-direction': message.isOutgoingMsg
                    ? 'row-reverse'
                    : 'row',
                }"
              >
                <MpAvatar :src="message.senderImage"></MpAvatar>
                <view
                  class="session-content-view"
                  :style="{
                    'align-items': message.isOutgoingMsg
                      ? 'flex-end'
                      : 'flex-start',
                  }"
                >
                  <text class="name"
                    ><text class="tag-text" v-if="message.isShowTag"
                      >【{{ message.tag }}】</text
                    >{{ message.senderName }}</text
                  >
                  <view
                    class="session-content-body-view"
                    :style="{
                      'flex-direction': message.isOutgoingMsg
                        ? 'row-reverse'
                        : 'row',
                    }"
                  >
                    <view
                      class="evaluation-plan-alter-view"
                      hover-class="hover-class"
                      hover-stay-time="70"
                      @tap.stop="onCheckEvaluationPlan(message)"
                    >
                      <image
                        src="/subPackChat/static/evaluation_plan.png"
                        style="z-index: 0"
                      >
                      </image>
                      <text
                        class="evaluation-plan-alter-title"
                        style="z-index: 1"
                        >{{ message.customBody.Name }}</text
                      >
                      <text
                        class="evaluation-plan-alter-content"
                        style="z-index: 1"
                        >失能程度及认知功能障碍测评</text
                      >
                      <text
                        class="evaluation-plan-alter-submit"
                        style="z-index: 1"
                        >点击评估</text
                      >
                    </view>
                    <view class="session-status-view">
                      <view style="width: 20px" />
                    </view>
                  </view>
                </view>
              </view>
            </view>
            <!-- 自定义 - 医生发送的宣教消息 -->
            <view
              class="session-custom-cell"
              v-else-if="
                message.messageType == 'custom' && message.customType == 6
              "
            >
              <view class="time-view" v-if="message.showTime">
                <text>{{ message.dateTime }}</text>
              </view>
              <view
                class="session-body-view"
                :style="{
                  'flex-direction': message.isOutgoingMsg
                    ? 'row-reverse'
                    : 'row',
                }"
              >
                <MpAvatar :src="message.senderImage"></MpAvatar>
                <view
                  class="session-content-view"
                  :style="{
                    'align-items': message.isOutgoingMsg
                      ? 'flex-end'
                      : 'flex-start',
                  }"
                >
                  <text class="name"
                    ><text class="tag-text" v-if="message.isShowTag"
                      >【{{ message.tag }}】</text
                    >{{ message.senderName }}</text
                  >
                  <view
                    class="session-content-body-view"
                    :style="{
                      'flex-direction': message.isOutgoingMsg
                        ? 'row-reverse'
                        : 'row',
                    }"
                  >
                    <view
                      class="mission-view"
                      :style="{
                        'flex-direction': message.isOutgoingMsg
                          ? 'row-reverse'
                          : 'row',
                      }"
                      hover-class="hover-class"
                      hover-stay-time="70"
                      @tap.stop="onCheckMission(message)"
                    >
                      <u--image
                        mode="aspectFill"
                        :src="message.customBody.image"
                        width="60px"
                        height="60px"
                      />
                      <text class="custom-text">{{
                        message.customBody.name
                      }}</text>
                    </view>
                    <view class="session-status-view">
                      <view style="width: 20px" />
                    </view>
                  </view>
                </view>
              </view>
            </view>
            <!-- 自定义 - 患者发送的已提交评估通知消息 -->
            <view
              class="session-custom-cell"
              v-else-if="
                message.messageType == 'custom' && message.customType == 7
              "
            >
              <view class="time-view" v-if="message.showTime">
                <text>{{ message.dateTime }}</text>
              </view>
              <view
                class="session-body-view"
                :style="{
                  'flex-direction': message.isOutgoingMsg
                    ? 'row-reverse'
                    : 'row',
                }"
              >
                <MpAvatar :src="message.senderImage"></MpAvatar>
                <view
                  class="session-content-view"
                  :style="{
                    'align-items': message.isOutgoingMsg
                      ? 'flex-end'
                      : 'flex-start',
                  }"
                >
                  <text class="name"
                    ><text class="tag-text" v-if="message.isShowTag"
                      >【{{ message.tag }}】</text
                    >{{ message.senderName }}</text
                  >
                  <view
                    class="session-content-body-view"
                    :style="{
                      'flex-direction': message.isOutgoingMsg
                        ? 'row-reverse'
                        : 'row',
                    }"
                  >
                    <view class="session-alter-view">
                      <view class="session-center-view">
                        <text class="custom-text"
                          >我已提交{{ message.customBody.assessName }}</text
                        >
                      </view>
                    </view>
                    <view class="session-status-view">
                      <u-loading-icon
                        color="#000000"
                        size="20"
                        v-if="message.deliveryState == 1"
                      />
                      <uni-icons
                        type="info-filled"
                        color="red"
                        size="20"
                        v-else-if="message.deliveryState == 0"
                      />
                      <view style="width: 20px" v-else />
                    </view>
                  </view>
                </view>
              </view>
            </view>
            <!-- 自定义 - 患者续方申请消息 -->
            <view
              class="session-custom-cell"
              v-else-if="
                message.messageType == 'custom' && message.customType == 13
              "
            >
              <view class="time-view" v-if="message.showTime">
                <text>{{ message.dateTime }}</text>
              </view>
              <view
                class="session-body-view"
                :style="{
                  'flex-direction': message.isOutgoingMsg
                    ? 'row-reverse'
                    : 'row',
                }"
              >
                <MpAvatar :src="message.senderImage"></MpAvatar>
                <view
                  class="session-content-view"
                  :style="{
                    'align-items': message.isOutgoingMsg
                      ? 'flex-end'
                      : 'flex-start',
                  }"
                >
                  <text class="name"
                    ><text class="tag-text" v-if="message.isShowTag"
                      >【{{ message.tag }}】</text
                    >{{ message.senderName }}</text
                  >
                  <view
                    class="session-content-body-view"
                    :style="{
                      'flex-direction': message.isOutgoingMsg
                        ? 'row-reverse'
                        : 'row',
                    }"
                  >
                    <view class="session-alter-view">
                      <view class="session-center-view">
                        <text class="custom-text"
                          >我提交了延续治疗方案申请</text
                        >
                      </view>
                    </view>
                    <view class="session-status-view">
                      <u-loading-icon
                        color="#000000"
                        size="20"
                        v-if="message.deliveryState == 1"
                      />
                      <uni-icons
                        type="info-filled"
                        color="red"
                        size="20"
                        v-else-if="message.deliveryState == 0"
                      />
                      <view style="width: 20px" v-else />
                    </view>
                  </view>
                </view>
              </view>
            </view>
            <!-- 自定义 - 问诊会话病情描述消息 -->
            <view
              class="session-custom-cell"
              v-else-if="
                message.messageType == 'custom' && message.customType == 10
              "
            >
              <view class="time-view" v-if="message.showTime">
                <text>{{ message.dateTime }}</text>
              </view>
              <view
                class="session-body-view"
                :style="{
                  'flex-direction': message.isOutgoingMsg
                    ? 'row-reverse'
                    : 'row',
                }"
              >
                <MpAvatar :src="message.senderImage"></MpAvatar>
                <view
                  class="session-content-view"
                  :style="{
                    'align-items': message.isOutgoingMsg
                      ? 'flex-end'
                      : 'flex-start',
                  }"
                >
                  <text class="name"
                    ><text class="tag-text" v-if="message.isShowTag"
                      >【{{ message.tag }}】</text
                    >{{ message.senderName }}</text
                  >
                  <view
                    class="session-content-body-view"
                    :style="{
                      'flex-direction': message.isOutgoingMsg
                        ? 'row-reverse'
                        : 'row',
                    }"
                  >
                    <view
                      class="session-alter-view"
                      hover-class="hover-class"
                      hover-stay-time="70"
                      @tap.stop="onCheckConsultDescription(message)"
                    >
                      <view class="session-alter-header-view">
                        <text>病情描述</text>
                      </view>
                      <text class="custom-text">{{
                        message.customBody.content
                      }}</text>
                      <view class="consult-description-more-view">
                        <text class="tag-text" style="font-size: 16px"
                          >查看更多</text
                        >
                        <uni-icons
                          type="forward"
                          size="13"
                          color="#29B7A3"
                        ></uni-icons>
                      </view>
                    </view>
                    <view class="session-status-view">
                      <view style="width: 20px" />
                    </view>
                  </view>
                </view>
              </view>
            </view>
            <!-- 自定义 - 处方单 -->
            <view
              class="session-custom-cell"
              v-else-if="
                message.messageType == 'custom' && message.customType == 11
              "
            >
              <view class="time-view" v-if="message.showTime">
                <text>{{ message.dateTime }}</text>
              </view>
              <view
                class="session-body-view"
                :style="{
                  'flex-direction': message.isOutgoingMsg
                    ? 'row-reverse'
                    : 'row',
                }"
              >
                <MpAvatar :src="message.senderImage"></MpAvatar>
                <view
                  class="session-content-view"
                  :style="{
                    'align-items': message.isOutgoingMsg
                      ? 'flex-end'
                      : 'flex-start',
                  }"
                >
                  <text class="name"
                    ><text class="tag-text" v-if="message.isShowTag"
                      >【{{ message.tag }}】</text
                    >{{ message.senderName }}</text
                  >
                  <view
                    class="session-content-body-view"
                    :style="{
                      'flex-direction': message.isOutgoingMsg
                        ? 'row-reverse'
                        : 'row',
                    }"
                  >
                    <view
                      class="session-alter-view"
                      hover-class="hover-class"
                      hover-stay-time="70"
                      @tap.stop="onCheckPrescription(message)"
                    >
                      <view
                        class="session-alter-header-view"
                        style="background-color: #ff9900"
                      >
                        <text>治疗方案</text>
                      </view>
                      <view class="session-center-view">
                        <text class="custom-text">点击查看</text>
                      </view>
                    </view>
                    <view class="session-status-view">
                      <view style="width: 20px" />
                    </view>
                  </view>
                </view>
              </view>
            </view>
            <!-- 自定义 - 患者发送的筛查结果消息 -->
            <view
              class="session-custom-cell"
              v-else-if="
                message.messageType == 'custom' && message.customType == 15
              "
            >
              <view class="time-view" v-if="message.showTime">
                <text>{{ message.dateTime }}</text>
              </view>
              <view
                class="session-body-view"
                :style="{
                  'flex-direction': message.isOutgoingMsg
                    ? 'row-reverse'
                    : 'row',
                }"
              >
                <MpAvatar :src="message.senderImage"></MpAvatar>
                <view
                  class="session-content-view"
                  :style="{
                    'align-items': message.isOutgoingMsg
                      ? 'flex-end'
                      : 'flex-start',
                  }"
                >
                  <text class="name"
                    ><text class="tag-text" v-if="message.isShowTag"
                      >【{{ message.tag }}】</text
                    >{{ message.senderName }}</text
                  >
                  <view
                    class="session-content-body-view"
                    :style="{
                      'flex-direction': message.isOutgoingMsg
                        ? 'row-reverse'
                        : 'row',
                    }"
                  >
                    <view
                      class="session-alter-view"
                      hover-class="hover-class"
                      hover-stay-time="70"
                      @tap.stop="onCheckRiskResult(message)"
                    >
                      <view class="session-alter-header-view">
                        <text>筛查结果</text>
                      </view>
                      <text class="custom-text"
                        >老年运动、认知功能障碍风险解读</text
                      >
                      <view class="consult-description-more-view">
                        <text class="tag-text" style="font-size: 16px"
                          >查看更多</text
                        >
                        <uni-icons
                          type="forward"
                          size="13"
                          color="#29B7A3"
                        ></uni-icons>
                      </view>
                    </view>
                    <view class="session-status-view">
                      <view style="width: 20px" />
                    </view>
                  </view>
                </view>
              </view>
            </view>
            <!-- 自定义 - 医生发送的风险评估方案消息 -->
            <view
              class="session-custom-cell"
              v-else-if="
                message.messageType == 'custom' && message.customType == 16
              "
            >
              <view class="time-view" v-if="message.showTime">
                <text>{{ message.dateTime }}</text>
              </view>
              <view
                class="session-body-view"
                :style="{
                  'flex-direction': message.isOutgoingMsg
                    ? 'row-reverse'
                    : 'row',
                }"
              >
                <MpAvatar :src="message.senderImage"></MpAvatar>
                <view
                  class="session-content-view"
                  :style="{
                    'align-items': message.isOutgoingMsg
                      ? 'flex-end'
                      : 'flex-start',
                  }"
                >
                  <text class="name"
                    ><text class="tag-text" v-if="message.isShowTag"
                      >【{{ message.tag }}】</text
                    >{{ message.senderName }}</text
                  >
                  <view
                    class="session-content-body-view"
                    :style="{
                      'flex-direction': message.isOutgoingMsg
                        ? 'row-reverse'
                        : 'row',
                    }"
                  >
                    <view
                      class="evaluation-plan-alter-view"
                      hover-class="hover-class"
                      hover-stay-time="70"
                      @tap.stop="onCheckRiskEvaluationPlan(message)"
                    >
                      <image
                        src="/subPackChat/static/risk_evaluation_plan.png"
                        style="z-index: 0"
                      >
                      </image>
                      <text
                        class="evaluation-plan-alter-title"
                        style="z-index: 1"
                        >评估方案</text
                      >
                      <text
                        class="evaluation-plan-alter-content"
                        style="z-index: 1; margin-right: 200rpx"
                        >老年运动、认知功能障碍评估</text
                      >
                      <text
                        class="evaluation-plan-alter-submit"
                        style="z-index: 1"
                        >点击评估</text
                      >
                    </view>
                    <view class="session-status-view">
                      <view style="width: 20px" />
                    </view>
                  </view>
                </view>
              </view>
            </view>
            <!-- 自定义 - 人员发送富文本类型消息(@) -->
            <view
              class="session-custom-cell"
              v-else-if="message.messageType == '@AT&'"
            >
              <view class="time-view" v-if="message.showTime">
                <text>{{ message.dateTime }}</text>
              </view>
              <view
                class="session-body-view"
                :style="{
                  'flex-direction': message.isOutgoingMsg
                    ? 'row-reverse'
                    : 'row',
                }"
              >
                <MpAvatar :src="message.senderImage"></MpAvatar>
                <view
                  class="session-content-view"
                  :style="{
                    'align-items': message.isOutgoingMsg
                      ? 'flex-end'
                      : 'flex-start',
                  }"
                >
                  <text class="name"
                    ><text class="tag-text" v-if="message.isShowTag"
                      >【{{ message.tag }}】</text
                    >{{ message.senderName }}</text
                  >
                  <view
                    class="session-content-body-view"
                    :style="{
                      'flex-direction': message.isOutgoingMsg
                        ? 'row-reverse'
                        : 'row',
                    }"
                  >
                    <view
                      :class="
                        message.isOutgoingMsg
                          ? 'session-custom-text-sender-cell'
                          : 'session-custom-text-receiver-cell'
                      "
                    >
                      <rich-text :nodes="message.nodes"></rich-text>
                    </view>
                    <view
                      class="session-status-view"
                      @tap.stop="onReTrySend(message)"
                    >
                      <u-loading-icon
                        color="#000000"
                        size="20"
                        v-if="message.deliveryState == 1"
                      />
                      <uni-icons
                        type="info-filled"
                        color="red"
                        size="20"
                        v-else-if="message.deliveryState == 0"
                      />
                      <view style="width: 20px" v-else />
                    </view>
                  </view>
                </view>
              </view>
            </view>
            <!-- 系统 - 居家训练方案每日打卡提醒消息 -->
            <view
              class="session-system-cell"
              v-else-if="
                message.messageType == 'custom' && message.customType == 9
              "
            >
              <view class="session-system-tip-view">
                <view class="time-view" v-if="message.showTime">
                  <text>{{ message.dateTime }}</text>
                </view>
                <text class="session-system-tip-view-title">{{
                  message.customBody.title
                }}</text>
                <text class="session-system-tip-view-content">
                  <text class="tag-text-bold"
                    >{{ message.customBody.atNames }} </text
                  >{{ message.customBody.message }}
                </text>
              </view>
            </view>
            <!-- 系统 - 训练方案即将结束通知消息 -->
            <view
              class="session-system-cell"
              v-else-if="
                message.messageType == 'custom' && message.customType == 8
              "
            >
              <view class="session-system-tip-view">
                <view class="time-view" v-if="message.showTime">
                  <text>{{ message.dateTime }}</text>
                </view>
                <text class="session-system-tip-view-title">{{
                  message.customBody.title
                }}</text>
                <text class="session-system-tip-view-content">
                  <text class="tag-text-bold"
                    >{{ message.customBody.atNames }}
                  </text>
                  {{ message.customBody.message }}
                </text>
                <view
                  class="submit-button"
                  hover-class="hover-class"
                  hover-stay-time="70"
                  @click="onApplyForRenewalScheme"
                >
                  <text>延续治疗</text>
                </view>
              </view>
            </view>
            <!-- 系统 - 医生更新病历通知消息 -->
            <view
              class="session-system-cell"
              v-else-if="
                message.messageType == 'custom' && message.customType == 12
              "
              @tap.stop="onOpenMedicalRecordPage(message.customBody.consultId)"
            >
              <view class="time-view" v-if="message.showTime">
                <text>{{ message.dateTime }}</text>
              </view>
              <text class="session-system-cell-text">
                医生更新了<text class="tag-text">电子病历</text>
              </text>
            </view>
            <!-- 系统 - 医生更新病历通知消息 -->
            <view
              class="session-system-cell"
              v-else-if="message.messageType == 'tip'"
            >
              <view class="time-view" v-if="message.showTime">
                <text>{{ message.dateTime }}</text>
              </view>
              <text class="session-system-cell-text">{{ message.text }}</text>
            </view>
            <!-- 系统 - 发起了视频通话 -->
            <view
              class="session-system-cell"
              v-else-if="message.messageType == 'invite'"
            >
              <view class="time-view" v-if="message.showTime">
                <text>{{ message.dateTime }}</text>
              </view>
              <text class="session-system-cell-text">发起了视频通话</text>
            </view>
            <!-- 不兼容的消息类型 -->
            <view class="session-system-cell" v-else>
              <view class="time-view" v-if="message.showTime">
                <text>{{ message.dateTime }}</text>
              </view>
              <text class="session-system-cell-text">不兼容的消息类型</text>
            </view>
          </u-list-item>

          <!-- footer -->
          <u-list-item>
            <view style="height: 96px" v-if="isShowServiceEvaluation"></view>
            <view style="height: 32rpx" v-else></view>
          </u-list-item>
        </u-list>
      </view>

      <!-- 底部部分 -->
      <view
        class="footer-container"
        id="footer"
        :style="{ 'padding-bottom': paddingBottom + 'px' }"
        v-if="state != 3 && sendable"
      >
        <!-- 输入Bar -->
        <view class="footer-input-view">
          <!-- 常用语按钮 -->
          <view
            class="footer-send-button"
            hover-class="hover-class"
            hover-stay-time="70"
            @click="onClickedPhrasesButton"
          >
            常用语
          </view>

          <!-- 输入框 -->
          <view
            class="footer-textarea-view"
            :class="{ 'is-audio': audioInput }"
          >
            <view
              class="audio-input"
              :class="{ 'is-recording': recordState === 'Recording' }"
              v-if="audioInput"
              @touchstart="handleTouchStart"
              @touchend="handleTouchEnd"
              @touchmove="handleTouchMove"
              @touchcancel="handleTouchCancel"
            >
              <!-- 这里不能通过v-if 移除 text。如果触摸开始时按住的区域是text,会无法识别到之后的触摸事件 -->
              <text v-show="recordState !== 'Recording'">按住说话</text>
              <view
                class="audio-record-controls"
                v-if="recordState === 'Recording'"
              >
                <view class="voice-waves" :class="{ 'is-playing': true }">
                  <div class="wave" v-for="n in 13" :key="n"></div>
                </view>
                <view class="record-tip">{{
                  needCancel ? '松手取消' : '松手发送'
                }}</view>
                <!--  <view class="cancel button">X</view>
                <view class="text button">文</view> -->
                <view class="audio-wrapper">
                  <view class="audio" :class="{ 'is-cancel': needCancel }">
                    <uni-icons type="mic" size="40"></uni-icons>
                  </view>
                </view>
              </view>
            </view>
            <textarea
              style="width: 100%"
              :style="{ display: audioInput ? 'none' : 'unset' }"
              :focus="isFocus"
              :value="inputContent"
              border="none"
              maxlength="-1"
              confirm-hold="true"
              confirm-type="send"
              placeholder="请输入"
              :disabled="inputDisabled"
              :disable-default-padding="true"
              :adjust-position="false"
              :show-confirm-bar="false"
              auto-height="true"
              auto-blur="true"
              fixed="true"
              :hold-keyboard="true"
              @click="onInputClicked"
              @focus="onInputFocus"
              @blur="onInputBlur"
              @input="onInputMessage"
              @linechange="onInputLineChange"
              @keyboardheightchange="onKeyboardHeightChange"
              @confirm="onClickedKeyboardSendButton"
            />
            <image
              v-if="!audioInput"
              class="footer-icon-image"
              src="/subPackChat/static/microphone.png"
              mode="aspectFit"
              @click="switchAudio"
            ></image>
            <image
              v-else
              class="footer-icon-image"
              src="/subPackChat/static/keyboard.png"
              mode="aspectFit"
              @click="switchAudio"
            ></image>
          </view>

          <!-- 发送按钮 -->
          <view
            class="footer-send-button"
            hover-class="hover-class"
            hover-stay-time="70"
            @click="onClickedSend"
            v-if="inputContent && inputContent.length > 0"
          >
            发送
          </view>
          <!-- 加号按钮 -->
          <view
            style="padding: 0 12px"
            hover-class="hover-class"
            hover-stay-time="70"
            @click="onClickedAdd"
            v-else
          >
            <image
              class="footer-icon-image"
              style="vertical-align: middle"
              src="/subPackChat/static/add.png"
              mode="aspectFit"
            ></image>
          </view>
        </view>
        <!-- 选择发送菜单栏 -->
        <scroll-view
          scroll-y="true"
          class="footer-menu-view"
          :style="{
            height: Math.ceil(inputMenuItemData.length / 4) * 210 + 'rpx',
          }"
          v-show="isShowInputMenu"
        >
          <view
            class="footer-menu-item"
            hover-class="hover-class"
            hover-stay-time="70"
            v-for="(item, index) in inputMenuItemData"
            :key="item.title"
            @click="onClickedMenuItem(item)"
          >
            <view class="footer-menu-item-image">
              <image
                :style="{
                  width: item.width + 'rpx',
                  height: item.height + 'rpx',
                }"
                :src="item.image"
                mode="aspectFit"
              ></image>
            </view>
            <text class="footer-menu-item-text">{{ item.title }}</text>
          </view>
        </scroll-view>
        <!-- 选择发送常用语-->
        <view class="footer-phrases-view" v-show="isShowPhrasesMenu">
          <!-- 常用语列表 -->
          <u-list
            customStyle="height:200px"
            v-if="phrasesList && phrasesList.length > 0"
          >
            <u-list-item v-for="(item, index) in phrasesList" :key="item.Id">
              <view
                class="footer-phrases-item"
                @click="onSelectPhrases(item.Text)"
              >
                <text class="footer-phrases-item-text">{{
                  `${item.Text}`.replaceAll('\n', '；')
                }}</text>
                <view
                  style="padding: 0 24rpx"
                  @tap.stop="onSendPhrases(item.Text)"
                >
                  <image
                    style="width: 17px; height: 20px"
                    src="/subPackChat/static/phrase-send.png"
                  >
                  </image>
                </view>
              </view>
            </u-list-item>
          </u-list>
          <!-- 无数据 -->
          <view v-else style="height: 200px">
            <u-empty
              mode="data"
              icon="http://cdn.uviewui.com/uview/empty/data.png"
              text="暂无可用常用语"
            >
            </u-empty>
          </view>
        </view>
      </view>
    </view>

    <!-- 固定在右侧的标签 -->
    <view
      class="navigator-item-tag"
      hover-class="hover-class"
      hover-stay-time="70"
      @click="onOpenMedicalRecordPage(consultRecord.Consult.Id)"
      v-if="consultRecord && (state == 2 || state == 3) && isOnlineConsultation"
    >
      <image src="/subPackChat/static/archives.png"></image>
      <text>门诊病历</text>
    </view>
    <view
      class="navigator-item-tag"
      hover-class="hover-class"
      hover-stay-time="70"
      @click="onOpenSessionGroupPage"
      v-if="
        session &&
        Object.keys(session).length > 0 &&
        (!consultRecord || Object.keys(consultRecord).length == 0)
      "
    >
      <image src="/subPackChat/static/users.png"></image>
      <text>群组信息</text>
    </view>

    <!-- 患者评价入口 -->
    <view v-if="isShowServiceEvaluation">
      <view class="bomm-btn-style" style="height: 100px"></view>
      <u-button
        type="success"
        @click="onServiceEvaluation"
        shape="circle"
        text="服务评价"
        customStyle="width:90%;bottom: 30px;position: fixed; left: 50%;transform: translateX(-50%);fontSize:16px;z-index:99"
      >
      </u-button>
    </view>

    <!-- 联系人弹框 -->
    <u-popup
      :show="isShowContactsSheetPop"
      round="10"
      mode="bottom"
      :closeOnClickOverlay="true"
      @close="onCloseContactsPop"
    >
      <view class="contacts-pop-list-view">
        <u-list>
          <u-list-item v-if="canAtUserList && canAtUserList.length > 0">
            <u-cell title="所有人" @click="onSelectAtUser(0)"> </u-cell>
          </u-list-item>
          <u-list-item v-for="(user, index) in canAtUserList" :key="user.Id">
            <u-cell :title="user.Name" @click="onSelectAtUser(index + 1)">
            </u-cell>
          </u-list-item>
        </u-list>
      </view>
    </u-popup>

    <!-- 语音转文字录音弹层 -->
    <u-modal
      :show="isShowRecordPop"
      title="温馨提示"
      @confirm="confirmSplk"
      @cancel="cancelSplk"
      :showCancelButton="true"
      confirmText="说完了"
      confirmColor="#29B7A3"
    >
      <SpeakCss slot="default" style="padding: 30px" />
    </u-modal>

    <!-- 查看电子处方弹层 -->
    <u-modal
      :show="isShowPrescriptionPop"
      title="温馨提示"
      :content="prescriptionPopPromptContent"
      showCancelButton="true"
      @confirm="onOpenOnlinePrescription"
      @cancel="isShowPrescriptionPop = false"
    >
    </u-modal>
    <u-popup
      :show="showPrivacyPopup"
      :closeOnClickOverlay="false"
      :round="10"
      mode="bottom"
      @close="close"
    >
      <view style="padding: 40rpx">
        <p
          style="
            text-align: center;
            margin-bottom: 40rpx;
            font-size: 20px;
            font-weight: 700;
          "
        >
          提示
        </p>
        <text>
          在你使用服务之前，请仔细阅读<text
            @click="onClickPrivacyPopupTitle()"
            style="color: #29b7a3"
            >{{ PrivacyPopupTitle }}</text
          >。如果你同意{{ PrivacyPopupTitle }}，请点击“同意”开始使用。
        </text>
        <view class="display-style" style="margin-top: 40rpx">
          <u-button
            text="拒绝"
            @click="handleRefusePrivacyAuthorization()"
          ></u-button>
          <button
            id="agree-btn"
            class="btn-agree"
            open-type="agreePrivacyAuthorization"
            @agreeprivacyauthorization="handleAgreePrivacyAuthorization()"
          >
            同意
          </button>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { getRoomInfo } from '@/api/im.js';
import { getConsultRecordInfo, setRead } from '@/api/consult.js';
import { getIMRoomRelationshipInfo } from '@/api/passport.js';
import {
  arrayNotEmpty,
  arrayValid,
  dataIsValid,
  numberValid,
  objNotEmpty,
  stringNotEmpty,
  stringValid,
  escapeRegExp,
  checkMicAuth,
} from '@/utils/utils';
import { getFormateDateTime } from '@/utils/validate.js';
import { ConsultClientEvent } from '@/utils/eventKeys.js';
import { IMManager, IMMessage, IMMessageObject, IMMessageType } from 'kfx-im';
import {
  applyContinueRx,
  checkApplyContinuePreState,
  getDoctorSendGaugeByGaugeSign,
  getGaugeById,
} from '../api/training';

const app = getApp();
const imManager = IMManager.instance;
const chatCacheManager = ChatCacheManager.instance();

import MpAvatar from '@/components/mp-avatar/mp-avatar.vue';
import MpImage from '@/components/mp-image/mp-image.vue';
import UserCacheStore from '@/libs/util/user_cache_store';
import AudioMessage from './AudioMessage.vue';
// import {
// 	genTestUserSig
// } from './libs/GenerateTestUserSig.js';

import SpeakCss from '@/components/mp-speakCss/speakCss.vue';
import { findPhrases } from '@/api/content';
import ChatCacheManager from '@/libs/util/chat_cache_manager';
import SessionState from '@/libs/util/session_state';

const plugin = requirePlugin('WechatSI');
const manager = plugin.getRecordRecognitionManager();
export default {
  components: {
    MpAvatar,
    MpImage,
    SpeakCss,
    AudioMessage,
    // tuicallkit
  },
  data() {
    // const userSing = genTestUserSig(app.globalData.userInfo.Id).userSig
    return {
      // config: {
      // 	sdkAppID: 1400780428, // 开通实时音视频服务创建应用后分配的 SDKAppID
      // 	userID: app.globalData.userInfo.Id, // 用户 ID，可以由您的帐号系统指定
      // 	userSig: userSing // 身份签名，相当于登录密码的作用
      // },
      navTitle: '会话',
      navHeight: 64,
      // 点击返回按钮，需要返回的路径，可为空
      backPath: null,
      // 通过点击返回按钮来返回
      isClickedLeftBtnToBack: false,
      // 当前页是否可视
      pageDidAppear: false,
      /**
       * 是否是“问诊流程”，且是发起问诊成功后进入本会话
       * 注意：由于后端状态刷新问题，发起问诊进入会话界面，可能获取到的问诊状态错误，故特此标记
       */
      fromAddInquiry: false,

      // ------------------------ 组件宽高 ------------------------

      // 整个屏幕高度，单位px
      screenHeight: 0,
      // 屏幕有效宽度，单位px
      safeWidth: 0,
      // 滚动视图列表的高度，单位px
      scrollHeight: 0,
      // 底部输入框+菜单栏部分的高度(不包含bottom)，单位px
      footerHeight: 0,
      // 整个footer距离底部距离(bottom)，单位px
      paddingBottom: 0,
      // 安全底部，单位px
      safeBottom: 0,
      // 键盘弹出高度，单位px
      keyboardHeight: 0,

      // ------------------------ 菜单栏相关 ------------------------

      // 是否将要显示footer中弹起菜单栏，此标记用于标记菜单显示前的一步动作，利于在键盘变化中监听
      willShowInputMenu: false,
      // 是否显示footer中弹起菜单栏
      isShowInputMenu: false,
      // 菜单栏内容
      inputMenuItemData: [
        {
          id: 'iamge',
          title: '拍照',
          image: '/subPackChat/static/camera.png',
          width: 60,
          height: 69,
        },
        {
          id: 'video',
          title: '拍视频',
          image: '/subPackChat/static/video.png',
          width: 64,
          height: 64,
        },
        {
          id: 'alumn',
          title: '相册',
          image: '/subPackChat/static/photo.png',
          width: 64,
          height: 64,
        },
        {
          id: 'audio',
          title: '语音转文',
          image: '/subPackChat/static/microphone.png',
          width: 64,
          height: 64,
        },
      ],

      // ----------------------- 常用语组件相关 -----------------------

      // 是否将要显示常用语菜单栏
      willShowPhrasesMenu: false,
      // 是否显示footer中弹起菜单栏
      isShowPhrasesMenu: false,
      // 常用语数据
      phrasesList: [],

      // --------------------------- 输入框 --------------------------

      // 输入的内容
      inputContent: '',
      // 输入框是否聚焦
      isFocus: false,
      // 输入框是否禁用
      inputDisabled: '',
      /** 录音状态
         @type "Idle" | "Recording" | "Cancel" | "Stop"
         */
      recordState: 'Idle',
      /** 是否语言输入*/
      audioInput: false,
      needCancel: false,

      // ------------------------- 滚动相关 -------------------------

      // 滚动聊天列表视图是否刷新(加载前值为loadmore，加载中为loading，没有数据为nomore)
      loadStatus: 'loadmore',
      // 滚动到最新位置
      scrollTop: 0,
      old: {
        scrollTop: 100000,
      },
      // 是否需要滚动到最新位置，默认是需要滚动到底部
      shouldScrollToBottom: true,

      // ------------------------- 弹层相关 -------------------------

      // 是否需要显示@人员弹出层
      isShowContactsSheetPop: false,
      // 是否需要显示语音转文字弹层
      isShowRecordPop: false,
      // 是否显示查看电子处方弹层
      isShowPrescriptionPop: false,
      // 查看电子处方弹层 - 需要展示的信息
      prescriptionPopPromptContent: null,
      // 查看电子处方弹层 - 处方id
      prescriptionId: null,

      // ------------------------- 会话相关 -------------------------

      // 是否是问诊群
      isConsultRoom: false,
      // 是否是居家患者群
      isHomePatientRoom: false,
      // 问诊流程 - 是否是医生在线问诊(或者治疗师咨询服务)
      isOnlineConsultation: false,

      // 当前会话
      session: {},
      // 会话房间号
      roomId: null,
      // 会话的消息列表数据
      messageList: [],
      // 群聊成员标签 格式：{userId: tag}
      relationship: {},
      // @人员列表
      canAtUserList: [],

      // 问诊会话 - 问诊详情
      consultRecord: {},
      // 问诊会话 - 当前问诊会话状态(0:未知 1:待接诊 2:咨询中 3:已结束)
      state: 0,
      // 问诊会话 - 问诊中倒计时计时器
      downCountTimer: null,
      // 问诊会话 - 问诊中倒计时
      downCount: 0,

      // 问诊id
      consultId: '',
      // 问诊状态变化，回调函数
      consultStateChangedHandle: null,

      // 诊后方案id
      programId: '',

      // 视频会话
      TUICalling: null,

      // 是否显示服务评价
      isShowServiceEvaluation: false,
      // 是否可以发送
      sendable: true,
    };
  },
  onLoad: async function (option) {
    this.saveWXCallBack();
    // 初始化语音
    this.initRecord();
    // 点击返回按钮，需要返回的路径，可为空
    this.backPath = option.backPath;
    // 传入消息Id，代表打开界面后定位到这条消息，可为空
    let messageId = option.messageId;
    // 传入问诊Id，问诊会话，可为空，roomId和consultId二者选一
    let consultId = option.consultId;
    this.consultId = option.consultId;
    // 传入房间Id，居家会话，可为空，roomId和consultId二选一
    this.roomId = option.roomId;
    // 传入诊后的方案id
    this.programId = option.programId;
    // 是否是通过问诊成功后进入本会话，可为空
    this.fromAddInquiry = option.fromAddInquiry == 'true';
    this.sendable = option.sendable != 'false';

    // 居家指导会话，问诊完成，开始执行方案，创建的训练方案，居家指导会话
    if (stringNotEmpty(this.roomId)) {
      uni.showLoading({
        title: '请求中...',
      });
      await this.loadSessionInfo();
      await this.loadData(messageId);

      let title = dataIsValid(this.session) ? this.session.name : '会话';
      this.navTitle = title;
    }

    // 问诊会话，通过问诊流程创建的会话
    if (stringNotEmpty(consultId)) {
      uni.showLoading({
        title: '请求中...',
      });
      await this.loadConsultRecordInfo(consultId);
      await this.loadData(messageId);

      let docUserName = this.consultRecord?.Consult?.DocUserName;
      let title = stringNotEmpty(docUserName) ? docUserName : '咨询会话';
      this.navTitle = title;
    }

    // 问诊状态改变
    this.consultStateChangedHandle = (e) => {
      console.debug('问诊状态发生变化', e.state);
      if (e.consultId == consultId) {
        if (e.state != this.state) {
          this.changeConsultState(e.state);
        }
      }
    };
    uni.$on(
      ConsultClientEvent.consultStateChanged,
      this.consultStateChangedHandle
    );
    this.share.title = '点击立刻咨询';
    this.share.path = '/pages/plan/Session';
    this.share.imageUrl =
      'https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/static/frontend/mp-patient/postDiagnosis-session.png';
    this.isUseDefaultParams = false;
  },
  onReady() {
    uni.getSystemInfo({
      success: (res) => {
        this.safeBottom = res.safeAreaInsets.bottom;
        this.screenHeight = res.screenHeight;
        this.safeWidth = res.windowWidth;
        this.paddingBottom = this.safeBottom;
        this.navHeight = 44 + res.statusBarHeight;
      },
    });
  },
  onShow() {
    // let that = this;
    // this.TUICalling = this.$refs.TUICallKit;
    // this.TUICalling.init();
    this.pageDidAppear = true;
  },
  // 页面隐藏，由前台退到后台
  onHide() {
    this.pageDidAppear = false;

    // 缓存聊天内容
    if (this.inputContent) {
      chatCacheManager.setChat(this.session.sessionId, this.inputContent);
    }
  },
  // 页面卸载
  onUnload() {
    uni.$off(
      ConsultClientEvent.consultStateChanged,
      this.consultStateChangedHandle
    );
    imManager.removeMessageListListener(this.messageListListener);
    // 退出正在进行中的会话
    imManager.exitChat(this.session.sessionId);

    // 缓存聊天内容
    if (this.inputContent) {
      chatCacheManager.setChat(this.session.sessionId, this.inputContent);
    }

    // 正处于当前页，侧滑返回则特殊处理
    if (!this.isClickedLeftBtnToBack && this.pageDidAppear) {
      this.popBefore();
    }
    // this.TUICalling.destroyed();
  },
  methods: {
    /** 重新加载会话信息 */
    async onReload() {
      await this.loadSessionInfo();
      this.loadData(this.messageId);
    },

    handleUserClick(user) {
      console.log('user', user);
      let userCacheStore = UserCacheStore.instance();
      let item = userCacheStore.getUser(user.senderId);
      console.log('item', item);
      const Roles = item.UserRoleDicts;
      if (!Roles || !Roles.length) return;
      const isDoctor = Roles.some(
        (s) => s === 'doctor' || s === 'nurse' || s === 'therapist'
      );
      if (isDoctor) {
        uni.navigateTo({
          url: '/subPackIndex/docDetail?docId=' + item.Id,
        });
      }
    },

    /** 语音和键盘切换 */
    switchAudio() {
      this.audioInput = !this.audioInput;
      if (this.audioInput) {
        // 取消事件
        this.onCloseMenuOrTextarea();
        checkMicAuth();
      }
    },
    // 判断触摸点是否在元素区域内
    isTouchInElement(touchX, touchY, elementRect) {
      // return touchX >= elementRect.left && touchX <= elementRect.left + elementRect.width &&
      //   touchY >= elementRect.top && touchY <= elementRect.top + elementRect.height;
      return this.isTouchInCircle(
        touchX,
        touchY,
        elementRect.width / 2,
        elementRect.top + 500,
        500
      );
    },
    isTouchInCircle(touchX, touchY, cx, cy, r_max) {
      // console.log(touchX, touchY, cx, cy);
      // 计算触摸点到圆心的距离
      let dx = touchX - cx;
      let dy = touchY - cy;
      let r = Math.sqrt(dx * dx + dy * dy); // 触摸点到圆心的距离

      // console.log('r', r, r_max);
      // 判断距离是否在圆的半径范围内
      return r <= r_max;
    },
    getAudioWrapperRect(once = false) {
      if (this.audioWrapperRect) return;
      if (!this.query) {
        this.query = uni.createSelectorQuery().in(this);
      }
      setTimeout(() => {
        this.query
          .select('.audio-wrapper')
          .boundingClientRect((data) => {
            if (data) {
              console.log('得到布局位置信息' + JSON.stringify(data));
              console.log('节点离页面顶部的距离为' + data.top);
              this.audioWrapperRect = data;
            } else {
              console.log('未获取到布局信息');
              if (!once) {
                console.log('再试一次');
                this.getAudioWrapperRect(true);
              }
            }
          })
          .exec();
      }, 50);
    },
    // getAudioCancelRect() {
    //   if (this.audioCancelRect) return;
    //   if (!this.query) {
    //     this.query = uni.createSelectorQuery().in(this);
    //   }
    //   this.query
    //     .select(".audio .audio-wrapper")
    //     .boundingClientRect((data) => {
    //       if (data) {
    //         console.log("得到布局位置信息" + JSON.stringify(data));
    //         console.log("节点离页面顶部的距离为" + data.top);
    //         this.audioCancelRect = data;
    //       }
    //     })
    //     .exec();
    // },
    handleTouchStart(e) {
      console.log('touchStart', e);
      this.startRecord().then(() => {
        // 等录音状态变更后再获取元素布局信息，提升稳定性
        this.getAudioWrapperRect();
      });
      this.needCancel = false;
    },
    handleTouchMove(e) {
      // console.log('touchMove', e);
      this.getAudioWrapperRect();

      if (this.audioWrapperRect) {
        const touch = e.touches[0];
        const touchX = touch.clientX;
        const touchY = touch.clientY;
        const isIn = this.isTouchInElement(
          touchX,
          touchY,
          this.audioWrapperRect
        );
        if (isIn) {
          if (this.needCancel) {
            this.needCancel = false;
          }
        } else {
          if (!this.needCancel) {
            this.needCancel = true;
          }
          if (touchX < this.audioWrapperRect.width / 2) {
            console.log('取消');
          } else {
            console.log('转文本');
          }
        }
      }
    },
    handleTouchEnd(e) {
      console.log('touchEnd', e);
      if (this.audioWrapperRect) {
        const touch = e.changedTouches[0]; // end 事件中 touchs 为空
        const touchX = touch.clientX;
        const touchY = touch.clientY;
        const isIn = this.isTouchInElement(
          touchX,
          touchY,
          this.audioWrapperRect
        );
        if (isIn) {
          console.log('在里面,发送');
          this.stopRecord();
        } else {
          console.log('不在里面，取消');
          this.cancelRecord();
        }
      } else {
        console.log('没有元素尺寸，取消');
        this.cancelRecord();
      }
    },
    handleTouchCancel(e) {
      console.log('touchCancel', e);
      this.cancelRecord();
    },
    async startRecord() {
      this.checkMicFlag = true;
      const auth = await checkMicAuth();
      // 如果在检测麦克风权限的时候停止或取消了录制，则不再继续
      if (!this.checkMicFlag) return;
      this.checkMicFlag = false;

      if (!auth) return;

      if (this.recordState != 'Idle') return;

      this.recordState = 'Recording';
      const recorder = uni.getRecorderManager();
      recorder.onStart((r) => {
        console.log('录音开始了', r);
      });
      recorder.onStop((r) => {
        // 录音时间到了1分钟会直接停止，此时 recordState == 'Recording'
        if (this.recordState === 'Recording') {
          this.recordState = this.needCancel ? 'Cancel' : 'Stop';
        }
        if (this.recordState === 'Stop') {
          const { tempFilePath, duration, size } = r;
          this.sendAudioMessage(tempFilePath, duration);
          console.log('录音停止了', r);
        } else {
          console.log('录音取消了');
        }
        this.recordState = 'Idle';
      });
      recorder.onError((e) => {
        this.recordState = 'Idle';
        console.error('录音错误了', e);
        wx.showToast({
          icon: 'none',
          title: `录音错误(${e?.errMsg})`,
        });
      });

      recorder.start();
      console.log('开始语音输入');
    },
    stopRecord() {
      this.checkMicFlag = false;
      if (this.recordState != 'Recording') return;

      this.recordState = 'Stop';
      const recorder = uni.getRecorderManager();
      recorder.stop();

      console.log('结束录音');
    },
    cancelRecord() {
      this.checkMicFlag = false;
      if (this.recordState != 'Recording') return;

      this.recordState = 'Cancel';
      const recorder = uni.getRecorderManager();
      recorder.stop();
      console.log('取消录音');
    },

    sendAudioMessage(audioFilePath, duration) {
      if (audioFilePath && duration) {
        const message = IMMessage.audio(
          IMMessageObject.audio(audioFilePath, duration)
        );
        this.sendMessage(message);
      } else {
        console.warn('音频文件路径或时长为空', {
          audioFilePath,
          duration,
        });
      }
    },

    // ------------------------ 语音识别 ------------------------

    // 初始化录音服务
    initRecord() {
      //有新的识别内容返回，则会调用此事件
      manager.onRecognize = (res) => {
        let text = res.result;
        this.inputContent = this.inputContent + text;
      };
      // 识别结束事件
      manager.onStop = (res) => {
        let text = res.result;
        if (text == '') {
          console.log('没有说话');
          return;
        }
        this.inputContent = this.inputContent + text;
      };
    },
    // 停止录音
    endStreamRecord() {
      console.log('=======结束====');
      manager.stop();
    },
    // 获取录音流数据
    async streamRecord() {
      const auth = await checkMicAuth(false);
      if (!auth) return;

      // 取消事件
      this.onCloseMenuOrTextarea();
      this.isShowRecordPop = true;
      console.log('=======开始====');
      manager.start({
        lang: 'zh_CN',
      });
    },
    // 录音弹层 - 确认
    confirmSplk() {
      this.endStreamRecord();
      this.isShowRecordPop = false;
    },
    // 录音弹层 - 取消
    cancelSplk() {
      this.isShowRecordPop = false;
      manager.stop();
    },

    // ------------------------ 返回 ------------------------

    // 点击返回按钮
    onLeftClick() {
      this.isClickedLeftBtnToBack = true;
      this.popBefore();
    },
    // 返回上一级
    popBefore() {
      if (stringNotEmpty(this.backPath)) {
        let backPath = this.backPath.substring(1);
        let pages = getCurrentPages();
        for (let index in pages) {
          let route = pages[index].route;
          if (backPath == route) {
            console.log('找到', route);
            uni.navigateBack({
              delta: pages.length - index,
            });
            return;
          }
        }

        // 需要跳转的页面不是当前页面栈里面的，主要查看时候是tabBar下的首页
        let paths = this.backPath.split('/');
        if (arrayNotEmpty(paths) && paths.length > 1) {
          let mainPath = paths[1];
          if (mainPath == 'pages') {
            uni.switchTab({
              url: this.backPath,
              success: (res) => {
                // console.log('getCurrentPages()', getCurrentPages())
                // let page = getCurrentPages().pop();
                // if (page == undefined || page == null) return;
                // // setTimeout(() => {
                // // 	uni.$emit('updatePages', 11)
                // // }, 500)
                // console.log('page',page)
                // page.onLoad()
              },
            });
            return;
          }
        }

        if (this.isClickedLeftBtnToBack) {
          uni.navigateBack();
        }
      } else {
        if (this.isClickedLeftBtnToBack) {
          const pages = getCurrentPages();
          if (pages.length > 1) {
            uni.navigateBack();
          } else {
            getApp().showMainPage();
          }
        }
      }
    },

    // ------------------------ 输入框 ------------------------

    // 获取聊天内容
    onInputMessage(e) {
      let value = e.detail.value;
      if (value && value.length > this.inputContent.length) {
        let lastWord = value.charAt(value.length - 1);
        if (lastWord == '@') {
          this.isFocus = false;
          this.isShowContactsSheetPop = true;
          uni.hideKeyboard();
        }
      }

      this.inputContent = value;
    },
    // 点击输入框
    onInputClicked() {
      // 如果此时菜单栏/常用语菜单未收起，则先收起，再弹出键盘
      if (this.isShowInputMenu) {
        this.isShowInputMenu = false;
      }
      if (this.isShowPhrasesMenu) {
        this.isShowPhrasesMenu = false;
      }
    },
    // 输入框开始聚焦，此时键盘已弹出
    onInputFocus(e) {
      this.isFocus = true;
    },
    // 输入框失去焦点，此时键盘已经收起
    onInputBlur(e) {
      this.isFocus = false;
    },
    // 输入框行数变化
    onInputLineChange(e) {
      this.refreshHeight();
    },

    // ------------------------ 键盘相关 ------------------------

    // 点击键盘'发送'按钮
    onClickedKeyboardSendButton(e) {
      this.onSendTextMessage();
    },
    // 键盘高度发生变化
    onKeyboardHeightChange(e) {
      let height = e.detail.height;
      if (height == 0 && (this.willShowInputMenu || this.willShowPhrasesMenu)) {
        // 等键盘收起，将要显示菜单栏/常用语弹层
        if (this.willShowInputMenu) {
          this.willShowInputMenu = false;
          this.isShowInputMenu = true;
        }
        if (this.willShowPhrasesMenu) {
          this.willShowPhrasesMenu = false;
          this.isShowPhrasesMenu = true;
        }
      }

      if (this.keyboardHeight != height) {
        this.keyboardHeight = height;
        this.paddingBottom = height > 0 ? height : this.safeBottom;
        this.refreshHeight();
      }
    },

    // ------------------------ 联系人相关 ------------------------

    // 选择@的人
    onSelectAtUser(index) {
      let userName =
        index == 0 ? '所有人' : this.canAtUserList[index - 1]?.Name;
      this.isShowContactsSheetPop = false;
      var value = this.inputContent.substring(0, this.inputContent.length - 2);
      this.inputContent = value + ' @' + userName + ' ';
      this.isFocus = true;
    },
    // 联系人弹出层关闭
    onCloseContactsPop() {
      this.isShowContactsSheetPop = false;
    },

    // ------------------------ 常用语相关 ------------------------

    // 点击 ‘常用语’ 按钮
    onClickedPhrasesButton() {
      if (this.keyboardHeight == 0) {
        // 没有弹起键盘
        this.isShowInputMenu = false;
        this.isShowPhrasesMenu = !this.isShowPhrasesMenu;
        this.refreshHeight();
      } else {
        // 如果键盘此时是弹起的，则等键盘收下去再显示菜单栏
        this.willShowPhrasesMenu = true;
        this.isFocus = false;
        uni.hideKeyboard();
      }
      if (this.audioInput) {
        this.audioInput = false;
      }
    },
    // 点击选择常用语
    onSelectPhrases(item) {
      this.inputContent = item;
    },
    // 点击直接发送常用语
    onSendPhrases(item) {
      this.isShowPhrasesMenu = false;
      this.refreshHeight();
      this.onSendTextMessage(item);
    },
    // 常用语弹出层关闭
    onClosePhrasesPop() {
      this.isShowPhrasesMenu = false;
    },

    // ------------------------ 菜单栏相关 ------------------------

    // 点击➕按钮
    onClickedAdd() {
      if (this.keyboardHeight == 0) {
        // 没有弹起键盘
        this.isShowPhrasesMenu = false;
        this.isShowInputMenu = !this.isShowInputMenu;
        this.refreshHeight();
      } else {
        // 如果键盘此时是弹起的，则等键盘收下去再显示菜单栏
        this.willShowInputMenu = true;
        this.isFocus = false;
        uni.hideKeyboard();
      }
      if (this.audioInput) {
        this.audioInput = false;
      }
    },
    // 点击‘发送’按钮
    onClickedSend() {
      this.onSendTextMessage();
    },
    // 点击底部菜单栏选项
    onClickedMenuItem(item) {
      switch (item.id) {
        case 'continuation-therapy':
          {
            // 申请续方
            this.onApplyForRenewalScheme();
          }
          break;
        case 'audio':
          {
            this.streamRecord();
          }
          break;
        case 'image':
        case 'video':
        case 'alumn':
          {
            const values = {
              image: 0,
              video: 1,
              alumn: 2,
            };
            // 发送照片/视频
            this.onSendMediaMessage(values[item.id]);
          }
          break;
      }
    },

    // ------------------------ 查看自定义会话内容 ------------------------

    // 查看病情描述
    onCheckConsultDescription(message) {
      console.debug('查看病情描述', message);
      let consultId = message.customBody.consultId;
      uni.navigateTo({
        url: '/subPackIndex/interviewDescription?id=' + consultId,
      });
    },
    // 查看评定量表
    async onCheckAssess(message) {
      console.debug('查看量表', message);
      uni.showLoading({
        title: '正在加载...',
        mask: true,
      });

      let onlyRead = (this.state > 2 && this.isConsultRoom) || false;
      // 获取该量表关联其他模块id
      let relatedId = message.customBody.consultId;
      // 问诊流程中的量表（问诊流程包含两种会话：1：居家 2：问诊）
      let roomType = this.isConsultRoom ? 2 : 1;
      if (!stringNotEmpty(relatedId)) {
        uni.hideLoading();
        uni.showToast({
          title: '获取量表数据错误',
          icon: 'none',
        });
        return;
      }

      // 获取量表最新执行记录
      var lastAssess;
      let assessSign = message.customBody.assessSign;
      let r0 = await getDoctorSendGaugeByGaugeSign(assessSign);
      if (r0.Type != 200) {
        uni.showToast({
          title: r.Content,
          icon: 'none',
        });
        return;
      }
      let assessList = r0.Data.PatEvaluateGauges;
      if (arrayNotEmpty(assessList)) lastAssess = assessList[0];
      if (objNotEmpty(lastAssess)) {
        // 显示量表评估结果
        uni.hideLoading();
        delete lastAssess.PatGaugeDiseaseRelations;
        delete lastAssess.PatGaugeProblems;
        delete lastAssess.PatGaugeResults;
        const obj = lastAssess;
        let url =
          '/subGauge/result?itemInfo=' +
          encodeURIComponent(JSON.stringify(obj)) +
          '&roomType=' +
          roomType +
          '&roomId=' +
          this.session.sessionId;
        if (this.state == 3) {
          url += '&showAgain=false';
        }
        uni.navigateTo({
          url,
        });
      } else {
        // 去评估量表
        let assessId = message.customBody.assessId;
        let rr = await getGaugeById(assessId);
        if (rr.Type != 200) {
          uni.hideLoading();
          uni.showToast({
            title: rr.Content,
            icon: 'none',
          });
          return;
        }
        if (!objNotEmpty(rr.Data)) {
          uni.hideLoading();
          uni.showToast({
            title: '量表不存在',
            icon: 'none',
          });
          return;
        }
        if (rr.Data.IsEnble != true) {
          uni.hideLoading();
          uni.showToast({
            title: '量表未启用，暂不可用',
            icon: 'none',
          });
          return;
        }
        uni.hideLoading();

        var url = `/subGauge/autonomy?id=${assessId}&DctSendSign=${assessSign}&RelatedId=${relatedId}&roomType=${roomType}&roomId=${this.session.sessionId}`;
        if (this.state == 3) {
          url += '&disabled=true';
        }
        uni.navigateTo({
          url: url,
        });
      }
    },
    // 查看处方单
    onCheckPrescription(message) {
      console.debug('查看处方单', message);
      const prescriptionId = message.customBody.prescriptionId;
      uni.navigateTo({
        url: '/subPrescription/detail?id=' + prescriptionId,
      });
    },
    // 查看宣教
    onCheckMission(message) {
      console.debug('查看康复宣教', message);
      const missionId = message.customBody.id;
      uni.navigateTo({
        url: '/subPropaganda/detail?id=' + missionId,
      });
      if (message.customBody.businessId) {
        // 设置宣教已读
        setRead({
          Id: message.customBody.businessId,
        });
      }
    },
    // 查看评估方案
    onCheckEvaluationPlan(message) {
      console.debug('查看评估方案', message);
      uni.navigateTo({
        url: '/subGauge/evaluationList?sign=' + message.customBody.DctSign,
      });
    },
    // 查看风险筛查结果
    onCheckRiskResult(message) {
      console.debug('查看风险筛查结果', message);
      const reportId = message.customBody.reportId;
      if (!reportId) {
        uni.showToast({
          title: '无法获取报告Id数据！',
          icon: 'none',
        });
        return;
      }
      uni.navigateTo({
        url: '/subRiskWarning/result?Id=' + reportId + '&scen=chat',
      });
    },
    // 查看风险评估方案
    onCheckRiskEvaluationPlan(message) {
      console.debug('查看风险评估方案', message);
      const reportId = message.customBody.reportId;
      if (!reportId) {
        uni.showToast({
          title: '无法获取报告Id数据！',
          icon: 'none',
        });
        return;
      }
      uni.navigateTo({
        url: `/subRiskWarning/gaugeList?reportId=${reportId}&consultId=${this.consultId}&programId=${this.programId}&isConsult=${this.isConsultRoom}`,
      });
    },

    // ------------------------ 跳转其他界面 ------------------------

    // 跳转群组成员信息界面
    onOpenSessionGroupPage() {
      let teamId = this.session.sessionId;
      let teamName = this.session.name;
      let url =
        '/subPackChat/sessionGroupInfoPage?teamId=' +
        stringValid(teamId) +
        '&teamName=' +
        stringValid(teamName);
      uni.navigateTo({
        url: url,
      });
    },
    // 跳转电子病历界面
    onOpenMedicalRecordPage(consultId) {
      uni.navigateTo({
        url: '/subPackIndex/user/outpatientArchives?consultId=' + consultId,
      });
    },
    // 跳转电子处方界面
    onOpenOnlinePrescription() {
      this.isShowPrescriptionPop = false;
      uni.navigateTo({
        url: '/subPrescription/detail?id=' + this.prescriptionId,
      });
    },

    // ------------------------ 发送会话 ------------------------

    // 点击发送文本消息
    onSendTextMessage(content) {
      let text = content || this.inputContent;
      if (!stringNotEmpty(text)) return;

      let whitespaceReg = RegExp('^[ \n\r]*$');
      let rrrr = text.match(whitespaceReg);
      if (dataIsValid(text.match(whitespaceReg))) {
        uni.showToast({
          title: '不能发送空白信息',
          icon: 'none',
        });
        return;
      }

      var keyWords = this.canAtUserList.map(
        (user) => `@${escapeRegExp(user.Name)}`
      );
      keyWords.push.apply(keyWords, ['@所有人']);
      let matchKeys = text.match(RegExp(`${keyWords.join('|')}`, 'g'));
      if (arrayNotEmpty(matchKeys)) {
        let atUsers = [];
        if (matchKeys.indexOf('@所有人') >= 0) {
          atUsers = this.canAtUserList.map((e) => {
            return {
              id: e.Id,
              name: e.Name,
            };
          });
        } else {
          let userNames = matchKeys.map((e) => e.substring(1));
          let atMembers = this.canAtUserList.filter(
            (e) => userNames.indexOf(e.Name) >= 0
          );
          atUsers = atMembers.map((e) => {
            return {
              id: e.Id,
              name: e.Name,
            };
          });
        }

        let message = IMMessage.at(text, atUsers);
        this.sendMessage(message);
        return;
      }

      let message = IMMessage.text(stringNotEmpty(text) ? text : '');
      this.sendMessage(message);
    },
    // 点击发送媒体类型消息(0:拍照片 1:拍视频 2:相册选择)
    onSendMediaMessage(type) {
      var mediaType = [];
      var sourceType = [];
      switch (type) {
        case 0:
          mediaType = ['image'];
          sourceType = ['camera'];
          break;
        case 1:
          mediaType = ['video'];
          sourceType = ['camera'];
          break;
        case 2:
          mediaType = ['image', 'video'];
          sourceType = ['album'];
          break;
        default:
          break;
      }

      let that = this;
      if (uni.canIUse('chooseMedia.success.tempFiles')) {
        uni.chooseMedia({
          mediaType: mediaType,
          sourceType: sourceType,
          maxDuration: 60,
          camera: 'back',
          success(res) {
            res.tempFiles.forEach((tempFile, index) => {
              if (res.type == 'image') {
                that.sendImageMessage(tempFile.tempFilePath);
              } else {
                that.sendVideoMessage(tempFile);
              }
            });
          },
          fail(err) {
            console.debug('选择失败', type);
          },
        });
      } else {
        uni.showToast({
          title:
            '当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。',
          icon: 'none',
        });
      }
    },
    // 发送图片消息
    async sendImageMessage(imagePath) {
      if (!stringNotEmpty(imagePath)) return;

      let that = this;
      uni.getImageInfo({
        src: imagePath,
        success: (res) => {
          let imageHeight = parseInt(res.height);
          let imageWidth = parseInt(res.width);
          if (numberValid(imageHeight) == 0 || numberValid(imageWidth) == 0) {
            uni.showToast({
              title: '未获取到图片尺寸',
              icon: 'none',
            });
            return;
          }

          let imageObject = IMMessageObject.image(
            imagePath,
            imageWidth,
            imageHeight
          );
          let imageMessage = IMMessage.image(imageObject);
          that.sendMessage(imageMessage);
        },
        fail: (err) => {
          uni.showToast({
            title: '发送图片失败' + err,
            icon: 'none',
          });
        },
      });
    },
    // 发送视频消息
    async sendVideoMessage(tempFile) {
      let videoPath = tempFile.tempFilePath;
      if (!stringNotEmpty(videoPath)) return;

      let videoObject = IMMessageObject.video(
        videoPath,
        tempFile.thumbTempFilePath,
        tempFile.duration * 1000,
        parseInt(tempFile.width),
        parseInt(tempFile.height)
      );

      let videoMessage = IMMessage.video(videoObject);
      this.sendMessage(videoMessage);
    },
    // 点击申请续方
    async onApplyForRenewalScheme() {
      uni.showLoading({
        title: '请求中...',
      });
      let r0 = await this.checkRenewalSchemeState();
      if (r0.Type != 200) {
        uni.hideLoading();
        uni.showToast({
          title: r0.Content,
          icon: 'none',
        });
        return;
      }

      if (r0.Data) {
        // 已经续方成功
        uni.hideLoading();
        this.isShowPrescriptionPop = true;
        this.prescriptionPopPromptContent = r0.Content;
        this.prescriptionId = r0.Data;
        return;
      }

      let r1 = await this.applyForRenewalScheme();
      uni.hideLoading();
      if (r1.Type != 200) {
        uni.showToast({
          title: r1.Content,
          icon: 'none',
        });
      } else {
        uni.showToast({
          title: '申请提交成功',
          icon: 'none',
        });
      }
    },
    // 重新发送消息
    onReTrySend(message) {
      console.debug('重新发送消息', message);
      this.sendMessage(message);
    },
    // 加载更多会话
    async onLoadMore(e) {
      if (dataIsValid(e) && e.detail.direction != 'top') {
        return;
      }

      this.loadStatus = 'loading';
      this.shouldScrollToBottom = false;
      var hasMore;
      try {
        hasMore = await imManager.loadMoreMessages();
      } catch (error) {
        console.debug('加载更多消息失败', error);
      }

      if (hasMore == false) {
        this.loadStatus = 'nomore';
      }
    },

    // ------------------------ 其他事件 ------------------------

    // 问诊结束 患者可以进行评价
    onServiceEvaluation() {
      uni.navigateTo({
        url:
          '/subServices/docIndex?item=' +
          encodeURIComponent(JSON.stringify(this.consultRecord.Doctor)) +
          '&consultId=' +
          this.consultId +
          '&ConsultWay=' +
          this.consultRecord.Consult.ConsultWay,
      });
    },
    // 图片预览
    onPreviewImage(url) {
      let imageUrl = uni.previewImage({
        current: 0,
        urls: [url],
        success: (res) => {
          console.debug('预览图片成功', res);
        },
        fail: (err) => {
          console.debug('预览图片失败', err);
        },
      });
    },
    // 点击空白，收起菜单栏、常用语列表或者输入框
    onCloseMenuOrTextarea() {
      if (this.isShowInputMenu) {
        this.isShowInputMenu = false;
        this.shouldScrollToBottom = false;
        this.refreshHeight();
      }
      if (this.isShowPhrasesMenu) {
        this.isShowPhrasesMenu = false;
        this.shouldScrollToBottom = false;
        this.refreshHeight();
      }

      // 当前键盘正弹起
      if (this.isFocus || this.keyboardHeight > 0) {
        this.shouldScrollToBottom = false;
        this.isFocus = false;
        uni.hideKeyboard();
      }
    },
    // 滚动到底部
    async scrollToBottom() {
      if (!this.shouldScrollToBottom) {
        this.shouldScrollToBottom = true;
        return;
      }

      let that = this;
      await this.$nextTick();
      setTimeout(function () {
        that.scrollTop = that.old.scrollTop++;
      }, 10);
    },
    // 刷新组件动态高度
    async refreshHeight() {
      await this.$nextTick();
      let that = this;
      setTimeout(() => {
        const query = uni.createSelectorQuery().in(this);
        query
          .select('#footer')
          .boundingClientRect((data) => {
            let height = dataIsValid(data) ? data.height : 0;
            that.footerHeight = height - that.paddingBottom;
            that.scrollHeight =
              that.screenHeight -
              that.navHeight -
              that.footerHeight -
              that.paddingBottom;
            that.scrollToBottom();
          })
          .exec();
      }, 10);
    },

    // -------------------- 咨询问诊相关 --------------------

    // 咨询会话倒计时开始计时
    startDownCount(count) {
      if (this.downCountTimer != null) {
        clearInterval(this.downCountTimer);
        this.downCountTimer = null;
      }

      let that = this;
      this.downCount = count;
      this.downCountTimer = setInterval(() => {
        let downCount = that.downCount;
        if (downCount <= 0) {
          clearInterval(that.downCountTimer);
          that.downCountTimer = null;
          that.downCount = 0;
        } else {
          that.downCount = downCount - 1;
        }
      }, 1000);
    },
    /**
     * 修改咨询会话当前问诊状态
     *
     * @param {Number} state 咨询状态 1:待接诊 2:接诊中 3:已结束
     */
    changeConsultState(state) {
      this.state = state;

      if (state == 2) {
        this.loadDownCount();
      } else {
        if (this.downCountTimer != null) {
          clearInterval(this.downCountTimer);
          this.downCountTimer = null;
        }
        this.downCount = 0;
        this.refreshHeight();
      }
    },

    // -------------------- 初始化数据 --------------------

    // 根据session，初始化数据
    async loadData(messageId) {
      // 没有获取到会话信息
      if (!objNotEmpty(this.session)) {
        uni.hideLoading();
        return;
      }

      // 加载常用语
      this.loadPhrasesData();

      this.isConsultRoom = this.session.expInfo.type == 3;
      this.isHomePatientRoom = this.session.expInfo.type == 2;

      if (this.isHomePatientRoom) {
        // 居家指导群，才显示续方功能
        this.inputMenuItemData.push({
          id: 'continuation-therapy',
          title: '延续治疗',
          image: '/subPackChat/static/renewal-scheme.png',
          width: 50,
          height: 60,
        });
      }

      // 获取群成员标签
      let that = this;
      if (!this.isConsultRoom) {
        this.loadIMRoomRelationshipInfo(this.session.sessionId);
      }

      // 监听消息变化
      this.messageListListener = (data) => {
        that.loadCurrentChatMessageList(data);
      };
      imManager.addMessageListListener(this.messageListListener);

      // 获取缓存聊天信息
      this.inputContent =
        chatCacheManager.getChat(this.session.sessionId) || '';
      // 开始会话
      await this.loadStartChat(
        this.session.sessionId,
        this.session.sessionType,
        messageId
      );
      uni.hideLoading();

      // 获取房间信息
      this.loadRoomInfo(this.session.sessionId);
    },
    // 根据房间号，获取会话信息
    async loadSessionInfo() {
      try {
        this.session = await SessionState.instance().findSession(this.roomId);
      } catch (e) {
        uni.showToast({
          title: e,
          icon: 'none',
        });
      }
    },

    // -------------------- 网络请求相关 --------------------

    // 会话开始
    async loadStartChat(sessionId, sessionType, messageId) {
      var r;
      try {
        r = await imManager.startChat(sessionId, sessionType);
      } catch (error) {
        console.error('开始会话失败', error);
      }
      if (r == true) {
        if (stringNotEmpty(messageId)) {
          imManager.loadMessageListToMessage(messageId).catch((err) => {
            console.debug('加载到指定消息出错', err);
          });
        } else {
          imManager.loadMoreMessages().catch((err) => {
            console.debug('加载更多消息出错', err);
          });
        }
      } else {
        console.debug('开始会话失败');
      }
    },
    // 请求当前会话消息列表数据
    async loadCurrentChatMessageList(data) {
      console.debug('正在进行的会话，消息列表有变化', data);

      // 更新加载状态
      if (this.loadStatus == 'loading') {
        if (data.length == this.messageList.length) {
          this.loadStatus = 'nomore';
        } else {
          this.loadStatus = 'loadmore';
        }
      }
      var messageList = data;
      this.messageList = [];

      let that = this;
      messageList.forEach((message, index) => {
        // 标记是否需要显示时间
        var showTime = false;
        if (index == 0) {
          showTime = true;
        } else {
          let lastMessage = messageList[index - 1];
          // 注意：此处的时间戳已经考虑东八区时区，不需要额外转换
          if (message.timestamp - lastMessage.timestamp > 5 * 60 * 1000) {
            showTime = true;
          }
        }
        message.showTime = showTime;

        // 时间
        let dateTime = new Date(message.timestamp);
        message.dateTime = getFormateDateTime(dateTime);

        // 标签
        let tag = that.relationship[message.senderId];
        message.tag = tag;
        message.isShowTag = stringNotEmpty(tag);

        // 获取文件(图片、视频)路径
        if (objNotEmpty(message.messageObject)) {
          if (message.messageType == IMMessageType.Image) {
            message.imageUrl = message.messageObject.url || '';
          } else if (message.messageType == IMMessageType.Video) {
            message.videoUrl = message.messageObject.url || '';
          }
          let mediaWidth = message.messageObject.width;
          let mediaHeight = message.messageObject.height;
          let mediaRatio =
            numberValid(mediaWidth) > 0 ? mediaHeight / mediaWidth : 1;
          if (mediaHeight < mediaWidth) {
            // 横着的
            message.mediaWidth = 150;
            message.mediaHeight = Math.max(mediaRatio * 150, 100);
          } else {
            // 竖着的
            message.mediaWidth = 100;
            message.mediaHeight = Math.min(mediaRatio * 100, 150);
          }
        }

        // @消息数据处理，生成富文本节点
        if (message.messageType == IMMessageType.At) {
          let keywords = [
            ...arrayValid(message.atUsers).map((user) => user.name),
            '所有人',
          ];
          message.nodes = this.getNodesWithKeywords(
            message.text,
            keywords,
            message.isOutgoingMsg
          );
        }

        // 自定义定制内容
        if (stringNotEmpty(message.customMessageContent)) {
          let json = JSON.parse(message.customMessageContent);
          message.customType = json.CustomType;
          message.customBody = json.Body;

          if (arrayNotEmpty(message.customBody.userNames)) {
            message.customBody.atNames = message.customBody.userNames.reduce(
              function (pre, name, index, arr) {
                return pre + '@' + name + ' ';
              },
              ''
            );
          }
        }

        that.messageList.push(message);
      });

      // 加载时候，是否将列表滑到最底部
      // 请求加载更多时候则不需要滑到最底部
      this.scrollToBottom();
    },
    // 获取房间信息(群成员、问诊倒计时)
    async loadRoomInfo(sessionId) {
      let that = this;
      let userId = app.globalData.userInfo.Id;
      let r = await getRoomInfo(sessionId, '');
      if (r.Type == 200) {
        if (this.state == 2) {
          // 获取问诊倒计时
          let count = r.Data.RoomInfo.Rules.TimeoutClose.timeout - r.Timestamp;
          this.startDownCount(Math.floor(count / 1000));
        }

        // 获取群成员信息
        let members = r.Data.Members;
        if (members) {
          let userCacheStore = UserCacheStore.instance();
          let userGuIds = members
            .filter((user) => user.UserGuid != userId)
            .map((user) => user.UserGuid);
          await userCacheStore.loadUserByUserIds(userGuIds);
          this.canAtUserList = userGuIds.map((userGuId) => {
            let user = userCacheStore.getUser(userGuId);
            return user;
          });
          this.canAtUserList = this.canAtUserList.filter((user) =>
            dataIsValid(user)
          );
        }
      } else {
        uni.showToast({
          title: r.Content,
          icon: 'none',
        });
      }
    },
    // 获取群成员标签
    async loadIMRoomRelationshipInfo(sessionId) {
      let r = await getIMRoomRelationshipInfo(sessionId);
      if (r.Type == 200) {
        this.relationship = r.Data;
      } else {
        uni.showToast({
          title: r.Content,
          icon: 'none',
        });
      }
    },
    // 咨询会话 - 获取咨询详情
    async loadConsultRecordInfo(consultId) {
      let r = await getConsultRecordInfo(consultId);
      if (r.Type == 200) {
        this.consultRecord = r.Data;
        this.isOnlineConsultation = this.consultRecord.Consult.ConsultWay == 1;
        this.roomId = r.Data.Consult.RoomId;
        if (stringNotEmpty(this.roomId)) {
          await this.loadSessionInfo();
        }

        var state = r.Data.Consult.State;
        /**
         * 注意：
         * 由于后端流程问题，如果网络请求过快，此刻获取的问诊状态可能未更新，
         * 可能此时 r.Data.Consult.State == 0。
         * 所以需要前端单独处理���情况，主动更改问诊状态为待接诊。
         */
        if (this.fromAddInquiry && state != 1) {
          state = 1;
        }
        this.state = state;
        this.isShowServiceEvaluation =
          state === 3 && !r.Data.Consult.IsEva && r.Data.Consult.Source === 1;
      }

      if (r.Type != 200) {
        uni.showToast({
          title: r.Content,
          icon: 'none',
        });
      }
    },
    // 咨询会话 - 获取倒计时
    async loadDownCount() {
      let r = await getRoomInfo(this.consultRecord.Consult.RoomId, '');
      if (r.Type == 200) {
        let count = r.Data.RoomInfo.Rules.TimeoutClose.timeout - r.Timestamp;
        this.startDownCount(Math.floor(count / 1000));
      } else {
        uni.showToast({
          title: r.Content,
          icon: 'none',
        });
      }
    },
    // 发送消息到服务器
    async sendMessage(message) {
      if (
        message.messageType == IMMessageType.Text ||
        message.messageType == IMMessageType.At
      ) {
        chatCacheManager.removeChat(this.session.sessionId);
        this.inputContent = '';
      }
      imManager.sendMessage(message).catch((err) => {
        console.error('发送消息失败', err);
      });
    },
    /**
     * 检查当前方案的续方的状态
     *
     * - Data: 需要续方的方案id
     * - Data == null: 可续方
     * - Data != null: 已续过方，可查看
     */
    async checkRenewalSchemeState() {
      let params = {
        programId: this.session.expInfo.referenceId,
      };
      let r = await checkApplyContinuePreState(params);
      if (r.Type != 200 && stringNotEmpty(r.Data)) {
        return {
          Type: 200,
          Data: r.Data,
          Content: r.Content,
        };
      }

      return r;
    },
    // 申请续方
    async applyForRenewalScheme() {
      let params = {
        Id: this.session.expInfo.referenceId,
      };
      let r = await applyContinueRx(params);
      return r;
    },
    // 获取常用语
    async loadPhrasesData() {
      let r = await findPhrases(0, null, null);
      if (r.Type == 200) {
        this.phrasesList = r.Data.Data || [];
      }

      return r;
    },

    // -------------------- 富文本 --------------------

    // 根据keywords将text分割处理，生成富文本nodes
    // isOutgoingMsg: 是否是发送方
    getNodesWithKeywords(text, keywords = [], isOutgoingMsg = true) {
      // 根据关键词分割，并去掉关键词
      keywords = keywords.map((key) => `@${key}`);
      let textList = text.split(RegExp(`${keywords.join('|')}`));

      // 筛选���现过的keyword
      let keyList = text.match(RegExp(`${keywords.join('|')}`, 'g'));

      var children = [];
      var indexKey = 0; // 取到的第几个keyword
      textList.forEach((text, index) => {
        var textNode = {
          name: 'span',
          attrs: {
            style: isOutgoingMsg
              ? 'font-size: 16px; color: #ffffff;'
              : 'font-size: 16px; color: #29B7A3;',
          },
          children: [
            {
              type: 'text',
              text: text,
            },
          ],
        };
        children.push(textNode);

        // 如果有keyword，则单独加入
        if (dataIsValid(keyList) && keyList.length > indexKey) {
          let keyNode = {
            name: 'span',
            attrs: {
              style: isOutgoingMsg
                ? 'font-size: 16px; color: #ffffff;'
                : 'font-size: 16px; color: #29B7A3;',
            },
            children: [
              {
                type: 'text',
                text: keyList[indexKey],
              },
            ],
          };
          indexKey++;
          children.push(keyNode);
        }
      });

      let nodes = [
        {
          name: 'div',
          children: children,
        },
      ];
      return nodes;
    },
    // 获取头富文本的节点
    getHeaderTextNodes() {
      switch (this.state) {
        case 1: {
          let nodes = [
            {
              name: 'div',
              attrs: {
                style: 'font-size: 16px; color: #999999;',
              },
              children: [
                {
                  type: 'text',
                  text: this.isOnlineConsultation
                    ? '待确认，等待医生确认'
                    : '待确认',
                },
              ],
            },
          ];
          return nodes;
        }
        case 2: {
          let count = this.downCount;
          let hours = count == null ? '--' : Math.floor(count / 3600);
          let minutes = count == null ? '--' : Math.floor((count % 3600) / 60);
          let seconds = count == null ? '--' : count % 60;
          let nodes = [
            {
              name: 'div',
              children: [
                {
                  name: 'span',
                  attrs: {
                    style: 'font-size: 16px; color: #999999;',
                  },
                  children: [
                    {
                      type: 'text',
                      // text: this.isOnlineConsultation ? '问诊中，' : '咨询中，'
                      text: '咨询中，',
                    },
                  ],
                },
                {
                  name: 'span',
                  attrs: {
                    style: 'font-size: 16px; color: #29B7A3;',
                  },
                  children: [
                    {
                      type: 'text',
                      text: `${hours}小时${minutes}分钟${seconds}秒`,
                    },
                  ],
                },
                {
                  name: 'span',
                  attrs: {
                    style: 'font-size: 16px; color: #999999;',
                  },
                  children: [
                    {
                      type: 'text',
                      // text: this.isOnlineConsultation ? '后本次问诊结束' : '后本次咨询结束'
                      text: '后本次咨询结束',
                    },
                  ],
                },
              ],
            },
          ];
          return nodes;
        }
        case 3: {
          let nodes = [
            {
              name: 'div',
              attrs: {
                style: 'font-size: 16px; color: #999999;',
              },
              children: [
                {
                  type: 'text',
                  // text: this.isOnlineConsultation ? '本次问诊已结束' : '本次咨询已结束'
                  text: '本次咨询已结束',
                },
              ],
            },
          ];
          return nodes;
        }
        default:
          return [];
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.no-data-view {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .no-data-button {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    margin-top: 24rpx;

    font-size: 28rpx;
    color: #29b7a3;
    font-weight: bold;
  }
}

.body-view {
  height: 100%;
}

.contacts-pop-list-view {
  width: 100%;
  height: 600rpx;

  /deep/ .u-list {
    height: auto !important;
  }
}

.header-view {
  width: 100%;
  height: 35px;
  padding-left: 32rpx;
  background-color: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  font-size: 24rpx;
  color: #333333;
  border-top: 1rpx solid #e5e5e5;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.footer-container {
  background-color: white;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: fixed;
  bottom: 0;
}

.footer-input-view {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 8px 0;
}

.footer-textarea-view {
  flex: 1;
  background-color: #f6f6f6;
  padding: 0 10px;
  border-radius: 5px;
  min-height: 35px;
  display: flex;
  justify-content: center;
  align-items: center;

  /deep/ .u-textarea {
    padding: 0;
    border-radius: 0;
    background-color: #f6f6f6;
  }

  &.is-audio {
    padding: 0;
    background-color: transparent;
  }

  .audio-input {
    flex: 1;
    background-color: #f8f7f7;
    color: #333333;
    text-align: center;
    border-radius: 5px;
    margin-right: 10px;
    height: 35px;
    line-height: 35px;
  }
}

.footer-textarea {
  width: 100%;
  box-sizing: border-box;
}

.footer-send-button {
  width: 60px;
  height: 35px;
  margin: 0 12px;
  background-color: #29b7a3;
  border-radius: 8rpx;
  font-size: 15px;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
}

.footer-menu-view {
  padding: 16rpx 32rpx;
}

.footer-menu-item {
  padding: 8rpx 0rpx;
  display: inline-block;
  text-align: center;
  width: 25%;

  &-image {
    width: 124rpx;
    height: 124rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto 10rpx auto;
    border-radius: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
  }

  &-text {
    font-size: 24rpx;
    color: #666666;
  }
}

.footer-phrases-view {
  padding: 16rpx 40rpx;
  width: 100%;
  height: 200px;

  /deep/ .u-list {
    height: 200px;
  }
}

.footer-phrases-item {
  padding: 20rpx 0;
  border-bottom: 2rpx solid #e5e5e5;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;

  &-text {
    font-size: 30rpx;
    color: #333333;
    flex: 1;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
}

.footer-icon-image {
  width: 35px;
  height: 35px;
  padding: 2px;
}

.list-container {
  background-color: #f7f7f7;
}

.session-custom-text-sender-cell {
  padding: 16rpx;
  border-radius: 8rpx;
  background-color: #29b7a3;

  text {
    font-size: 16px;
    color: #ffffff;
    word-break: break-all;
    word-wrap: break-word;
  }
}

.session-custom-text-receiver-cell {
  padding: 16rpx;
  border-radius: 8rpx;
  background-color: #ffffff;

  text {
    font-size: 16px;
    color: #333333;
    word-break: break-all;
    word-wrap: break-word;
  }
}

.session-system-cell {
  width: 100%;
  padding-top: 32rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  &-text {
    font-size: 16px;
    color: #333333;
  }
}

.session-system-tip-view {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  background-color: white;
  width: 100%;
  margin-top: 32rpx;
  padding: 16rpx 32rpx;
  border-radius: 8rpx;
  box-shadow: 0 0 8rpx rgba(0, 0, 0, 0.1);

  &-title {
    font-size: 16px;
    color: #29b7a3;
    margin-bottom: 16rpx;
  }

  &-content {
    font-size: 16px;
    color: #333333;
    word-break: break-all;
    word-wrap: break-word;
  }
}

.session-custom-cell {
  width: 100%;
  margin-top: 32rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}

.session-media-view {
  padding: 16rpx;
  border-radius: 8rpx;
  background-color: #d7f4de;
}

.consult-description-more-view {
  width: 100%;
  padding: 0 8rpx 8rpx 8rpx;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  border-bottom-left-radius: 8rpx;
  border-bottom-right-radius: 8rpx;
}

.mission-view {
  padding: 16rpx;
  background-color: white;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}

.session-body-view {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
}

.session-content-body-view {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-end;
}

.session-content-view {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  padding: 0 24rpx;
}

.session-alter-view {
  flex: 1;
  background-color: white;
  display: flex;
  flex-direction: column;
}

.session-alter-header-view {
  width: 100%;
  padding: 16rpx;
  background-color: #29b7a3;
  display: flex;
  justify-content: center;
  align-items: center;
  border-top-left-radius: 8rpx;
  border-top-right-radius: 8rpx;
}

.session-alter-header-view text {
  font-size: 16px;
  color: white;
  font-weight: bold;
  word-break: break-all;
  word-wrap: break-word;
}

.session-center-view {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.session-status-view {
  padding: 0 10px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
}

.session-item-footer {
  height: 35px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.evaluation-plan-alter-view {
  flex: 1;
  padding: 24rpx 32rpx;
  min-height: 212rpx;
  background-color: white;
  border-radius: 8rpx;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
}

.evaluation-plan-alter-title {
  font-size: 16px;
  font-weight: bold;
  color: #29b7a3;
}

.evaluation-plan-alter-content {
  padding-top: 4rpx;
  font-size: 13px;
  font-weight: 500;
  color: #333333;
  flex: 1;
}

.evaluation-plan-alter-submit {
  padding: 4rpx 16rpx;
  font-size: 14px;
  color: white;
  background-color: #29b7a3;
  border-radius: 30rpx;
  margin-top: 16rpx;
}

.evaluation-plan-alter-view image {
  position: absolute;
  right: 0;
  bottom: 0;
  height: 212rpx;
  width: 286rpx;
}

.submit-button {
  background-color: #29b7a3;
  font-size: 15;
  color: #ffffff;
  font-weight: bold;
  margin: 24rpx 0 10rpx 0;
  width: -webkit-fill-available;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 24rpx 32rpx;
  border-radius: 8rpx;
}

.at-text {
  font-size: 24rpx;
  color: #333333;
}

.at-text-tag {
  font-size: 24rpx;
  color: #29b7a3;
}

.name {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 10rpx;
}

.custom-text {
  font-size: 16px;
  color: #333333;
  padding: 16rpx;
  word-wrap: break-word;
  word-break: break-all;
}

.time-view {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 16rpx;
}

.time-view text {
  font-size: 28rpx;
  color: #999999;
}

.tag-text-bold {
  font-weight: bold;
  font-size: 16px;
  color: #29b7a3;
  word-break: break-all;
  word-wrap: break-word;
}

.tag-text {
  font-size: 16px;
  color: #29b7a3;
}

.hover-class {
  opacity: 0.5;
}

@keyframes waveAnim {
  0%,
  100% {
    transform: scaleY(1);
  }

  50% {
    transform: scaleY(0.2);
  }
}

.audio-input.is-recording {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.6);
  height: unset;
  margin: unset;
}

.audio-record-controls {
  $inputHeight: 150px;
  $buttonHeight: 80px;
  $space: 20px;

  .voice-waves {
    position: absolute;
    left: 100px;
    right: 100px;
    bottom: $inputHeight + $buttonHeight + 2 * $space;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 3px;
    height: 20px;
    background-color: #29b7a3;
    height: 50px;
    border-radius: 10px;
    padding: 8px;
  }

  .wave {
    width: 3px;
    height: 100%;
    background-color: #666;
    border-radius: 2px;
    transform: scaleY(0.5);

    @for $i from 1 through 13 {
      &:nth-child(#{$i}) {
        animation: waveAnim
          1.2s
          linear
          #{if($i >=7, ($i - 7) * 0.2, (7 - $i) * 0.2)}s
          infinite;
      }
    }
  }

  .button {
    height: 80px;
    width: 80px;
    background-color: #333333;
    border-radius: 40px;
    text-align: center;
    line-height: 80px;
    color: #ccc;
  }

  .record-tip {
    position: absolute;
    left: 100px;
    right: 100px;
    bottom: $inputHeight + $buttonHeight;
    text-align: center;
  }

  .cancel {
    position: absolute;
    left: 20px;
    bottom: $inputHeight + 20px;
  }

  .text {
    position: absolute;
    right: 20px;
    bottom: $inputHeight + 20px;
  }

  .audio-wrapper {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    height: $inputHeight;

    .audio {
      --width: 1000px;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      height: var(--width);
      width: var(--width);
      background-color: #666;
      border-radius: 1000px;
      border: 4px solid #999;
      padding-top: 20px;
    }

    .audio.is-cancel {
      background-color: #999;
    }
  }
}
</style>
