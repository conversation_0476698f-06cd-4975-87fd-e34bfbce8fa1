import Vue from 'vue';
import Vuex from 'vuex';
import training from '@/store/modules/training.js';
import message from '@/store/modules/message.js';

Vue.use(Vuex);

const store = new Vuex.Store({
  state: {
    // hasLogin: false,
    // userInfo: {},
  },
  mutations: {
    // login(state, provider) {
    // 	console.log('provider', provider);
    // 	state.hasLogin = true;
    // 	state.userInfo = provider;
    // 	uni.setStorage({ //缓存用户登陆状态
    // 		key: 'userInformation',
    // 		data: provider
    // 	})
    // 	//需要在此处加login 否则每次重新编译小程序会导致uni.checkSession每次都走fail
    // 	uni.login({
    // 		provider: 'weixin',
    // 		success: function(loginRes) {
    // 			console.log('调用login', loginRes);
    // 		}
    // 	});
    // },
    // logout(state) {
    // 	state.hasLogin = false;
    // 	state.userInfo = {};
    // 	uni.removeStorage({
    // 		key: 'userInfo'
    // 	})
    // },
  },
  actions: {},
  modules: {
    training,
    message,
  },
  plugins: process.env.NODE_ENV !== 'production' ? [Vuex.createLogger()] : [],
});

export default store;
