import { getLastMessageInfoFromType } from '@/api/message';
import { implementPrescription } from '@/api/consult.js';

// 0:医生康复日报
/// 1:患者康复日报
// 2:医生排班
// 3:医生职称修改
// 4:医生机构资质审核
// 5:医生平台资质审核
/// 6:患者实名认证
/// 7:家人实名认证
/// 8:申请家人授权
/// 9:解除家人授权
/// 10:患者治疗安排
/// 11:患者康复宣教
/// 12:患者24小时训练
/// 13:患者康复训练日报
// 14:授权结果
// 15:同步未读消息数量操作
/// 16:同步患者群聊
/// 18:院外康复  新的训练下达
// 19:问诊通知=19
// 20:管理通知=20
// 21:处方审核通知=21
/// 22:处方失效通知=22，处方失效6小时前发送
/// 23:订单已发货通知=23，后台点击发货后发送
/// 24:设备使用保证金退款成功通知=24，后台确认设备入库并退款成功后发送
/// 25:处方审核通知(患者)=25
/// 26:问诊结束（患者）=26
// 27:患者治疗一半（医生治疗师）=27
/// 28:治疗方案结束（患者）=28

/// 诊疗和交易物流
const tradeLogisticsMessageTypes = [18, 22, 23, 24, 25, 26, 28, 45];
const message = {
  namespaced: true,
  state: {
    text: '我是message模块下state.text的值',
    unreadMessageCount: 0,
    prescriptionId: '',
  },
  getters: {
    showbadge(state) {
      return state.unreadMessageCount > 0;
    },
    showGiveMoeny(state) {
      return !!state.prescriptionId;
    },
  },
  mutations: {
    updateUnreadMessageCount(state, count) {
      state.unreadMessageCount = count || 0;
    },
    updatePrescriptionId(state, prescriptionId) {
      state.prescriptionId = prescriptionId || '';
    },
  },
  actions: {
    /**
     * 同步服务器消息状态
     */
    async syncMessageStatus({ commit }) {
      // 获取最近的一条消息
      const r = await getLastMessageInfoFromType(tradeLogisticsMessageTypes);
      if (r.isFailure) throw r.msg;
      commit('updateUnreadMessageCount', r.data?.UnReadCount || 0);
    },

    async syncPrescriptionId({ commit }) {
      // 获取最近的一条消息
      const r = await implementPrescription();

      commit('updatePrescriptionId', r.Data || '');
    },

    /**
     * 处理接收到的消息
     * @@param {MessageEntity} message
     */
    async receiveMessage({ dispatch, commit, state }, message) {
      if (tradeLogisticsMessageTypes.find((e) => e === message.Type)) {
        commit('updateUnreadMessageCount', state.unreadMessageCount + 1);
        // updateTradeLogisticsCategory(
        // 	message, _tradeLogisticsMessageCategory.unReadCount + 1);
      }

      switch (message.type) {
        case 15:
          // 同步未读消息数量
          dispatch('syncMessageStatus');
          break;
        case 16:
          // 同步患者群聊
          // Global.sl.get<SessionState>().refreshTeamSessionList();
          break;
        case 18:
          // 院外康复  新的训练下达
          // Global.sl.get<TrainingManager>().updateNewTraining(true);
          break;
      }
    },
  },
};

export default message;
