// ---------------------------------------------------------
//  说明：
//  服务端事件 - 由服务器发送，客户端接收的事件
//  客户端事件 - 由客户端发送，客户端接收的事件，即模块组件间的通知事件
// ---------------------------------------------------------

// 用户模块，服务器事件
export const UserServerEvent = {
  /**用户密码发生改变*/
  passwordChanged: 'OnPasswordChanged',

  /**用户注销账号*/
  userLogout: 'UserLogout',

  /**是否有视频电话打过来*/
  onVideoCall: 'OnVideoCall',

  /**用户被踢下线*/
  onCloseConnectionFromServerSide: 'OnCloseConnectionFromServerSide',
};

// 用户模块，客户端事件
export const UserClientEvent = {
  /**用户密码发生改变*/
  passwordChanged: 'UserClientEvent_passwordChanged',

  /**用户注销账号*/
  userLogout: 'UserClientEvent_userLogout',

  /**是否有视频电话打过来*/
  onVideoCall: 'OnVideoCall',
  /**用户被踢下线*/
  onCloseConnectionFromServerSide: 'OnCloseConnectionFromServerSide',
};

// 训练模块，服务端事件
export const TrainingServerEvent = {
  /**方案调整了*/
  trainingPlanChanged: 'trainingPlanChanged',
};

// 训练模块，客户端事件
export const TrainingClientEvent = {
  /**切换训练方案*/
  switchTrainingPlan: 'trainingClientEvent_switchTrainingPlan',

  /**方案调整了*/
  trainingPlanChanged: 'trainingClientEvent_trainingPlanChanged',

  /**
   * 打卡成功，通知刷新数据
   *
   * 已经成功打卡的动作id：[actionId...]
   */
  punchSuccess: 'trainingClientEvent_punchSuccess',
};

// 问诊模块，服务器事件
export const ConsultServerEvent = {
  /**咨询状态改变*/
  consultStateChanged: 'ConsultEvent_State_Changed_signalr',
};

// 问诊模块，客户端事件
export const ConsultClientEvent = {
  /**咨询状态改变事件*/
  consultStateChanged: 'ConsultClientEvent_consultStateChanged',

  /**咨询会话未读消息数量发生变化*/
  consultUnreadMessageCountChanged:
    'ConsultServerEvent_consultUnreadMessageCountChanged',
};

// 会话模块，客户端事件
export const SessionClientEvent = {
  /**会话列表发生改变*/
  sessionListChanged: 'SessionClientEvent_sessionListChanged',
};

// 处方模块，服务器事件
export const PrescriptionServerEvent = {
  /**处方费用支付成功*/
  payTreatSuccessSignalR: 'PrescriptionEvent_pay_treat_success_signalr',

  /**处方状态改变*/
  prescriptionStateChanged: 'PrescriptionEvent_State_Changed_signalr',
};

// 处方模块，客户端事件
export const PrescriptionClientEvent = {
  /**处方费用支付成功*/
  payTreatSuccess: 'PrescriptionEvent_pay_treat_success',

  /**处方状态改变事件*/
  prescriptionStateChanged: 'ConsultClientEvent_prescriptionStateChanged',
};

// 消息模块，服务器事件
export const MessageServerEvent = {
  /**接收新的消息*/
  messageReceived: 'NotifyMessageReceived',
};

// 消息模块，客户端事件
export const MessageClientEvent = {
  /**接收新消息*/
  messageReceived: 'MessageClientEvent_messageReceived',
  /** 接受到医生下达的方案 */
  programmeIssued: 'MessageClientEvent_programmeIssued',
};

// 社区治疗模块，服务器事件
export const CommunityServiceEvent = {
  /* 社区治疗被执行 */
  refreshMoItemExec: 'refreshMoItemExec',
};

// 社区治疗模块，客户端事件
export const CommunityClientEvent = {
  /* 社区治疗被执行 */
  refreshMoItemExec: 'CommunityClientEvent_refreshMoItemExec',
};

// 量表模块，客户端事件
export const AssessClientEvent = {
  /**提交量表成功*/
  updateAssess: 'AssessClientEvent_updateAssess',
  /**提交风险评估完成*/
  updateRiskAssess: 'AssessClientEvent_updateRiskAssess',
};

// 随访模块，客户端事件
export const FollowUpClientEvent = {
  /**新增随访任务*/
  add: 'FollowUpClientEvent_add',

  /**随访任务中的量表/问卷被填写*/
  detailSave: 'FollowUpClientEvent_detailSave',
};

// 病历模块，客户端事件
export const MedicalClientEvent = {
  /**修改病历*/
  updateMedical: 'MedicalClientEvent_updateMedical',
};

// 脊柱筛查模块，客户端事件
export const SpineClientEvent = {
  /** 查看脊柱侧弯科普知识 */
  lookSpineMissions: 'SpineClientEvent_lookSpineMissions',

  /** 成功匹配脊椎筛查+足底压力测试 */
  spineAndFootTestMatched: 'SpineClientEvent_spineScreeningAndFootTestMatched',
};
