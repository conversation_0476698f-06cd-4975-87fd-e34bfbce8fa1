// 用来储存数据到本地的key
const StoreKeys = {
  // 不再提示的训练动作
  ignoreTrainingId: 'ignoreTrainingId',

  // 不再提示的训练 所需要绑定设备
  ignoreTrainingBindDeviceId: 'ignoreTrainingBindDeviceId',

  // 缓存会话未发送内容
  cacheChatContentMap: 'cacheChatContentMap',

  // 训练计划 - 是否显示该计划医助弹框
  // eg: {
  // 	计划id: true
  // }
  showAssistantMap: 'planShowAssist',

  // 训练计划 - 居家动作医嘱，收起的分组名集合
  collapseGroupActionTypes: 'planCollapseGroups',

  // 是否显示完善用户修改个人信息弹窗
  showPerfectUserInfo: 'isOpenDialog',

  /**
   * 脊椎筛查 - 查询人信息JSON字符串
   * {
   *   name: '张三',
   *   phone: '138xxxxxxx',
   * }
   */
  spineScreeningUserInfo: 'spineScreeningUserInfo',
};

export default StoreKeys;
