import dayjs from 'dayjs';
/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path);
}

// 分钟转小时分钟
export function toHoursAndMinutes(totalMinutes, type = 0) {
  if (type == 1) {
    var day = parseInt(Math.floor(totalMinutes / 1440));
    var hour =
      day > 0
        ? Math.floor((totalMinutes - day * 1440) / 60)
        : Math.floor(totalMinutes / 60);
    var minute =
      hour > 0
        ? Math.floor(totalMinutes - day * 1440 - hour * 60)
        : totalMinutes;
    var time = '';
    if (day > 0) time += day + '天';
    if (hour > 0) time += hour + '小时';
    if (minute > 0) time += minute + '分钟';
    return time;
  } else {
    const hours = Math.floor(totalMinutes / 60);
    const minutes =
      totalMinutes % 60 < 10
        ? '0' + String(totalMinutes % 60)
        : totalMinutes % 60;
    return {
      hours,
      minutes,
    };
  }
}
var utc = require('dayjs/plugin/utc');
dayjs.extend(utc);
/**
 * 时间格式化，默认按 UTC 处理。
 * 建议用 formateDate 函数替换，因为大多数情况是不需要特别指定 utc 的。
 * 由于这个数据用的地方太多了，暂时保留。
 */
export function dateFormat(
  date,
  type = 'YYYY-MM-DD HH:mm:ss',
  enableUtc = true,
  add
) {
  if (date === undefined) {
    return '';
  }

  if (!enableUtc) {
    if (add) {
      return dayjs(date).add(add.Count, add.Type).format(type);
    }
    return dayjs(date).format(type);
  }
  if (add) {
    return dayjs.utc(date).add(add.Count, add.Type).format(type);
  }
  return dayjs.utc(date).format(type);
}

/**
 * 格式化日期时间
 *
 * @param {Date|number|string} date
 * @param {sring} formatter 格式化字符串. 默认 YYYY-MM-DD HH:mm:ss
 */
export function formateDate(date, formatter = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) return '';
  return dateFormat(date, formatter, false);
}

/**
 * 格式化时间
 * 时间显示规则：当天显示 HH:mm 当年显示 MM-dd HH:mm 跨年显示 yyyy-MM-dd HH:mm
 */
export function getFormateDateTime(date) {
  if (!date) return null;

  const now = new Date();
  let type = 'YYYY-MM-DD HH:mm';
  if (date.getFullYear() == now.getFullYear()) {
    if (date.getMonth() == now.getMonth() && date.getDay() == now.getDay()) {
      type = 'HH:mm';
    } else {
      type = 'MM-DD HH:mm';
    }
  }
  return dayjs(date).format(type);
}

/**
 * 获取日期 [星期一]..[星期天] 的星期数 1-7
 *
 * @param {Date} date 需要获取星期的日期
 */
export function getWeek(date) {
  if (!(date instanceof Date) || date == undefined || date == null) return;

  let week = date.getDay();
  if (week == 0) {
    return 7;
  } else {
    return week;
  }
}
/**
 * 获取指定日期的0点时间
 * @param {Date} date - 需要处理的日期对象
 * @returns {Date} 返回一个新的Date对象，表示当天的0点
 */
export function getStartOfDay(date) {
  return new Date(
    date.getFullYear(),
    date.getMonth(),
    date.getDate(),
    0,
    0,
    0,
    0
  );
}

/**
 * 获取指定日期的24点时间（即下一天的0点）
 * @param {Date} date - 需要处理的日期对象
 * @returns {Date} 返回一个新的Date对象，表示当天的24点（下一天的0点）
 */
export function getEndOfDay(date, isNextDay) {
  if (isNextDay) {
    return new Date(
      date.getFullYear(),
      date.getMonth(),
      date.getDate() + 1,
      0,
      0,
      0,
      0
    );
  }
  return new Date(
    date.getFullYear(),
    date.getMonth(),
    date.getDate(),
    23,
    59,
    59,
    0
  );
}

/**
 * 获取指定日期所在月份的第一天0点
 * @param {Date} date - 需要处理的日期对象
 * @returns {Date} 返回一个新的Date对象，表示当月第一天的0点
 */
export function getFirstDayOfMonth(date) {
  return new Date(date.getFullYear(), date.getMonth(), 1, 0, 0, 0, 0);
}

/**
 * 获取指定日期所在月份的最后一天23:59:59.999
 * @param {Date} date - 需要处理的日期对象
 * @returns {Date} 返回一个新的Date对象，表示当月最后一天的最后一毫秒
 */
export function getLastDayOfMonth(date) {
  return new Date(date.getFullYear(), date.getMonth() + 1, 0, 23, 59, 59, 0);
}

// 生成guid
export function guid() {
  function S4() {
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
  }
  return (
    S4() +
    S4() +
    '-' +
    S4() +
    '-' +
    S4() +
    '-' +
    S4() +
    '-' +
    S4() +
    S4() +
    S4()
  );
}
