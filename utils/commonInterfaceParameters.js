import {
  queryDepartments
} from '../api/passport.department';
import {
  getUserProfile
} from '@/api/passport.js';
/**
 * @param {object} params
 * @param {string} params.OrgId - 机构Id
 * @description 获取科室列表
 */
export const getDepartmentList = async (params) => {
  if (!params.OrgId) {
    throw new Error('获取科室必须传入参数OrgId');
  }

  const res = await queryDepartments({
    OrgId: params.OrgId,
    IsEnabled: true,
    Pageable: false,
    SingleOne: false
  });
  if (res.Data) {
    return res.Data;
  } else {
    return [];
  }
};

export const getDoctorList = async (params) => {
  let defaultParams = {
    Keyword: params.Keyword || '',
    DepartmentId: params.DepartmentId || null,
    DtoTypeName: params.DtoTypeName || 'OrganizationDoctorListOutputDto',
    Pageable: true,
    RoleType: params.RoleType || null,
    PageSize: params.PageSize || 20,
    PageIndex: params.PageIndex || 1,
    OrgIds: params.OrgIds || null,
  };
  const res = await getUserProfile(defaultParams);
  if (res.Type === 200) {
    return res.Data.Row;
  } else {
    return [];
  }
};
