import {
	HubConnectionBuilder,
	LogLevel,
	HttpTransportType,
	HubConnection,
	HubConnectionState,
} from 'signalr-uni';

// export class SignalChannelHooks {
// 	didAddRequestCallback;
// 	didReceiveResponseCallback;
// 	didRemoveRequestCallback;
// 	didReceiveResponseWithoutRequestCallback;
// }
const HeaderSignalRCallbackKey = 'hwkj-custom-signalr-callback';
const HeaderSignalRTypeKey = 'hwkj-custom-signalr-type';
const kRequestResponseChannelKey = 'RequestResponseChannel';
const kCloseConnectionFromServerSide = 'OnCloseConnectionFromServerSide';


export enum SignalChannelState {
	Disconnected = 'Disconnected',

	Connected = 'Connected',

	Reconnecting = 'Reconnecting',
}

type StateListener = (state : SignalChannelState) => void;

export class SignalChannel {
	static _instance : SignalChannel;
	static get instance() {
		if (!this._instance) {

		}
		return this._instance;
	}

	private _url : string;
	private _accessTokenFactory : () => string;

	constructor(url : string, accessTokenFactory : () => string) {
		this._url = url;
		this._accessTokenFactory = accessTokenFactory;

		this._hubConnection = null;
		this._needReconnect = false;
		this._methods = new Map();
		// this._requestMap = new Map();
		this._state = SignalChannelState.Disconnected;
		this._stateListener = [];
		this._closeHandler = (args : any) => {
			console.info('服务端请求关闭连接 ' + args);
			this._updateState(SignalChannelState.Disconnected);
			this.stop();
		};
		this._connectionCloseHandler = (error : any) => {
			console.warn(error);
			this._updateState(SignalChannelState.Disconnected);
			if (this._needReconnect) this.reconnect();
		};
	}

	/**
	 * signalr 连接
	 */
	private _hubConnection : HubConnection | null;

	/**
	 * 意外断开后是否需要重连
	 */
	private _needReconnect : boolean;

	/**
	 * 添加的监听方法列表
	 */
	private _methods : Map<string, Array<() => void>>;

	/**
	 * 接受到服务端发送的断开事件时的处理方法
	 */
	private _closeHandler : (args : any) => void;
	/**
	 * 监听到连接断开后的处理方法
	 */
	private _connectionCloseHandler : (error : any) => void;


	/// 待处理请求集合
	// Map<String, Completer<APIResult>> 
	// _requestMap;

	/**
	 * @param {string} 状态
	 */
	private _state : SignalChannelState;
	get state() : SignalChannelState {
		return this._state;
	}
	/**
	 * 更新状态
	 */
	private _updateState(newState : SignalChannelState) {
		if (newState != this._state) {
			this._state = newState;
			this._stateListener.forEach((listener) => {
				listener(newState);
			});
		}
	}

	/**
	 * 状态监听者
	 */
	private _stateListener : Array<StateListener>;

	/**
	 * 添加状态监听者
	 */
	addStateListener(listener : StateListener) {
		this._stateListener.push(listener);
	}
	/**
	 * 移除状态监听者
	 */
	removeStateListener(listener : StateListener) {
		const index = this._stateListener.findIndex((e) => e === listener);
		if (index != -1) this._stateListener.splice(index, 1);
	}

	/**
	 * 连接状态，和 signal_channel 状态不同
	 */
	get connectionState() : HubConnectionState {
		if (!this._hubConnection) throw '_hubConnection 未初始化';
		return this._hubConnection.state;
	}

	// SignalChannelHooks 
	// hooks;

	dispose() {
		this._methods = new Map();
		// this._requestMap = new Map();
		this._state = SignalChannelState.Disconnected;
		this._stateListener = [];
	}

	/**
	 * 开启通道
	 */
	async open() { this.start(); }
	async start() {
		try {
			if (this._hubConnection && this._hubConnection.state == HubConnectionState.Connected) {
				return;
			}

			this._hubConnection = new HubConnectionBuilder()
				.withUrl(this._url, {
					skipNegotiation: true,
					transport: HttpTransportType.WebSockets,
					accessTokenFactory: () => this._accessTokenFactory(),
				})
				.withAutomaticReconnect([0, 2000, 4000, 8000, 10000, 30000, 60000])
				.configureLogging(LogLevel.Debug)
				.build();

			// this._hubConnection.on(kRequestResponseChannelKey, () => this._handleRequestResponse());
			this._hubConnection.on(kCloseConnectionFromServerSide, this._closeHandler);
			this._hubConnection.onclose(this._connectionCloseHandler);

			this._methods.forEach((methodList, methodName) => {
				methodList.forEach((newMethod) => {
					if (!this._hubConnection) throw '_hubConnection 未初始化';
					this._hubConnection.on(methodName, newMethod);
				});
			});

			this._needReconnect = true;
			await this._hubConnection.start();
			this._updateState(SignalChannelState.Connected);
		} catch (e) {
			this._updateState(SignalChannelState.Disconnected);
			throw e;
		}
	}

	/**
	 * 关闭通道
	 */
	async close() { this.stop(); }
	async stop(autoReconnect = false) {
		this._needReconnect = autoReconnect || false;

		let preHub = this._hubConnection;
		this._hubConnection = null;

		// this._requestMap.values
		// 	.where((element) => !element.isCompleted)
		// 	.forEach((element) => {
		// 		element.complete(APIResult.failure('连接断开', null));
		// 	});
		// this._requestMap.clear();
		if (preHub && preHub.state == HubConnectionState.Connected) await preHub.stop();
		this._updateState(SignalChannelState.Disconnected);
	}

	/**
	 * 重新连接
	 */
	async reconnect() {
		if (this._hubConnection && this._hubConnection.state == HubConnectionState.Connected) return;

		this._updateState(SignalChannelState.Reconnecting);
		return this.start();
	}



	// void on(String methodName, MethodInvocationFunc newMethod) {
	//   if (_hubConnection == null) throw SignalRChannelError('先执行 start');
	//   _hubConnection.on(methodName, newMethod);
	// }
	//
	// void off(String methodName, {MethodInvocationFunc method}) {
	//   if (_hubConnection == null) throw SignalRChannelError('先执行 start');
	//   _hubConnection.off(methodName, method: method);
	// }

	cleanMethods() {
		this._methods.clear();
	}

	///  Registers a handler that will be invoked when the hub method with the specified method name is invoked.
	///
	/// methodName: The name of the hub method to define.
	/// newMethod: The handler that will be raised when the hub method is invoked.
	///
	on(methodName : string, newMethod : () => void) {
		if (!this._hubConnection) throw '_hubConnection 未初始化';
		if (!(methodName) || !(newMethod)) {
			return;
		}

		methodName = methodName.toLowerCase();
		if (!this._methods.has(methodName)) {
			this._methods.set(methodName, []);
		}

		const handlers = this._methods.get(methodName) as Array<() => void>;
		// Preventing adding the same handler multiple times.
		const index = handlers.findIndex((e) => e === newMethod);
		if (index !== -1) {
			return;
		}


		handlers.push(newMethod);
		this._hubConnection.on(methodName, newMethod);
	}

	/// Removes the specified handler for the specified hub method.
	///
	/// You must pass the exact same Function instance as was previously passed to HubConnection.on. Passing a different instance (even if the function
	/// body is the same) will not remove the handler.
	///
	/// methodName: The name of the method to remove handlers for.
	/// method: The handler to remove. This must be the same Function instance as the one passed to {@link @aspnet/signalr.HubConnection.on}.
	/// If the method handler is omitted, all handlers for that method will be removed.
	///
	off(methodName : string, method : () => void) {
		if (!this._hubConnection) throw '_hubConnection 未初始化';
		if (!(methodName)) {
			return;
		}
		methodName = methodName.toLowerCase();

		if (method) {
			if (this._methods.has(methodName)) {
				const handlers = this._methods.get(methodName);
				if (!handlers) {
					return;
				}
				let removeIdx = handlers.indexOf(method);
				if (removeIdx != -1) {
					handlers.splice(removeIdx, 1);
					if (handlers.length === 0) {
						this._methods.delete(methodName);
					}
				}
			}
		} else {
			this._methods.delete(methodName);
		}
		this._hubConnection.off(methodName, method);
	}

	// /// 添加待处理的请求
	// // addRequest( requestId, Completer < APIResult > completer) {
	// addRequest(requestId, completer) {
	// 	console.debug('添加待处理请求 ' + requestId);
	// 	this._requestMap[requestId] = completer;
	// 	this.hooks.didAddRequest.call(requestId);
	// }

	// /// 移除请求
	// removeRequest(requestId) {
	// 	console.debug('移除待处理请求 $requestId');
	// 	this._requestMap.remove(requestId);
	// 	this.hooks.didRemoveRequest.call(requestId);
	// }

	// /// 处理请求的响应结果
	// _handleRequestResponse(response) {
	// 	console.debug('处理响应 ' + response);
	// 	data = response[0];
	// 	let requestId = data['SignalrType'];
	// 	if (_requestMap.containsKey(requestId)) {
	// 		hooks.didReceiveResponse.call(requestId);
	// 		let complete = _requestMap.remove(requestId);
	// 		if (!complete.isCompleted) complete.complete(APIResult.success(data));
	// 	} else {
	// 		hooks.didReceiveResponseWithoutRequest.call(requestId);
	// 	}
	// }
}