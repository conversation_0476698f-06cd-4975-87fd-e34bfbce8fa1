<template>
  <!-- <view class="container">
		<button @click="test">开启视频通话</button>
		<live-pusher id='livePusher' ref="livePusher" class="livePusher" :url="urls" mode="SD" :muted="true"
			:enable-camera="true" :auto-focus="true" :beauty="1" whiteness="2" aspect="16:9" @statechange="statechange"
			@netstatus="netstatus" @error="error" style="width: 100%;height: 400px;"></live-pusher>

		<live-player :src="urls2" autoplay @statechange="statechange1" @error="error1"
			style="width: 300px; height: 225px;" :data-user="dataUser" mode="RTC" debug="true" />
	</view> -->
  <view class="">
    <tuicallkit
      ref="TUICallKit"
      @getCallFin="getCallFin"
      @getCallStatus="getCallStatus"
    ></tuicallkit>
    <u-navbar title="视频通话" @leftClick="leftClick"> </u-navbar>
    <!-- <view v-if="type == 1">
			<view style="height: 150px;"></view>
			<button type="default" @click="call(id1)">打电话给小涛</button>
			<button type="default" @click="call(id2)">打电话给杰哥</button>
			<button type="default" @click="call(id3)">打电话给田老师</button>
			<button type="default" @click="call(id4)">打电话给阮老师</button>
			<button type="default" @click="call(id5)">打电话给邓老师</button>
			<button type="default" @click="call(id6)">打电话给小河</button>
			<button type="default" @click="call(id7)">打电话小号</button>
			<button type="default" @click="call(id8)">打给flutter</button>
		</view> -->
    <u-popup
      :show="showPrivacyPopup"
      :closeOnClickOverlay="false"
      :round="10"
      mode="bottom"
      @close="closePrivacyPopup()"
    >
      <view style="padding: 40rpx">
        <p
          style="
            text-align: center;
            margin-bottom: 40rpx;
            font-size: 20px;
            font-weight: 700;
          "
        >
          提示
        </p>
        <text>
          在你使用服务之前，请仔细阅读<text
            @click="onClickPrivacyPopupTitle()"
            style="color: #29b7a3"
            >{{ PrivacyPopupTitle }}</text
          >。如果你同意{{ PrivacyPopupTitle }}，请点击“同意”开始使用。
        </text>
        <view class="display-style" style="margin-top: 40rpx">
          <u-button
            text="拒绝"
            @click="handleRefusePrivacyAuthorization()"
          ></u-button>
          <!-- <u-button type="primary" id="agree-btn" text="同意" openType="agreePrivacyAuthorization"
						@agreeprivacyauthorization="handleAgreePrivacyAuthorization()"></u-button> -->
          <button
            id="agree-btn"
            class="btn-agree"
            open-type="agreePrivacyAuthorization"
            @agreeprivacyauthorization="handleAgreePrivacyAuthorization()"
          >
            同意
          </button>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { sendVideoCall } from '@/api/message.js';
import { getTXGetSign } from '@/api/identity.js';

const app = getApp();
export default {
  data() {
    return {
      id1: '7fa21d50-f8ef-49e6-b614-2a0d7452d0de',
      id2: '3a045e85-7138-cfee-62d2-ef82809426ad',
      id3: '3a07d9b4-066b-9efc-01ad-5ea15ee5b7cc',
      id4: '3a00fdef-a8a4-41a1-0c5a-808a9e3285fe',
      id5: '3a07d501-968f-d9b1-364d-b2400259bca3',
      id6: '3a058d3d-e71c-bca0-2b1c-c7b7cc191b04',
      id7: '3a067739-9121-2f9f-2af5-e109356e9a0e',
      id8: 'f6f6c339-44b1-42a9-be7c-0c1291367e6e-d',
      record: false,
      camera: false,
      type: 1,
      config: {
        sdkAppID: 1400780428, // 开通实时音视频服务创建应用后分配的 SDKAppID
        userID: app.globalData.userInfo.Id, // 用户 ID，可以由您的帐号系统指定
        userSig: '', // 身份签名，相当于登录密码的作用
      },
      TUICalling: null,
      docId: {},
      callStatus: '',
      client: null,
    };
  },
  onLoad({ type, docInfo }) {
    this.saveWXCallBack();
    this.type = type;
    this.docInfo = JSON.parse(docInfo);
    uni.showLoading({
      title: '正在加载数据',
    });
  },
  onReady() {
    getTXGetSign({
      userid: app.globalData.userInfo.Id,
      tail: '',
    }).then((res) => {
      if (res.Type === 200) {
        const init = this.$refs.TUICallKit.init({
          sdkAppID: 1400780428, // 替换为您自己账号下的 SDKAppId
          userID: app.globalData.userInfo.Id, // 填写当前用的 userID
          userSig: res.Data,
        });
        if (init) {
          console.log('init', init);
          init
            .then(async (ress) => {
              this.TUICalling = this.$refs.TUICallKit;
              this.TUICalling.setSelfInfo(
                app.globalData.userInfo.Name,
                app.globalData.userInfo.HeadImg
                  ? app.globalData.userInfo.HeadImg
                  : ''
              );
              let data = {
                ReceiveIds: [
                  this.docInfo.type === 0
                    ? this.docInfo.userId
                    : this.docInfo.userId,
                ],
                ClientMethod: 'VideoInviteResponse',
                Body: app.globalData.userInfo.Id,
              };
              let datas = await sendVideoCall(data);
              uni.hideLoading();
            })
            .catch((err) => {
              console.log('视频通话初始化失败', err);
            });
        }
      }
    });
  },
  onShow() {},
  methods: {
    leftClick() {
      uni.showModal({
        content: '是否结束通话?',
        success: (res) => {
          if (res.confirm) {
            if (!this.callStatus) {
              uni.navigateBack();
            } else if (this.callStatus == 'calling') {
              if (this.TUICalling.reject) {
                this.TUICalling.reject();
              } else {
                uni.navigateBack();
              }
            } else if (this.callStatus == 'connected') {
              if (this.TUICalling._hangUp) {
                this.TUICalling._hangUp();
              } else {
                uni.navigateBack();
              }
            }
          }
        },
      });
    },
    getCallFin(data) {
      console.log('获取到的数据', data);
      if (data.detail) {
        uni.navigateBack();
        console.log('视频打完了');
      }
    },
    getCallStatus(data) {
      console.log('data.detail', data.detail);
      this.callStatus = data.detail;
    },
    getSettingRecord(
      options = {
        scope: 'scope.record',
        content: '请前往设置页打开麦克风',
      }
    ) {
      var self = this;
      return new Promise((resolve, reject) => {
        uni.getSetting({
          success: (res) => {
            let auth = res.authSetting[options.scope];
            console.warn('scope.record=', auth, typeof auth);
            if (auth === true) {
              // 用户已经同意授权
              resolve(true);
            } else if (auth === undefined) {
              // 首次发起授权
              uni.authorize({
                scope: options.scope,
                success() {
                  resolve(true);
                },
                fail(res) {},
              });
            } else if (auth === false) {
              // 非首次发起授权，用户拒绝过 => 弹出提示对话框
              uni.showModal({
                title: '授权提示',
                content: options.content,
                success: (tipRes) => {
                  if (tipRes.confirm) {
                    uni.openSetting({
                      success: (settingRes) => {
                        if (settingRes.authSetting[options.scope]) {
                          resolve(true);
                        }
                        console.warn('settingRes', settingRes);
                      },
                    });
                  }
                },
              });
            }
          },
        });
      });
    },

    //申请获取相机权限
    checkScopeCamera() {
      var options = {
        scope: 'scope.camera',
        content: '请前往设置页打开摄像头',
      };
      var promise2 = this.getSettingRecord(options);
      promise2.then((res) => {
        if (res) {
          uni.showToast({
            title: '相机权限已获取',
          });
        }
      });
    },
    checkScope() {
      return new Promise((resolve, reject) => {
        var promise1 = this.getSettingRecord();
        var options = {
          scope: 'scope.camera',
          content: '请前往设置页打开摄像头',
        };
        var promise2 = this.getSettingRecord(options);
        Promise.all([promise1, promise2]).then((res) => {
          console.warn('Promise.all', res);
          if (res[0] && res[1]) {
            console.warn('获取权限成功');
            uni.showToast({
              title: '获取麦克风、相机权限成功',
              icon: 'none',
            });
            resolve(true);
          } else {
            reject(false);
          }
        });
      });
    },
    call(id) {
      this.checkScope()
        .then(async (res) => {
          console.log('resssssssssssss', res);
          // sendVideoCall(data).then(res=>{
          // 	// this.$refs.TUICallKit.handleSDKReady(e,()=>{
          // 	// 	this.$refs.TUICallKit.setSelfInfo(app.globalData.userInfo.Name, app.globalData.userInfo
          // 	// 		.HeadImg);
          // 	// 	this.$refs.TUICallKit.call({
          // 	// 		userID: id,
          // 	// 		type: 2
          // 	// 	})
          // 	// })
          // 	console.log('this.$refs.TUICallKit.handleSDKReady()',this.$refs.TUICallKit.handleSDKReady())
          // }).catch(err=>{
          // 	console.log('请求出错了')
          // })
          // if (!datas) {
          // 	// this.$refs.TUICallKit.setSelfInfo(app.globalData.userInfo.Name, app.globalData.userInfo
          // 	// 	.HeadImg);
          // 	// this.$refs.TUICallKit.call({
          // 	// 	userID: id,
          // 	// 	type: 2
          // 	// })
          // 	this.TUICalling.call({
          // 		userID: id,
          // 		type: 2
          // 	})
          // }
          // sendVideoCall(data)
          // console.log('this.$refs.TUICallKit.handleSDKReady()',this.$refs.TUICallKit.handleSDKReady())
        })
        .catch((err) => {});
    },
  },
  onUnload() {
    this.TUICalling.destroyed();
  },
};
</script>

<style></style>
