import{TRTC as t,TIM as e,TSignaling as i}from"./libSrcConfig";class a{on(t,e,i){"function"==typeof e?(this._stores=this._stores||{},(this._stores[t]=this._stores[t]||[]).push({cb:e,ctx:i})):console.error("listener must be a function")}emit(t){this._stores=this._stores||{};let e,i=this._stores[t];if(i){i=i.slice(0),e=[].slice.call(arguments,1),e[0]={eventCode:t,data:e[0]};for(let t=0,a=i.length;t<a;t++)i[t].cb.apply(i[t].ctx,e)}}off(t,e){if(this._stores=this._stores||{},!arguments.length)return void(this._stores={});const i=this._stores[t];if(!i)return;if(1===arguments.length)return void delete this._stores[t];let a;for(let t=0,n=i.length;t<n;t++)if(a=i[t].cb,a===e){i.splice(t,1);break}}}const n={INVITED:"INVITED",GROUP_CALL_INVITEE_LIST_UPDATE:"GROUP_CALL_INVITEE_LIST_UPDATE",USER_ENTER:"USER_ENTER",USER_LEAVE:"USER_LEAVE",USER_ACCEPT:"USER_ACCEPT",USER_UPDATE:"USER_UPDATE",REJECT:"REJECT",NO_RESP:"NO_RESP",LINE_BUSY:"LINE_BUSY",CALLING_CANCEL:"CALLING_CANCEL",CALLING_TIMEOUT:"CALLING_TIMEOUT",CALL_END:"CALL_END",USER_VIDEO_AVAILABLE:"USER_VIDEO_AVAILABLE",USER_AUDIO_AVAILABLE:"USER_AUDIO_AVAILABLE",USER_VOICE_VOLUME:"USER_VOICE_VOLUME",SDK_READY:"SDK_READY",KICKED_OUT:"KICKED_OUT",CALL_MODE:"CALL_MODE",HANG_UP:"HANG_UP",MESSAGE_SENT_BY_ME:"onMessageSentByMe",ERROR:"ERROR"},s={IDLE:"idle",CALLING:"calling",CONNECTED:"connected"},r=1,o=2,l=3,h=4,c=5,d=1,u={AUDIO:1,VIDEO:2},g="inviter",p="invitee",I=["","audioCall","videoCall"],T="audio",m="video",f={EAR:"ear",SPEAKER:"speaker"},E={BASE:Math.pow(2,16),PRO:Math.pow(2,17),ULTIMATE:Math.pow(2,18)},v=-1002,D=-1003,y=-1101,C=-1201,S=-1202,R=-1203,_='The package you purchased does not support this ability. You can refer to "Service Activation" to purchase: https://buy.cloud.tencent.com/avc',w="TIM  SDK version is too old, Please upgrade version >= 2.20.1",L="camera or microphone not authorized",A="init is not called, the TUICallEngine API needs to be used after init",N="invite",U="switch-audio-video";class b{constructor(t){this.TSignaling=t.TSignaling}handleNewSignaling(t,e){return{extraInfo:"",...t,version:4,businessID:"av_call",platform:"MiniApp",data:{cmd:I[t.call_type],room_id:t.room_id,message:"",...e}}}extractCallingInfoFromMessage(t){if(!t||"TIMCustomElem"!==t.type)return"";const e=JSON.parse(t.payload.data);if(e.businessID!==d)return"";switch(e.actionType){case r:{const t=JSON.parse(e.data);return t.call_end>0&&!e.groupID?t.call_end:0!==t.call_end&&t.room_id?"发起通话":"结束群聊"}case o:return"取消通话";case l:return"已接听";case h:return"拒绝通话";case c:return"无应答";default:return""}}handleError(t,e){return console.error(`TSignalingClint ${e}`,t),t}_handleInviteData(t){const{type:e,roomID:i,userIDList:a,hangup:n,switchMode:s}=t;if(n)return JSON.stringify(this.handleNewSignaling({version:0,call_type:e,call_end:n.callEnd},{cmd:"hangup"}));if(s){const t={version:0,call_type:e,room_id:i},a={cmd:"switchToVideo"};return s===T&&(t.switch_to_audio_call="switch_to_audio_call",a.cmd="switchToAudio"),JSON.stringify(this.handleNewSignaling(t,a))}return JSON.stringify(this.handleNewSignaling({version:0,call_type:e,room_id:i},{userIDs:a}))}_handleInviteGroupData(t){const{type:e,roomID:i,hangup:a}=t;let n=null;return n=a?JSON.stringify(this.handleNewSignaling({version:0,call_type:e,call_end:a.call_end},{cmd:"hangup"})):JSON.stringify(this.handleNewSignaling({version:0,call_type:e,room_id:i})),n}async invite(t){const{userID:e,offlinePushInfo:i,hangup:a,switchMode:n}=t;try{return await this.TSignaling.invite({userID:e,data:this._handleInviteData(t),timeout:a?0:30,offlinePushInfo:i})}catch(t){return a?this.handleError(t,"hangup C2C"):n?this.handleError(t,n):this.handleError(t,"invite")}}async inviteGroup(t){const{groupID:e,userIDList:i,offlinePushInfo:a,hangup:n}=t;try{return await this.TSignaling.inviteInGroup({groupID:e,inviteeList:i,timeout:n?0:30,data:this._handleInviteGroupData(t),offlinePushInfo:a})}catch(t){return n?this.handleError(t,"hangup group"):this.handleError(t,"inviteGroup")}}async accept(t,e){const{inviteID:i,type:a,...n}=t;try{return await this.TSignaling.accept({inviteID:i,data:JSON.stringify(this.handleNewSignaling({version:0,call_type:a,...n},e))})}catch(t){return this.handleError(t,"accept")}}async reject(t){const{inviteID:e,type:i,lineBusy:a}=t,n={version:0,call_type:i};let s=null;a?(n.line_busy=a,s=JSON.stringify(this.handleNewSignaling(n,{message:"lineBusy"}))):s=JSON.stringify(this.handleNewSignaling(n));try{return await this.TSignaling.reject({inviteID:e,data:s})}catch(t){return a?this.handleError(t,"line_busy"):this.handleError(t,"reject")}}async cancel(t){const{inviteID:e,callType:i}=t;try{return await this.TSignaling.cancel({inviteID:e,data:JSON.stringify(this.handleNewSignaling({version:0,call_type:i}))})}catch(t){return this.handleError(t,"cancel")}}async hangup(t){const{userIDList:e,callType:i,callEnd:a=0,isGroupCall:n=!1,groupID:s=""}=t,r={version:0,call_type:i,call_end:a},o=JSON.stringify(this.handleNewSignaling(r,{cmd:"hangup"}));n&&s?await this.TSignaling.inviteInGroup({inviteeList:e,groupID:s,data:o,timeout:0}):await this.TSignaling.invite({userID:e[0],data:o,timeout:0})}async switchCallMode(t){const{userID:e,callType:i,roomID:a,mode:n}=t;return this.invite({userID:e,type:i,roomID:a,switchMode:n})}destroyed(){this.TSignaling=null}setLogLevel(t){this.TSignaling.setLogLevel(t)}}class O{constructor(t){this._emitter=t.emitter}onCallEnd(t){const{userID:e,callEnd:i,message:a,roomID:s,callMediaType:r,callRole:o,totalTime:l}=t;this._emitter.emit(n.CALL_END,{userID:e,callEnd:i,message:a,roomID:s,callMediaType:r,callRole:o,totalTime:l})}onInvited(t){const{sponsor:e,inviteeList:i,isFromGroup:a,inviteData:s,inviteID:r,userIDList:o}=t;this._emitter.emit(n.INVITED,{sponsor:e,inviteeList:i,userIDList:o,isFromGroup:a,inviteID:r,inviteData:s})}onLineBusy(t){const{inviteID:e,invitee:i,userID:a}=t;this._emitter.emit(n.LINE_BUSY,{inviteID:e,invitee:i,userID:a,reason:"line busy"})}onReject(t){const{inviteID:e,invitee:i,userID:a}=t;this._emitter.emit(n.REJECT,{inviteID:e,invitee:i,userID:a,reason:"reject"})}onNoResp(t){const{groupID:e="",inviteID:i,sponsor:a,timeoutUserList:s,userIDList:r}=t;this._emitter.emit(n.NO_RESP,{groupID:e,inviteID:i,sponsor:a,timeoutUserList:s,userIDList:r})}onCancel(t){const{inviteID:e,invitee:i,userID:a}=t;this._emitter.emit(n.CALLING_CANCEL,{inviteID:e,invitee:i,userID:a})}onTimeout(t){const{inviteID:e,groupID:i,sponsor:a,timeoutUserList:s}=t;this._emitter.emit(n.CALLING_TIMEOUT,{groupID:i,inviteID:e,sponsor:a,timeoutUserList:s})}onUserAccept(t){const{userID:e,userList:i}=t;this._emitter.emit(n.USER_ACCEPT,{userID:e,userList:i})}onUserEnter(t){const{userID:e,playerList:i}=t;this._emitter.emit(n.USER_ENTER,{userID:e,playerList:i})}onUserLeave(t){const{userID:e,playerList:i}=t;this._emitter.emit(n.USER_LEAVE,{userID:e,playerList:i})}onUserUpdate(t){const{pusher:e,playerList:i}=t;this._emitter.emit(n.USER_UPDATE,{pusher:e,playerList:i})}onSdkReady(t){this._emitter.emit(n.SDK_READY,t)}onKickedOut(t){this._emitter.emit(n.KICKED_OUT,t)}onCallMode(t){this._emitter.emit(n.CALL_MODE,t)}onMessageSentByMe(t){this._emitter.emit(n.MESSAGE_SENT_BY_ME,t)}destroyed(){this._emitter=null}}const M="1.1.0";let V,x;V="undefined"!=typeof console?console:"undefined"!=typeof global&&global.console?global.console:"undefined"!=typeof window&&window.console?window.console:{};const P=function(){},$=["assert","clear","count","debug","dir","dirxml","error","exception","group","groupCollapsed","groupEnd","info","log","markTimeline","profile","profileEnd","table","time","timeEnd","timeStamp","trace","warn"];let{length:k}=$;for(;k--;)x=$[k],console[x]||(V[x]=P);V.methods=$;var H=V;const G=function(t){return"function"==typeof Array.isArray?Array.isArray(t):"array"===function(t){return Object.prototype.toString.call(t).match(/^\[object (.*)\]$/)[1].toLowerCase()}(t)};let j=0;Date.now||(Date.now=function(){return(new Date).getTime()});const K={now(){0===j&&(j=Date.now()-1);const t=Date.now()-j;return t>4294967295?(j+=4294967295,Date.now()-j):t},utc:()=>Math.round(Date.now()/1e3)},B=function(t){return t instanceof Error};let z=0;const J=new Map;function F(){const t=new Date;return`TUICallEngine ${t.toLocaleTimeString("en-US",{hour12:!1})}.${function(t){let e;switch(t.toString().length){case 1:e=`00${t}`;break;case 2:e=`0${t}`;break;default:e=t}return e}(t.getMilliseconds())}:`}const Y={_data:[],_length:0,_visible:!1,arguments2String(t){let e;if(1===t.length)e=F()+t[0];else{e=F();for(let n=0,{length:s}=t;n<s;n++)a=t[n],G(a)||function(t){return null!==t&&"object"==typeof t}(a)?B(t[n])?e+=(i=t[n],JSON.stringify(i,["message","code"])):e+=JSON.stringify(t[n]):e+=t[n],e+=" "}var i,a;return e},debug(){if(z<=-1){const t=this.arguments2String(arguments);Y.record(t,"debug"),H.debug(t)}},log(){if(z<=0){const t=this.arguments2String(arguments);Y.record(t,"log"),H.log(t)}},info(){if(z<=1){const t=this.arguments2String(arguments);Y.record(t,"info"),H.info(t)}},warn(){if(z<=2){const t=this.arguments2String(arguments);Y.record(t,"warn"),H.warn(t)}},error(){if(z<=3){const t=this.arguments2String(arguments);Y.record(t,"error"),H.error(t)}},time(t){J.set(t,K.now())},timeEnd(t){if(J.has(t)){const e=K.now()-J.get(t);return J.delete(t),e}return H.warn(`未找到对应label: ${t}, 请在调用 logger.timeEnd 前，调用 logger.time`),0},setLevel(t){t<4&&H.log(`${F()}set level from ${z} to ${t}`),z=t},getLevel:()=>z,record(t,e){1100===Y._length&&(Y._data.splice(0,100),Y._length=1e3),Y._length++,Y._data.push(`${t} [${e}] \n`)},getLog:()=>Y._data};var q="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},X={exports:{}};(function(t,e,i){var a=function(t,e,i){i=n.extend({},n.options,i);var s=n.runValidations(t,e,i);if(s.some((function(t){return n.isPromise(t.error)})))throw new Error("Use validate.async if you want support for promises");return a.processValidationResults(s,i)},n=a;n.extend=function(t){return[].slice.call(arguments,1).forEach((function(e){for(var i in e)t[i]=e[i]})),t},n.extend(a,{version:{major:0,minor:13,patch:1,metadata:null,toString:function(){var t=n.format("%{major}.%{minor}.%{patch}",n.version);return n.isEmpty(n.version.metadata)||(t+="+"+n.version.metadata),t}},Promise:"undefined"!=typeof Promise?Promise:null,EMPTY_STRING_REGEXP:/^\s*$/,runValidations:function(t,e,i){var a,s,r,o,l,h,c,d=[];for(a in(n.isDomElement(t)||n.isJqueryElement(t))&&(t=n.collectFormValues(t)),e)for(s in r=n.getDeepObjectValue(t,a),o=n.result(e[a],r,t,a,i,e)){if(!(l=n.validators[s]))throw c=n.format("Unknown validator %{name}",{name:s}),new Error(c);h=o[s],(h=n.result(h,r,t,a,i,e))&&d.push({attribute:a,value:r,validator:s,globalOptions:i,attributes:t,options:h,error:l.call(l,r,h,a,t,i)})}return d},processValidationResults:function(t,e){t=n.pruneEmptyErrors(t,e),t=n.expandMultipleErrors(t,e),t=n.convertErrorMessages(t,e);var i=e.format||"grouped";if("function"!=typeof n.formatters[i])throw new Error(n.format("Unknown format %{format}",e));return t=n.formatters[i](t),n.isEmpty(t)?void 0:t},async:function(t,e,i){var a=(i=n.extend({},n.async.options,i)).wrapErrors||function(t){return t};!1!==i.cleanAttributes&&(t=n.cleanAttributes(t,e));var s=n.runValidations(t,e,i);return new n.Promise((function(r,o){n.waitForResults(s).then((function(){var l=n.processValidationResults(s,i);l?o(new a(l,i,t,e)):r(t)}),(function(t){o(t)}))}))},single:function(t,e,i){return i=n.extend({},n.single.options,i,{format:"flat",fullMessages:!1}),n({single:t},{single:e},i)},waitForResults:function(t){return t.reduce((function(t,e){return n.isPromise(e.error)?t.then((function(){return e.error.then((function(t){e.error=t||null}))})):t}),new n.Promise((function(t){t()})))},result:function(t){var e=[].slice.call(arguments,1);return"function"==typeof t&&(t=t.apply(null,e)),t},isNumber:function(t){return"number"==typeof t&&!isNaN(t)},isFunction:function(t){return"function"==typeof t},isInteger:function(t){return n.isNumber(t)&&t%1==0},isBoolean:function(t){return"boolean"==typeof t},isObject:function(t){return t===Object(t)},isDate:function(t){return t instanceof Date},isDefined:function(t){return null!=t},isPromise:function(t){return!!t&&n.isFunction(t.then)},isJqueryElement:function(t){return t&&n.isString(t.jquery)},isDomElement:function(t){return!!t&&!(!t.querySelectorAll||!t.querySelector)&&(!(!n.isObject(document)||t!==document)||("object"==typeof HTMLElement?t instanceof HTMLElement:t&&"object"==typeof t&&null!==t&&1===t.nodeType&&"string"==typeof t.nodeName))},isEmpty:function(t){var e;if(!n.isDefined(t))return!0;if(n.isFunction(t))return!1;if(n.isString(t))return n.EMPTY_STRING_REGEXP.test(t);if(n.isArray(t))return 0===t.length;if(n.isDate(t))return!1;if(n.isObject(t)){for(e in t)return!1;return!0}return!1},format:n.extend((function(t,e){return n.isString(t)?t.replace(n.format.FORMAT_REGEXP,(function(t,i,a){return"%"===i?"%{"+a+"}":String(e[a])})):t}),{FORMAT_REGEXP:/(%?)%\{([^\}]+)\}/g}),prettify:function(t){return n.isNumber(t)?100*t%1==0?""+t:parseFloat(Math.round(100*t)/100).toFixed(2):n.isArray(t)?t.map((function(t){return n.prettify(t)})).join(", "):n.isObject(t)?n.isDefined(t.toString)?t.toString():JSON.stringify(t):(t=""+t).replace(/([^\s])\.([^\s])/g,"$1 $2").replace(/\\+/g,"").replace(/[_-]/g," ").replace(/([a-z])([A-Z])/g,(function(t,e,i){return e+" "+i.toLowerCase()})).toLowerCase()},stringifyValue:function(t,e){return(e&&e.prettify||n.prettify)(t)},isString:function(t){return"string"==typeof t},isArray:function(t){return"[object Array]"==={}.toString.call(t)},isHash:function(t){return n.isObject(t)&&!n.isArray(t)&&!n.isFunction(t)},contains:function(t,e){return!!n.isDefined(t)&&(n.isArray(t)?-1!==t.indexOf(e):e in t)},unique:function(t){return n.isArray(t)?t.filter((function(t,e,i){return i.indexOf(t)==e})):t},forEachKeyInKeypath:function(t,e,i){if(n.isString(e)){var a,s="",r=!1;for(a=0;a<e.length;++a)switch(e[a]){case".":r?(r=!1,s+="."):(t=i(t,s,!1),s="");break;case"\\":r?(r=!1,s+="\\"):r=!0;break;default:r=!1,s+=e[a]}return i(t,s,!0)}},getDeepObjectValue:function(t,e){if(n.isObject(t))return n.forEachKeyInKeypath(t,e,(function(t,e){if(n.isObject(t))return t[e]}))},collectFormValues:function(t,e){var i,a,s,r,o,l,h={};if(n.isJqueryElement(t)&&(t=t[0]),!t)return h;for(e=e||{},r=t.querySelectorAll("input[name], textarea[name]"),i=0;i<r.length;++i)if(s=r.item(i),!n.isDefined(s.getAttribute("data-ignored"))){var c=s.name.replace(/\./g,"\\\\.");l=n.sanitizeFormValue(s.value,e),"number"===s.type?l=l?+l:null:"checkbox"===s.type?s.attributes.value?s.checked||(l=h[c]||null):l=s.checked:"radio"===s.type&&(s.checked||(l=h[c]||null)),h[c]=l}for(r=t.querySelectorAll("select[name]"),i=0;i<r.length;++i)if(s=r.item(i),!n.isDefined(s.getAttribute("data-ignored"))){if(s.multiple)for(a in l=[],s.options)(o=s.options[a])&&o.selected&&l.push(n.sanitizeFormValue(o.value,e));else{var d=void 0!==s.options[s.selectedIndex]?s.options[s.selectedIndex].value:"";l=n.sanitizeFormValue(d,e)}h[s.name]=l}return h},sanitizeFormValue:function(t,e){return e.trim&&n.isString(t)&&(t=t.trim()),!1!==e.nullify&&""===t?null:t},capitalize:function(t){return n.isString(t)?t[0].toUpperCase()+t.slice(1):t},pruneEmptyErrors:function(t){return t.filter((function(t){return!n.isEmpty(t.error)}))},expandMultipleErrors:function(t){var e=[];return t.forEach((function(t){n.isArray(t.error)?t.error.forEach((function(i){e.push(n.extend({},t,{error:i}))})):e.push(t)})),e},convertErrorMessages:function(t,e){var i=[],a=(e=e||{}).prettify||n.prettify;return t.forEach((function(t){var s=n.result(t.error,t.value,t.attribute,t.options,t.attributes,t.globalOptions);return n.isString(s)?("^"===s[0]?s=s.slice(1):!1!==e.fullMessages&&(s=n.capitalize(a(t.attribute))+" "+s),s=s.replace(/\\\^/g,"^"),s=n.format(s,{value:n.stringifyValue(t.value,e)}),void i.push(n.extend({},t,{error:s}))):void i.push(t)})),i},groupErrorsByAttribute:function(t){var e={};return t.forEach((function(t){var i=e[t.attribute];i?i.push(t):e[t.attribute]=[t]})),e},flattenErrorsToArray:function(t){return t.map((function(t){return t.error})).filter((function(t,e,i){return i.indexOf(t)===e}))},cleanAttributes:function(t,e){function i(t,e,i){return n.isObject(t[e])?t[e]:t[e]=!!i||{}}return n.isObject(e)&&n.isObject(t)?(e=function(t){var e,a={};for(e in t)t[e]&&n.forEachKeyInKeypath(a,e,i);return a}(e),function t(e,i){if(!n.isObject(e))return e;var a,s,r=n.extend({},e);for(s in e)a=i[s],n.isObject(a)?r[s]=t(r[s],a):a||delete r[s];return r}(t,e)):{}},exposeModule:function(t,e,i,a,n){i?(a&&a.exports&&(i=a.exports=t),i.validate=t):(e.validate=t,t.isFunction(n)&&n.amd&&n([],(function(){return t})))},warn:function(t){"undefined"!=typeof console&&console.warn&&console.warn("[validate.js] "+t)},error:function(t){"undefined"!=typeof console&&console.error&&console.error("[validate.js] "+t)}}),a.validators={presence:function(t,e){if(!1!==(e=n.extend({},this.options,e)).allowEmpty?!n.isDefined(t):n.isEmpty(t))return e.message||this.message||"can't be blank"},length:function(t,e,i){if(n.isDefined(t)){var a,s=(e=n.extend({},this.options,e)).is,r=e.maximum,o=e.minimum,l=e.tokenizer||function(t){return t},h=[],c=(t=l(t)).length;return n.isNumber(c)?(n.isNumber(s)&&c!==s&&(a=e.wrongLength||this.wrongLength||"is the wrong length (should be %{count} characters)",h.push(n.format(a,{count:s}))),n.isNumber(o)&&c<o&&(a=e.tooShort||this.tooShort||"is too short (minimum is %{count} characters)",h.push(n.format(a,{count:o}))),n.isNumber(r)&&c>r&&(a=e.tooLong||this.tooLong||"is too long (maximum is %{count} characters)",h.push(n.format(a,{count:r}))),h.length>0?e.message||h:void 0):e.message||this.notValid||"has an incorrect length"}},numericality:function(t,e,i,a,s){if(n.isDefined(t)){var r,o,l=[],h={greaterThan:function(t,e){return t>e},greaterThanOrEqualTo:function(t,e){return t>=e},equalTo:function(t,e){return t===e},lessThan:function(t,e){return t<e},lessThanOrEqualTo:function(t,e){return t<=e},divisibleBy:function(t,e){return t%e==0}},c=(e=n.extend({},this.options,e)).prettify||s&&s.prettify||n.prettify;if(n.isString(t)&&e.strict){var d="^-?(0|[1-9]\\d*)";if(e.onlyInteger||(d+="(\\.\\d+)?"),d+="$",!new RegExp(d).test(t))return e.message||e.notValid||this.notValid||this.message||"must be a valid number"}if(!0!==e.noStrings&&n.isString(t)&&!n.isEmpty(t)&&(t=+t),!n.isNumber(t))return e.message||e.notValid||this.notValid||this.message||"is not a number";if(e.onlyInteger&&!n.isInteger(t))return e.message||e.notInteger||this.notInteger||this.message||"must be an integer";for(r in h)if(o=e[r],n.isNumber(o)&&!h[r](t,o)){var u="not"+n.capitalize(r),g=e[u]||this[u]||this.message||"must be %{type} %{count}";l.push(n.format(g,{count:o,type:c(r)}))}return e.odd&&t%2!=1&&l.push(e.notOdd||this.notOdd||this.message||"must be odd"),e.even&&t%2!=0&&l.push(e.notEven||this.notEven||this.message||"must be even"),l.length?e.message||l:void 0}},datetime:n.extend((function(t,e){if(!n.isFunction(this.parse)||!n.isFunction(this.format))throw new Error("Both the parse and format functions needs to be set to use the datetime/date validator");if(n.isDefined(t)){var i,a=[],s=(e=n.extend({},this.options,e)).earliest?this.parse(e.earliest,e):NaN,r=e.latest?this.parse(e.latest,e):NaN;return t=this.parse(t,e),isNaN(t)||e.dateOnly&&t%864e5!=0?(i=e.notValid||e.message||this.notValid||"must be a valid date",n.format(i,{value:arguments[0]})):(!isNaN(s)&&t<s&&(i=e.tooEarly||e.message||this.tooEarly||"must be no earlier than %{date}",i=n.format(i,{value:this.format(t,e),date:this.format(s,e)}),a.push(i)),!isNaN(r)&&t>r&&(i=e.tooLate||e.message||this.tooLate||"must be no later than %{date}",i=n.format(i,{date:this.format(r,e),value:this.format(t,e)}),a.push(i)),a.length?n.unique(a):void 0)}}),{parse:null,format:null}),date:function(t,e){return e=n.extend({},e,{dateOnly:!0}),n.validators.datetime.call(n.validators.datetime,t,e)},format:function(t,e){(n.isString(e)||e instanceof RegExp)&&(e={pattern:e});var i,a=(e=n.extend({},this.options,e)).message||this.message||"is invalid",s=e.pattern;if(n.isDefined(t))return n.isString(t)?(n.isString(s)&&(s=new RegExp(e.pattern,e.flags)),(i=s.exec(t))&&i[0].length==t.length?void 0:a):a},inclusion:function(t,e){if(n.isDefined(t)&&(n.isArray(e)&&(e={within:e}),e=n.extend({},this.options,e),!n.contains(e.within,t))){var i=e.message||this.message||"^%{value} is not included in the list";return n.format(i,{value:t})}},exclusion:function(t,e){if(n.isDefined(t)&&(n.isArray(e)&&(e={within:e}),e=n.extend({},this.options,e),n.contains(e.within,t))){var i=e.message||this.message||"^%{value} is restricted";return n.isString(e.within[t])&&(t=e.within[t]),n.format(i,{value:t})}},email:n.extend((function(t,e){var i=(e=n.extend({},this.options,e)).message||this.message||"is not a valid email";if(n.isDefined(t))return n.isString(t)&&this.PATTERN.exec(t)?void 0:i}),{PATTERN:/^(?:[a-z0-9!#$%&'*+\/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+\/=?^_`{|}~-]+)*|"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])*")@(?:(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])$/i}),equality:function(t,e,i,a,s){if(n.isDefined(t)){n.isString(e)&&(e={attribute:e});var r=(e=n.extend({},this.options,e)).message||this.message||"is not equal to %{attribute}";if(n.isEmpty(e.attribute)||!n.isString(e.attribute))throw new Error("The attribute must be a non empty string");var o=n.getDeepObjectValue(a,e.attribute),l=e.comparator||function(t,e){return t===e},h=e.prettify||s&&s.prettify||n.prettify;return l(t,o,e,i,a)?void 0:n.format(r,{attribute:h(e.attribute)})}},url:function(t,e){if(n.isDefined(t)){var i=(e=n.extend({},this.options,e)).message||this.message||"is not a valid url",a=e.schemes||this.schemes||["http","https"],s=e.allowLocal||this.allowLocal||!1,r=e.allowDataUrl||this.allowDataUrl||!1;if(!n.isString(t))return i;var o="^(?:(?:"+a.join("|")+")://)(?:\\S+(?::\\S*)?@)?(?:",l="(?:\\.(?:[a-z\\u00a1-\\uffff]{2,}))";return s?l+="?":o+="(?!(?:10|127)(?:\\.\\d{1,3}){3})(?!(?:169\\.254|192\\.168)(?:\\.\\d{1,3}){2})(?!172\\.(?:1[6-9]|2\\d|3[0-1])(?:\\.\\d{1,3}){2})",o+="(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[1-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)*"+l+")(?::\\d{2,5})?(?:[/?#]\\S*)?$",r&&(o="(?:"+o+")|(?:^data:(?:\\w+\\/[-+.\\w]+(?:;[\\w=]+)*)?(?:;base64)?,[A-Za-z0-9-_.!~\\*'();\\/?:@&=+$,%]*$)"),new RegExp(o,"i").exec(t)?void 0:i}},type:n.extend((function(t,e,i,a,s){if(n.isString(e)&&(e={type:e}),n.isDefined(t)){var r,o=n.extend({},this.options,e),l=o.type;if(!n.isDefined(l))throw new Error("No type was specified");if(r=n.isFunction(l)?l:this.types[l],!n.isFunction(r))throw new Error("validate.validators.type.types."+l+" must be a function.");if(!r(t,o,i,a,s)){var h=e.message||this.messages[l]||this.message||o.message||(n.isFunction(l)?"must be of the correct type":"must be of type %{type}");return n.isFunction(h)&&(h=h(t,e,i,a,s)),n.format(h,{attribute:n.prettify(i),type:l})}}}),{types:{object:function(t){return n.isObject(t)&&!n.isArray(t)},array:n.isArray,integer:n.isInteger,number:n.isNumber,string:n.isString,date:n.isDate,boolean:n.isBoolean},messages:{}})},a.formatters={detailed:function(t){return t},flat:n.flattenErrorsToArray,grouped:function(t){var e;for(e in t=n.groupErrorsByAttribute(t))t[e]=n.flattenErrorsToArray(t[e]);return t},constraint:function(t){var e;for(e in t=n.groupErrorsByAttribute(t))t[e]=t[e].map((function(t){return t.validator})).sort();return t}},a.exposeModule(a,this,t,e,i)}).call(q,X.exports,X,null);var W=X.exports;const Z="number",Q="string",tt="object",et="function",it={createInstance:{sdkAppID:{presence:!0,type:Z},tim:{type:tt}},destroyInstance:{},init:{userID:{presence:!0,type:Q},userSig:{presence:!0,type:Q}},on:{eventCode:{presence:!0,type:Q},handler:{presence:!0,type:et},context:{presence:!0,type:tt}},off:{eventCode:{presence:!0,type:Q},handler:{presence:!0,type:et}},call:{userID:{presence:!0,type:Q},type:{presence:!0,type:Z}},groupCall:{groupID:{presence:!0,type:Q},type:{presence:!0,type:Z},userIDList:{presence:!0,type:"array"}},accept:{},reject:{},hangup:{},switchCallMediaType:{type:{presence:!0,type:Z}},openCamera:{},closeCamera:{},switchCamera:{},openMicrophone:{},closeMicrophone:{},selectAudioPlaybackDevice:{type:{presence:!0,type:Q}},setSelfInfo:{nickName:{presence:!0,type:Q},avatar:{presence:!0,type:Q}}},at={createInstance:"",destroyInstance:"",init:"",on:"",off:"",call:[s.IDLE],groupCall:[s.IDLE],accept:[s.CALLING],reject:[s.CALLING],hangup:[s.CALLING,s.CONNECTED],switchCallMediaType:[s.CALLING,s.CONNECTED],openCamera:[s.CALLING,s.CONNECTED],closeCamera:[s.CALLING,s.CONNECTED],switchCamera:[s.CALLING,s.CONNECTED],openMicrophone:[s.CALLING,s.CONNECTED],closeMicrophone:[s.CALLING,s.CONNECTED],selectAudioPlaybackDevice:[s.CALLING,s.CONNECTED],setSelfInfo:""},nt={createInstance:!1,destroyInstance:!0,init:!1,on:!1,off:!1,call:!0,groupCall:!0,accept:!0,reject:!0,hangup:!0,switchCallMediaType:!0,openCamera:!0,closeCamera:!0,switchCamera:!0,openMicrophone:!0,closeMicrophone:!0,selectAudioPlaybackDevice:!0,setSelfInfo:!0};class st extends Error{code;message;data;constructor(t,e,i){super(e),this.code=t,this.message=e,this.data=i}static error(t,e,i){return i?new st(t,e,i):new st(t,e)}}W.validators.type.types.function=function(t){return t instanceof Function};class rt{paramsMatchedRule=it;statusMatchedRule=at;requireInit=nt;getParamsMatchedRule(t){return this.paramsMatchedRule[t]}getStatusMatchedRule(t){return this.statusMatchedRule[t]}getInitReadyRule(t){return this.requireInit[t]}}class ot{notify(t,e,i){throw st.error(e,`[TUICallEngine:${t.api},${e}]${i}`)}}class lt{api="";attributes={};callStatus="";capabilityCode;checkDevicePermissions;checkMicrophonePermissions;checkCameraPermissions;initReady;constructor(t){this.api=t.api,this.attributes=t.attributes,this.callStatus=t.callStatus,this.capabilityCode=t.capabilityCode,this.checkDevicePermissions=t.checkDevicePermissions,this.checkMicrophonePermissions=t.checkMicrophonePermissions,this.checkCameraPermissions=t.checkCameraPermissions,this.initReady=t.initReady}}class ht{#t;constructor(){this.#t=[]}addAlertHandler(t){this.#t.push(t)}async check(t,e){for(let i=0;i<this.#t.length;i++)await this.#t[i].check(t,e)}checkSync(t,e){for(let i=0;i<this.#t.length;i++)this.#t[i].check(t,e)}}class ct{rule;notification;constructor(t,e){this.rule=t,this.notification=e}async check(t){}}class dt extends ct{constructor(t,e){super(t,e)}check(t){const e=this.rule.getParamsMatchedRule(t.api);if(e&&t.attributes){const i=W(t.attributes,e);if(void 0!==i)for(const e in i)Object.prototype.hasOwnProperty.call(i,e)&&this.notification.notify(t,S,i[e])}}}class ut extends ct{constructor(t,e){super(t,e)}check(t){if(t.callStatus){const e=this.rule.getStatusMatchedRule(t.api);e&&!e.includes(t.callStatus)&&this.notification.notify(t,R,`The current state is ${t.callStatus},cannot call this interface`)}}}class gt extends ct{constructor(t,e){super(t,e)}async check(t,e={}){if(t.capabilityCode)try{!1===(await e.tim.callExperimentalAPI("isCommercialAbilityEnabled",t.capabilityCode)).data.enabled&&this.notification.notify(t,v,_)}catch(e){2905===e.code?this.notification.notify(t,D,w):this.notification.notify(t,v,_)}}}class pt extends ct{constructor(t,e){super(t,e)}async check(t,e){t.checkDevicePermissions&&(wx.authorize({scope:"scope.record"}),wx.authorize({scope:"scope.camera"}),await wx.getSetting().then((e=>{e.authSetting["scope.camera"]&&e.authSetting["scope.record"]||this.notification.notify(t,y,L)}))),t.checkMicrophonePermissions&&(wx.authorize({scope:"scope.record"}),await wx.getSetting().then((e=>{e.authSetting["scope.record"]||this.notification.notify(t,y,L)}))),t.checkCameraPermissions&&(wx.authorize({scope:"scope.camera"}),await wx.getSetting().then((e=>{e.authSetting["scope.camera"]||this.notification.notify(t,y,L)})))}}class It extends ct{constructor(t,e){super(t,e)}check(t,e){void 0!==t.initReady&&this.rule.getInitReadyRule(t.api)!==t.initReady&&this.notification.notify(t,C,A)}}class Tt{alertRule;notification;alert;static instance;initializeBeans(){this.alertRule=new rt,this.notification=new ot,this.alert=new ht,this.alert.addAlertHandler(new dt(this.alertRule,this.notification)),this.alert.addAlertHandler(new It(this.alertRule,this.notification)),this.alert.addAlertHandler(new gt(this.alertRule,this.notification)),this.alert.addAlertHandler(new pt(this.alertRule,this.notification)),this.alert.addAlertHandler(new ut(this.alertRule,this.notification))}getAlert(){return this.alert}static getInstance(){return this.instance||(this.instance=new Tt,this.instance.initializeBeans()),this.instance}}const mt="TUICallEngine";class ft{static instance=null;static AUDIO_PLAYBACK_DEVICE=f;static MEDIA_TYPE=u;static EVENT=n;static STATUS=s;_initReady=!1;_timExternal=!1;constructor(s){this.data={config:{sdkAppID:s.sdkAppID,userID:"",userSig:"",type:1}},this.initData(),this.EVENT=n,this.MEDIA_TYPE=u,this.CALL_TYPE=u,this._emitter=new a,this.TRTC=new t(this,{TUIScene:"TUICalling"}),wx.TUIScene="TUICalling",s.tim?(this.tim=s.tim,this._timExternal=!0):wx.$TIM?(this.tim=wx.$TIM,this._timExternal=!0):this.tim=e.create({SDKAppID:s.sdkAppID}),wx.$TSignaling||(wx.$TSignaling=new i({SDKAppID:s.sdkAppID,tim:this.tim})),this.TSignalingClient=new b({TSignaling:wx.$TSignaling}),this.TRTCCallingDelegate=new O({emitter:this._emitter}),Y.info(`${mt} SDK Version:${M}, SDKAppID:${s.sdkAppID}`)}static createInstance(t){return Tt.getInstance().getAlert().checkSync(new lt({api:"createInstance",attributes:t})),ft.instance||(ft.instance=new ft(t)),ft.instance.initData(),ft.instance}static destroyInstance(){null!==ft.instance&&(Tt.getInstance().getAlert().checkSync(new lt({api:"destroyInstance",initReady:this.instance._initReady})),null!==ft.instance&&(ft.instance.destroyed(),ft.instance=null))}setLogLevel(t){"number"!=typeof t||isNaN(t)?Y.error(`${mt},setLogLevel parameter must be a number(0~4)`):(Y.setLevel(t),this.TSignalingClient.setLogLevel(t),this.TRTC.setLogLevel(t))}initData(){const t={callStatus:s.IDLE,soundMode:this.data.config.type===u.AUDIO?f.EAR:f.SPEAKER,active:!1,invitation:{inviteID:"",inviter:"",type:"",roomID:""},startTalkTime:0,localUser:null,remoteUsers:[],remoteID:"",timer:null,chatTimeNum:0,chatTime:"00:00:00",screen:"pusher",pusher:{},playerList:[],inviterInviteID:{},isInviter:!0,unHandledInviteeList:[],handledInviteeList:[],isGroupCall:!1,groupID:"",switchCallModeStatus:!0,enterRoomStatus:!1};this.data={...this.data,...t}}async handleNewInvitationReceived(t){const e=this.getCallStatus();Y.log(`${mt}.onNewInvitationReceived - params:`,t,`currentCallStatus:${e}`);const{data:{inviter:i,inviteeList:a,data:n,inviteID:r,groupID:o}}=t,l=JSON.parse(n);if(this.getUserID()===i)return;const h=!!o,c={inviter:i,inviteeList:a,inviteID:r,isGroupCall:h,inviteData:l,groupID:o,currentCallStatus:e};if("hangup"!==l?.data?.cmd)h||!this.judgeSwitchCallMode(l)?e===s.IDLE?this.handleInvitedSignal(c):this.TSignalingClient.reject({inviteID:r,type:n.call_type,lineBusy:"line_busy"}):e!==s.IDLE&&l.room_id===this.data.invitation.roomID&&(await this.handleSwitchCallModeTSignaling(r,l),this.addInviterInviteID(U,r));else{if(Y.log(`${mt}.hangup - params:`,i,l,this.getHandledInviteeList(),this.getUnHandledInviteeList(),e,h),e===s.IDLE)return;await this.handleHangupSignal(c)}}async handleHangupSignal(t){const{inviter:e,currentCallStatus:i,inviteData:a}=t,n=this.isGroupCall(),r=this.isInviter(),o={userID:e,roomID:this.getRoomID(),callMediaType:this.getCallType(),callRole:this.isInviter()?g:p,totalTime:a.call_end||0,callEnd:a.call_end||0,message:""},l=this.getHandledInviteeList();if(i===s.CALLING){if(-1===l.indexOf(e))return}else if(this.deleteHandledInviteeList([e]),-1===l.indexOf(e)&&l.length>1)return;if(n){this.deleteHandledInviteeList([e]),this.data.playerList=this.data.playerList.filter((t=>t.userID!==e));const t=this.getHandledInviteeList(),a=this.getUnHandledInviteeList();r?1===t.length&&0===a.length&&(this.TRTCCallingDelegate.onCallEnd(o),await this._resetTUICallEngine()):(i===s.CALLING&&(this.TRTCCallingDelegate.onUserLeave({userID:e,playerList:this.data.playerList}),0===t.length&&1===a.length&&(this.TRTCCallingDelegate.onCancel({inviteID:this.data.invitation.inviteID,invitee:this.getUserID(),userID:this.getUserID()}),await this._resetTUICallEngine())),i===s.CONNECTED&&1===t.length&&0===a.length&&(this.TRTCCallingDelegate.onCallEnd(o),await this._resetTUICallEngine()))}else this.TRTCCallingDelegate.onCallEnd(o),await this._resetTUICallEngine()}async handleSwitchCallModeTSignaling(t,e){const i={inviteID:t,type:e.call_type},a={cmd:"switchToVideo"};e.call_type===u.VIDEO&&(i.switch_to_audio_call="switch_to_audio_call",a.cmd="switchToAudio");const n=await this.TSignalingClient.accept(i,a);this.setSwitchCallModeStatus(!1),this.handleCallMode(e.call_type,n)}judgeSwitchCallMode(t){return t.switch_to_audio_call&&"switch_to_audio_call"===t.switch_to_audio_call||t.data&&"switchToAudio"===t.data.cmd||t.data&&"switchToVideo"===t.data.cmd}handleCallMode(t,e){Y.log(`${mt}.handleCallMode - type`,t);const i=t!==u.VIDEO;this.setPusherAttributesHandler({enableCamera:i}),i?(this.data.config.type=u.VIDEO,this.data.invitation.type=u.VIDEO):(this.data.config.type=u.AUDIO,this.data.invitation.type=u.AUDIO),this.TRTCCallingDelegate.onCallMode({type:this.data.config.type,message:e.data.message}),this.setSwitchCallModeStatus(!0)}handleInvitedSignal(t){const{inviter:e,inviteeList:i,isGroupCall:a,groupID:n,inviteID:r,inviteData:o}=t;this.data.remoteID=e,this.data.config.type=o.call_type,this.data.invitation.inviteID=r,this.data.invitation.inviter=e,this.data.invitation.type=o.call_type,this.data.invitation.roomID=o.room_id,this.data.isInviter=!1,this.data.isGroupCall=a,this.addHandledInviteeList([e]),this.addUnHandledInviteeList(i),a&&(this.data.groupID=n),this.changeCallStatus(s.CALLING),this.addInviterInviteID(N,r),Y.log(`${mt} NEW_INVITATION_RECEIVED invitation: `,this.data.callStatus,this.data.invitation);const l={sponsor:e,inviteeList:i,userIDList:i,isFromGroup:a,inviteID:r,groupID:n,inviteData:{version:o.version,callType:o.call_type,roomID:o.room_id}};this.setPusherAttributesHandler({enableCamera:this.data.config.type===u.VIDEO}),wx.createLivePusherContext().startPreview(),this.TRTCCallingDelegate.onInvited(l)}addInviterInviteID(t,e){0===(this.data.inviterInviteID[t]||[]).length?this.data.inviterInviteID[t]=[e]:this.data.inviterInviteID[t].push(e)}async handleInviteeAccepted(t){Y.log(`${mt} onInviteeAccepted - params:`,t);const e=JSON.parse(t.data.data),i=t.data.invitee,{inviteID:a}=t.data;if(-1===this.getInviterInviteID(N).indexOf(a))return;if(this.data.callStatus===s.IDLE)return;if(!this.isGroupCall()&&this.judgeSwitchCallMode(e)&&!this.data.switchCallModeStatus)return void this.handleCallMode(this.data.invitation.type);const n=this.getCallStatus();if(i===this.getUserID()&&n===s.CALLING)return this.TRTCCallingDelegate.onCancel({inviteID:this.data.invitation.inviteID,invitee:i,userID:i}),void await this._resetTUICallEngine();this.isInviter()&&this.data.callStatus===s.CALLING&&this.changeCallStatus(s.CONNECTED);const r=await this.getUserProfile(this.data.unHandledInviteeList.map((t=>({userID:t}))));this.TRTCCallingDelegate.onUserAccept({userID:i,userList:r}),this.addHandledInviteeList([i]),this.deleteUnHandledInviteeList([i])}async handleInviteeRejected(t){Y.log(`${mt} onInviteeRejected - params`,t);const{inviteID:e}=t.data;if(!this.isGroupCall()&&!this.getSwitchCallModeStatus()&&-1!==this.getInviterInviteID(U).indexOf(e))return Y.log(`${mt}.onInviteeRejected - Audio and video switching is not available`),void this.setSwitchCallModeStatus(!0);const i=t.data.invitee,{inviter:a}=t.data,n=this.getUserID(),r=this.getCallStatus(),o=JSON.parse(t.data.data);if(-1===this.getInviterInviteID(N).indexOf(e))return;if(r===s.IDLE)return;if(n===i&&r===s.CALLING)return this.TRTCCallingDelegate.onCancel({inviteID:this.data.invitation.inviteID,invitee:i,userID:i}),void await this._resetTUICallEngine();const l=this.isGroupCall(),h=this.isInviter();this.deleteUnHandledInviteeList([i]);const c=this.getUnHandledInviteeList(),d=this.getHandledInviteeList(),u={inviteID:this.data.invitation.inviteID,invitee:n,userID:n},g={inviteID:this.data.invitation.inviteID,invitee:i,userID:i},{invitation:I}=this.data;if(r!==s.CONNECTED||-1===d.indexOf(i))if("line_busy"===o.line_busy||""===o.line_busy||o?.data?.message&&"lineBusy"===o.data.message)a===I.inviter&&this.TRTCCallingDelegate.onLineBusy({inviteID:this.data.invitation.inviteID,invitee:i,userID:i}),l?0===c.length&&1===d.length&&(this.TRTCCallingDelegate.onCancel(u),await this._resetTUICallEngine()):(this.TRTCCallingDelegate.onCancel(u),await this._resetTUICallEngine());else if(l){if(this.TRTCCallingDelegate.onReject(g),r===s.CALLING&&(h?0===c.length&&1===d.length&&(this.TRTCCallingDelegate.onCancel(u),await this._resetTUICallEngine()):1===c.length&&0===d.length&&(this.TRTCCallingDelegate.onCancel(u),await this._resetTUICallEngine())),r===s.CONNECTED&&0===c.length&&1===d.length){const t={roomID:this.getRoomID(),callMediaType:this.getCallType(),callRole:p,totalTime:Math.round((Date.now()-this.data.startTalkTime)/1e3),callEnd:Math.round((Date.now()-this.data.startTalkTime)/1e3),message:"",userID:i};this.TRTCCallingDelegate.onCallEnd(t),await this._resetTUICallEngine()}}else this.TRTCCallingDelegate.onReject(g),this.TRTCCallingDelegate.onCancel(u),await this._resetTUICallEngine()}async handleInvitationCancelled(t){this.data.invitation.inviteID===t.data.inviteID&&this.data.callStatus!==s.IDLE&&(Y.log(mt,"onInvitationCancelled",`inviteID:${t.data.inviteID} inviter:${t.data.inviter} data:${t.data.data}`),this.TRTCCallingDelegate.onCancel({inviteID:this.data.invitation.inviteID,invitee:t.data.inviter,userID:t.data.inviter}),await this._resetTUICallEngine())}async handleInvitationTimeout(t){const{inviter:e,inviteeList:i=[],inviteID:a}=t.data,n=this.getUserID(),r=this.isGroupCall(),o=this.isInviter(),l=this.getCallStatus();if(this.deleteUnHandledInviteeList(i),Y.warn(`${mt}.onInvitationTimeout - params:`,t,`currentUserID:${n} isInviter:${o} isGroupCall:${r} currentCallStatus:${l} inviteID:${this?._newInvitationData?.inviteID}`),-1===this.getInviterInviteID(N).indexOf(a))return;if(l===s.IDLE)return;const h=this.getHandledInviteeList(),c=this.getUnHandledInviteeList(),d={groupID:this.data.groupID,inviteID:this.data.invitation.inviteID,sponsor:e,timeoutUserList:i,userIDList:i},u={inviteID:this.data.invitation.inviteID,invitee:n,userID:n};if(r){if(l===s.CALLING&&(o?(this.TRTCCallingDelegate.onNoResp(d),0===c.length&&(this.TRTCCallingDelegate.onCancel(u),await this._resetTUICallEngine())):-1===i.indexOf(n)?this.TRTCCallingDelegate.onNoResp(d):(this.TRTCCallingDelegate.onCancel(u),await this._resetTUICallEngine())),l===s.CONNECTED&&(this.TRTCCallingDelegate.onNoResp(d),1===h.length&&0===c.length)){const t={roomID:this.getRoomID(),callMediaType:this.getCallType(),callRole:this.isInviter()?g:p,totalTime:Math.round((Date.now()-this.data.startTalkTime)/1e3),callEnd:Math.round((Date.now()-this.data.startTalkTime)/1e3),message:"",userID:this.getUserID()};this.TRTCCallingDelegate.onCallEnd(t),await this._resetTUICallEngine()}}else o?(this.TRTCCallingDelegate.onNoResp(d),this.TRTCCallingDelegate.onCancel(u)):this.TRTCCallingDelegate.onCancel(u),await this._resetTUICallEngine()}handleSDKReady(){Y.log(mt,"TSignaling SDK ready"),this.TSignalingResolve(),this.TRTCCallingDelegate.onSdkReady({message:"SDK ready"});this.tim.getMyProfile().then((t=>{this.data.localUser=t.data})).catch((t=>{Y.warn("getMyProfile error:",t)}))}handleKickedOut(){this.hangup(),this.TRTCCallingDelegate.onKickedOut({message:"kicked out"})}_addTSignalingEvent(){wx.$TSignaling.on(i.EVENT.NEW_INVITATION_RECEIVED,this.handleNewInvitationReceived,this),wx.$TSignaling.on(i.EVENT.INVITEE_ACCEPTED,this.handleInviteeAccepted,this),wx.$TSignaling.on(i.EVENT.INVITEE_REJECTED,this.handleInviteeRejected,this),wx.$TSignaling.on(i.EVENT.INVITATION_CANCELLED,this.handleInvitationCancelled,this),wx.$TSignaling.on(i.EVENT.INVITATION_TIMEOUT,this.handleInvitationTimeout,this),wx.$TSignaling.on(i.EVENT.SDK_READY,this.handleSDKReady,this),wx.$TSignaling.on(i.EVENT.KICKED_OUT,this.handleKickedOut,this),i.EVENT.MESSAGE_SENT_BY_ME&&wx.$TSignaling.on(i.EVENT.MESSAGE_SENT_BY_ME,this.onMessageSentByMe,this)}_removeTSignalingEvent(){wx.$TSignaling.off(i.EVENT.NEW_INVITATION_RECEIVED,this.handleNewInvitationReceived),wx.$TSignaling.off(i.EVENT.INVITEE_ACCEPTED,this.handleInviteeAccepted),wx.$TSignaling.off(i.EVENT.INVITEE_REJECTED,this.handleInviteeRejected),wx.$TSignaling.off(i.EVENT.INVITATION_CANCELLED,this.handleInvitationCancelled),wx.$TSignaling.off(i.EVENT.INVITATION_TIMEOUT,this.handleInvitationTimeout),wx.$TSignaling.off(i.EVENT.SDK_READY,this.handleSDKReady),wx.$TSignaling.off(i.EVENT.KICKED_OUT,this.handleKickedOut),i.EVENT.MESSAGE_SENT_BY_ME&&wx.$TSignaling.off(i.EVENT.MESSAGE_SENT_BY_ME,this.onMessageSentByMe,this)}async onRemoteUserJoin(t){const{userID:e,userList:i,playerList:a}=t.data;Y.log(mt,"REMOTE_USER_JOIN",t,e),this.data.playerList=a.length>0?await this.getUserProfile(a):this.data.playerList,this.addHandledInviteeList([e]),this.deleteUnHandledInviteeList([e]),this.data.startTalkTime||(this.data.startTalkTime=Date.now()),this.TRTCCallingDelegate.onUserEnter({userID:t.data.userID,playerList:this.data.playerList}),Y.log(mt,"REMOTE_USER_JOIN","playerList:",this.data.playerList,"userList:",i)}async onRemoteUserLeave(t){const{userID:e}=t.data;if(Y.log(mt,"REMOTE_USER_LEAVE",t,t.data.userID),!this.data.isGroupCall){await this.TSignalingClient.hangup({userIDList:[e],callType:this.data.config.type,callEnd:Math.round((Date.now()-this.data.startTalkTime)/1e3)}),this.TRTCCallingDelegate.onUserLeave({userID:e,playerList:this.data.playerList});const t=this.isInviter()?g:p,i={roomID:this.getRoomID(),callMediaType:this.getCallType(),callRole:t,totalTime:Math.round((Date.now()-this.data.startTalkTime)/1e3),callEnd:Math.round((Date.now()-this.data.startTalkTime)/1e3),message:"",userID:this.getUserID()};return this.TRTCCallingDelegate.onCallEnd(i),Y.log(mt,"REMOTE_USER_LEAVE","playerList:"),void await this._resetTUICallEngine()}this.deleteHandledInviteeList([e]),this.data.playerList=this.data.playerList.filter((t=>t.userID!==e)),this.TRTCCallingDelegate.onUserLeave({userID:e,playerList:this.data.playerList})}onLocalNetStateUpdate(t){const{netStatus:e}=t.data.pusher;Y.log(mt,"onLocalNetStateUpdate",e),this.data.pusher=t.data.pusher,this.TRTCCallingDelegate.onUserUpdate({pusher:this.data.pusher,playerList:this.data.playerList})}async onRemoteNetStateUpdate(t){const{playerList:e}=t.data;Y.log(mt,"onRemoteNetStateUpdate",e),this.data.playerList=this._updateUserProfile(this.data.playerList,e),this.TRTCCallingDelegate.onUserUpdate({pusher:this.data.pusher,playerList:this.data.playerList})}onError(t){Y.log(mt,"onError",t)}onRemoteVideoAdd(t){Y.log("* room REMOTE_VIDEO_ADD",t);const{player:e}=t.data;this.setPlayerAttributesHandler(e,{muteVideo:!1})}onRemoteVideoRemove(t){Y.log("* room REMOTE_VIDEO_REMOVE",t);const{player:e}=t.data;this.setPlayerAttributesHandler(e,{muteVideo:!0})}async onRemoteAudioAdd(t){Y.log("* room REMOTE_AUDIO_ADD",t);const e=await this.getUserProfile([t.data.player]);this.setPlayerAttributesHandler(e[0],{muteAudio:!1})}onRemoteAudioRemove(t){Y.log("* room REMOTE_AUDIO_REMOVE",t);const{player:e}=t.data;this.setPlayerAttributesHandler(e,{muteAudio:!0})}async onRemoteAudioVolumeUpdate(t){Y.log("* room REMOTE_AUDIO_VOLUME_UPDATE",t);const{playerList:e}=t.data;this.data.playerList=this._updateUserProfile(this.data.playerList,e),this.TRTCCallingDelegate.onUserUpdate({pusher:this.data.pusher,playerList:this.data.playerList})}onLocalAudioVolumeUpdate(t){const{pusher:e}=t.data;this.data.pusher=e,this.TRTCCallingDelegate.onUserUpdate({pusher:this.data.pusher,playerList:this.data.playerList})}onMessageSentByMe(t){this.TRTCCallingDelegate.onMessageSentByMe(t)}_addTRTCEvent(){this.TRTC.on(this.TRTC.EVENT.REMOTE_USER_JOIN,this.onRemoteUserJoin,this),this.TRTC.on(this.TRTC.EVENT.REMOTE_USER_LEAVE,this.onRemoteUserLeave,this),this.TRTC.on(this.TRTC.EVENT.LOCAL_NET_STATE_UPDATE,this.onLocalNetStateUpdate,this),this.TRTC.on(this.TRTC.EVENT.REMOTE_NET_STATE_UPDATE,this.onRemoteNetStateUpdate,this),this.TRTC.on(this.TRTC.EVENT.ERROR,this.onError,this),this.TRTC.on(this.TRTC.EVENT.REMOTE_VIDEO_ADD,this.onRemoteVideoAdd,this),this.TRTC.on(this.TRTC.EVENT.REMOTE_VIDEO_REMOVE,this.onRemoteVideoRemove,this),this.TRTC.on(this.TRTC.EVENT.REMOTE_AUDIO_ADD,this.onRemoteAudioAdd,this),this.TRTC.on(this.TRTC.EVENT.REMOTE_AUDIO_REMOVE,this.onRemoteAudioRemove,this),this.TRTC.on(this.TRTC.EVENT.REMOTE_AUDIO_VOLUME_UPDATE,this.onRemoteAudioVolumeUpdate,this),this.TRTC.on(this.TRTC.EVENT.LOCAL_AUDIO_VOLUME_UPDATE,this.onLocalAudioVolumeUpdate,this)}_removeTRTCEvent(){this.TRTC.off(this.TRTC.EVENT.REMOTE_USER_JOIN,this.onRemoteUserJoin),this.TRTC.off(this.TRTC.EVENT.REMOTE_USER_LEAVE,this.onRemoteUserLeave),this.TRTC.off(this.TRTC.EVENT.LOCAL_NET_STATE_UPDATE,this.onLocalNetStateUpdate),this.TRTC.off(this.TRTC.EVENT.REMOTE_NET_STATE_UPDATE,this.onRemoteNetStateUpdate),this.TRTC.off(this.TRTC.EVENT.ERROR,this.onError),this.TRTC.off(this.TRTC.EVENT.REMOTE_VIDEO_ADD,this.onRemoteVideoAdd),this.TRTC.off(this.TRTC.EVENT.REMOTE_VIDEO_REMOVE,this.onRemoteVideoRemove),this.TRTC.off(this.TRTC.EVENT.REMOTE_AUDIO_ADD,this.onRemoteAudioAdd),this.TRTC.off(this.TRTC.EVENT.REMOTE_AUDIO_REMOVE,this.onRemoteAudioRemove),this.TRTC.off(this.TRTC.EVENT.REMOTE_AUDIO_VOLUME_UPDATE,this.onRemoteAudioVolumeUpdate),this.TRTC.off(this.TRTC.EVENT.LOCAL_AUDIO_VOLUME_UPDATE,this.onLocalAudioVolumeUpdate)}initTRTC(){const t=this.TRTC.createPusher({beautyLevel:5});this.data.pusher=t.pusherAttributes}enterRoom(t){this._addTRTCEvent();const{roomID:e}=t,i=Object.assign(this.data.config,{roomID:e,enableMic:!0,autopush:!0,enableAgc:!0,enableAns:!0,enableCamera:t.callType===u.VIDEO});this.data.enterRoomStatus=!0,this.data.pusher=this.TRTC.enterRoom(i),wx.createLivePusherContext().startPreview(),this.TRTC.getPusherInstance().start()}async exitRoom(){await this.TRTC.getPusherInstance().stop();const t=await this.TRTC.exitRoom();this.data.pusher=t.pusher,this.data.playerList=t.playerList,this.data.unHandledInviteeList=[],this.data.enterRoomStatus=!1,this.initTRTC(),this._removeTRTCEvent()}setPusherAttributesHandler(t){this.data.pusher=this.TRTC.setPusherAttributes(t),this.TRTCCallingDelegate.onUserUpdate({pusher:this.data.pusher,playerList:this.data.playerList})}async setPlayerAttributesHandler(t,e){const i=this.TRTC.setPlayerAttributes(t.streamID,e);Y.warn("setPlayerAttributesHandler",i),this.data.playerList=i.length>0?this._updateUserProfile(this.data.playerList,i):this.data.playerList,this.TRTCCallingDelegate.onUserUpdate({pusher:this.data.pusher,playerList:this.data.playerList})}_mutePlayerAudio(t){const e=t.currentTarget.dataset.value;e.hasAudio&&e.muteAudio?this.setPlayerAttributesHandler(e,{muteAudio:!1}):!e.hasAudio||e.muteAudio||this.setPlayerAttributesHandler(e,{muteAudio:!0})}_mutePlayerVideo(t){const e=t.currentTarget.dataset.value;e.hasVideo&&e.muteVideo?this.setPlayerAttributesHandler(e,{muteVideo:!1}):!e.hasVideo||e.muteVideo||this.setPlayerAttributesHandler(e,{muteVideo:!0})}_pusherAudioHandler(){this.data.pusher.enableMic?(this.setPusherAttributesHandler({enableMic:!1}),this.TRTC.getPusherInstance().setMICVolume({volume:0})):(this.setPusherAttributesHandler({enableMic:!0}),this.TRTC.getPusherInstance().setMICVolume({volume:100}))}_pusherVideoHandler(){this.data.pusher.enableCamera?this.setPusherAttributesHandler({enableCamera:!1}):this.setPusherAttributesHandler({enableCamera:!0})}async init(t){return Tt.getInstance().getAlert().checkSync(new lt({api:"init",attributes:t})),await this.login(t)}async login(t){return this.data.config.userID=t.userID,this.data.config.userSig=t.userSig,new Promise(((e,i)=>{wx.$TSignaling.login({userID:t.userID,userSig:t.userSig}).then((t=>(Y.log(mt,"login","IM login success",t),this._initReady=!0,this._resetTUICallEngine(),this._addTSignalingEvent(),this.initTRTC(),this.TSignalingResolve=e,null)))}))}async logout(){this.data.callStatus!==s.CALLING&&this.data.callStatus!==s.CONNECTED||(this.isInviter()?await this.hangup():await this.reject()),await this._resetTUICallEngine(),wx.$TSignaling.logout({userID:this.data.config.userID,userSig:this.data.config.userSig}).then((t=>(Y.log(mt,"logout","IM logout success"),this._removeTSignalingEvent(),this._removeTRTCEvent(),t))).catch((t=>{throw Y.error(mt,"logout","IM logout failure"),new Error(t)}))}on(t,e,i){Tt.getInstance().getAlert().checkSync(new lt({api:"on",attributes:{eventCode:t,handler:e,context:i},initReady:this._initReady})),this._emitter.on(t,e,i)}off(t,e){Tt.getInstance().getAlert().checkSync(new lt({api:"off",attributes:{eventCode:t,handler:e}})),this._emitter.off(t,e)}async call(t){const{userID:e,type:i}=t;if(await Tt.getInstance().getAlert().check(new lt({api:"call",attributes:t,initReady:this._initReady,callStatus:this.data.callStatus,capabilityCode:E.BASE,checkMicrophonePermissions:!0,checkCameraPermissions:i===u.VIDEO}),{tim:this.tim}),this.getUserID()===e)throw Y.log(`${mt}.call failed. Called id can't be yourself. userId:${this.getUserID()}, inviteeId:${e}`),new Error(`${mt}.call failed. Called id can't be yourself. userId:${this.getUserID()}, inviteeId:${e}`);try{const a=Math.floor(2147483646*Math.random()+1);this.enterRoom({roomID:a,callType:i});const n=await this.TSignalingClient.invite({roomID:a,...t});return Y.log(`${mt} call(userID: ${e}, type: ${i}) success, ${n}`),this.data.config.type=i,this.data.invitation.inviteID=n.inviteID,this.data.invitation.inviter=this.data.config.userID,this.data.invitation.type=i,this.data.invitation.roomID=a,this.data.isInviter=!0,this.data.remoteID=e,this.changeCallStatus(s.CALLING),this.addInviterInviteID(N,n.inviteID),this.addUnHandledInviteeList([e]),this.addHandledInviteeList([this.getUserID()]),{data:n.data,pusher:this.data.pusher}}catch(t){Y.log(`${mt} call(userID:${e},type:${i}) failed', error: ${t}`)}}async groupCall(t){const{type:e}=t;await Tt.getInstance().getAlert().check(new lt({api:"groupCall",attributes:t,initReady:this._initReady,callStatus:this.data.callStatus,capabilityCode:E.PRO,checkMicrophonePermissions:!0,checkCameraPermissions:e===u.VIDEO}),{tim:this.tim});const i=this.data.roomID||Math.floor(2147483646*Math.random()+1);this.enterRoom({roomID:i,callType:e});try{const a=await this.TSignalingClient.inviteGroup({roomID:i,...t});return this.data.config.type=t.type,this.data.invitation.inviteID=a.inviteID,this.data.invitation.inviter=this.data.config.userID,this.data.invitation.type=e,this.data.invitation.roomID=i,this.data.isInviter=!0,this.data.isGroupCall=!0,this.data.groupID=t.groupID,this.addUnHandledInviteeList(t.userIDList),this.addHandledInviteeList([this.getUserID()]),this.changeCallStatus(s.CALLING),this.addInviterInviteID(N,a.inviteID),Y.log(mt,"inviteInGroup OK",a),{data:a.data,pusher:this.data.pusher}}catch(t){Y.log(mt,"inviteInGroup failed",t)}}async accept(){return await Tt.getInstance().getAlert().check(new lt({api:"accept",initReady:this._initReady,callStatus:this.data.callStatus,checkDevicePermissions:!0})),new Promise(((t,e)=>{Y.log(mt,"accept() inviteID: ",this.data.invitation.inviteID),this.data.callStatus===s.CALLING&&(this.data.config.type!==u.VIDEO||this.data.isGroupCall?this.handleAccept(t,e):wx.createLivePusherContext().stopPreview({success:()=>{const i=setTimeout((async()=>{clearTimeout(i),this.handleAccept(t,e)}),0)}}))}))}async handleAccept(t,e){this.enterRoom({roomID:this.data.invitation.roomID,callType:this.data.config.type}),this.changeCallStatus(s.CONNECTED);const i=await this.TSignalingClient.accept({inviteID:this.data.invitation.inviteID,type:this.data.config.type});0===i.code?(Y.log(mt,"accept OK"),this.addHandledInviteeList([this.getUserID()]),this.deleteUnHandledInviteeList([this.getUserID()]),t({message:i.data.message,pusher:this.data.pusher})):(Y.error(mt,"accept failed",i),e(i))}async reject(){if(await Tt.getInstance().getAlert().checkSync(new lt({api:"reject",callStatus:this.data.callStatus,initReady:this._initReady})),this.data.invitation.inviteID){const t=await this.TSignalingClient.reject({inviteID:this.data.invitation.inviteID,type:this.data.config.type});return Y.log(mt,"reject OK",t),this.TRTCCallingDelegate.onCancel({inviteID:this.data.invitation.inviteID,invitee:this.getUserID(),userID:this.getUserID()}),await this._resetTUICallEngine(),t}return Y.warn(`${mt} 未收到邀请，无法拒绝`),"未收到邀请，无法拒绝"}async hangup(){if(await Tt.getInstance().getAlert().checkSync(new lt({api:"hangup",callStatus:this.data.callStatus,initReady:this._initReady})),this.isInviter()&&this.data.callStatus===s.CALLING)return await this.TSignalingClient.cancel({inviteID:this.data.invitation.inviteID,callType:this.data.invitation.type}),this.TRTCCallingDelegate.onCancel({inviteID:this.data.invitation.inviteID,invitee:this.getUserID(),userID:this.getUserID()}),void await this._resetTUICallEngine();this.data.isGroupCall&&await this.TSignalingClient.hangup({isGroupCall:this.isGroupCall(),userIDList:[...this.data.handledInviteeList.filter((t=>t!==this.getUserID())),...this.data.unHandledInviteeList],groupID:this.data.groupID,callType:this.data.config.type,callEnd:Math.round((Date.now()-this.data.startTalkTime)/1e3)});const t={roomID:this.getRoomID(),callMediaType:this.getCallType(),callRole:this.isInviter()?g:p,totalTime:Math.round((Date.now()-this.data.startTalkTime)/1e3),callEnd:Math.round((Date.now()-this.data.startTalkTime)/1e3),message:"",userID:this.getUserID()};this.TRTCCallingDelegate.onCallEnd(t),await this._resetTUICallEngine()}getRoomID(){return this.data.invitation.roomID}getCallType(){return this.data.invitation.type}isGroupCall(){return this.data.isGroupCall}getUserID(){return this.data.config.userID}isInviter(){return this.data.isInviter}getHandledInviteeList(){return this.data.handledInviteeList||[]}getUnHandledInviteeList(){return this.data.unHandledInviteeList||[]}getCallStatus(){return this.data.callStatus}getInviterInviteID(t){return this.data.inviterInviteID[t]||[]}addUnHandledInviteeList(t){0!==t.length&&(t.forEach((t=>{-1===this.data.unHandledInviteeList.indexOf(t)&&this.data.unHandledInviteeList.push(t)})),this.data.unHandledInviteeList=Array.from(new Set(this.data.unHandledInviteeList)))}deleteUnHandledInviteeList(t){0!==t.length&&t.forEach((t=>{this.data.unHandledInviteeList=this.data.unHandledInviteeList.filter((e=>e!==t))}))}addHandledInviteeList(t){0!==t.length&&(t.forEach((t=>{-1===this.data.handledInviteeList.indexOf(t)&&this.data.handledInviteeList.push(t)})),this.data.handledInviteeList=Array.from(new Set(this.data.handledInviteeList)))}deleteHandledInviteeList(t){0!==t.length&&t.forEach((t=>{this.data.handledInviteeList=this.data.handledInviteeList.filter((e=>e!==t))}))}getSwitchCallModeStatus(){return this.data.switchCallModeStatus}changeCallStatus(t){switch(Y.log("进入callStatus",t),this.data.callStatus=t,t){case s.CONNECTED:if(this.data.timer)return;this.data.timer=setInterval((()=>{this.data.chatTime=function(t){const e=t;let i,a,n,s;return e>=3600?(a=parseInt(e/3600)<10?`0${parseInt(e/3600)}`:parseInt(e/3600),n=parseInt(e%60/60)<10?`0${parseInt(e%60/60)}`:parseInt(e%60/60),s=e%3600<10?"0"+e%3600:e%3600,s>60&&(n=parseInt(s/60)<10?`0${parseInt(s/60)}`:parseInt(s/60),s=s%60<10?"0"+s%60:s%60),i=`${a}:${n}:${s}`):e>=60&&e<3600?(n=parseInt(e/60)<10?`0${parseInt(e/60)}`:parseInt(e/60),s=e%60<10?"0"+e%60:e%60,i=`00:${n}:${s}`):e<60&&(s=e<10?`0${e}`:e,i=`00:00:${s}`),i}(this.data.chatTimeNum),this.data.chatTimeNum+=1,this.data.pusher.chatTime=this.data.chatTime,this.data.pusher.chatTimeNum=this.data.chatTimeNum,this.TRTCCallingDelegate.onUserUpdate({pusher:this.data.pusher,playerList:this.data.playerList})}),1e3);break;case s.IDLE:clearInterval(this.data.timer),this.data.timer=null,this.data.chatTime="00:00:00",this.data.chatTimeNum=0}}async _resetTUICallEngine(t,e){Y.log(mt," _resetTUICallEngine()",this.data.enterRoomStatus),this.data.enterRoomStatus&&await this.exitRoom(t,e),this.changeCallStatus(s.IDLE),this.data.config.type=u.AUDIO,this.initData()}async startRemoteView(t){this.data.playerList.forEach((e=>{if(e.userID===t)return e.muteVideo=!1,void Y.log(`${mt}, startRemoteView(${t})`)}))}stopRemoteView(t){this.data.playerList.forEach((e=>{if(e.userID===t)return e.muteVideo=!0,void Y.log(`${mt}, stopRemoteView(${t})`)}))}openCamera(){Tt.getInstance().getAlert().checkSync(new lt({api:"openCamera",callStatus:this.data.callStatus,initReady:this._initReady})),this.data.pusher.enableCamera||this._pusherVideoHandler(),Y.log(`${mt}, openCamera() pusher: ${this.data.pusher}`)}closeCamera(){Tt.getInstance().getAlert().checkSync(new lt({api:"closeCamera",callStatus:this.data.callStatus,initReady:this._initReady})),this.data.pusher.enableCamera&&this._pusherVideoHandler(),Y.log(`${mt}, closeCamera() pusher: ${this.data.pusher}`)}switchCamera(){if(Tt.getInstance().getAlert().checkSync(new lt({api:"switchCamera",callStatus:this.data.callStatus,initReady:this._initReady})),this.data.callStatus!==s.CONNECTED){const t="front"===this.data.pusher.frontCamera?"back":"front";this.setPusherAttributesHandler({frontCamera:t}),wx.createLivePusherContext().switchCamera()}else this.TRTC.getPusherInstance().switchCamera();Y.log(`${mt}, switchCamera(), frontCamera${this.data.pusher.frontCamera}`)}openMicrophone(){Tt.getInstance().getAlert().checkSync(new lt({api:"openMicrophone",callStatus:this.data.callStatus,initReady:this._initReady})),this.data.pusher.enableMic||this._pusherAudioHandler(),Y.log(`${mt}, openMicrophone() pusher: ${this.data.pusher}`)}closeMicrophone(){Tt.getInstance().getAlert().checkSync(new lt({api:"closeMicrophone",callStatus:this.data.callStatus,initReady:this._initReady})),this.data.pusher.enableMic&&this._pusherAudioHandler(),Y.log(`${mt}, closeMicrophone() pusher: ${this.data.pusher}`)}selectAudioPlaybackDevice(t){Tt.getInstance().getAlert().checkSync(new lt({api:"selectAudioPlaybackDevice",callStatus:this.data.callStatus,attributes:{type:t},initReady:this._initReady}));let e=!1;switch(t){case"speaker":e=!0;break;case"ear":e=!1}return this.setHandsFree(e)}setHandsFree(t){return this.data.soundMode=t?"speaker":"ear",Y.log(`${mt}, setHandsFree() result: ${this.data.soundMode}`),this.data.soundMode}async switchAudioCall(){if(this.isGroupCall())Y.warn(`${mt}.switchToAudioCall is not applicable to groupCall.`);else if(this.data.invitation.type!==u.AUDIO){if(this.data.switchCallModeStatus)return this.setSwitchCallModeStatus(!1),this.setPusherAttributesHandler({enableCamera:!1}),this.TSignalingClient.switchCallMode({userID:this.data.unHandledInviteeList[0]||this.data.playerList[0].userID,callType:this.data.invitation.type,roomID:this.data.invitation.roomID,mode:T});Y.warn(`${mt} audio and video call switching.`)}else Y.warn(`${mt} Now the call mode is audio call.`)}async switchCallMediaType(t){if(await Tt.getInstance().getAlert().check(new lt({api:"switchCallMediaType",attributes:{type:t},callStatus:this.data.callStatus,initReady:this._initReady})),this.isGroupCall())Y.warn(`${mt}.switchToAudioCall is not applicable to groupCall.`);else if(this.data.switchCallModeStatus)switch(t){case u.AUDIO:return this.setSwitchCallModeStatus(!1),this.setPusherAttributesHandler({enableCamera:!1}),this.TSignalingClient.switchCallMode({userID:this.data.unHandledInviteeList[0]||this.data.playerList[0].userID,callType:this.data.invitation.type,roomID:this.data.invitation.roomID,mode:T});case u.VIDEO:throw Y.warn(`${mt} Audio switching Video is not supported yet`),new Error(`${mt} Audio switching Video is not supported yet`)}else Y.warn(`${mt} audio and video call switching.`)}setSwitchCallModeStatus(t){this.data.switchCallModeStatus=t}async switchVideoCall(){if(this.isGroupCall())Y.warn(`${mt}.switchToVideoCall is not applicable to groupCall.`);else if(this.data.invitation.type!==u.VIDEO){if(this.data.switchCallModeStatus)return this.setSwitchCallModeStatus(!1),this.setPusherAttributesHandler({enableCamera:!0}),this.TSignalingClient.switchCallMode({userID:this.data.playerList[0].userID,callType:this.data.invitation.type,roomID:this.data.invitation.roomID,mode:m});Y.warn(`${mt} audio and video call switching.`)}else Y.warn(`${mt} Now the call mode is video call.`)}setSoundMode(t){let e=!1,i=!1;switch(t||(i=!0,t=this.data.soundMode),t){case"speaker":e=!0;break;case"ear":e=!1}return this.setHandsFree(i?!e:e)}_hangUp(){this.hangup()}_pusherStateChangeHandler(t){this.TRTC.pusherEventHandler(t)}_playerStateChange(t){this._emitter.emit(n.REMOTE_STATE_UPDATE,t)}_playerAudioVolumeNotify(t){this.data.playerList.length>0&&this.TRTC.playerAudioVolumeNotify(t)}_pusherAudioVolumeNotify(t){this.TRTC.pusherAudioVolumeNotify(t)}_updateUserProfile(t,e){if(0===e.length||0===t.length)return e;return e.map((e=>{const i=e,a=t.filter((t=>t.userID===e.userID));return i.avatar=a[0]&&a[0].avatar?a[0].avatar:"",i.nick=a[0]&&a[0].nick?a[0].nick:"",i}))}_getUserProfile(t){this.tim.getUserProfile({userIDList:t}).then((t=>{Y.log("getUserProfile success",t),Y.log(t.data),this.data.remoteUsers=t.data})).catch((t=>{Y.warn("getUserProfile error:",t)}))}async getUserProfile(t,e="array"){if(0===t.length)return[];const i=t.map((t=>t.userID)),a=await this.tim.getUserProfile({userIDList:i});let n=null;switch(e){case"array":n=t.map(((t,e)=>(t.avatar=a.data[e].avatar,t.nick=a.data[e].nick,t)));break;case"map":n={},t.forEach(((t,e)=>{t.avatar=a.data[e].avatar,t.nick=a.data[e].nick,n[t.userID]=t}))}return n}setSelfInfo(t,e){return Tt.getInstance().getAlert().checkSync(new lt({api:"setSelfInfo",attributes:{nickName:t,avatar:e},initReady:this._initReady})),this.tim.updateMyProfile({nick:t,avatar:e})}_pusherNetStatus(t){this.TRTC.pusherNetStatusHandler(t)}_playNetStatus(t){this.TRTC.playerNetStatus(t)}_toggleViewSize(t){const{screen:e}=t.currentTarget.dataset;return Y.log("get screen",e,t),1===this.data.playerList.length&&e!==this.data.screen&&this.data.invitation.type===u.VIDEO&&(this.data.screen=e),this.data.screen}getTim(){return this.tim}async destroyed(){this.data.callStatus!==s.CALLING&&this.data.callStatus!==s.CONNECTED||(this.isInviter()?this.hangup():this.reject()),this._resetTUICallEngine(),this._removeTSignalingEvent(),this._removeTRTCEvent(),this._timExternal||await this.logout(),this._initReady=!1}async checkDevicePermissions(){return new Promise((async(t,e)=>{wx.getSetting&&wx.getSetting()||t(),wx.authorize({scope:"scope.record"}),wx.authorize({scope:"scope.camera"}),wx.getSetting().then((i=>{Y.log("getSetting",i),i.authSetting["scope.camera"]&&i.authSetting["scope.record"]?t():e("camera or record not authorized")}))}))}async callExperimentalAPI(t){return new Promise((async(e,i)=>{try{const a=await this.tim.callExperimentalAPI("isCommercialAbilityEnabled",t);a.data.enabled?e(a):i("The package you purchased does not support this ability")}catch(t){2905===t.code?i("tim version is too old, Please upgrade version >= 2.20.1"):i("The package you purchased does not support this ability")}}))}}export{f as AUDIO_PLAYBACK_DEVICE,n as EVENT,u as MEDIA_TYPE,s as STATUS,ft as TUICallEngine,ft as default};
