/* 全屏设置 */
.TUICalling-connected-layout {
  width: 100%;
  height: 100%;
}

.TUICalling-connected-video {
  width: 100%;
  height: 180%;
}

/* 本地音频 */
.stream-box {
  float: left;
  width: 187px;
  height: 187px;
  position: absolute;
  top: 13vh;
}

/* 远端音频列表 */
.swiper {
  margin-top: 107px;
  min-height: 374px;
}

.invite-calling-list {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  justify-content: flex-start
}

.invite-calling-item {
  flex: 0.5;
  /*设置最小宽度，才会让元素排不下，导致换行排列*/
  min-width: 50%;
  height: 187px;
  position: relative;
}

.invite-calling-item image {
  width: 100%;
  height: 100%;
}

/* 本地视频 */
.play-item {
  width: 100%;
  height: 187px;
  position: relative;
}

.pusher-ownvideo {
  width: 100%;
  height: 100%;
}


/* 远端视频列表 */
.swiper-list {
  min-height: 189px;
}

.player-list {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  justify-content: center
}

.player-item {
  flex: 0.5;
  /*设置最小宽度，才会让元素排不下，导致换行排列*/
  min-width: 50%;
  min-height: 187px;
}

/* 音量图标 */
.player-control {
  background-color: rgba(0, 0, 0, .4);
  border-radius: 0 6px 6px 0;
  color: #fff;
  z-index: 999;
  position: absolute;
  bottom: 0px;
  left: 0px;
  display: flex;
  align-items: center;
  height: 32px;
  max-width: 50%;
  z-index: 99;
}

.player-control image {
  width: 32px;
  height: 32px;
}

.player-control .name {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-left: 10px;
  margin-right: 10px;
  flex: 1;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.control-buttons {
  display: flex;
}

.icon-button {
  margin-right: 10px;
}

/* 菜单 */
.handle-btns {
  position: absolute;
  bottom: 44px;
  width: 100vw;
  z-index: 3;
  display: flex;
  flex-direction: column;
}

.handle-btns .btn-list {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
}

.button-container {
  display: flex;
  flex-direction: column;
  text-align: center;
}

.btn-normal {
  width: 8vh;
  height: 8vh;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  /* background: white; */
  justify-content: center;
  align-items: center;
  border-radius: 50%;
}

.btn-image {
  width: 100%;
  height: 100%;
  background: none;
}

.btn-hangup {
  width: 8vh;
  height: 8vh;
  /*background: #f75c45;*/
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
}

.btn-hangup>.btn-image {
  width: 100%;
  height: 100%;
  background: none;
}

.TRTCCalling-call-audio {
  width: 100%;
  height: 100%;
}

.btn-footer {
  position: relative;
}

.btn-footer .multi-camera {
  width: 32px;
  height: 32px;
}

.btn-footer .camera {
  width: 64px;
  height: 64px;
  position: fixed;
  left: 16px;
  top: 107px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(255, 255, 255, 0.7);
}

.btn-footer .camera .camera-image {
  width: 32px;
  height: 32px;
}

.audio {
  padding-top: 15vh;
  background: #ffffff;
}

.pusher-audio {
  width: 0;
  height: 0;
}

.player-audio {
  width: 0;
  height: 0;
}

.other-view {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 18px;
  letter-spacing: 0;
  font-weight: 400;
  padding: 16px;
}

.white {
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  padding: 5px;
}

.black {
  color: #000000;
  padding: 5px;
}

.TRTCCalling-call-audio-box {
  margin-top: 100px;
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  justify-content: center
}

/*
.mutil-img {
    justify-content: flex-start !important;
} */

/* .TRTCCalling-call-audio-img {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.TRTCCalling-call-audio-img > image {
    width: 25vw;
    height: 25vw;
    margin: 0 4vw;
    border-radius: 4vw;
    position: relative;
}

.TRTCCalling-call-audio-img text {
    font-size: 20px;
    color: #333333;
    letter-spacing: 0;
    font-weight: 500;
}  */

.TRTCCalling-calling-list {
  flex: 0.5;
  /*设置最小宽度，才会让元素排不下，导致换行排列*/
  min-width: 50%;
  min-height: 187px;
  position: relative;
}

.TRTCCalling-calling-list image {
  width: 100%;
  height: 100%;
}

.TRTCCalling-calling-item-id {
  position: absolute;
  left: 2%;
  bottom: 2%;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.btn-list-item {
  flex: 1;
  display: flex;
  justify-content: center;
  padding: 16px 0;
}

.btn-image-small {
  transform: scale(.7);
}

.avatar {
  background: #dddddd;
}

.btn-container {
  display: flex;
  align-items: center;
  position: relative;
}

.invite-calling-header-left {
  position: absolute;
  right: -88px;
}

.invite-calling-header-left image {
  width: 32px;
  height: 32px;
}

.call-switch .call-operate {
  width: 4vh;
  height: 3vh;
}

.call-operate image {
  width: 100%;
  height: 100%;
  background: none;
}

.call-switch text {
  padding: 0;
  font-size: 14px;
}

.btn-operate-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.btn-operate-item text {
  padding: 8px 0;
  font-size: 18px;
  color: #666666;
  letter-spacing: 0;
  font-weight: 400;
  font-size: 14px;
}

.invite-calling-item-message {
  position: absolute;
  top: 0;
  left: 0;
  float: left;
  background: rgba(0, 0, 0, 0.60);
  width: 100%;
  height: 100%;
  z-index: 2;
}

.invite-calling-item-loadimg {
  position: absolute;
  left: calc(50% - 20px);
  top: calc(50% - 20px);
  width: 40px;
  height: 40px;
  -webkit-transform: rotate(360deg);
  animation: rotation 2s linear infinite;
  -moz-animation: rotation 2s linear infinite;
  -webkit-animation: rotation 2s linear infinite;
  -o-animation: rotation 2s linear infinite;
}


@-webkit-keyframes rotation {
  from {
    -webkit-transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(360deg);
  }
}

.invite-calling-item-loadimg image {
  width: 100%;
  height: 100%;
}

.invite-calling-item-id {
  position: absolute;
  left: 2%;
  bottom: 2%;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}