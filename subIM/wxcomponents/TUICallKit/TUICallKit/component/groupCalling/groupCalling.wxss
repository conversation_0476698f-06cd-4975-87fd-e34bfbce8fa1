.footer {
	position: absolute;
	bottom: 5vh;
	width: 100%;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}

.button-container {
	display: flex;
	flex-direction: column;
	text-align: center;
}

.btn-operate {
	display: flex;
	justify-content: space-between;
	/* flex-direction: column;
    text-align: center; */

}

.btn-operate-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-bottom: 20px;
}

.btn-operate-item text {
	font-size: 14px;
	color: #f0e9e9;
	padding: 5px;
	letter-spacing: 0;
	font-weight: 400;
}

.call-switch text {
	padding: 5px;
	color: #f0e9e9;
	font-size: 14px;
}

.call-operate {
	width: 8vh;
	height: 8vh;
	border-radius: 8vh;
	margin: 0 15vw;
	box-sizing: border-box;
	display: flex;
	justify-content: center;
	align-items: center;
}

.call-switch .call-operate {
	width: 4vh;
	height: 3vh;
}

.call-operate image {
	width: 100%;
	height: 100%;
	background: none;
}

.tips {
	font-size: 20px;
	color: #FFFFFF;
	letter-spacing: 0;
	margin: 0 auto;
	/* text-shadow: 0 1px 2px rgba(0,0,0,0.40); */
	font-weight: 600;
	max-width: 150px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.tips-subtitle {
	font-family: PingFangSC-Regular;
	font-size: 14px;
	color: #FFFFFF;
	letter-spacing: 0;
	text-align: right;
	/* text-shadow: 0 1px 2px rgba(0,0,0,0.30); */
	font-weight: 400;
}

.invite-call {
	/* background: #ffffff; */
	position: absolute;
	top: 0;
	z-index: 100;
	width: 100px;
	height: 187px;
}

.invite-call .local-video {
	width: 100px;
	height: 187px;
}

.invite-call .invite-calling {
	position: absolute;
	top: 0;
	z-index: 101;
	width: 100vw;
	height: 100vh;
}

.invite-calling-header {
	margin-top: 107px;
	display: flex;
	justify-content: flex-end;
	padding: 0 16px;
}

.btn-container {
	display: flex;
	align-items: center;
	position: relative;
}

.invite-calling-header-left {
	position: absolute;
	right: 0;
}

.invite-calling-header-left image {
	width: 32px;
	height: 32px;
}

.invite-calling-header-right {
	display: flex;
	align-items: center;
}

.invite-calling-header-message {
	display: flex;
	flex-direction: column;
	padding: 0 16px;
}

.invite-calling-header-right image {
	width: 100px;
	height: 100px;
	border-radius: 12px;
}

.invite-calling .footer {
	position: absolute;
	bottom: 5vh;
	width: 100%;
}

.invite-calling .btn-operate {
	display: flex;
	justify-content: center;
	align-items: center;
}


.hidden {
	display: none;
}

.trtc-calling {
	width: 100vw;
	height: 100vh;
	overflow: hidden;
	margin: 0;
	z-index: 99;
}

.audio-call {
	width: 100vw;
	height: 100vh;
	position: absolute;
	top: 0;
	z-index: 100;
	background: #FFFFFF;
}

.audio-call>.btn-operate {
	display: flex;
	justify-content: center;
}

.audio-call>image {
	width: 40vw;
	height: 40vw;
	display: block;
	margin: 20vw 30vw;
	margin-top: 40vw;
}

.invite-calling-single>image {
	width: 120px;
	height: 120px;
	border-radius: 12px;
	display: block;
	margin: 120px auto 15px;
	/* margin: 20vw 30vw; */
}

.invite-calling-single .tips {
	width: 100%;
	height: 40px;
	line-height: 40px;
	text-align: center;
	font-size: 20px;
	color: #333333;
	letter-spacing: 0;
	font-weight: 500;
}

.invite-calling-single .tips-subtitle {
	height: 20px;
	font-family: PingFangSC-Regular;
	font-size: 14px;
	color: #97989C;
	letter-spacing: 0;
	font-weight: 400;
	text-align: center;
}

.swiper {
	margin-top: 107px;
	min-height: 374px;
}

.invite-calling-list {

	display: flex;
	flex-wrap: wrap;
	width: 100%;
	justify-content: flex-start
}

.invite-calling-item {
	flex: 0.5;
	/*设置最小宽度，才会让元素排不下，导致换行排列*/
	min-width: 50%;
	height: 187px;
	position: relative;
}

.invite-calling-item image {
	width: 100%;
	height: 100%;
}

.invite-calling-item-message {
	position: absolute;
	top: 0;
	left: 0;
	float: left;
	background: rgba(0, 0, 0, 0.60);
	width: 100%;
	height: 100%;
	z-index: 2;
}

.invite-calling-item-loadimg {
	position: absolute;
	left: calc(50% - 20px);
	top: calc(50% - 20px);
	width: 40px;
	height: 40px;
	-webkit-transform: rotate(360deg);
	animation: rotation 2s linear infinite;
	-moz-animation: rotation 2s linear infinite;
	-webkit-animation: rotation 2s linear infinite;
	-o-animation: rotation 2s linear infinite;
}


@-webkit-keyframes rotation {
	from {
		-webkit-transform: rotate(0deg);
	}

	to {
		-webkit-transform: rotate(360deg);
	}
}

.invite-calling-item-loadimg image {
	width: 100%;
	height: 100%;
}

.invite-calling-item-id {
	position: absolute;
	left: 2%;
	bottom: 2%;
	font-family: PingFangSC-Regular;
	font-weight: 400;
	font-size: 12px;
	color: #FFFFFF;
}

.avatar {
	background-color: black;
}


/* 被叫者 */
.invite-txt {
	width: 126px;
	height: 20px;
	font-family: PingFangSC-Regular;
	font-weight: 400;
	font-size: 14px;
	color: #333333;
	letter-spacing: 0;
	margin: 16px auto 60px auto;
}

.invite-other-txt {
	width: 112px;
	height: 20px;
	font-family: PingFangSC-Regular;
	font-weight: 400;
	font-size: 14px;
	color: #333333;
	letter-spacing: 0;
	margin: 0 auto 24px auto;
}

.invite-other-list {
	position: absolute;
	left: 14vw;
	margin-top: 0 auto;
	display: flex;
	flex-wrap: wrap;
	width: 272px;
	justify-content: center;
	flex-wrap: wrap;
}

.invite-other-item {
	flex: 0.25;
	/*设置最小宽度，才会让元素排不下，导致换行排列*/
	text-align: center;
	max-width: 64px;
	margin: 2px;
}

.invite-other-item image {
	border-radius: 10%;
	max-width: 64px;
	height: 64px;
}

.invite-other-item-name {
	font-family: PingFangSC-Regular;
	font-weight: 400;
	font-size: 12px;
	color: #666666;
	letter-spacing: 0;
	line-height: 18px;
}