!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(require("tim-wx-sdk")):"function"==typeof define&&define.amd?define(["tim-wx-sdk"],e):"object"==typeof exports?exports.TSignaling=e(require("tim-wx-sdk")):t.TSignaling=e(t.TIM)}(window,(function(t){return function(t){var e={};function n(i){if(e[i])return e[i].exports;var r=e[i]={i:i,l:!1,exports:{}};return t[i].call(r.exports,r,r.exports,n),r.l=!0,r.exports}return n.m=t,n.c=e,n.d=function(t,e,i){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:i})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var i=Object.create(null);if(n.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var r in t)n.d(i,r,function(e){return t[e]}.bind(null,r));return i},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=7)}([function(t,e,n){"use strict";n.d(e,"e",(function(){return h})),n.d(e,"g",(function(){return I})),n.d(e,"c",(function(){return g})),n.d(e,"f",(function(){return v})),n.d(e,"b",(function(){return m})),n.d(e,"d",(function(){return T})),n.d(e,"a",(function(){return S})),n.d(e,"h",(function(){return b}));const i="undefined"!=typeof window,r=("undefined"!=typeof wx&&wx.getSystemInfoSync,i&&window.navigator&&window.navigator.userAgent||""),o=/AppleWebKit\/([\d.]+)/i.exec(r),s=(o&&parseFloat(o.pop()),/iPad/i.test(r)),a=/iPhone/i.test(r)&&!s,u=/iPod/i.test(r),l=a||s||u,c=(function(){const t=r.match(/OS (\d+)_/i);t&&t[1]&&t[1]}(),/Android/i.test(r)),d=function(){const t=r.match(/Android (\d+)(?:\.(\d+))?(?:\.(\d+))*/i);if(!t)return null;const e=t[1]&&parseFloat(t[1]),n=t[2]&&parseFloat(t[2]);return e&&n?parseFloat(t[1]+"."+t[2]):e||null}(),f=(c&&/webkit/i.test(r),/Firefox/i.test(r),/Edge/i.test(r)),_=!f&&/Chrome/i.test(r),p=(function(){const t=r.match(/Chrome\/(\d+)/);t&&t[1]&&parseFloat(t[1])}(),/MSIE/.test(r),/MSIE\s8\.0/.test(r),function(){const t=/MSIE\s(\d+)\.\d/.exec(r);let e=t&&parseFloat(t[1]);!e&&/Trident\/7.0/i.test(r)&&/rv:11.0/.test(r)&&(e=11)}(),/Safari/i.test(r),/TBS\/\d+/i.test(r));(function(){const t=r.match(/TBS\/(\d+)/i);if(t&&t[1])t[1]})(),!p&&/MQQBrowser\/\d+/i.test(r),!p&&/ QQBrowser\/\d+/i.test(r),/(micromessenger|webbrowser)/i.test(r),/Windows/i.test(r),/MAC OS X/i.test(r),/MicroMessenger/i.test(r);n(2),n(1);const h=function(t){return"map"===y(t)},I=function(t){return"set"===y(t)},g=function(t){return"file"===y(t)},v=function(t){if("object"!=typeof t||null===t)return!1;const e=Object.getPrototypeOf(t);if(null===e)return!0;let n=e;for(;null!==Object.getPrototypeOf(n);)n=Object.getPrototypeOf(n);return e===n},E=function(t){return"function"==typeof Array.isArray?Array.isArray(t):"array"===y(t)},m=function(t){return E(t)||function(t){return null!==t&&"object"==typeof t}(t)},T=function(t){return t instanceof Error},y=function(t){return Object.prototype.toString.call(t).match(/^\[object (.*)\]$/)[1].toLowerCase()};let D=0;Date.now||(Date.now=function(){return(new Date).getTime()});const S={now:function(){0===D&&(D=Date.now()-1);const t=Date.now()-D;return t>4294967295?(D+=4294967295,Date.now()-D):t},utc:function(){return Math.round(Date.now()/1e3)}},b=function(t){return JSON.stringify(t,["message","code"])}},function(t,e,n){"use strict";n.r(e);var i=n(3),r=n(0);let o=0;const s=new Map;function a(){const t=new Date;return"TSignaling "+t.toLocaleTimeString("en-US",{hour12:!1})+"."+function(t){let e;switch(t.toString().length){case 1:e="00"+t;break;case 2:e="0"+t;break;default:e=t}return e}(t.getMilliseconds())+":"}const u={_data:[],_length:0,_visible:!1,arguments2String(t){let e;if(1===t.length)e=a()+t[0];else{e=a();for(let n=0,i=t.length;n<i;n++)Object(r.b)(t[n])?Object(r.d)(t[n])?e+=Object(r.h)(t[n]):e+=JSON.stringify(t[n]):e+=t[n],e+=" "}return e},debug:function(){if(o<=-1){const t=this.arguments2String(arguments);u.record(t,"debug"),i.a.debug(t)}},log:function(){if(o<=0){const t=this.arguments2String(arguments);u.record(t,"log"),i.a.log(t)}},info:function(){if(o<=1){const t=this.arguments2String(arguments);u.record(t,"info"),i.a.info(t)}},warn:function(){if(o<=2){const t=this.arguments2String(arguments);u.record(t,"warn"),i.a.warn(t)}},error:function(){if(o<=3){const t=this.arguments2String(arguments);u.record(t,"error"),i.a.error(t)}},time:function(t){s.set(t,r.a.now())},timeEnd:function(t){if(s.has(t)){const e=r.a.now()-s.get(t);return s.delete(t),e}return i.a.warn(`未找到对应label: ${t}, 请在调用 logger.timeEnd 前，调用 logger.time`),0},setLevel:function(t){t<4&&i.a.log(a()+"set level from "+o+" to "+t),o=t},record:function(t,e){1100===u._length&&(u._data.splice(0,100),u._length=1e3),u._length++,u._data.push(`${t} [${e}] \n`)},getLog:function(){return u._data}};e.default=u},function(t,e,n){"use strict";n.r(e);e.default={MSG_PRIORITY_HIGH:"High",MSG_PRIORITY_NORMAL:"Normal",MSG_PRIORITY_LOW:"Low",MSG_PRIORITY_LOWEST:"Lowest",KICKED_OUT_MULT_ACCOUNT:"multipleAccount",KICKED_OUT_MULT_DEVICE:"multipleDevice",KICKED_OUT_USERSIG_EXPIRED:"userSigExpired",NET_STATE_CONNECTED:"connected",NET_STATE_CONNECTING:"connecting",NET_STATE_DISCONNECTED:"disconnected",ENTER_ROOM_SUCCESS:"JoinedSuccess",ALREADY_IN_ROOM:"AlreadyInGroup"}},function(t,e,n){"use strict";(function(t){let n,i;n="undefined"!=typeof console?console:void 0!==t&&t.console?t.console:"undefined"!=typeof window&&window.console?window.console:{};const r=function(){},o=["assert","clear","count","debug","dir","dirxml","error","group","groupCollapsed","groupEnd","info","log","profile","profileEnd","table","time","timeEnd","timeStamp","trace","warn"];let s=o.length;for(;s--;)i=o[s],console[i]||(n[i]=r);e.a=n}).call(this,n(9))},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});e.default={NEW_INVITATION_RECEIVED:"ts_new_invitation_received",INVITEE_ACCEPTED:"ts_invitee_accepted",INVITEE_REJECTED:"ts_invitee_rejected",INVITATION_CANCELLED:"ts_invitation_cancelled",INVITATION_TIMEOUT:"ts_invitation_timeout",SDK_READY:"ts_im_ready",SDK_NOT_READY:"ts_im_not_ready",TEXT_MESSAGE_RECEIVED:"ts_text_message_received",CUSTOM_MESSAGE_RECEIVED:"ts_custom_message_received",REMOTE_USER_JOIN:"ts_remote_user_join",REMOTE_USER_LEAVE:"ts_remote_user_leave",KICKED_OUT:"ts_kicked_out",NET_STATE_CHANGE:"ts_net_state_change",MESSAGE_SENT_BY_ME:"ts_message_sent_by_me"}},function(t,e,n){"use strict";var i,r,o;Object.defineProperty(e,"__esModule",{value:!0}),e.ErrorCode=e.BusinessID=e.ActionType=void 0,function(t){t[t.INVITE=1]="INVITE",t[t.CANCEL_INVITE=2]="CANCEL_INVITE",t[t.ACCEPT_INVITE=3]="ACCEPT_INVITE",t[t.REJECT_INVITE=4]="REJECT_INVITE",t[t.INVITE_TIMEOUT=5]="INVITE_TIMEOUT"}(i||(i={})),e.ActionType=i,function(t){t[t.SIGNAL=1]="SIGNAL"}(r||(r={})),e.BusinessID=r,function(t){t[t.ERR_INVALID_PARAMETERS=6017]="ERR_INVALID_PARAMETERS",t[t.ERR_SDK_SIGNALING_INVALID_INVITE_ID=8010]="ERR_SDK_SIGNALING_INVALID_INVITE_ID",t[t.ERR_SDK_SIGNALING_NO_PERMISSION=8011]="ERR_SDK_SIGNALING_NO_PERMISSION"}(o||(o={})),e.ErrorCode=o},function(e,n){e.exports=t},function(t,e,n){"use strict";var i=this&&this.__awaiter||function(t,e,n,i){return new(n||(n=Promise))((function(r,o){function s(t){try{u(i.next(t))}catch(t){o(t)}}function a(t){try{u(i.throw(t))}catch(t){o(t)}}function u(t){var e;t.done?r(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(s,a)}u((i=i.apply(t,e||[])).next())}))},r=this&&this.__generator||function(t,e){var n,i,r,o,s={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(o){return function(a){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;s;)try{if(n=1,i&&(r=2&o[0]?i.return:o[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,o[1])).done)return r;switch(i=0,r&&(o=[2&o[0],r.value]),o[0]){case 0:case 1:r=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,i=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(!(r=s.trys,(r=r.length>0&&r[r.length-1])||6!==o[0]&&2!==o[0])){s=0;continue}if(3===o[0]&&(!r||o[1]>r[0]&&o[1]<r[3])){s.label=o[1];break}if(6===o[0]&&s.label<r[1]){s.label=r[1],r=o;break}if(r&&s.label<r[2]){s.label=r[2],s.ops.push(o);break}r[2]&&s.ops.pop(),s.trys.pop();continue}o=e.call(t,s)}catch(t){o=[6,t],i=0}finally{n=r=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,a])}}},o=this&&this.__spreadArrays||function(){for(var t=0,e=0,n=arguments.length;e<n;e++)t+=arguments[e].length;var i=Array(t),r=0;for(e=0;e<n;e++)for(var o=arguments[e],s=0,a=o.length;s<a;s++,r++)i[r]=o[s];return i};Object.defineProperty(e,"__esModule",{value:!0});var s=n(5),a=n(8),u=n(4),l=n(2),c=n(1),d=n(10),f=n(11),_=n(12),p=n(13),h=n(6),I=n(15).version,g=n(16),v=function(){function t(t){if(this._outerEmitter=null,this._safetyCallbackFactory=null,this._tim=null,this._imSDKAppID=0,this._userID=null,this._groupID="",this._isHandling=!1,this._inviteInfoMap=new Map,this.offlineSupport=!0,c.default.info("TSignaling version:"+I),d.default(t.SDKAppID))return c.default.error("TSignaling 请传入 SDKAppID !!!"),null;d.default(t.offlineSupport)||"boolean"!=typeof t.offlineSupport?this.offlineSupport=!0:this.offlineSupport=t.offlineSupport,this._outerEmitter=new a.default,this._outerEmitter._emit=this._outerEmitter.emit,this._outerEmitter.emit=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=t[0],i=[n,{name:t[0],data:t[1]}];this._outerEmitter._emit.apply(this._outerEmitter,o(i))}.bind(this),this._safetyCallbackFactory=new _.default,t.tim?this._tim=t.tim:this._tim=h.create({SDKAppID:t.SDKAppID,scene:"TSignaling"}),this.messageClient=new g.default({tim:this._tim}),this._imSDKAppID=t.SDKAppID,this._tim.on(h.EVENT.SDK_READY,this._onIMReady.bind(this)),this._tim.on(h.EVENT.SDK_NOT_READY,this._onIMNotReady.bind(this)),this._tim.on(h.EVENT.KICKED_OUT,this._onKickedOut.bind(this)),this._tim.on(h.EVENT.NET_STATE_CHANGE,this._onNetStateChange.bind(this)),this._tim.on(h.EVENT.MESSAGE_RECEIVED,this._onMessageReceived.bind(this))}return t.prototype.setLogLevel=function(t){c.default.setLevel(t),this._tim.setLogLevel(t)},t.prototype.login=function(t){return i(this,void 0,void 0,(function(){return r(this,(function(e){return c.default.log("TSignaling.login",t),this._userID=t.userID,[2,this._tim.login(t)]}))}))},t.prototype.logout=function(){return i(this,void 0,void 0,(function(){return r(this,(function(t){return c.default.log("TSignaling.logout"),this._userID="",this._inviteInfoMap.clear(),[2,this._tim.logout()]}))}))},t.prototype.on=function(t,e,n){c.default.log("TSignaling.on eventName:"+t),this._outerEmitter.on(t,this._safetyCallbackFactory.defense(t,e,n),n)},t.prototype.off=function(t,e){c.default.log("TSignaling.off eventName:"+t);var n=this._safetyCallbackFactory.find(t,e);null!==n&&(this._outerEmitter.off(t,n),this._safetyCallbackFactory.delete(t,e))},t.prototype.joinGroup=function(t){return i(this,void 0,void 0,(function(){return r(this,(function(e){return c.default.log("TSignaling.joinGroup groupID:"+t),this._groupID=t,[2,this._tim.joinGroup({groupID:t})]}))}))},t.prototype.quitGroup=function(t){return i(this,void 0,void 0,(function(){return r(this,(function(e){return c.default.log("TSignaling.quitGroup groupID:"+t),[2,this._tim.quitGroup(t)]}))}))},t.prototype.sendTextMessage=function(t){return i(this,void 0,void 0,(function(){var e;return r(this,(function(n){return e=this._tim.createTextMessage({to:t.to,conversationType:!0===t.groupFlag?h.TYPES.CONV_GROUP:h.TYPES.CONV_C2C,priority:t.priority||h.TYPES.MSG_PRIORITY_NORMAL,payload:{text:t.text}}),[2,this._tim.sendMessage(e)]}))}))},t.prototype.sendCustomMessage=function(t){return i(this,void 0,void 0,(function(){var e;return r(this,(function(n){return e=this._tim.createCustomMessage({to:t.to,conversationType:!0===t.groupFlag?h.TYPES.CONV_GROUP:h.TYPES.CONV_C2C,priority:t.priority||h.TYPES.MSG_PRIORITY_NORMAL,payload:{data:t.data||"",description:t.description||"",extension:t.extension||""}}),[2,this._tim.sendMessage(e)]}))}))},t.prototype.invite=function(t){return i(this,void 0,void 0,(function(){var e,n,i,o,a,u,l;return r(this,(function(r){switch(r.label){case 0:return e=p.generate(),c.default.log("TSignaling.invite",t,"inviteID="+e),d.default(t)||d.default(t.userID)?[2,Promise.reject(new f.default({code:s.ErrorCode.ERR_INVALID_PARAMETERS,message:"userID is invalid"}))]:(n=t.userID,i=t.data,o=t.timeout,a=t.offlinePushInfo,u={businessID:s.BusinessID.SIGNAL,inviteID:e,inviter:this._userID,actionType:s.ActionType.INVITE,inviteeList:[n],data:i,timeout:d.default(o)?0:o,groupID:""},[4,this._sendCustomMessage(n,u,a)]);case 1:return 0===(l=r.sent()).code?(c.default.log("TSignaling.invite ok"),this._inviteInfoMap.set(e,u),this._startTimer(u,!0),[2,{inviteID:e,code:l.code,data:l.data}]):[2,l]}}))}))},t.prototype.inviteInGroup=function(t){return i(this,void 0,void 0,(function(){var e,n,i,o,a,u,l,_;return r(this,(function(r){switch(r.label){case 0:return e=p.generate(),c.default.log("TSignaling.inviteInGroup",t,"inviteID="+e),d.default(t)||d.default(t.groupID)?[2,Promise.reject(new f.default({code:s.ErrorCode.ERR_INVALID_PARAMETERS,message:"groupID is invalid"}))]:(n=t.groupID,i=t.inviteeList,o=t.data,a=t.timeout,u=t.offlinePushInfo,l={businessID:s.BusinessID.SIGNAL,inviteID:e,inviter:this._userID,actionType:s.ActionType.INVITE,inviteeList:i,data:o,timeout:d.default(a)?0:a,groupID:n},[4,this._sendCustomMessage(n,l,u)]);case 1:return 0===(_=r.sent()).code?(c.default.log("TSignaling.inviteInGroup ok"),this._inviteInfoMap.set(e,l),this._startTimer(l,!0),[2,{inviteID:e,code:_.code,data:_.data}]):[2,_]}}))}))},t.prototype.cancel=function(t){return i(this,void 0,void 0,(function(){var e,n,i,o,a,u,l,_,p;return r(this,(function(r){switch(r.label){case 0:return c.default.log("TSignaling.cancel",t),d.default(t)||d.default(t.inviteID)||!this._inviteInfoMap.has(t.inviteID)||this._isHandling?[2,Promise.reject(new f.default({code:s.ErrorCode.ERR_SDK_SIGNALING_INVALID_INVITE_ID,message:"inviteID is invalid or invitation has been processed"}))]:(this._isHandling=!0,e=t.inviteID,n=t.data,i=this._inviteInfoMap.get(e),o=i.inviter,a=i.groupID,u=i.inviteeList,o!==this._userID?[3,2]:(l={businessID:s.BusinessID.SIGNAL,inviteID:e,inviter:o,actionType:s.ActionType.CANCEL_INVITE,inviteeList:u,data:n,timeout:0,groupID:a},_=a||u[0],[4,this._sendCustomMessage(_,l)]));case 1:return p=r.sent(),this._isHandling=!1,p&&0===p.code?(c.default.log("TSignaling.cancel ok"),this._deleteInviteInfoByID(e),[2,{inviteID:e,code:p.code,data:p.data}]):[2,p];case 2:return c.default.error("TSignaling.cancel unmatched inviter="+o+" and userID="+this._userID),this._isHandling=!1,[2,Promise.reject(new f.default({code:s.ErrorCode.ERR_SDK_SIGNALING_NO_PERMISSION,message:"信令请求无权限，比如取消非自己发起的邀请，接受或则拒绝非发给自己的邀请"}))]}}))}))},t.prototype.accept=function(t){return i(this,void 0,void 0,(function(){var e,n,i,o,a,u,l,_;return r(this,(function(r){switch(r.label){case 0:return c.default.log("TSignaling.accept",t),d.default(t)||d.default(t.inviteID)||!this._inviteInfoMap.has(t.inviteID)||this._isHandling?[2,Promise.reject(new f.default({code:s.ErrorCode.ERR_SDK_SIGNALING_INVALID_INVITE_ID,message:"inviteID is invalid or invitation has been processed"}))]:(this._isHandling=!0,e=t.inviteID,n=t.data,i=this._inviteInfoMap.get(e),o=i.inviter,a=i.groupID,i.inviteeList.includes(this._userID)?(u={businessID:s.BusinessID.SIGNAL,inviteID:e,inviter:o,actionType:s.ActionType.ACCEPT_INVITE,inviteeList:[this._userID],data:n,timeout:0,groupID:a},l=a||o,[4,this._sendCustomMessage(l,u)]):[3,2]);case 1:return _=r.sent(),this._isHandling=!1,_&&0===_.code?(c.default.log("TSignaling.accept ok"),this._updateLocalInviteInfo(u),[2,{inviteID:e,code:_.code,data:_.data}]):[2,_];case 2:return c.default.error("TSignaling.accept inviteeList do not include userID="+this._userID+". inviteID="+e+" groupID="+a),this._isHandling=!1,[2,Promise.reject(new f.default({code:s.ErrorCode.ERR_SDK_SIGNALING_INVALID_INVITE_ID,message:"inviteID is invalid or invitation has been processed"}))]}}))}))},t.prototype.reject=function(t){return i(this,void 0,void 0,(function(){var e,n,i,o,a,u,l,_;return r(this,(function(r){switch(r.label){case 0:return c.default.log("TSignaling.reject",t),d.default(t)||d.default(t.inviteID)||!this._inviteInfoMap.has(t.inviteID)||this._isHandling?[2,Promise.reject(new f.default({code:s.ErrorCode.ERR_SDK_SIGNALING_INVALID_INVITE_ID,message:"inviteID is invalid or invitation has been processed"}))]:(this._isHandling=!0,e=t.inviteID,n=t.data,i=this._inviteInfoMap.get(e),o=i.inviter,a=i.groupID,i.inviteeList.includes(this._userID)?(u={businessID:s.BusinessID.SIGNAL,inviteID:e,inviter:o,actionType:s.ActionType.REJECT_INVITE,inviteeList:[this._userID],data:n,timeout:0,groupID:a},l=a||o,[4,this._sendCustomMessage(l,u)]):[3,2]);case 1:return _=r.sent(),this._isHandling=!1,_&&0===_.code?(c.default.log("TSignaling.reject ok"),this._updateLocalInviteInfo(u),[2,{inviteID:e,code:_.code,data:_.data}]):[2,_];case 2:return c.default.error("TSignaling.reject inviteeList do not include userID="+this._userID+". inviteID="+e+" groupID="+a),this._isHandling=!1,[2,Promise.reject(new f.default({code:s.ErrorCode.ERR_SDK_SIGNALING_INVALID_INVITE_ID,message:"inviteID is invalid or invitation has been processed"}))]}}))}))},t.prototype._onIMReady=function(t){return i(this,void 0,void 0,(function(){var t;return r(this,(function(e){switch(e.label){case 0:return c.default.log("TSignaling._onIMReady"),this._outerEmitter.emit(u.default.SDK_READY),!0!==this.offlineSupport?[3,2]:[4,this.messageClient.getHistoricalSignaling()];case 1:t=e.sent(),!d.default(t)&&this._onMessageReceived({data:t}),e.label=2;case 2:return[2]}}))}))},t.prototype._onIMNotReady=function(t){c.default.log("TSignaling.onSdkNotReady"),this._outerEmitter.emit(u.default.SDK_NOT_READY)},t.prototype._onKickedOut=function(t){c.default.log("TSignaling._onKickedOut"),this._outerEmitter.emit(u.default.KICKED_OUT,t.data)},t.prototype._onNetStateChange=function(t){c.default.log("TSignaling._onNetStateChange"),this._outerEmitter.emit(u.default.NET_STATE_CHANGE,t.data)},t.prototype._onMessageReceived=function(t){var e=this;c.default.log("TSignaling._onMessageReceived inviteID="+JSON.stringify(t));var n=t.data,i=n.filter((function(t){return t.type===h.TYPES.MSG_TEXT}));c.default.log("TSignaling._onMessageReceived textMessages"+JSON.stringify(i)),d.default(i)||this._outerEmitter.emit(u.default.TEXT_MESSAGE_RECEIVED,i);var r=n.filter((function(t){return t.type===h.TYPES.MSG_GRP_TIP&&t.payload.operationType===h.TYPES.GRP_TIP_MBR_JOIN}));d.default(r)||this._outerEmitter.emit(u.default.REMOTE_USER_JOIN,r);var o=n.filter((function(t){return t.type===h.TYPES.MSG_GRP_TIP&&t.payload.operationType===h.TYPES.GRP_TIP_MBR_QUIT}));d.default(o)||this._outerEmitter.emit(u.default.REMOTE_USER_LEAVE,o);var a=n.filter((function(t){return t.type===h.TYPES.MSG_CUSTOM})),l=[];a.forEach((function(t){var n,i=t.payload.data,r=!0;try{n=JSON.parse(i)}catch(t){r=!1}if(r){var o=n.businessID,a=n.actionType;if(1===o)switch(a){case s.ActionType.INVITE:e._onNewInvitationReceived(n);break;case s.ActionType.REJECT_INVITE:e._onInviteeRejected(n);break;case s.ActionType.ACCEPT_INVITE:e._onInviteeAccepted(n);break;case s.ActionType.CANCEL_INVITE:e._onInvitationCancelled(n);break;case s.ActionType.INVITE_TIMEOUT:e._onInvitationTimeout(n)}else{if("av_call"===o)return!0;c.default.warn("TSignaling._onMessageReceived unknown businessID="+o),l.push(t)}}else l.push(t)})),d.default(l)||this._outerEmitter.emit(u.default.CUSTOM_MESSAGE_RECEIVED,l)},t.prototype._hasLocalInviteInfo=function(t,e){var n=t.inviteID,i=t.groupID;if(c.default.log("TSignaling._hasLocalInviteInfo inviteID="+n+" groupID="+i),!this._inviteInfoMap.has(n))return!1;var r=this._inviteInfoMap.get(n).inviteeList;return!i||(e?r.length>0:r.length>0&&r.includes(this._userID))},t.prototype._startTimer=function(t,e){var n=this;void 0===e&&(e=!0);var i=t.timeout;if(c.default.log("TSignaling._startTimer timeout="+i+" isInvitator="+e+" timeoutStatus="+(0===i)),0!==i)var r=e?i+5:i,o=1,s=setInterval((function(){var i=n._hasLocalInviteInfo(t,e);c.default.log("TSignaling.setInterval hasInviteInfo="+i),o<r&&i?++o:(i&&n._sendTimeoutNotice(t,e),clearInterval(s))}),1e3)},t.prototype._sendTimeoutNotice=function(t,e){return i(this,void 0,void 0,(function(){var n,i,o,a,l,d,f,_;return r(this,(function(r){switch(r.label){case 0:return n=t.inviteID,i=t.groupID,o=t.inviteeList,a=t.inviter,l=t.data,d=e?i||o[0]:i||a,c.default.log("TSignaling._sendTimeoutNotice inviteID="+n+" to="+d+" isInvitator="+e),f={businessID:s.BusinessID.SIGNAL,inviteID:n,inviter:a,actionType:s.ActionType.INVITE_TIMEOUT,inviteeList:e?o:[this._userID],data:l,timeout:0,groupID:i},[4,this._sendCustomMessage(d,f)];case 1:return(_=r.sent())&&0===_.code&&(this._outerEmitter.emit(u.default.INVITATION_TIMEOUT,{inviter:a,inviteID:n,groupID:i,inviteeList:f.inviteeList,isSelfTimeout:!0}),e?this._deleteInviteInfoByID(n):this._updateLocalInviteInfo(f)),[2,_]}}))}))},t.prototype._onNewInvitationReceived=function(e){var n=e.inviteID,i=e.inviter,r=e.inviteeList,o=e.groupID,s=e.data,a=r.includes(this._userID);c.default.log("TSignaling._onNewInvitationReceived",e,"myselfIncluded="+a);var u=JSON.parse(s),l=o&&(0===u.call_end||d.default(u.room_id));d.default(u.room_id)&&u.call_end>0||l?this._outerEmitter.emit(t.EVENT.NEW_INVITATION_RECEIVED,{inviteID:n,inviter:i,groupID:o,inviteeList:r,data:e.data||""}):(o&&a||!o)&&(this._inviteInfoMap.set(n,e),this._outerEmitter.emit(t.EVENT.NEW_INVITATION_RECEIVED,{inviteID:n,inviter:i,groupID:o,inviteeList:r,data:e.data||""}),this._startTimer(e,!1))},t.prototype._onInviteeRejected=function(t){var e=t.inviteID,n=t.inviter,i=t.groupID,r=this._inviteInfoMap.has(e);c.default.log("TSignaling._onInviteeRejected inviteID="+e+" hasInviteID="+r+" inviter="+n+" groupID="+i),(i&&r||!i)&&(this._updateLocalInviteInfo(t),this._outerEmitter.emit(u.default.INVITEE_REJECTED,{inviteID:e,inviter:n,groupID:i,invitee:t.inviteeList[0],data:t.data||""}))},t.prototype._onInviteeAccepted=function(t){var e=t.inviteID,n=t.inviter,i=t.groupID,r=this._inviteInfoMap.has(e);c.default.log("TSignaling._onInviteeAccepted inviteID="+e+" hasInviteID="+r+" inviter="+n+" groupID="+i),(i&&r||!i)&&(this._updateLocalInviteInfo(t),this._outerEmitter.emit(u.default.INVITEE_ACCEPTED,{inviteID:e,inviter:n,groupID:i,invitee:t.inviteeList[0],data:t.data||""}))},t.prototype._onInvitationCancelled=function(t){var e=t.inviteID,n=t.inviter,i=t.groupID,r=this._inviteInfoMap.has(e);c.default.log("TSignaling._onInvitationCancelled inviteID="+e+" hasInviteID="+r+" inviter="+n+" groupID="+i),(i&&r||!i)&&(this._deleteInviteInfoByID(e),this._outerEmitter.emit(u.default.INVITATION_CANCELLED,{inviteID:e,inviter:n,groupID:i,data:t.data||""}))},t.prototype._onInvitationTimeout=function(t){var e=t.inviteID,n=t.inviter,i=t.groupID,r=t.data,o=this._inviteInfoMap.has(e);c.default.log("TSignaling._onInvitationTimeout inviteID="+e+" hasInviteID="+o+" inviter="+n+" groupID="+i),c.default.log("TSignaling._onInvitationTimeout data="+t.data);var s=JSON.parse(r),a=i&&(0===s.call_end||d.default(s.room_id));d.default(s.room_id)&&s.call_end>0||a||(i&&o||!i)&&(this._updateLocalInviteInfo(t),this._outerEmitter.emit(u.default.INVITATION_TIMEOUT,{inviteID:e,inviter:n,groupID:i,inviteeList:t.inviteeList,isSelfTimeout:!1}))},t.prototype._updateLocalInviteInfo=function(t){var e=t.inviteID,n=t.inviter,i=t.inviteeList,r=t.groupID;if(c.default.log("TSignaling._updateLocalInviteInfo inviteID="+e+" inviter="+n+" groupID="+r),r){if(this._inviteInfoMap.has(e)){var o=i[0],s=this._inviteInfoMap.get(e).inviteeList;s.includes(o)&&(s.splice(s.indexOf(o),1),c.default.log("TSignaling._updateLocalInviteInfo remove "+o+" from localInviteeList. "+s.length+" invitees left")),0===s.length&&this._deleteInviteInfoByID(e)}}else this._deleteInviteInfoByID(e)},t.prototype._deleteInviteInfoByID=function(t){this._inviteInfoMap.has(t)&&(c.default.log("TSignaling._deleteInviteInfoByID remove "+t+" from inviteInfoMap."),this._inviteInfoMap.delete(t))},t.prototype._sendCustomMessage=function(t,e,n){var o,a,l,d;return i(this,void 0,void 0,(function(){var i,f,_,p,I,g;return r(this,(function(r){switch(r.label){case 0:return i=e.groupID,f=this._tim.createCustomMessage({to:t,conversationType:i?h.TYPES.CONV_GROUP:h.TYPES.CONV_C2C,priority:h.TYPES.MSG_PRIORITY_HIGH,payload:{data:JSON.stringify(e)}}),e.actionType!==s.ActionType.INVITE?[3,2]:(p={title:(_=n||{}).title||"",description:_.description||"您有一个通话请求",androidOPPOChannelID:_.androidOPPOChannelID||"",extension:this._handleOfflineInfo(e,_.extension||"")},c.default.log("TSignaling.offlinePushInfo "+JSON.stringify(p)),[4,this._tim.sendMessage(f,{offlinePushInfo:{title:p.title,description:p.description,androidOPPOChannelID:p.androidOPPOChannelID,extension:p.extension}})]);case 1:return I=r.sent(),(null===(o=null==I?void 0:I.data)||void 0===o?void 0:o.message)&&this._outerEmitter.emit(u.default.MESSAGE_SENT_BY_ME,null===(a=null==I?void 0:I.data)||void 0===a?void 0:a.message),[2,I];case 2:return[4,this._tim.sendMessage(f)];case 3:return g=r.sent(),(null===(l=null==g?void 0:g.data)||void 0===l?void 0:l.message)&&this._outerEmitter.emit(u.default.MESSAGE_SENT_BY_ME,null===(d=null==g?void 0:g.data)||void 0===d?void 0:d.message),[2,g]}}))}))},t.prototype._handleOfflineInfo=function(t,e){var n=JSON.parse(t.data),i={action:t.actionType,call_type:n.call_type,room_id:n.room_id,call_id:t.inviteID,timeout:n.timeout,version:4,invited_list:t.inviteeList};t.groupID&&(i.group_id=t.groupID);var r={entity:{action:2,chatType:t.groupID?2:1,content:JSON.stringify(i),sendTime:parseInt(Date.now()/1e3+""),sender:t.inviter,version:1},userData:e||""},o=JSON.stringify(r);return c.default.log("TSignaling._handleOfflineInfo "+o),o},t.EVENT=u.default,t.TYPES=l.default,t}();e.default=v},function(t,e,n){"use strict";n.r(e),n.d(e,"default",(function(){return s}));const i=Function.prototype.apply,r=new WeakMap;function o(t){return r.has(t)||r.set(t,{}),r.get(t)}class s{constructor(t=null,e=console){const n=o(this);return n._events=new Set,n._callbacks={},n._console=e,n._maxListeners=null===t?null:parseInt(t,10),this}_addCallback(t,e,n,i){return this._getCallbacks(t).push({callback:e,context:n,weight:i}),this._getCallbacks(t).sort((t,e)=>t.weight>e.weight),this}_getCallbacks(t){return o(this)._callbacks[t]}_getCallbackIndex(t,e){return this._has(t)?this._getCallbacks(t).findIndex(t=>t.callback===e):null}_achieveMaxListener(t){return null!==o(this)._maxListeners&&o(this)._maxListeners<=this.listenersNumber(t)}_callbackIsExists(t,e,n){const i=this._getCallbackIndex(t,e),r=-1!==i?this._getCallbacks(t)[i]:void 0;return-1!==i&&r&&r.context===n}_has(t){return o(this)._events.has(t)}on(t,e,n=null,i=1){const r=o(this);if("function"!=typeof e)throw new TypeError(e+" is not a function");return this._has(t)?(this._achieveMaxListener(t)&&r._console.warn(`Max listeners (${r._maxListeners}) for event "${t}" is reached!`),this._callbackIsExists(...arguments)&&r._console.warn(`Event "${t}" already has the callback ${e}.`)):(r._events.add(t),r._callbacks[t]=[]),this._addCallback(...arguments),this}once(t,e,n=null,r=1){const o=(...r)=>(this.off(t,o),i.call(e,n,r));return this.on(t,o,n,r)}off(t,e=null){const n=o(this);let i;return this._has(t)&&(null===e?(n._events.delete(t),n._callbacks[t]=null):(i=this._getCallbackIndex(t,e),-1!==i&&(n._callbacks[t].splice(i,1),this.off(...arguments)))),this}emit(t,...e){return this._has(t)&&this._getCallbacks(t).forEach(t=>i.call(t.callback,t.context,e)),this}clear(){const t=o(this);return t._events.clear(),t._callbacks={},this}listenersNumber(t){return this._has(t)?this._getCallbacks(t).length:null}}},function(t,e){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(t){"object"==typeof window&&(n=window)}t.exports=n},function(t,e,n){"use strict";n.r(e);var i=n(0);const r=Object.prototype.hasOwnProperty;e.default=function(t){if(null==t)return!0;if("boolean"==typeof t)return!1;if("number"==typeof t)return 0===t;if("string"==typeof t)return 0===t.length;if("function"==typeof t)return 0===t.length;if(Array.isArray(t))return 0===t.length;if(t instanceof Error)return""===t.message;if(Object(i.f)(t)){for(const e in t)if(r.call(t,e))return!1;return!0}return!!(Object(i.e)(t)||Object(i.g)(t)||Object(i.c)(t))&&0===t.size}},function(t,e,n){"use strict";n.r(e);class i extends Error{constructor(t){super(),this.code=t.code,this.message=t.message,this.data=t.data||{}}}e.default=i},function(t,e,n){"use strict";n.r(e);var i=n(1),r=n(4),o=n.n(r);e.default=class{constructor(){this._funcMap=new Map}defense(t,e,n){if("string"!=typeof t)return null;if(0===t.length)return null;if("function"!=typeof e)return null;if(this._funcMap.has(t)&&this._funcMap.get(t).has(e))return this._funcMap.get(t).get(e);this._funcMap.has(t)||this._funcMap.set(t,new Map);let i=null;return this._funcMap.get(t).has(e)?i=this._funcMap.get(t).get(e):(i=this._pack(t,e,n),this._funcMap.get(t).set(e,i)),i}defenseOnce(t,e,n){return"function"!=typeof e?null:this._pack(t,e,n)}find(t,e){return"string"!=typeof t||0===t.length||"function"!=typeof e?null:this._funcMap.has(t)?this._funcMap.get(t).has(e)?this._funcMap.get(t).get(e):(i.default.log(`SafetyCallback.find: 找不到 func —— ${t}/${""!==e.name?e.name:"[anonymous]"}`),null):(i.default.log(`SafetyCallback.find: 找不到 eventName-${t} 对应的 func`),null)}delete(t,e){return"function"==typeof e&&(!!this._funcMap.has(t)&&(!!this._funcMap.get(t).has(e)&&(this._funcMap.get(t).delete(e),0===this._funcMap.get(t).size&&this._funcMap.delete(t),!0)))}_pack(t,e,n){return function(){try{e.apply(n,Array.from(arguments))}catch(e){const n=Object.values(o.a).indexOf(t),r=Object.keys(o.a)[n];i.default.error(`接入侧事件 EVENT.${r} 对应的回调函数逻辑存在问题，请检查！`,e)}}}}},function(t,e,n){
/**
 * UUID.js - RFC-compliant UUID Generator for JavaScript
 *
 * @file
 * <AUTHOR>
 * @version v4.2.12
 * @license Apache License 2.0: Copyright (c) 2010-2022 LiosK
 */
var i;i=function(e){"use strict";function i(){var t=r._getRandomInt;this.timestamp=0,this.tick=0,this.sequence=t(14),this.node=1099511627776*(1|t(8))+t(40)}function r(){}return r.generate=function(){var t=r._getRandomInt,e=r._hexAligner;return e(t(32),8)+"-"+e(t(16),4)+"-"+e(16384|t(12),4)+"-"+e(32768|t(14),4)+"-"+e(t(48),12)},r._getRandomInt=function(t){if(t<0||t>53)return NaN;var e=0|***********Math.random();return t>30?e+***********(0|Math.random()*(1<<t-30)):e>>>30-t},r._hexAligner=function(t,e){for(var n=t.toString(16),i=e-n.length,r="0";i>0;i>>>=1,r+=r)1&i&&(n=r+n);return n},r.overwrittenUUID=e,function(){var t=r._getRandomInt;r.useMathRandom=function(){r._getRandomInt=t};var e=null,i=t;"undefined"!=typeof window&&(e=window.crypto||window.msCrypto)?e.getRandomValues&&"undefined"!=typeof Uint32Array&&(i=function(t){if(t<0||t>53)return NaN;var n=new Uint32Array(t>32?2:1);return n=e.getRandomValues(n)||n,t>32?n[0]+***********(n[1]>>>64-t):n[0]>>>32-t}):(e=n(14))&&e.randomBytes&&(i=function(t){if(t<0||t>53)return NaN;var n=e.randomBytes(t>32?8:4),i=n.readUInt32BE(0);return t>32?i+***********(n.readUInt32BE(4)>>>64-t):i>>>32-t}),r._getRandomInt=i}(),r.FIELD_NAMES=["timeLow","timeMid","timeHiAndVersion","clockSeqHiAndReserved","clockSeqLow","node"],r.FIELD_SIZES=[32,16,16,8,8,48],r.genV4=function(){var t=r._getRandomInt;return(new r)._init(t(32),t(16),16384|t(12),128|t(6),t(8),t(48))},r.parse=function(t){var e;if(e=/^\s*(urn:uuid:|\{)?([0-9a-f]{8})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{2})([0-9a-f]{2})-([0-9a-f]{12})(\})?\s*$/i.exec(t)){var n=e[1]||"",i=e[8]||"";if(n+i===""||"{"===n&&"}"===i||"urn:uuid:"===n.toLowerCase()&&""===i)return(new r)._init(parseInt(e[2],16),parseInt(e[3],16),parseInt(e[4],16),parseInt(e[5],16),parseInt(e[6],16),parseInt(e[7],16))}return null},r.prototype._init=function(){var t=r.FIELD_NAMES,e=r.FIELD_SIZES,n=r._binAligner,i=r._hexAligner;this.intFields=new Array(6),this.bitFields=new Array(6),this.hexFields=new Array(6);for(var o=0;o<6;o++){var s=parseInt(arguments[o]||0);this.intFields[o]=this.intFields[t[o]]=s,this.bitFields[o]=this.bitFields[t[o]]=n(s,e[o]),this.hexFields[o]=this.hexFields[t[o]]=i(s,e[o]>>>2)}return this.version=this.intFields.timeHiAndVersion>>>12&15,this.bitString=this.bitFields.join(""),this.hexNoDelim=this.hexFields.join(""),this.hexString=this.hexFields[0]+"-"+this.hexFields[1]+"-"+this.hexFields[2]+"-"+this.hexFields[3]+this.hexFields[4]+"-"+this.hexFields[5],this.urn="urn:uuid:"+this.hexString,this},r._binAligner=function(t,e){for(var n=t.toString(2),i=e-n.length,r="0";i>0;i>>>=1,r+=r)1&i&&(n=r+n);return n},r.prototype.toString=function(){return this.hexString},r.prototype.equals=function(t){if(!(t instanceof r))return!1;for(var e=0;e<6;e++)if(this.intFields[e]!==t.intFields[e])return!1;return!0},r.NIL=(new r)._init(0,0,0,0,0,0),r.genV1=function(){null==r._state&&r.resetState();var t=(new Date).getTime(),e=r._state;t!=e.timestamp?(t<e.timestamp&&e.sequence++,e.timestamp=t,e.tick=r._getRandomInt(12)):e.tick<9992?e.tick+=1+r._getRandomInt(3):e.sequence++;var n=r._getTimeFieldValues(e.timestamp),i=n.low+e.tick,o=4095&n.hi|4096;e.sequence&=16383;var s=e.sequence>>>8|128,a=255&e.sequence;return(new r)._init(i,n.mid,o,s,a,e.node)},r.resetState=function(){r._state=new i},r._tsRatio=1/4,r._state=null,r._getTimeFieldValues=function(t){var e=t-Date.UTC(1582,9,15),n=e/***********1e4&268435455;return{low:1e4*(268435455&e)%**********,mid:65535&n,hi:n>>>16,timestamp:e}},"object"==typeof t.exports&&(t.exports=r),r}(i)},function(t,e){},function(t){t.exports=JSON.parse('{"name":"tsignaling","version":"1.0.2","description":"腾讯云 Web 信令 SDK","main":"./src/index.ts","scripts":{"lint":"./node_modules/.bin/eslint ./src","fix":"./node_modules/.bin/eslint --fix ./src","ts2js":"tsc src/index.ts --outDir build/ts2js","doc":"npm run ts2js && npm run doc:clean && npm run doc:build","doc:build":"./node_modules/.bin/jsdoc -c build/jsdoc/jsdoc.json && node ./build/jsdoc/fix-doc.js","doc:clean":"node ./build/jsdoc/clean-doc.js","build:wx":"cross-env NODE_ENV=wx webpack --config webpack.prod.config.js","build:web":"node node_modules/cross-env/src/bin/cross-env.js NODE_ENV=web node_modules/webpack/bin/webpack.js --config webpack.prod.config.js","build:package":"node build/package-bundle.js","prerelease":"npm run changelog && npm run build:web && npm run build:wx && npm run build:package && node ./build/copy.js","start:wx":"cross-env NODE_ENV=wx  webpack-dev-server --config webpack.config.js","start:web":"node node_modules/cross-env/src/bin/cross-env.js NODE_ENV=web node_modules/webpack-dev-server/bin/webpack-dev-server.js --config webpack.dev.config.js","build_withcopy":"npm run build:web && cp dist/npm/tsignaling-js.js ../TIM-demo-web/node_modules/tsignaling/tsignaling-js.js","build_withcopy:mp":"npm run build:wx && cp dist/npm/tsignaling-wx.js ../TIM-demo-mini/static/component/TRTCCalling/utils/tsignaling-wx.js","changelog":"cp CHANGELOG.md build/jsdoc/tutorials/CHANGELOG.md"},"husky":{"hooks":{"pre-commit":"npm run lint"}},"lint-staged":{"*.{.ts,.tsx}":["eslint","git add"]},"keywords":["腾讯云","即时通信","信令"],"author":"","license":"ISC","devDependencies":{"conventional-changelog-cli":"^2.1.1","cross-env":"^7.0.2","fs-extra":"^9.0.1","html-webpack-plugin":"^4.3.0","ts-loader":"^7.0.5","typescript":"^3.9.9","webpack":"^4.43.0","webpack-cli":"^3.3.11","webpack-dev-server":"^3.11.0"},"dependencies":{"@typescript-eslint/eslint-plugin":"^4.22.1","@typescript-eslint/parser":"^4.22.1","EventEmitter":"^1.0.0","docdash-blue":"^1.1.3","eslint":"^5.16.0","eslint-config-google":"^0.13.0","eslint-plugin-classes":"^0.1.1","jsdoc":"^3.6.4","jsdoc-plugin-typescript":"^2.0.5","pretty":"^2.0.0","replace":"^1.2.0","uuidjs":"^4.2.5"}}')},function(t,e,n){"use strict";var i,r=this&&this.__extends||(i=function(t,e){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),o=this&&this.__awaiter||function(t,e,n,i){return new(n||(n=Promise))((function(r,o){function s(t){try{u(i.next(t))}catch(t){o(t)}}function a(t){try{u(i.throw(t))}catch(t){o(t)}}function u(t){var e;t.done?r(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(s,a)}u((i=i.apply(t,e||[])).next())}))},s=this&&this.__generator||function(t,e){var n,i,r,o,s={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(o){return function(a){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;s;)try{if(n=1,i&&(r=2&o[0]?i.return:o[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,o[1])).done)return r;switch(i=0,r&&(o=[2&o[0],r.value]),o[0]){case 0:case 1:r=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,i=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(!(r=s.trys,(r=r.length>0&&r[r.length-1])||6!==o[0]&&2!==o[0])){s=0;continue}if(3===o[0]&&(!r||o[1]>r[0]&&o[1]<r[3])){s.label=o[1];break}if(6===o[0]&&s.label<r[1]){s.label=r[1],r=o;break}if(r&&s.label<r[2]){s.label=r[2],s.ops.push(o);break}r[2]&&s.ops.pop(),s.trys.pop();continue}o=e.call(t,s)}catch(t){o=[6,t],i=0}finally{n=r=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,a])}}};Object.defineProperty(e,"__esModule",{value:!0});var a=n(5),u=n(6),l=function(){function t(){Object.defineProperty(this,"currentTime",{get:function(){return(new Date).getTime()}})}return t.prototype.doFilter=function(t){return o(this,void 0,void 0,(function(){return s(this,(function(t){return[2]}))}))},t}(),c=function(){function t(){this.filterList=[],this.result=[]}return t.prototype.register=function(t){this.filterList.push(t)},t.prototype.run=function(t){return void 0===t&&(t=[]),o(this,void 0,void 0,(function(){var e,n;return s(this,(function(i){switch(i.label){case 0:this.result=t,e=0,i.label=1;case 1:return e<this.filterList.length?(n=this,[4,this.filterList[e].doFilter(this.result)]):[3,4];case 2:n.result=i.sent(),i.label=3;case 3:return e++,[3,1];case 4:return[2,this.result]}}))}))},t}(),d=function(t){function e(e){var n=t.call(this)||this;return n.tim=null,n.expiredTime=3e4,n.maxCircleLength=20,n.tim=e.tim,n}return r(e,t),e.prototype.doFilter=function(t){return o(this,void 0,void 0,(function(){var t,e,n,i,r,o,a,u;return s(this,(function(s){switch(s.label){case 0:return t=[],[4,this.tim.getConversationList()];case 1:if(e=s.sent(),n=e.code,i=e.data,r=i.conversationList.length,o=r<=20?r:this.maxCircleLength,0===n)for(a=0;a<o;a++)(u=i.conversationList[a]).unreadCount>0&&u.lastMessage.lastTime&&1e3*u.lastMessage.lastTime>this.currentTime-this.expiredTime&&t.push(u);return[2,t]}}))}))},e}(l),f=function(t){function e(e){var n=t.call(this)||this;return n.tim=null,n.expiredTime=3e4,n.tim=e.tim,n}return r(e,t),e.prototype.doFilter=function(t){return o(this,void 0,void 0,(function(){var e,n,i,r,o,a,l,c,d=this;return s(this,(function(s){switch(s.label){case 0:e=[],n=0,s.label=1;case 1:return n<t.length?(i=t[n],r=i.conversationID,o=i.unreadCount,[4,this.tim.getMessageList({conversationID:r,count:o<=15?o:15})]):[3,4];case 2:a=s.sent(),l=a.code,c=a.data,0===l&&e.push(c.messageList.filter((function(t){return t.type===u.TYPES.MSG_CUSTOM&&t.time&&1e3*t.time>d.currentTime-d.expiredTime}))),s.label=3;case 3:return n++,[3,1];case 4:return[2,e.flat().sort((function(t,e){return t.time-e.time}))]}}))}))},e}(l),_=function(t){function e(){return t.call(this)||this}return r(e,t),e.prototype.doFilter=function(t){return o(this,void 0,void 0,(function(){return s(this,(function(e){return[2,t.filter((function(t){var e=!1;try{e=JSON.parse(t.payload.data).businessID===a.BusinessID.SIGNAL}catch(t){}return e}))]}))}))},e}(l),p=function(t){function e(){return t.call(this)||this}return r(e,t),e.prototype.doFilter=function(t){return o(this,void 0,void 0,(function(){var e,n,i;return s(this,(function(r){return Array.isArray(t)?0===t.length?[2,[]]:(e=new Map,t.forEach((function(t,n){var i=JSON.parse(t.payload.data),r=i.inviteID,o=i.actionType;!e.has(r)||e.get(r).actionType!==a.ActionType.INVITE||o!==a.ActionType.CANCEL_INVITE&&o!==a.ActionType.ACCEPT_INVITE&&o!==a.ActionType.REJECT_INVITE&&o!==a.ActionType.INVITE_TIMEOUT?e.set(r,{index:n,actionType:o}):e.delete(r)})),n=[],e.forEach((function(t){t.actionType===a.ActionType.INVITE&&n.push(t.index)})),0===n.length?[2,[]]:(i=[],n.sort((function(t,e){return t-e})).forEach((function(e){i.push(t[e])})),[2,i])):[2,[]]}))}))},e}(l),h=function(){function t(t){this.tim=null;var e=t.tim;this.tim=e}return t.prototype.getHistoricalSignaling=function(){return o(this,void 0,void 0,(function(){var t;return s(this,(function(e){return(t=new c).register(new d({tim:this.tim})),t.register(new f({tim:this.tim})),t.register(new _),t.register(new p),[2,t.run()]}))}))},t}();e.default=h}]).default}));