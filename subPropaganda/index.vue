<template>
  <view class="container">
    <!-- <u--input shape="circle" placeholder="请输入搜索内容" suffixIcon="search"  @clickCubfixIcon="clickCubfixIcon"
			prefixIconStyle="font-size: 22px;color: #909399" v-model="query.KeyWord" @confirm="confirm"></u--input> -->
    <view class="container-search">
      <u-input
        placeholder="请搜索康复知识"
        customStyle="{backgroundColor:#FAFAFA}"
        v-model="query.KeyWord"
      >
        <u-button
          slot="suffix"
          @tap="confirm"
          type="success"
          icon="search"
        ></u-button>
      </u-input>
    </view>
    <!-- <u-tabs :list="topList" @click="click"></u-tabs> -->
    <view class="tabs-list">
      <scroll-view scroll-x="true">
        <view style="flex-wrap: nowrap; display: flex">
          <template v-for="(i, index) in topList">
            <view
              class="tabs-list-item"
              :class="
                query.RecoveryMissionType == i.Id ||
                (!i.Id && query.RecoveryMissionType == '')
                  ? 'tabs-list-item-choose'
                  : ''
              "
              @click="click(i)"
            >
              {{ i.name }}
            </view>
          </template>
        </view>
      </scroll-view>
    </view>

    <u-list @scrolltolower="scrolltolower" v-if="indexList.length > 0">
      <u-list-item v-for="(item, index) in indexList" :key="item.ContentId">
        <u-cell @click="toDetail(item)" :border="false">
          <p
            slot="title"
            class="p-style"
            style="
              color: rgba(51, 51, 51, 1);
              margin-left: 20rpx;
              font-size: 32rpx;
            "
          >
            {{ item.Title }}
          </p>
          <u-image
            slot="icon"
            :showLoading="true"
            :src="item.ShowImg"
            width="70px"
            height="70px"
            customStyle="marginLeft:24rpx"
          />
        </u-cell>
      </u-list-item>

      <view style="height: 50px"></view>
    </u-list>
    <u-empty
      mode="data"
      icon="http://cdn.uviewui.com/uview/empty/data.png"
      text="暂无数据"
      v-if="indexList.length == 0"
    ></u-empty>
  </view>
</template>

<script>
import { getClassification } from '@/api/content.js';
import { getFirstRecoveryMission } from '@/api/consult.js';
import { getMissionImageUrl } from '../utils/mission';
const app = getApp();
export default {
  data() {
    return {
      topList: [],
      query: {
        PageIndex: 1,
        PageSize: 12,
        KeyWord: '',
        RecoveryMissionType: '',
        PatId: '',
        OrgId: '',
      },
      indexList: [],
    };
  },
  onLoad() {
    this.getTop();
    this.getList();
  },
  methods: {
    clickCubfixIcon() {
      this.confirm();
    },
    async getTop() {
      let res = await getClassification(app.globalData.orgId);
      if (res.Type == 200) {
        let arr = [];
        arr.push({
          name: '全部',
        });
        res.Data.forEach((e) => {
          e.Children.forEach((k) => {
            k.Children.forEach((j) => {
              j.name = j.Name;
              arr.push(j);
            });
          });
        });
        this.topList = arr;
      }
    },
    confirm() {
      const isFlag = uni.$inputValueRegExp.test(this.query.KeyWord);
      if (!isFlag) {
        uni.showToast({
          title: this.$errorInputValue,
          icon: 'none',
        });
        return;
      }
      this.query.PageIndex = 1;
      this.indexList = [];
      this.getList();
    },
    click(e) {
      console.log('e', e);
      this.query.RecoveryMissionType = e.Id;
      this.query.PageIndex = 1;
      this.indexList = [];
      this.getList();
    },
    async getList() {
      if (app.globalData.orgId) {
        this.query.OrgId = app.globalData.orgId;
      }
      if (app.globalData.userInfo.Id) {
        this.query.PatId = app.globalData.userInfo.Id;
      }
      let res = await getFirstRecoveryMission(this.query);
      if (res.Type == 200) {
        res.Data.Data.forEach((e, index) => {
          if (!e.ShowImg) {
            e.ShowImg = getMissionImageUrl(index);
          }
          this.indexList.push(e);
        });
      }
    },
    scrolltolower() {
      console.log(1111);
      this.query.PageIndex++;
      this.getList();
    },
    toDetail(item) {
      console.log('item', item);
      uni.navigateTo({
        url: './detail?id=' + item.ContentId,
      });
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  overflow: hidden;

  .container-search {
    background-color: white;
    width: 100%;
    margin-bottom: 32rpx;
    padding: 32rpx 32rpx 24rpx 32rpx;

    /deep/ .u-input {
      padding: 0 0 0 6px !important;
    }
  }

  .tabs-list {
    padding: 0 32rpx;
    width: 100%;
    overflow-x: auto;

    .tabs-list-item {
      border-radius: 16rpx;
      flex-shrink: 0;
      margin-right: 20px;
      width: 100px !important;
      height: 40px;
      text-align: center;
      line-height: 40px;
      background-color: white;
      color: black;
    }

    .tabs-list-item-choose {
      background-color: #29b7a3;
      color: white;
    }
  }

  /deep/ .u-list {
    height: calc(100% - 120px) !important;
  }

  /deep/ .insideStyle {
    padding: 0 32rpx;
  }

  /deep/ .u-cell__body {
    padding: 10px 0;
  }

  /deep/ .u-list-item {
    background-color: white;
    margin-bottom: 24rpx;
    // box-shadow: -1px -1px 4px -2px rgba(0, 0, 0, 0.3) inset;
    box-shadow: 5rpx 6rpx 18rpx 0 rgba(0, 0, 0, 0.08);
    border-radius: 12rpx;
    transform: translateY(10px);
  }

  /deep/ .u-cell {
    border-radius: 16rpx;
  }

  /deep/ .u-tabs {
    background-color: white !important;
  }

  /deep/ .u-input {
    background-color: white !important;
    margin-bottom: 10px;
  }
}
</style>
