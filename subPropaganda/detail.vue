<template>
  <view class="container">
    <view class="container-title">{{ obj.Title }}</view>
    <YzAudio v-if="obj.Media && obj.Media.length" ref="player1" />
    <view style="height: 24rpx"></view>
    <u-parse
      :content="obj.Text"
      @navigate="navigate(e)"
      :tag-style="{ video: 'width:100%;height:225px' }"
    ></u-parse>
  </view>
</template>

<script>
const app = getApp();
import { GetRecoveryMissionById } from '@/api/content.js';
import { dateFormat } from '@/utils/validate.js';
import { operaFollowUpPlanDetail } from '@/api/consult.js';
import YzAudio from '@/components/yz-audio/yz-audio.vue';
export default {
  data() {
    return {
      id: '',
      obj: {},
      ShowId: '',
    };
  },
  components: {
    YzAudio,
  },
  onInit(option) {
    console.log('宣教详情:onInit', option);
  },
  onLoad(option) {
    console.log('宣教详情:onLoad', option);
    this.id = option.id;
    this.getDetailInfo(option);
    this.isdefalut = true;
    if (option.ShowId) {
      this.ShowId = option.ShowId;
    }
  },
  methods: {
    async getDetailInfo(option) {
      if (option.share) {
        uni.setStorageSync('shareObj', {
          RelationId: option.orgId,
          OrganizationId: option.orgId,
          Type: 5,
        });
        app.globalData.orgId = option.orgId;
        app.globalData.orgName = option.orgName;
        uni.setStorageSync('chooseOrgName', option.orgName);
        uni.setStorageSync('chooseOrgID', option.orgId);
      }
      let res = await GetRecoveryMissionById({
        contentId: this.id,
      });
      if (res.Type == 200) {
        res.Data.CreatedTime = dateFormat(res.Data.CreatedTime);
        this.obj = res.Data;
        this.share.imageUrl =
          res.Data.ShowImg ||
          'https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/static/frontend/mp-patient/card-missionary.png';
        this.share.title = res.Data.Title;
        this.handleSetAudioSrcInfo();
        if (this.ShowId) {
          operaFollowUpPlanDetail({
            RelatedId: this.ShowId, //数据操作ID，计划明细ID
            OperaUserId: app.globalData.userInfo.Id,
            OperaUserName: app.globalData.userInfo.Name,
            ExecName: null,
          });
        }
      } else {
        uni.showModal({
          showCancel: false,
          content: res.Content,
          complete() {
            uni.navigateBack();
          },
        });
      }
    },
    navigate(e) {
      console.log('e', e);
    },
    handleSetAudioSrcInfo() {
      if (!this.obj.Media || !this.obj.Media.length) return;
      this.$nextTick(() => {
        const player1 = this.$refs.player1;
        player1.setSrc(this.obj.Media[0]);
        player1.setName(this.obj.Title);
        this.obj.ShowImg && player1.setPoster(this.obj.ShowImg);
      });
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  background: white;
  padding: 20rpx;

  &-title {
    margin-bottom: 24rpx;
    font-size: 18px;
    font-weight: 600;
  }
}
</style>
