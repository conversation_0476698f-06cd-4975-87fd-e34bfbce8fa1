<template>
  <view class="container">
    <u-swiper
      height="200"
      :list="info.GoodsImageUrlList"
      indicator
      indicatorMode="line"
      imgMode="scaleToFill"
    >
    </u-swiper>
    <view class="container-detail">
      <view class="container-detail-top">
        <p class="container-detail-top-title">{{ info.ClassifyName }}</p>
        <p class="container-detail-top-moeny">￥{{ info.SalePrice }}</p>
      </view>

      <view class="container-detail-min" style="display: flex; width: 100%">
        <p style="width: 50px">参数</p>
        <view
          style="display: flex; flex: 1; overflow-x: auto"
          v-if="info.CategoryPropertyList.length > 0"
        >
          <view
            style="flex-shrink: 0; text-align: center; margin-left: 26rpx"
            v-for="(item, index) in info.CategoryPropertyList"
          >
            <p class="container-detail-min-value">{{ item.PropValueName }}</p>
            <p class="container-detail-min-name">{{ item.PropName }}</p>
          </view>
        </view>
        <view v-else>暂无参数说明</view>
      </view>

      <view class="container-detail-parse">
        <u-parse :content="info.GoodsDesc"></u-parse>
      </view>
    </view>
    <u-gap height="80" bgColor="#F7F7F7"></u-gap>
    <view class="container-botton">
      <!-- <u-button :plain="true" shape="circle" :text="BtTitle"
				:customStyle="{'backgroundColor': applyState == 1 ? '#29B7A3' : '#FFFFFF', 'color': applyState == 1 ? 'white' : '#29B7A3','border': applyState == 1 ? '' : '4rpx solid #29B7A3'}"
				customStyle="width:200rpx;" @click="onSubsidies">
			</u-button> -->
      <u-button
        :plain="applyState == 1 ? false : true"
        shape="circle"
        :text="BtTitle"
        color="#29B7A3"
        customStyle="width:200rpx;"
        @click="onSubsidies"
      >
      </u-button>
      <u-button
        color="#29B7A3"
        shape="circle"
        text="直接购买"
        customStyle="flex: 1;margin-left: 20rpx"
        @click="buyFJ"
      ></u-button>
    </view>
  </view>
</template>

<script>
import { getDetail } from '@/api/consult.js';
export default {
  data() {
    return {
      goodsId: '',
      info: {},
      classifyId: '',
      prescriptionId: '',
      BtTitle: '申请补贴',
      applyInfo: null,
      applyState: -1,
    };
  },
  onLoad({ goodsId, classifyId, prescriptionId }) {
    this.goodsId = goodsId;
    this.classifyId = classifyId;
    this.prescriptionId = prescriptionId;
    this.getDetail();
  },
  methods: {
    async getDetail() {
      const res = await getDetail({
        goodsId: this.goodsId,
        classifyId: this.classifyId,
        prescriptionId: this.prescriptionId,
      });
      if (res.Type === 200) {
        res.Data.ShopInfo.GoodsImageUrlList = JSON.parse(
          res.Data.ShopInfo.GoodsImageUrl
        );
        res.Data.ShopInfo.RentGoodsDetail.ClassifyId =
          res.Data.ShopInfo.RentGoodsDetail.ClassifyId ||
          res.Data.ShopInfo.ClassifyId;
        if (!res.Data.ShopInfo.CategoryPropertyList) {
          res.Data.ShopInfo.CategoryPropertyList = [];
        }
        this.info = res.Data.ShopInfo;
        res.Data.ApplyInfo && this.getApplyState(res.Data.ApplyInfo.State);
        this.applyInfo = res.Data.ApplyInfo;
      }
    },
    buyFJ() {
      uni.navigateToMiniProgram({
        appId: this.info.MinAppId,
        path: this.info.MinSharePath,
      });
    },
    getApplyState(state) {
      this.applyState = state;
      switch (state) {
        case 0:
          this.BtTitle = '已申请';
          break;
        case 1:
          this.BtTitle = '补贴购买';
          break;
        case 2:
          this.BtTitle = '已拒绝';
          break;
        case 3:
          this.BtTitle = '已作废';
          break;
      }
    },
    onSubsidies() {
      if (!this.applyInfo) {
        // 没有提交过申请
        const info = encodeURIComponent(
          JSON.stringify(this.info.RentGoodsDetail)
        );
        uni.navigateTo({
          url:
            './subsidies?info=' +
            info +
            '&prescriptionId=' +
            this.prescriptionId +
            '&goodsId=' +
            this.goodsId,
        });
      } else {
        // 提交过了申请
        if (this.applyState === 1) {
          // 通过
          uni.navigateToMiniProgram({
            appId: this.info.RentGoodsDetail.MinAppId,
            path: this.info.RentGoodsDetail.MinSharePath,
          });
        } else {
          const info = encodeURIComponent(
            JSON.stringify(this.info.RentGoodsDetail)
          );
          const info2 = encodeURIComponent(JSON.stringify(this.applyInfo));
          console.log('info2', info2);
          uni.navigateTo({
            url:
              './subsidies?info=' +
              info +
              '&prescriptionId=' +
              this.prescriptionId +
              '&goodsId=' +
              this.goodsId +
              '&applyInfo=' +
              info2,
          });
        }
      }
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  &-detail {
    padding: 28rpx 24rpx;

    &-top {
      background-image: url('/static/images/goodsBack.png');
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center center;
      position: relative;
      height: 85px;
      padding: 20rpx 32rpx;

      &-moeny {
        font-size: 17px;
        font-weight: 600;
        color: #ffffff;
        margin-top: 10rpx;
      }

      &-title {
        font-size: 34rpx;
        font-weight: 600;
        color: #ffffff;
        line-height: 48rpx;
      }
    }

    &-min {
      margin-top: 12px;
      position: relative;
      height: 85px;
      padding: 24rpx;
      box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(27, 188, 156, 0.08);
      border-radius: 16rpx;
      background: #ffffff;

      &-value {
        font-weight: 400;
        color: #333333;
        font-size: 24rpx;
      }

      &-name {
        font-weight: 400;
        color: #999999;
        font-size: 24rpx;
      }
    }
  }

  &-parse {
    margin-top: 12rpx;
  }

  &-botton {
    height: 100rpx;
    width: 90%;
    position: fixed;
    bottom: 40rpx;
    left: 5%;
    right: 5%;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20rpx;
  }
}
</style>
