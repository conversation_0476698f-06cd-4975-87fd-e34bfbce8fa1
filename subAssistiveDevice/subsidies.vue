<template>
  <view class="container">
    <view class="container-fix" v-if="ShowId">
      <span v-if="applyInfos.State === 0">辅具咨询师审核中，请耐心等待</span>
      <span v-if="applyInfos.State === 2"
        >审核未通过，拒绝原因：{{ applyInfos.Reason || '未知' }}</span
      >
    </view>
    <view class="container-top">
      <u-image
        :showLoading="true"
        :src="infoData.DefaultImageUrl"
        width="192rpx"
        height="192rpx"
        mode="aspectFit"
      ></u-image>
      <view style="margin-left: 24rpx">
        <p style="margin-bottom: 10rpx; font-size: 34rpx">
          {{ infoData.Title }}
        </p>
        <p
          style="
            margin-bottom: 20rpx;
            font-weight: 400;
            color: #333333;
            font-size: 10px;
          "
        >
          型号：{{ infoData.OuterGoodsCode || '暂无型号描述' }}
        </p>
        <p
          style="
            margin-bottom: 20rpx;
            font-weight: 400;
            color: #333333;
            font-size: 10px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          "
        >
          厂家：{{ infoData.SubTitle || '暂无数据' }}
        </p>
        <p
          style="
            font-weight: 400;
            color: #333333;
            font-size: 10px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          "
        >
          参数：{{ infoData.CategoryPropertyListData || '暂无数据' }}
        </p>
      </view>
    </view>

    <view class="container-else">
      <p style="margin-bottom: 16rpx">证明资料</p>
      <u-upload
        :fileList="fileList3"
        @afterRead="afterRead"
        @delete="deletePic"
        name="5"
        multiple
        :maxCount="9"
        width="80"
        height="80"
        customStyle="marginRight:16rpx"
      ></u-upload>

      <u-cell-group :border="false">
        <u-cell
          :border="false"
          :title="item.PropName"
          :value="item.PropValueName"
          v-for="item in infoData.InnerPropertyList"
          :key="item.PropId"
        ></u-cell>
      </u-cell-group>
    </view>

    <u-button
      type="success"
      @click="save"
      shape="circle"
      :text="btnText"
      :disabled="disabledLodaing"
      customStyle="width:90%;bottom: 30px;position: fixed; left: 50%;transform: translateX(-50%);z-index:999"
    >
    </u-button>
    <u-toast ref="uToast"></u-toast>
    <u-popup
      :show="showPrivacyPopup"
      :closeOnClickOverlay="false"
      :round="10"
      mode="bottom"
      @close="closePrivacyPopup()"
    >
      <view style="padding: 40rpx">
        <p
          style="
            text-align: center;
            margin-bottom: 40rpx;
            font-size: 20px;
            font-weight: 700;
          "
        >
          提示
        </p>
        <text>
          在你使用服务之前，请仔细阅读<text
            @click="onClickPrivacyPopupTitle()"
            style="color: #29b7a3"
            >{{ PrivacyPopupTitle }}</text
          >。如果你同意{{ PrivacyPopupTitle }}，请点击“同意”开始使用。
        </text>
        <view class="display-style" style="margin-top: 40rpx">
          <u-button
            text="拒绝"
            @click="handleRefusePrivacyAuthorization()"
          ></u-button>
          <!-- <u-button type="primary" id="agree-btn" text="同意" openType="agreePrivacyAuthorization"
						@agreeprivacyauthorization="handleAgreePrivacyAuthorization()"></u-button> -->
          <button
            id="agree-btn"
            class="btn-agree"
            open-type="agreePrivacyAuthorization"
            @agreeprivacyauthorization="handleAgreePrivacyAuthorization()"
          >
            同意
          </button>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
const app = getApp();
import { uploadFileToServer } from '../services/UploadService';
import { addPrescritionAssistApply } from '@/api/consult.js';
export default {
  data() {
    return {
      infoData: {},
      prescriptionId: '',
      disabledLodaing: false,
      applyInfos: {},
      ShowId: '',
      btnText: '提交申请',
      fileList3: [],
      okNextBer: true,
    };
  },
  onLoad({ info, prescriptionId, goodsId, applyInfo }) {
    this.saveWXCallBack();
    this.prescriptionId = prescriptionId;
    this.goodsId = goodsId;
    const infoData = JSON.parse(decodeURIComponent(info));
    if (applyInfo) {
      const applyInfoData = JSON.parse(decodeURIComponent(applyInfo));
      applyInfoData.Media = JSON.parse(applyInfoData.Media);
      let arr = [];
      applyInfoData.Media.forEach((k) => {
        arr.push({
          url: k,
        });
      });
      applyInfoData.MediaList = arr;
      this.initData(applyInfoData);
    }
    const CategoryPropertyListData = '';
    infoData.CategoryPropertyList &&
      infoData.CategoryPropertyList.length > 0 &&
      infoData.CategoryPropertyList.forEach((v, index) => {
        CategoryPropertyListData +=
          v.PropName +
          v.PropValueName +
          (index != infoData.CategoryPropertyList.length - 1 ? '/' : '');
      });
    infoData.CategoryPropertyListData = CategoryPropertyListData;
    this.infoData = infoData;
  },
  methods: {
    initData(applyInfoData) {
      this.applyInfos = applyInfoData;
      this.ShowId = applyInfoData.ShowId;
      this.fileList3 = applyInfoData.MediaList;
      this.btnText = '重新提交';
    },
    async afterRead(event) {
      // 当设置 mutiple 为 true 时, file 为数组格式，否则为对象格式
      let lists = [].concat(event.file);
      let fileListLen = this.fileList3.length;
      lists.map((item) => {
        this.fileList3.push({
          ...item,
          status: 'uploading',
          message: '上传中',
        });
        this.okNextBer = false;
      });
      for (let i = 0; i < lists.length; i++) {
        const result = await uploadFileToServer(lists[i].url);
        if (result) {
          if (i === lists.length - 1) {
            this.okNextBer = true;
          }
        }
        let item = this.fileList3[fileListLen];
        this.fileList3.splice(
          fileListLen,
          1,
          Object.assign(item, {
            status: 'success',
            message: '',
            url: result.data.Data,
          })
        );
        fileListLen++;
      }
    },
    // 删除图片
    deletePic(event) {
      this.fileList3.splice(event.index, 1);
    },
    async save() {
      if (!this.okNextBer) {
        this.$refs.uToast.show({
          message: '请等待图片上传完成再点击',
          type: 'error',
        });
        return;
      }
      if (this.fileList3.length <= 0) {
        this.$refs.uToast.show({
          message: '请上传相关证明',
          type: 'error',
        });
        return;
      }
      const Media = [];
      this.fileList3.forEach((v) => {
        Media.push(v.url);
      });
      const data = {
        PrescriptionId: this.prescriptionId, //方案ID
        ApplyUserId: app.globalData.userInfo.Id, //申请人ID，当前登陆人
        ApplyUserName: app.globalData.userInfo.Name, //申请人名称
        ClassifyId: this.infoData.ClassifyId, //辅具类型ID
        GoodsName: this.infoData.ClassifyName, //具体辅具名称
        GoodsImg: this.infoData.DefaultImageUrl, //具体辅具图片
        GoodsId: this.goodsId, //具体辅具ID
        Media: JSON.stringify(Media),
      };
      if (this.ShowId) {
        data.Id = this.ShowId;
      }
      this.disabledLodaing = true;
      const res = await addPrescritionAssistApply(data);
      if (res.Type === 200) {
        getApp().subscribeMessage(
          () => {
            this.$refs.uToast.show({
              message: '提交成功，等待审核',
              type: 'success',
            });
            let pages = getCurrentPages();
            const prePage = pages[pages.length - 2]; //上一个页面
            if (prePage.$vm.getDetail) {
              prePage.$vm.getDetail();
            }
            setTimeout(() => {
              this.disabledLodaing = false;
              uni.navigateBack();
            }, 2000);
          },
          'AssistSupportApply',
          'ShortTerm'
        );
      } else {
        this.$refs.uToast.show({
          message: res.Content,
          type: 'error',
        });
        this.disabledLodaing = false;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  position: relative;

  &-fix {
    text-align: center;
    padding: 20rpx;
    background-color: #29b7a3;
    color: white;
    font-size: 18px;
  }

  &-top {
    width: 90%;
    background: #ffffff;
    border-radius: 16rpx;
    border: 2rpx solid #29b7a3;
    margin: 24rpx auto;
    display: flex;
    padding: 24rpx;
  }

  &-else {
    background-color: #ffffff;
    border-radius: 18rpx;
    height: 100vh;
    padding: 24rpx 56rpx;

    /deep/ .u-upload__button {
      background-color: #f7f7f7 !important;
    }
  }
}
</style>
