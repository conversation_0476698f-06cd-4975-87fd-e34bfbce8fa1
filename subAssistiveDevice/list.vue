<template>
  <view class="container">
    <u-sticky bgColor="#fff">
      <u-tabs
        :list="topList"
        @change="topChange"
        :current="current"
        lineColor="#1BBC9C"
      ></u-tabs>
    </u-sticky>
    <view class="container-remark">
      {{ topList[tabIndex].remark || '暂无说明' }}
    </view>

    <view class="container-list">
      <div
        v-for="(item, index) in items"
        :key="index"
        class="container-list-item"
        @click="onSeeDetail(item)"
      >
        <u-image
          :showLoading="true"
          :src="item.DefaultImageUrl"
          width="100%"
          :height="imageHeight"
          mode="scaleToFill"
        ></u-image>
        <view style="padding: 0 20rpx; margin-top: 10rpx">
          <p
            class="p-style11"
            style="margin-bottom: 8rpx; font-size: 32rpx; font-weight: 600"
          >
            {{ item.Title }}
          </p>
          <div
            style="
              display: flex;
              align-items: flex-end;
              justify-content: space-between;
            "
          >
            <p
              class="p-style2"
              style="font-weight: 400; color: #333333; font-size: 20rpx"
            >
              型号：{{ item.OuterGoodsCode || '暂无型号描述' }}
            </p>
            <p style="font-weight: 600; color: #ff4242; font-size: 20rpx">
              <span>￥</span
              ><span style="font-size: 28rpx">{{ item.SalePrice }}</span>
            </p>
          </div>
        </view>
        <!-- <view class="container-list-item-logo">康复行</view> -->
      </div>
    </view>
    <u-loadmore :status="status" @loadmore="getMoreData" />
    <u-gap height="50" bgColor="#F7F7F7"></u-gap>
  </view>
</template>

<script>
import { getlist } from '@/api/shop.js';
export default {
  data() {
    return {
      topList: [],
      tabIndex: 0,
      items: [],
      PageIndex: 1,
      status: 'loadmore',
      prescriptionId: '',
      imageHeight: 150,
      current: 0,
    };
  },
  onLoad({ info, TrainingActionId, goodsId }) {
    this.prescriptionId = TrainingActionId;
    const data = JSON.parse(decodeURIComponent(info));
    this.FormattingData(data, goodsId);
    this.reSize();
  },
  methods: {
    reSize() {
      const systemInfo = uni.getSystemInfoSync();
      this.imageHeight = ((systemInfo.windowWidth - 20) / 2 / 16) * 10;
    },
    FormattingData(data, goodsId) {
      const arr = [];
      data.forEach((v) => {
        v.AssistInfoObj = JSON.parse(v.AssistInfo);
        arr.push({
          name: v.AssistInfoObj.ClassifyName,
          id: v.AssistInfoObj.ClassifyId,
          remark: v.Remark,
        });
      });
      this.topList = arr;
      this.chooseIndex(goodsId);
      // this.getDataList()
    },
    chooseIndex(goodsId) {
      const index = this.topList.findIndex((e) => e.id === goodsId);
      if (index > -1) {
        this.current = index;
        this.topChange({
          index,
        });
      }
    },
    topChange(index) {
      this.PageIndex = 1;
      this.tabIndex = index.index;
      this.items = [];
      this.getDataList();
    },
    async getDataList() {
      const ClassifyId = this.topList[this.tabIndex].id;
      const data = {
        PageIndex: this.PageIndex,
        PageSize: 20,
        ClassifyId: ClassifyId,
      };
      const res = await getlist(data);
      if (res.Type === 200 && res.Data.Data.length > 0) {
        res.Data.Data.forEach((v) => {
          this.items.push(v);
        });
      } else if (res.Type === 200 && res.Data.Data.length < data.PageSize) {
        this.status = 'nomore';
      } else {
        this.status = 'nomore';
      }
    },
    getMoreData() {
      if (this.status === 'loadmore') {
        this.PageIndex++;
        this.getDataList();
      }
    },
    onSeeDetail(item) {
      uni.navigateTo({
        url:
          './goodsDetail?goodsId=' +
          item.Id +
          '&classifyId=' +
          this.topList[this.tabIndex].id +
          '&prescriptionId=' +
          this.prescriptionId,
      });
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  /deep/ .u-tabs__wrapper__nav__line {
    bottom: 12rpx !important;
  }

  &-remark {
    width: calc(100% - 64rpx);
    margin: 16rpx auto 24rpx;
    background-color: #1bbc9c;
    border-radius: 8rpx;
    font-size: 28rpx;
    font-weight: 600;
    color: #ffffff;
    padding: 20rpx 24rpx;
  }

  &-list {
    display: flex;
    flex-wrap: wrap;

    &-item {
      padding: 4rpx;
      padding-bottom: 20rpx;
      /* 减去 margin 和 border 的宽度 */
      flex-basis: calc(50% - 10px);
      /* 为了让四个div之间产生一定的间距 */
      margin: 5px;
      box-sizing: border-box;
      background: #ffffff;
      border-radius: 9px;
      border: 2px solid #1bbc9c;
      position: relative;

      &-logo {
        width: 92rpx;
        height: 36rpx;
        background: #1bbc9c;
        border-radius: 9px 0px 9px 0px;
        line-height: 36rpx;
        position: absolute;
        top: 0;
        left: 0;
        font-size: 10px;
        font-weight: 400;
        color: #ffffff;
        text-align: center;
      }
    }
  }

  /* 补齐空缺 */
  &-list::after {
    content: '';
    /* 减去 margin 和 border 的宽度 */
    flex-basis: calc(50% - 10px);
    margin: 5px;
    box-sizing: border-box;
  }
}
</style>
