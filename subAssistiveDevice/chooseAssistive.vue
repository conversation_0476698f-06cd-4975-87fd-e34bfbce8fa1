<template>
  <div>
    <u-cell
      :title="item.name"
      :label="item.remark"
      v-for="item in listData"
      :key="item.id"
      @click="onToList(item)"
    >
      <u-image
        slot="icon"
        :showLoading="true"
        :src="item.image"
        width="60px"
        height="60px"
      >
      </u-image>
    </u-cell>
  </div>
</template>

<script>
export default {
  data() {
    return {
      listData: [],
      info: '',
      prescriptionId: '',
    };
  },
  onLoad({ info, TrainingActionId }) {
    this.info = info;
    this.prescriptionId = TrainingActionId;
    const data = JSON.parse(decodeURIComponent(info));
    this.FormattingData(data);
  },
  methods: {
    FormattingData(data) {
      const arr = [];
      data.forEach((v) => {
        v.AssistInfoObj = JSON.parse(v.AssistInfo);
        arr.push({
          name: v.AssistInfoObj.ClassifyName,
          id: v.AssistInfoObj.ClassifyId,
          remark: v.Remark,
          image: v.AssistInfoObj.ClassifyImg,
        });
      });
      this.listData = arr;
    },
    onToList(item) {
      uni.navigateTo({
        url:
          './list?info=' +
          this.info +
          '&TrainingActionId=' +
          this.prescriptionId +
          '&goodsId=' +
          item.id,
      });
    },
  },
};
</script>

<style scoped lang="scss">
/deep/ .u-cell__body {
  background-color: white !important;
}
</style>
